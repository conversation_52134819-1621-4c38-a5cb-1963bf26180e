<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="采购录票" scope="application"/>
<%@ include file="../../common/common.jsp" %>
<script type="text/javascript"
        src='<%=basePath%>static/js/finance/invoice/buy_invoice_input.js?rnd=${resourceVersionKey}'></script>
<style>
    .main-container {
        padding-top: 120px;
    }

    .form-header {
        position: fixed;
        width: 100%;
        display: flex;
        top: 0;
        left: 0;
        padding: 20px;
        z-index: 99;
        background: #fff;
        border-bottom: solid 1px #DDDDDD;
        min-width: 920px;
        line-height: 1.5;
    }

    .form-header.fixed {
        box-shadow: rgba(0, 0, 0, 0.08) 0px 7px 14px;
    }

    .form-header .form-header-img {
        width: 120px;
        height: 70px;
        margin-right: 20px;
        position: relative;
    }


    .form-header .form-header-img:hover .form-header-img-inner {
        display: flex;
    }

    .form-header .form-header-img .form-header-img-inner {
        width: 120px;
        height: 70px;
        top: 0;
        left: 0;
        position: absolute;
        display: none;
        align-items: center;
        justify-content: center;
        background: rgba(0, 0, 0, .6);
        cursor: pointer;
    }

    .form-header .form-header-img .form-header-img-inner::before {
        content: "";
        width: 20px;
        height: 20px;
        display: block;
        background-image: url('<%=basePath%>static/images/fapiao-icon.svg');
        background-size: 100% 100%;
    }

    .form-header .form-header-img img {
        width: 120px;
        height: 70px;
    }

    .form-header .form-header-info {
        flex: 1;
    }

    .form-header .form-header-info .form-header-t {
        display: flex;
        margin-bottom: 10px;
    }

    .form-header .form-header-item {
        flex: 1;
    }

    .form-header .form-header-item .item-label {
        font-size: 12px;
        color: #999;
    }

    .form-header .form-header-item .item-value {
        font-size: 16px;
    }

    .form-header .form-header-tip {
        display: flex;
    }

    .form-header .tip-item {
        color: #999;
        margin-right: 20px;
        position: relative;
        white-space: nowrap;
    }

    .form-header .tip-item.item-mark {
        white-space: normal;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
        height: 18px;
    }

    .form-header .tip-item::before {
        content: "";
        position: absolute;
        width: 1px;
        height: 10px;
        background: #e1e5e8;
        right: -10px;
        top: 4px;
    }

    .form-header .tip-item:last-child::before {
        display: none;
    }

    .form-header .orange {
        color: #f60;
    }

    .form-header .red {
        color: #E64545;
        font-weight: 700;
    }

    .form-header .green {
        color: #13BF13;
    }

    .green {
        color: #13BF13;
    }

</style>

<script>
    window.onload = function() {
        var elements = $('[id^="invoice_num"]');
        elements.each(function() {
            this.dispatchEvent(new Event('blur'));
        });
    }
</script>

<!-- 获取当前日期 -->
<jsp:useBean id="now" class="java.util.Date"/>
<fmt:formatDate value="${now}" type="both" dateStyle="long" var="today" pattern="yyyy-MM-dd"/>
<div class="main-container">
    <div class="form-header J-header">
        <div class="form-header-img J-show-pic">
            <img src="<%= basePath %>static/images/fapiao.svg" alt="">
            <div class="form-header-img-inner" title="查看大图"></div>
            <!-- 这边放发票图片的地址 -->
            <input type="hidden" class="J-pic-url" value="${hxInvoiceVo.attachment}">
        </div>
        <div class="form-header-info">
            <div class="form-header-t">
                <div class="form-header-item">
                    <div class="item-label">发票号</div>
                    <div class="item-value">${hxInvoiceVo.invoiceNum}</div>
                </div>
                <div class="form-header-item">
                    <div class="item-label">发票代码</div>
                    <div class="item-value">${hxInvoiceVo.invoiceCode}</div>
                </div>
                <div class="form-header-item">
                    <div class="item-label">发票总额</div>
                    <div class="item-value J-amount-total">
                        <fmt:formatNumber type="number" value="${hxInvoiceVo.amount}" pattern="0.00"
                                          maxFractionDigits="2"/>
                    </div>
                </div>
                <div class="form-header-item">
                    <div class="item-label">本次录票金额</div>
                    <div class="item-value J-amount">0.00</div>
                    <input type="hidden" class="item-value J-record-amount" value="${hxInvoiceVo.recordedAmount}">
                </div>
                <div class="form-header-item">
                    <div class="item-label">剩余可录票金额</div>
                    <div class="item-value orange J-amount-left"></div>
                </div>
            </div>

            <div class="form-header-tip">
                <div class="tip-item">
                    <c:if test="${hxInvoiceVo.taxRate != null && hxInvoiceVo.invoiceCategory != null}">
                        ${hxInvoiceVo.taxRate}%增值税<c:if test="${hxInvoiceVo.invoiceCategory == '0'}">普通</c:if><c:if
                            test="${hxInvoiceVo.invoiceCategory == '1'}">专用</c:if>发票
                    </c:if>
                    <input type="hidden" id="taxRateFlag" value="<fmt:formatNumber type="number"
							value="${hxInvoiceVo.taxRate/100}" pattern="#.00" />">
                </div>

                <div class="tip-item">
                    <date:date value="${hxInvoiceVo.createTime}" format="yyyy-MM-dd"/>
                </div>
                <div class="tip-item">
                    <c:choose>
                        <c:when test="${hxInvoiceVo.colorType == 1}">蓝字有效</c:when>
                        <c:when test="${hxInvoiceVo.colorType == 2}">红字有效</c:when>
                        <c:when test="${hxInvoiceVo.colorType == 3}">蓝字作废</c:when>
                        <c:when test="${hxInvoiceVo.colorType == 4}">失控</c:when>
                        <c:when test="${hxInvoiceVo.colorType == 5}">异常</c:when>
                    </c:choose>
                </div>
                <div class="tip-item item-mark" title="这里是备注">
                    ${hxInvoiceVo.comment}
                </div>
            </div>
            <form method="post" action="./saveBuyorderInvoice.do" id="invoiceForm">
                <input type="hidden" name="formToken" value="${formToken}"/>
                <input type="hidden" name="saveInvoiceType" id="saveInvoiceType" value="${inputInvoiceType}">
                <!-- 发票代码 -->
                <input type="hidden" name="invoiceCode" id="invoiceCode" value="${hxInvoiceVo.invoiceCode}">
                <!-- 发票号 -->
                <input type="hidden" name="invoiceNo" id="invoiceNo" value="${hxInvoiceVo.invoiceNum}">
                <input type="hidden" name="invoiceType" value="${invoiceType}">
                <!-- 发票红蓝字 -->
                <input type="hidden" name="invoiceColor" id="invoiceColor" value="2-1">
                <input type="hidden" name="colorType" id="colorType" value="2"/>
                <input type="hidden" name="isEnable" id="isEnable" value="1"/>
                <input type="hidden" name="invoiceHref" value="${hxInvoiceVo.attachment}">
                <input type="hidden" id="amount" value="0">
                <!-- 4月1号后税率只有13% -->
                <c:choose>
                    <c:when test="${today >= '2019-04-01'}">
                        <c:set var="taxes" value="${hxInvoiceVo.taxRate/100}"></c:set>
                    </c:when>
                    <c:otherwise>
                        <c:set var="taxes" value="0.16"></c:set>
                    </c:otherwise>
                </c:choose>
                <input type="hidden" name="ratio" id="ratio" value="${taxes}">
                <span id="hideValue" style="display: none;"></span>
            </form>
        </div>
    </div>
    <div class='list-pages-search'>
        <form method="post" id="search" action="<%=basePath%>finance/invoice/buyInvoiceInput.do">
            <ul>
                <li>
                    <label class="infor_name">产品型号</label>
                    <input type="text" class="input-middle" name="model" id="model" value="${bo.model}"/>
                </li>
                <li>
                    <label class="infor_name">供应商</label>
                    <input type="text" class="input-middle" name="traderName" id="traderName" value="${bo.traderName}"/>
                </li>
                <li>
                    <label class="infor_name">产品名称</label>
                    <input type="text" class="input-middle" name="goodsName" id="goodsName" value="${bo.goodsName}"/>
                </li>
                <li>
                    <label class="infor_name">产品品牌</label>
                    <input type="text" class="input-middle" name="brandName" id="brandName" value="${bo.brandName}"/>
                </li>
                <li>
                    <label class="infor_name">订单号</label>
                    <input type="text" class="input-middle" name="buyorderNo" id="buyorderNo" value="${bo.buyorderNo}"/>
                </li>
                <li>
                    <label class="infor_name">票种</label>
                    <select class="input-middle" name="invoiceType" id="invoiceType">
                        <option value="">全部</option>
                        <c:forEach var="list" items="${invoiceTypeList}" varStatus="status">
                            <option value="${list.sysOptionDefinitionId}"
                                    <c:if test="${list.sysOptionDefinitionId eq bo.invoiceType}">selected</c:if>>${list.title}</option>
                        </c:forEach>
                    </select>
                </li>
                <li>
                    <label class="infor_name">采购备注</label>
                    <input type="text" class="input-middle" name="insideComments" id="insideComments"
                           value="${bo.insideComments}"/>
                </li>
            </ul>
            <div class="tcenter">
                <span class="confSearch bt-small bt-bg-style bg-light-blue" onclick="search();"
                      id="searchSpan">搜索</span>
                <span class="bt-small bg-light-blue bt-bg-style mr20" onclick="reset();">重置</span>
            </div>
            <input type="hidden" name="hxInvoiceId" value="${hxInvoiceVo.hxInvoiceId}">
            <input type="hidden" id="warnMessage"
                   value="录票规则: |本次录票总额-可录票金额| <= 1 或 当前录票金额与产品金额不匹配，继续提交后将由财务判断是否合规！">
            <input type="hidden" id="rebateWarnMessage"
                   value="录票规则:  |本次录票总额-（可录票金额-未录票sku返利金额）| > 0 无法录票，本次录入值应小于等于实际可录金额">
        </form>
    </div>

    <div class="parts table-style10-1" id="buyorderInfo">
        <div class="title-container" style='margin-bottom: 10px;'>
            <div class="table-title nobor">产品及订单信息</div>
        </div>
        <c:forEach items="${buyOrderList}" var="list" varStatus="buyNum">
            <table class="table table-style10">
                <thead>
                <tr>
                    <th class="wid15">订单号</th>
                    <th class="wid20">生效时间</th>
                    <th>供应商</th>
                    <th class="wid10">订单总额</th>
                    <th>票种</th>
                    <th class="wid20">付款时间</th>
                    <th>开票备注</th>
                </tr>
                </thead>
                <tbody>
                <tr>
                    <td>
                        <c:choose>
                            <c:when test="${hxInvoiceVo.invoiceStatus eq 5}">
                                <a class="addtitle" href="javascript:void(0);"
                                   tabtitle='{"num":"viewbuyorder${list.buyorderId}","link":"./buyorderExpense/details.do?buyorderExpenseId=${list.buyorderId}","title":"订单信息"}'>${list.buyorderNo}</a>
                            </c:when>
                            <c:otherwise>
                                <a class="addtitle" href="javascript:void(0);"
                                   tabtitle='{"num":"viewbuyorder${list.buyorderId}","link":"./order/buyorder/viewBuyordersh.do?buyorderId=${list.buyorderId}","title":"订单信息"}'>${list.buyorderNo}</a>
                            </c:otherwise>
                        </c:choose>
                    </td>
                    <td><date:date value="${list.validTime}"/></td>
                    <td>
                        <div class="supplier-color">
                            <a class="addtitle" href="javascript:void(0);"
                               tabTitle='{"num":"viewsupplier${list.traderId}","link":"./trader/supplier/baseinfo.do?traderId=${list.traderId}","title":"供应商信息"}'>${list.traderName}</a>
                        </div>
                    </td>
                    <td>${list.totalAmount}</td>
                    <td>
                        <c:forEach var="sysList" items="${invoiceTypeList}">
                            <c:if test="${list.invoiceType eq sysList.sysOptionDefinitionId}">${sysList.title}</c:if>
                        </c:forEach>
                    </td>
                    <td><date:date value="${list.paymentTime}"/></td>
                    <td>${list.invoiceComments}</td>
                </tr>
                <tr>
                    <td colspan="7" class="table-container">
                        <table class="table">
                            <thead class="order_info_thead">
                            <tr>
                                <th>产品/服务</th>
                                <th>品牌</th>
                                <th>型号</th>
                                <th>采购价</th>
                                <th class="wid6">单位</th>
                                <th class="wid6">总数</th>
                                <th class="wid8">已入库数量</th>
                                <th>已录票数量</th>
                                <th>已录票总额</th>
                                <th class="wid10">录票单价</th>
                                <th class="wid10">本次录票数量</th>
                                <th class="wid10">本次录票总额</th>
                                <th class="wid8">不含税金额</th>
                                <th class="wid6">税额</th>
                                <th class="wid6"><span>全选</span><br/><input type="checkbox" name="allcheck"
                                                                              id="checkbox_all${buyNum.index}"
                                                                              onclick="selectAll(this)"></th>
                            </tr>
                            </thead>
                            <tbody class="order_info_body">
                            <c:forEach var="goods" items="${list.buyorderGoodsVoList}">
                                <tr>
                                    <td class="text-left">
                                        <div class="brand-color1">
                                            <a class="addtitle" href="javascript:void(0);"
                                               tabtitle='{"num":"viewgoods${goods.goodsId}","link":"./goods/goods/viewbaseinfo.do?goodsId=${goods.goodsId}","title":"产品信息"}'>${newSkuInfosMap[goods.sku].SHOW_NAME}</a>
                                        </div>
                                        <div>${newSkuInfosMap[goods.sku].SKU_NO}</div>
                                        <div>${newSkuInfosMap[goods.sku].MATERIAL_CODE}</div>
                                        <input type="hidden" id="goodsType${buyNum.index}_${goods.buyorderGoodsId}"
                                               name="goodsType" value="503">
                                        <input type="hidden" name="relatedId"
                                               id="relatedId${buyNum.index}_${goods.buyorderGoodsId}"
                                               value="${list.buyorderId}">
                                        <input type="hidden" name="buyorderGoodsId"
                                               id="buyorderGoodsId${buyNum.index}_${goods.buyorderGoodsId}"
                                               value="${goods.buyorderGoodsId}">
                                    </td>
                                    <td>${newSkuInfosMap[goods.sku].BRAND_NAME}</td>
                                    <td>${newSkuInfosMap[goods.sku].MODEL}</td>
                                    <td>${goods.price}</td>
                                    <td>${newSkuInfosMap[goods.sku].UNIT_NAME}</td>
                                    <td>${goods.num}</td>
                                    <td>${goods.arrivalNum}</td>
                                    <td>${goods.invoicedNum}</td>
                                    <td>${goods.invoicedTotalAmount}</td>
                                    <input type="hidden" name="update_num_pice"
                                           onclick="updateInvoice(${goods.price},${goods.arrivalNum},${goods.invoicedNum},${buyNum.index},${goods.buyorderGoodsId});">
                                    <input type="hidden" id="inNum${buyNum.index}_${goods.buyorderGoodsId}"
                                           value="${goods.invoicedNum}"/>
                                    <fmt:parseNumber value="${goods.price}" type="number" var="goods_price"/>
                                        <%-- 将已录票数量转化为保留0个小数点 (仅作判断除数是否为0用) --%>
                                    <fmt:parseNumber value="${goods.invoicedNum}" type="number" var="innNum"/>
                                    <td id="invoice_price${buyNum.index}_${goods.buyorderGoodsId}">

                                        <c:choose>
                                            <c:when test="${(goods.arrivalNum - innNum) eq 0}">
                                                <fmt:formatNumber type="number" value="0" maxFractionDigits="9"/>
                                            </c:when>

                                            <c:otherwise>
                                                <fmt:parseNumber
                                                        value="${(goods.price * goods.arrivalNum)-goods.invoicedTotalAmount}"
                                                        type="number" pattern="0.*********" var="goods_amount"/>
                                                <fmt:formatNumber type="number"
                                                                  value="${goods_amount/(goods.arrivalNum - goods.invoicedNum)}"
                                                                  maxFractionDigits="9"/><!-- 录票单价 -->
                                            </c:otherwise>
                                        </c:choose>

                                    </td>
                                    <td>
                                        <input type="text" id="invoice_num${buyNum.index}_${goods.buyorderGoodsId}"
                                               name="invoice_num" alt="${buyNum.index}_${goods.buyorderGoodsId}"
                                               value="<fmt:formatNumber type="number" groupingUsed="false" value="${goods.arrivalNum - goods.invoicedNum}" maxFractionDigits="10" />"
                                               onBlur="invoiceNumChange(this,${goods.price},${goods.arrivalNum - goods.invoicedNum}, ${goods.rebatePrice});">
                                        <!--  onkeyup="value=value.replace(/[^\d.]/g,'');" -->
                                        <input type="hidden" id="max_num${buyNum.index}_${goods.buyorderGoodsId}"
                                               value="${goods.arrivalNum - goods.invoicedNum}"/>
                                    </td>
                                    <td>
                                        <input type="text"
                                               id="invoice_totle_amount${buyNum.index}_${goods.buyorderGoodsId}"
                                               name="invoice_totle_amount"
                                               alt="${buyNum.index}_${goods.buyorderGoodsId}"
                                               value="<fmt:formatNumber type="number" groupingUsed="false" value="${(goods.price * goods.arrivalNum)-goods.invoicedTotalAmount}" pattern="0.00" maxFractionDigits="2" />"
                                               onBlur="invoiceTotleAmountChange(this,<fmt:formatNumber type="number" value="${(goods.price * goods.arrivalNum)-goods.invoicedTotalAmount}" pattern="0.00" maxFractionDigits="2" />,${goods.price},${goods.arrivalNum}, ${goods.invoicedNum});">
                                        <input type="hidden" id="max_price${buyNum.index}_${goods.buyorderGoodsId}"
                                               value="<fmt:formatNumber type="number" value="${(goods.price * goods.arrivalNum)-goods.invoicedTotalAmount}" pattern="0.00" maxFractionDigits="2" />"/>
                                        <input type="hidden" id="goods_price${buyNum.index}_${goods.buyorderGoodsId}"
                                               value="<fmt:formatNumber type="number" value="${goods.price}" pattern="0.00" maxFractionDigits="2" />"/>
                                        <input type="hidden" id="standard_price${buyNum.index}_${goods.buyorderGoodsId}"
                                               value="<fmt:formatNumber type="number" value="${(goods.price * goods.arrivalNum)-goods.invoicedTotalAmount}" pattern="0.00" maxFractionDigits="2" />"/>
                                        <input type="hidden" id="deleteRebatePrice${buyNum.index}_${goods.buyorderGoodsId}"
                                               value="<fmt:formatNumber type="number" value="${goods.price - goods.rebatePrice}" pattern="0.00" maxFractionDigits="2" />"/>
                                    </td>
                                    <td id="invoice_no_tax${buyNum.index}_${goods.buyorderGoodsId}">
                                        <fmt:formatNumber type="number" value="${goods_amount/(1+taxes)}"
                                                          pattern="0.*********" maxFractionDigits="9"/>
                                    </td>
                                    <td id="invoice_tax${buyNum.index}_${goods.buyorderGoodsId}">
                                        <fmt:formatNumber type="number" value="${goods_amount-(goods_amount/(1+taxes))}"
                                                          pattern="0.*********" maxFractionDigits="9"/>
                                    </td>
                                    <td class="single_check_box">
                                        <input type="checkbox" id="${buyNum.index}_${goods.buyorderGoodsId}"
                                               value="${list.invoiceType}" name="selectInvoiceName"
                                               goodsPrice="${goods.price}"
                                               maxNum="${goods.arrivalNum - goods.invoicedNum}"
                                               maxPrice=
                                                   <fmt:formatNumber type="number"
                                                                     value="${(goods.price * goods.arrivalNum)-goods.invoicedTotalAmount}"
                                                                     pattern="0.00"
                                                                     maxFractionDigits="2"/> inNum="${goods.invoicedNum}"
                                               class="${list.traderId}"
                                               onchange="selectBuyOrder(this,${goods.price},${goods.arrivalNum - goods.invoicedNum},
                                                   <fmt:formatNumber type="number"
                                                                     value="${(goods.price * goods.arrivalNum)-goods.invoicedTotalAmount}"
                                                                     pattern="0.00"
                                                                     maxFractionDigits="2"/>,${goods.invoicedNum},'${buyNum.index}_${goods.buyorderGoodsId}','${buyNum.index}')">
                                    </td>
                                </tr>
                            </c:forEach>
                            <c:forEach var="goods" items="${list.buyorderExpenseItemDtos}">
                                <tr>

                                    <td class="text-left">
                                        <span class="green">[费用]</span>
                                        <div class="brand-color1">
                                            <a class="addtitle" href="javascript:void(0);"
                                               tabtitle='{"num":"viewgoods${goods.goodsId}","link":"./goods/goods/viewbaseinfo.do?goodsId=${goods.goodsId}","title":"产品信息"}'>${newSkuInfosMap[goods.sku].SHOW_NAME}</a>
                                        </div>
                                        <div>${newSkuInfosMap[goods.sku].SKU_NO}</div>
                                        <div>${newSkuInfosMap[goods.sku].MATERIAL_CODE}</div>
                                        <input type="hidden"
                                               id="goodsType${buyNum.index}_${goods.buyorderExpenseItemId}"
                                               name="goodsType" value="4126">
                                        <input type="hidden" name="relatedId"
                                               id="relatedId${buyNum.index}_${goods.buyorderExpenseItemId}"
                                               value="${goods.buyorderExpenseId}">
                                        <input type="hidden" name="buyorderExpenseGoodsId"
                                               id="buyorderGoodsId${buyNum.index}_${goods.buyorderExpenseItemId}"
                                               value="${goods.buyorderExpenseItemId}">
                                    </td>
                                    <td>${newSkuInfosMap[goods.sku].BRAND_NAME}</td>
                                    <td>${newSkuInfosMap[goods.sku].MODEL}</td>
                                    <td>${goods.price}</td>
                                    <td>${newSkuInfosMap[goods.sku].UNIT_NAME}</td>
                                    <td>${goods.num}</td>
                                    <td>${goods.arrivalNum}</td>
                                    <fmt:parseNumber
                                            value="${(goods.price * goods.arrivalNum)-goods.invoicedTotalAmount}"
                                            type="number" pattern="0.*********" var="goods_amount"/>
                                    <td>${goods.invoicedNum}</td>
                                    <td>${goods.invoicedTotalAmount}</td>
                                    <input type="hidden" name="update_num_pice"
                                           onclick="updateInvoice(${goods.price},${goods.arrivalNum},${goods.invoicedNum},${buyNum.index},${goods.buyorderExpenseItemId});">
                                    <input type="hidden" id="inNum${buyNum.index}_${goods.buyorderExpenseItemId}"
                                           value="${goods.invoicedNum}"/>
                                    <fmt:parseNumber value="${goods.price}" type="number" var="goods_price"/>
                                        <%-- 将已录票数量转化为保留0个小数点 (仅作判断除数是否为0用) --%>
                                    <fmt:parseNumber value="${goods.invoicedNum}" type="number" var="innNum"/>

                                    <td id="invoice_price${buyNum.index}_${goods.buyorderExpenseItemId}">
                                        <fmt:parseNumber
                                                value="${(goods.price * goods.arrivalNum)-goods.invoicedTotalAmount}"
                                                type="number" pattern="0.*********" var="goods_amount"/>
                                        <fmt:formatNumber type="number" value="${goods_amount/(goods.arrivalNum - goods.invoicedNum)}"  maxFractionDigits="9" /><!-- 录票单价 -->
                                    </td>

                                    <td>
                                        <input type="text"
                                               id="invoice_num${buyNum.index}_${goods.buyorderExpenseItemId}"
                                               name="invoice_num" alt="${buyNum.index}_${goods.buyorderExpenseItemId}"
                                               value="<fmt:formatNumber type="number" groupingUsed="false" value="${goods.arrivalNum - goods.invoicedNum}" maxFractionDigits="10" />"
                                               onBlur="invoiceNumChange(this,${goods.price},${goods.arrivalNum - goods.invoicedNum}, 0);">
                                        <input type="hidden" id="max_num${buyNum.index}_${goods.buyorderExpenseItemId}"
                                               value="${goods.arrivalNum - goods.invoicedNum}"/>
                                    </td>
                                    <td>
                                        <input type="text"
                                               id="invoice_totle_amount${buyNum.index}_${goods.buyorderExpenseItemId}"
                                               name="invoice_totle_amount"
                                               alt="${buyNum.index}_${goods.buyorderExpenseItemId}"
                                               value="<fmt:formatNumber type="number" groupingUsed="false" value="${(goods.price * goods.arrivalNum)-goods.invoicedTotalAmount}" pattern="0.00" maxFractionDigits="2" />"
                                               onBlur="invoiceTotleAmountChange(this,<fmt:formatNumber type="number" value="${(goods.price * goods.arrivalNum)-goods.invoicedTotalAmount}" pattern="0.00" maxFractionDigits="2" />,${goods.price}, 0, 0);">
                                        <input type="hidden"
                                               id="max_price${buyNum.index}_${goods.buyorderExpenseItemId}"
                                               value="<fmt:formatNumber type="number" value="${(goods.price * goods.arrivalNum)-goods.invoicedTotalAmount}" pattern="0.00" maxFractionDigits="2" />"/>
                                        <input type="hidden"
                                               id="goods_price${buyNum.index}_${goods.buyorderExpenseItemId}"
                                               value="<fmt:formatNumber type="number" value="${goods.price}" pattern="0.00" maxFractionDigits="2" />"/>
                                        <input type="hidden"
                                               id="standard_price${buyNum.index}_${goods.buyorderExpenseItemId}"
                                               value="<fmt:formatNumber type="number" value="${(goods.price * goods.arrivalNum)-goods.invoicedTotalAmount}" pattern="0.00" maxFractionDigits="2" />"/>
                                        <input type="hidden" id="deleteRebatePrice${buyNum.index}_${goods.buyorderExpenseItemId}" value="-1"/>
                                    </td>
                                    <td id="invoice_no_tax${buyNum.index}_${goods.buyorderExpenseItemId}">
                                        <fmt:formatNumber type="number" value="${goods_amount/(1+taxes)}"
                                                          pattern="0.*********" maxFractionDigits="9"/>
                                    </td>
                                    <td id="invoice_tax${buyNum.index}_${goods.buyorderExpenseItemId}">
                                        <fmt:formatNumber type="number" value="${goods_amount-(goods_amount/(1+taxes))}"
                                                          pattern="0.*********" maxFractionDigits="9"/>
                                    </td>
                                    <td class="single_check_box">
                                        <!-- 订单锁定不允许录票 -->
                                        <input type="checkbox" id="${buyNum.index}_${goods.buyorderExpenseItemId}"
                                               value="${list.invoiceType}" name="selectInvoiceName"
                                               goodsPrice="${goods.price}"
                                               maxNum="${goods.arrivalNum - goods.invoicedNum}"
                                               maxPrice=
                                                   <fmt:formatNumber type="number"
                                                                     value="${(goods.price * goods.arrivalNum)-goods.invoicedTotalAmount}"
                                                                     pattern="0.00"
                                                                     maxFractionDigits="2"/> inNum="${goods.invoicedNum}"
                                               class="${list.traderId}"
                                               onchange="selectBuyOrder(this,${goods.price},${goods.arrivalNum - goods.invoicedNum},
                                                   <fmt:formatNumber type="number"
                                                                     value="${(goods.price * goods.arrivalNum)-goods.invoicedTotalAmount}"
                                                                     pattern="0.00"
                                                                     maxFractionDigits="2"/>,${goods.invoicedNum},'${buyNum.index}_${goods.buyorderExpenseItemId}', '${buyNum.index}')">
                                    </td>
                                </tr>
                            </c:forEach>
                            </tbody>
                        </table>
                    </td>
                </tr>
                </tbody>
            </table>
        </c:forEach>
        <c:if test="${!empty buyOrderList or !empty buyOrderList}">
        <div class="tablelastline">
            本次录票总数：<span class="warning-color1" id="selectNum">0.00</span>，本次录票总额：<span
                class="warning-color1" id="selectPrice">0.00</span>
        </div>
        <div class="table-friend-tip">
            友情提示 <br/>1、本次录票数量<=已到货数量-已录票数量；<br/> 2、做负时，|本次录票数量|<=已录票数量； <br/>
            3、由于算法问题，数额有可能产生0.01元的误差，请忽视该误差；<br/>4、仅允许输入不为0的数字，可精确到小数点后2位；
        </div>
    </div>
    </c:if>
    <c:if test="${empty buyOrderList and empty buyOrderList}">
        <div class='noresult' style='margin-top:-11px;'>
            查询无结果！请尝试使用其他搜索条件。
        </div>
    </c:if>
    <div class="table-buttons tcenter">
        <button type="button" class="bt-bg-style bg-light-green bt-small" onclick="addInvoice();">提交</button>
    </div>
</div>
<%@ include file="../../common/footer.jsp" %>
