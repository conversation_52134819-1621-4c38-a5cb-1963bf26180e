package com.vedeng.crm.config;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.crm.constant.CrmConstants;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.service.UserApiService;
import com.vedeng.security.SecurityConstant;
import com.vedeng.security.service.model.AccountInfo;
import com.vedeng.security.service.model.Permission;
import com.vedeng.security.service.model.SSOUser;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.session.Session;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: ShiroSessionInterceptor
 * @date 2024/7/20 15:33
 */
public class ShiroSessionInterceptor implements HandlerInterceptor {

    private UserApiService userApiService = ApplicationContextProvider.getContext().getBean(UserApiService.class);

//    @Value("${shiro_redis_session:36000000L}")
    private Long shrioSessionTimeOut = 2592000000L;//30天。



    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        request.setAttribute("staticResourceVersion", CrmConstants.getStaticResourceVersion());
        request.setAttribute("checkCrmIsInner", CrmConstants.getCheckCrmIsInner());
        Session sessionShiro = SecurityUtils.getSubject().getSession();
        if(sessionShiro !=null &&  sessionShiro.getTimeout() < shrioSessionTimeOut){
            sessionShiro.setTimeout(shrioSessionTimeOut);
        }
        if (sessionShiro == null) {
            // 异常处理：处理Session无效的情况
            // 可以记录日志或执行其他必要的操作
            return true;
        }
        SSOUser user = (SSOUser) sessionShiro.getAttribute(SecurityConstant.CURRENT_SSO_USER_ATTRIBUTE_NAME);
        if (user != null) {
            HttpSession session = request.getSession();
            if (session.getAttribute(ErpConstant.CURRENT_USER) == null ) {
                AccountInfo account = user.getAccount();
                Permission permission = user.getPermission();
                Set<String> permissions = null;
                if (CollUtil.isNotEmpty(permission.getPermissions())) {
                    permissions = permission.getPermissions();
                }
                CurrentUser currentUser = CurrentUser.builder()
                        .id(account.getAccountId())
                        .actualName(account.getRealName())
                        .username(account.getAccountName())
                        .permissions(permissions)
                        .isAdmin(Convert.toInt(account.getAdmin()))
                        .build();
                UserDto userDto =  userApiService.getUserBaseInfo(account.getAccountId());
                currentUser.setHeadPic(userDto.getAliasHeadPicture());
                currentUser.setNumber(userDto.getNumber());
                session.setAttribute(ErpConstant.CURRENT_USER, currentUser);
            }
        }
        return true;
    }
}
