package com.vedeng.infrastructure.kingdee.common;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.*;
import com.vedeng.infrastructure.kingdee.domain.command.*;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/* loaded from: k3cloud-webapi-sdk7.9.2.jar:com/kingdee/bos/webapi/sdk/K3CloudApi.class */
@Slf4j
public class K3CloudApi extends WebApiClient {
    public K3CloudApi() {
    }

    public K3CloudApi(String serverUrl) {
        this(serverUrl, 120);
    }

    public K3CloudApi(IdentifyInfo identify) {
        super(identify);
    }

    public K3CloudApi(String serverUrl, int timeout) {
        super(serverUrl, timeout);
    }

    public List<DataCenter> getDataCenters() {
        return execute("Kingdee.BOS.ServiceFacade.ServicesStub.Account.AccountService.GetDataCenterList", (Object[]) null, new TypeToken<ArrayList<DataCenter>>() {
        }.getType());
    }

    public <T> SaveResult save(String formId, SaveExtCommand<T> param) throws Exception {
        return save(formId, param, InvokeMode.Syn);
    }

    public <T> SaveResult save(String formId, SaveExtCommand<T> param, InvokeMode mode) throws Exception {
        return new Gson().fromJson(executeJson("Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Save", new Object[]{formId, param.toJson()}, mode), SaveResult.class);
    }

    public <T> SaveResult batchSave(String formId, BatchSaveExtCommand<T> param) throws Exception {
        return batchSave(formId, param, InvokeMode.Syn);
    }

    public <T> SaveResult batchSave(String formId, BatchSaveExtCommand<T> param, InvokeMode mode) throws Exception {
        return new Gson().fromJson(executeJson("Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.BatchSave", new Object[]{formId, param.toJson()}, mode), SaveResult.class);
    }

    public <T> SaveResult update(String formId, UpdateExtCommand<T> param) throws Exception {
        return update(formId, param, InvokeMode.Syn);
    }

    public <T> SaveResult update(String formId, UpdateExtCommand<T> param, InvokeMode mode) throws Exception {
        return new Gson().fromJson(executeJson("Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Save", new Object[]{formId, param.toJson()}, mode), SaveResult.class);
    }

    public OperatorResult submit(String formId, OperateParam param) throws Exception {
        return execute("Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Submit", new Object[]{formId, param.toJson()}, OperatorResult.class);
    }

    public OperatorResult submit(String formId, OperateExtCommand param) throws Exception {
        return execute("Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Submit", new Object[]{formId, param.toJson()}, OperatorResult.class);
    }

    public OperatorResult delete(String formId, OperateExtCommand param) throws Exception {
        return execute("Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Delete", new Object[]{formId, param.toJson()}, OperatorResult.class);
    }

    public OperatorResult delete(String formId, OperateParam param) throws Exception {
        return execute("Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Delete", new Object[]{formId, param.toJson()}, OperatorResult.class);
    }

    public OperatorResult audit(String formId, OperateExtCommand param) throws Exception {
        return execute("Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Audit", new Object[]{formId, param.toJson()}, OperatorResult.class);
    }

    public OperatorResult audit(String formId, OperateParam param) throws Exception {
        return execute("Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Audit", new Object[]{formId, param.toJson()}, OperatorResult.class);
    }

    public OperatorResult unAudit(String formId, OperateExtCommand param) throws Exception {
        return execute("Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.UnAudit", new Object[]{formId, param.toJson()}, OperatorResult.class);
    }

    public OperatorResult unAudit(String formId, OperateParam param) throws Exception {
        return execute("Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.UnAudit", new Object[]{formId, param.toJson()}, OperatorResult.class);
    }

    public OperatorResult view(String formId, OperateParam param) throws Exception {
        return execute("Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.View", new Object[]{formId, param.toJson()}, OperatorResult.class);
    }

    public <T> List<T> executeBillQuery(QueryParam data, Class type) throws Exception {
        String json = executeJson("Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.ExecuteBillQuery", new Object[]{data.toJson()}, InvokeMode.Syn);
        if (!json.contains(",\"IsSuccess\":false,")) {
            return loadDataList(data.FieldKeys, type, json);
        }
        throw new Exception(loadErrorMsg(json));
    }

    public String save(String formid, String data) throws Exception {
        return execute("Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Save", new Object[]{formid, data});
    }

    public String batchSave(String formid, String data) throws Exception {
        return execute("Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.BatchSave", new Object[]{formid, data});
    }

    public String audit(String formid, String data) throws Exception {
        return execute("Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Audit", new Object[]{formid, data});
    }

    public String delete(String formid, String data) throws Exception {
        return execute("Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Delete", new Object[]{formid, data});
    }

    public String unAudit(String formid, String data) throws Exception {
        return execute("Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.UnAudit", new Object[]{formid, data});
    }

    public String submit(String formid, String data) throws Exception {
        return execute("Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Submit", new Object[]{formid, data});
    }

    public String view(String formid, String data) throws Exception {
        return execute("Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.View", new Object[]{formid, data});
    }

    public List<List<Object>> executeBillQuery(String data) throws Exception {
        return (List) execute("Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.ExecuteBillQuery", new Object[]{data}, (new ArrayList()).getClass());
    }

    public String draft(String formid, String data) throws Exception {
        return execute("Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Draft", new Object[]{formid, data});
    }

    public String allocate(String formid, String data) throws Exception {
        return execute("Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Allocate", new Object[]{formid, data});
    }

    public String excuteOperation(String formid, String opNumber, String data) throws Exception {
        return execute("Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.ExcuteOperation", new Object[]{formid, opNumber, data});
    }

    public String flexSave(String formid, String data) throws Exception {
        return execute("Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.FlexSave", new Object[]{formid, data});
    }

    public String sendMsg(String data) throws Exception {
        return execute("Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.SendMsg", new Object[]{data});
    }

    public String push(String formid, String data) throws Exception {
        return execute("Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Push", new Object[]{formid, data});
    }

    public String groupSave(String formid, String data) throws Exception {
        return execute("Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.GroupSave", new Object[]{formid, data});
    }

    public String disassembly(String formid, String data) throws Exception {
        return execute("Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Disassembly", new Object[]{formid, data});
    }

    public String queryBusinessInfo(String data) throws Exception {
        return execute("Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.QueryBusinessInfo", new Object[]{data});
    }

    public String queryGroupInfo(String data) throws Exception {
        return execute("Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.QueryGroupInfo", new Object[]{data});
    }

    public OperatorResult workflowAudit(String data) throws Exception {
        String execute = execute("Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.WorkflowAudit", new Object[]{data});
        return new Gson().fromJson(execute, OperatorResult.class);
    }


    public String groupDelete(String data) throws Exception {
        return execute("Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.GroupDelete", new Object[]{data});
    }

    public String cancelAllocate(String formid, String data) throws Exception {
        return execute("Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.CancelAllocate", new Object[]{formid, data});
    }

    public String switchOrg(String data) throws Exception {
        return execute("Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.SwitchOrg", new Object[]{data});
    }

    public String cancelAssign(String formid, String data) throws Exception {
        return execute("Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.CancelAssign", new Object[]{formid, data});
    }

    public String getSysReportData(String formid, String data) throws Exception {
        return execute("Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.GetSysReportData", new Object[]{formid, data});
    }

    public String attachmentUpload(String data) throws Exception {
        return execute("Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.AttachmentUpload", new Object[]{data});
    }

    public String attachmentDownLoad(String data) throws Exception {
        return execute("Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.AttachmentDownLoad", new Object[]{data});
    }

    public RepoResult CheckAuthInfo() throws Exception {
        try {
            if (executeBillQuery("{\"FormId\":\"BD_Currency\",\"FieldKeys\":\"FCODE\",\"OrderString\":\"\",\"FilterString\":\" FNUMBER='PRE001' \",\"TopRowCount\":\"0\",\"StartRow\":\"0\",\"Limit\":\"0\"}").toString().contains("[CNY]")) {
                RepoResult result = new RepoResult() {
                };
                RepoStatus restatus = new RepoStatus() {
                };
                restatus.setIsSuccess(true);
                result.setResponseStatus(restatus);
                return result;
            }
            RepoResult result2 = new RepoResult() {
            };
            RepoStatus restatus2 = new RepoStatus() {
            };
            restatus2.setIsSuccess(false);
            restatus2.setErrorCode("500");
            new RepoError() {
            }.setMessage("The query fails！User authentication information is wrong！");
            result2.setResponseStatus(restatus2);
            return result2;
        } catch (Exception e) {
            log.error("【CheckAuthInfo】处理异常",e);
            RepoResult result3 = new RepoResult() {
            };
            RepoStatus restatus3 = new RepoStatus() {
            };
            restatus3.setIsSuccess(false);
            restatus3.setErrorCode("500");
            new RepoError() {
            }.setMessage(e.getMessage());
            result3.setResponseStatus(restatus3);
            return result3;
        }
    }
}
