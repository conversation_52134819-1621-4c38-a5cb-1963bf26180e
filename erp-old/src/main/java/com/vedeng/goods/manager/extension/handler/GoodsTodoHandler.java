package com.vedeng.goods.manager.extension.handler;

import com.vedeng.todolist.service.ITodoInstance;

/**
 * <AUTHOR> [<EMAIL>]
 */
public interface GoodsTodoHandler {

    /**
     * Constants for service id.
     */
    int NONE = 0;
    int PRICING_SERVICE_ID = 1;
    int GOODS_DELIVERY_DATE_SERVICE_ID = 2;
    int SUPPLIER_AFTER_SALES_SERVICE_ID = 3;
    int VD_AFTER_SALES_SERVICE_ID = 4;
    int REPORT_SERVICE_ID = 5;
    int SIGN_CONTRACT_SERVICE_ID = 6;
    int OPERATION_SERVICE_ID = 7;

    /**
     * Gets the unique service Id.
     *
     * @return
     */
    int getServiceId();

    ITodoInstance getITodoInstance();

    boolean isDone();

    void onHandler();

    void finish(Object param);

    /**
     * Whether the item of goods is done
     *
     * @return
     */
    boolean hasTodoItems();
}
