package com.vedeng.erp.trader.domain.entity;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;


/**
 * @description 客户代理品牌
 * <AUTHOR>
 * @date 2023/8/11 16:46
 **/
@Getter
@Setter
@ToString
@NoArgsConstructor
public class TraderCustomerAgentBrandEntity {
    private Integer traderCustomerAgentBrandId;

    /**
    * 客户ID
    */
    private Integer traderCustomerId;

    /**
    * 品牌ID
    */
    private Integer brandId;
}