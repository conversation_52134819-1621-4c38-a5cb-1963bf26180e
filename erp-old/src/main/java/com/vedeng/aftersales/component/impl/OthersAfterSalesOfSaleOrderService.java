package com.vedeng.aftersales.component.impl;

import com.vedeng.aftersales.component.AbstractAfterSalesOrderServiceOfSaleOrder;
import com.vedeng.aftersales.component.dto.AbstractAfterSaleOrderDto;
import com.vedeng.aftersales.component.dto.AfterSaleOrderAddDto;
import com.vedeng.aftersales.component.dto.AfterSaleOrderCloseDto;
import com.vedeng.aftersales.component.exception.AfterSaleValidateException;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.AfterSalesDetail;
import org.springframework.stereotype.Service;

/**
 * @Author: daniel
 * @Date: 2021/6/21 15 57
 * @Description:
 */
@Service
public class OthersAfterSalesOfSaleOrderService extends AbstractAfterSalesOrderServiceOfSaleOrder {


    @Override
    protected void customerAfterSaleOrderCreateValidate(Object afterSaleOrderAddDto) throws AfterSaleValidateException {

    }

    @Override
    protected void customerPropertySet(AfterSaleOrderAddDto afterSaleOrderAddDto, AfterSales afterSales) {

    }

    @Override
    protected void customerAfterSaleOrderClose(AfterSaleOrderCloseDto afterSaleOrderCloseDto) {

    }

    @Override
    protected void customerAddAfterSaleGood(AfterSaleOrderAddDto afterSaleOrderAddDto) {

    }

    @Override
    protected <T extends AbstractAfterSaleOrderDto> void customerAddAfterSaleDetail(AfterSalesDetail afterSalesDetail, T afterSaleOrderAddDto) {

    }

    @Override
    protected void customeAddDealOfSaleOrder(Object addDto) {

    }

    @Override
    protected void customerAfterSaleOrderModifyValidate(Object modifyDto) throws AfterSaleValidateException {

    }
}
