
$("#mytable").append("<span style=\"display:none;\"><a class=\"addtitle can-click\" href=\"javascript:void(0);\" id=\"openList\"></a></span>");
$.ajax({
    async:false,
    url:'/price/skuPriceModifyRecord/isNeedNotify.do',
    data:{},
    type:"POST",
    dataType : "json",
    success:function(data){
        if(data.code == 1){
            index = layer.confirm('商品价格发生变动，请前往查看', {
                btn: ['前往', '关闭']
                ,btn2: function(index, layero){
                }
            }, function(index, layero){
                //前往按钮的回调
                $.ajax({
                    url:'/price/skuPriceModifyRecord/readPriceChangeRecord.do',
                    type:"POST",
                    data:{},
                    dataType : "json",
                    async: false,
                    success:function(data)
                    {
                        if(data.code == 0){
                            var tital= "{\"num\":\"purchaseTask"+1+"\",\"link\":\"/price/skuPriceModifyRecord/index.do?fromMenuFlag=0\"}";
                            $("#openList").attr('tabTitle',tital);
                            document.getElementById("openList").click();
                            layer.close(index);

                        }
                    }
                });

            });
        }
    }
})