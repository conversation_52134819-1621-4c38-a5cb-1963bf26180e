// 表单日期 -> 点击打开弹层选择时间
Vue.component('ui-form-date-picker', {
    template: `
        <div class="vd-ui-form-date-wrapper">
            <div class="vd-ui-form-date-input-wrap" @click="showDialog">
                <ui-placeholder v-if="!displayValue">{{ placeholder }}</ui-placeholder>
                <div v-else class="date-input-show">
                    <div class="value text-line-1">{{ displayValue }}</div>
                    <i @click.stop="handlerClear" class="vd-ui_icon icon-error2"></i>
                </div>
            </div>

            <crm-slide-dialog ref="dateDialog" :title="title" :maskHide="false">
                <div class="vd-ui-form-date-panel" v-if="type == 'date'">
                    <ui-date-picker
                        type="date"
                        v-model="tempDateTime"
                        :disabled-date="disabledDate"
                        :max="maxTime"
                        :min="minTime"
                        @input="handlerChange"
                    ></ui-date-picker>
                </div>

                <!-- 再次打开面板时， 默认到开始时间??? -->
                <div class="vd-ui-form-date-panel" v-else-if="type == 'range'">
                    <ui-daterange-picker
                        v-model="tempDateTime"
                        :disabled-date="disabledDate"
                        :start-placeholder="startPlaceholder"
                        :end-placeholder="endPlaceholder"
                        @change="handlerRangeChange"
                    ></ui-daterange-picker>
                </div>
                <div class="slide-dialog-default-footer">
                    <div class="btn-cancel-flex">
                        <vd-ui-button @click="handlerCancel">取消</vd-ui-button>
                    </div>
                    <div class="btn-confirm-flex">
                        <vd-ui-button type="primary" @click="handlerConfirm">确定</vd-ui-button>
                    </div>
                </div>
            </crm-slide-dialog>
        </div>
    `,
    data() {
        return {
            dateTime: '', // 原值记录
            tempDateTime: '', // 传到下一层组件的值，
        }
    },
    props: {
        value: {},
        // 弹窗title
        title: String,
        // 范围:range   默认单个日期:date
        type: {
            type: String,
            default: 'date',
        },
        disabledDate: Function,

        /* 单选入参 */
        placeholder: String,
        maxTime: [String, Date],
        minTime: [String, Date],

        /* 范围入参 */
        startPlaceholder: {
            type: String,
            default: '开始时间'
        },
        endPlaceholder: {
            type: String,
            default: '结束时间'
        }
    },
    computed: {
        displayValue () {
            let str = '';
            if (Object.prototype.toString.call(this.value) == '[object Array]') {
                str = this.value[0] || '';
                this.value[1] && (str += ` - ${this.value[1]}`);
            } else {
                str = this.value || '';
            }
            return str;
        },
    },
    watch: {
        "value": {
            handler () {
                if (this.value) {
                    let defaultVal = this.type == 'range' ? [] : '';
                    this.dateTime = this.value || defaultVal;
                    this.tempDateTime = this.value || defaultVal;
                }
            },
            deep: true,
            immediate: true,
        }
    },
    mounted() {
    },
    methods: {
        showDialog() {
            this.tempDateTime = this.dateTime;
            this.$refs.dateDialog.show();
        },
        handlerClear () {
            if (this.type == 'range') {
                this.dateTime = [];
            } else {
                this.dateTime = '';
            }
            this.tempDateTime = this.dateTime;
            this.handlerConfirm();
        },

        // Slide-Dialog ↓↓↓
        handlerChange(data) {
            console.log('change', data, this.tempDateTime);
        },
        handlerRangeChange (data) {
            console.log('range change:', data, this.tempDateTime);
        },
        // 取消
        handlerCancel () {
            this.$refs.dateDialog.hide();
            this.tempDateTime = this.dateTime; // 取消后·面板恢复原值
        },
        // 确定
        handlerConfirm () {
            let value = JSON.parse(JSON.stringify(this.tempDateTime));
            this.$emit('input', value);
            this.$emit('change', value);
            setTimeout(()=> {
                this.handlerCancel();
            }, 30);
        },
    }
})
