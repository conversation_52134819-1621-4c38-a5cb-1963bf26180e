package com.vedeng.infrastructure.file.domain;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 文件
 * @date 2024/9/27 8:59
 */
@Data
public class GlobalFileDto {

    /**
     * 附件ID，自增主键
     */
    private Integer attachmentId;

    /**
     * 附件类型
     */
    private Integer attachmentType;

    /**
     * 业务类型
     */
    private Integer attachmentFunction;

    /**
     * 业务id
     */
    private Integer relatedId;

    /**
     * 附件名称
     */
    private String name;

    /**
     * 域名
     */
    private String domain;

    /**
     * 地址
     */
    private String uri;

    /**
     * 描述
     */
    private String alt;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 添加时间
     */
    private Long addTime;

    /**
     * 添加人
     */
    private Integer creator;

    /**
     * 文件后缀
     */
    private String suffix;

    /**
     * 完整预览路径: 协议+域名+路径
     */
    private String wholeDisplayUrl;

    /**
     * 完整下载路径: 协议+域名+路径
     */
    private String wholeDownloadUrl;



}
