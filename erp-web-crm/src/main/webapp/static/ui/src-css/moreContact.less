@import (less) '../common.css';

.more-contact-component {
    position: relative;
    margin-bottom: 15px;

    .contact-list {
        .contact-item {
            position: relative;

            .delete {
                position: absolute;
                left: 573px;
                top: 0;
                z-index: 5;
                width: 34px;
                height: 30px;

                > i {
                    width: 34px;
                    height: 30px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    cursor: pointer;

                    &:hover {
                        color: #f60;
                    }
                }
            }
        }
    }

    .add-contact {
        padding-left: 250px;
        margin-top: 15px;

        > a {
            font-size: 12px;
            color: #09f;
            cursor: pointer;

            &:hover {
                color: #f60;
            }

            .icon-add {
                font-size: 16px;
                vertical-align: -2px;
            }
        }
    }

}

.modal-form-wrap {
    min-height: 32px;
}
.other {
    margin-top: 10px;

    .tips {
        margin-top: 5px;
        color: #999;
    }
}