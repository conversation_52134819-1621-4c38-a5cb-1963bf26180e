package com.vedeng.crm.business.quote.facade;

import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.crm.business.quote.domain.dto.*;
import com.vedeng.erp.system.dto.UserDto;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

public interface QuoteFacade {

    QuoteCreateAppChat createAppChat(CreateAppChatDto createAppChatDto,CurrentUser currentUser);
    void sendAppChat(Integer businessChanceId);

    String importNeedsExcelFile(MultipartFile file, Integer quoteorderId, CurrentUser currentUser) throws Exception;

    CrmCoreSkuInfoDto queryInfoBySkuNo(QuerySkuRequestDto requestDto);

    List<CrmCoreSkuInfoDto> queryInfoBySkuNoFast(QuerySkusRequestDto requestDto);

    void exportExcel(HttpServletResponse response,Integer quoteorderId,CurrentUser currentUser) throws IOException;

    QuoteSyncResponseDto sync(QuoteSyncRequestDto quoteSyncRequestDto,CurrentUser currentUser);

    void updateQuoteGoods(QuoteGoodsUpdateRequestDto quoteGoodsUpdateRequestDto, CurrentUser currentUser);

    void insertQuoteGoods(QuoteGoodsInsertRequestDto quoteGoodsUpdateRequestDto, CurrentUser currentUser,boolean checkRepeat);

    void insertOrUpdate(QuoteGoodsRequestDto quoteGoodsRequestDto, CurrentUser currentUser);

    void updateReportStatus(QuoteGoodsRequestDto quoteGoodsRequestDto, CurrentUser currentUser);

    void deleteQuoteGoods(QuoteGoodsDeleteRequestDto deleteRequestDto, CurrentUser currentUser);

    void deleteQuoteNeeds(QuoteNeedsDeleteRequestDto deleteRequestDto, CurrentUser currentUser);

    void validQuote(QuoteValidRequestDto quoteValidRequestDto, CurrentUser currentUser);

    String batchAddQuoteGoods(BatchAddQuoteGoods batchAddQuoteGoods, CurrentUser currentUser);
     /**
     * 添加自定义商品
     * @param batchAddQuoteGoods
     * @param currentUser
     * @return
     */
    String addQuoteGoodsNoSku(CrmQuoteOrderGoodsNoSkuDto crmQuoteOrderGoodsNoSkuDto, CurrentUser currentUser);

    List<AppChatUserDto> queryUserByQuoteorderId(Integer quoteorderId);

    List<CrmUserDto> querySupplierUser();

    void goConsultationReport(ConsultationReportDto consultationReportDto, CurrentUser currentUser);

    void goFinishConsultationReport(Integer quoteorderId, CurrentUser currentUser);

    QuoteSyncInfoDto queryQuoteDetail(Integer quoteorderId);

    CheckAuthorityRepDto checkQuoteApply(Integer quoteorderId);

    List<CrmQuoteOrderCoreSkuDto> findQuoteOrderGoods(Integer quoteorderId);

    QuoteApplyDto queryQuoteApply(Integer quoteorderId,List<CrmQuoteOrderCoreSkuDto> crmQuoteorderGoodsList);

    QuoteSyncInfoDto syncEdit(Integer quoteorderId);


    List<CrmUserDto > queryAskUser(List<String> skuNos);

    void singleAddGoods(SingleAddGoodsRequestDto singleAddGoodsRequestDto, CurrentUser currentUser);
    String batchAddGoodsNeeds(BatchAddGoodsNeedsRequestDto batchAddGoodsNeedsRequestDto, CurrentUser currentUser);

    List<UserDto> getChatGroupUser(Integer businessChanceId);

    QuoteTaskTipsDto taskTips(CurrentUser currentUser, Integer quoteorderId);

    QuoteSummaryInfoDto summaryInfo(Integer quoteorderId);

    void sendAppMessageCard(Integer businessChanceId,String title,String description);

    void updateAppChatName(Integer businessChanceId,String newName);

    CrmQuoteShareDto getQuoteShardInfoById(Integer quoteorderId);

    /**
     * 校验报价单中是否有未建档商品
     * @param quoteorderId
     * @return
     */
    Integer getQuoteWithNoSkuInfoById(Integer quoteorderId);

    String checkQuoteSkuCheckStatus(Integer quoteorderId);


    void addQuoteNeedsDesc(QuoteNeedsDescReqDto requestDto, CurrentUser currentUser);

    void addQuoteGoodsRemark(QuoteGoodsRemarkReqDto requestDto, CurrentUser currentUser);

    List<QuoteorderGoodsRemarkDto> queryAllRemark(Integer quoteorderGoodsId);

    Integer selectQuoteorderIdByBusinessChanceId(Integer bussinessChanceId);

    void close(Integer quoteorderId);
    
    /**
     * 建群审批，兼容企微回调
     * @param approverAppChatDto
     * @param currentUser
     * @return
     */
	QuoteCreateAppChat approverAppChat(ApproverAppChatDto approverAppChatDto, CurrentUser currentUser) throws Exception;
	
	/**
	 * 获取报价单销售的上级负责人
	 * @param appChatDto
	 * @param quoteorderId
	 */
	UserDto queryChargeInfo(Integer quoteorderId);
	
	/**
	 * 更新企微模板消息的按钮状态
	 * @param responseCode 72小时有效，且只可使用一次
	 */
	void updateQwMessageButton(String responseCode,String toUserId);
}
