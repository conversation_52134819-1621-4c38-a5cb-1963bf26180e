<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.logistics.dao.ExpressDetailMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.logistics.model.ExpressDetail" >
    <id column="EXPRESS_DETAIL_ID" property="expressDetailId" jdbcType="INTEGER" />
    <result column="EXPRESS_ID" property="expressId" jdbcType="INTEGER" />
    <result column="BUSINESS_TYPE" property="businessType" jdbcType="INTEGER" />
    <result column="RELATED_ID" property="relatedId" jdbcType="INTEGER" />
    <result column="NUM" property="num" jdbcType="INTEGER" />
    <result column="AMOUNT" property="amount" jdbcType="DECIMAL" />
  </resultMap>

  <sql id="Base_Column_List" >
    EXPRESS_DETAIL_ID, EXPRESS_ID, BUSINESS_TYPE, RELATED_ID, NUM, AMOUNT
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from T_EXPRESS_DETAIL
    where EXPRESS_DETAIL_ID = #{expressDetailId,jdbcType=INTEGER}
  </select>
    <select id="getExpressArrivalDetailByRelatedIdListAndBusinessType" resultType="com.vedeng.logistics.model.vo.ExpressArrivalDetailVo">
      select ed.NUM                              as DELIVERY_NUM,
             if(e.ARRIVAL_STATUS = 2, ed.NUM, 0) as ARRIVAL_NUM,
             ed.RELATED_ID,
             e.ARRIVAL_TIME,
             e.ADD_TIME                          as DELIVERY_TIME
      from T_EXPRESS e
             join T_EXPRESS_DETAIL ed on e.EXPRESS_ID = ed.EXPRESS_ID
      where e.IS_ENABLE = 1
        and ed.RELATED_ID in
        <foreach collection="relatedIdList" item="item" index="index" open="(" close=")" separator=",">
          #{item}
        </foreach>
        and ed.BUSINESS_TYPE = #{businessType}
    </select>
    <select id="batchBuyorderAllDeliveryNum" resultType="com.vedeng.order.model.vo.BuyorderGoodsVo" parameterType="com.vedeng.order.model.vo.BuyorderGoodsVo">
      select
      sum(NUM) AS deliveryNum, RELATED_ID AS buyorderGoodsId
      from
      T_EXPRESS_DETAIL
      where BUSINESS_TYPE = 515
      AND RELATED_ID in
      <foreach collection="bgList" item="bg" index="index" open="(" close=")" separator=",">
        #{bg.buyorderGoodsId}
      </foreach>
      GROUP BY RELATED_ID
    </select>
  <select id="selectAllByRelatedKey" resultType="com.vedeng.logistics.model.Express">
	  SELECT b.*
	  FROM T_EXPRESS_DETAIL a
	  		 LEFT JOIN T_EXPRESS b on a.EXPRESS_ID = b.EXPRESS_ID
	  WHERE a.RELATED_ID = #{bgv.buyorderGoodsId,jdbcType=INTEGER}
	    AND a.BUSINESS_TYPE = 515
	    AND (b.LOGISTICS_COMMENTS IS NULL OR b.LOGISTICS_COMMENTS != '虚拟快递单')
	    AND b.IS_ENABLE = 1
	    AND b.ARRIVAL_STATUS = 0
	</select>
  <resultMap id="resultMapByExpress" type="com.vedeng.logistics.model.Express">

    <id column="EXPRESS_ID" property="expressId" jdbcType="INTEGER" />
    <result column="LOGISTICS_ID" property="logisticsId" jdbcType="INTEGER" />
    <result column="LOGISTICS_NO" property="logisticsNo" jdbcType="VARCHAR" />
    <result column="COMPANY_ID" property="companyId" jdbcType="INTEGER" />
    <result column="DELIVERY_TIME" property="deliveryTime" jdbcType="BIGINT" />
    <result column="ARRIVAL_STATUS" property="arrivalStatus" jdbcType="BIT" />
    <result column="ARRIVAL_TIME" property="arrivalTime" jdbcType="BIGINT" />
    <result column="DELIVERY_FROM" property="deliveryFrom" jdbcType="INTEGER" />
    <result column="LOGISTICS_COMMENTS" property="logisticsComments" jdbcType="VARCHAR" />
    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
    <result column="IS_ENABLE" property="isEnable" jdbcType="BIT" />
    <result column="LOGISTICS_NAME" property="logisticsName" jdbcType="VARCHAR" />
    <result column="PAYMENT_TYPE" property="paymentType" jdbcType="INTEGER" />
    <result column="CARD_NUMBER" property="cardnumber" jdbcType="VARCHAR" />
    <result column="BUSINESS_TYPE" property="business_Type" jdbcType="INTEGER" />
    <result column="REAL_WEIGHT" property="realWeight" jdbcType="DECIMAL" />
    <result column="NUM" property="j_num" jdbcType="INTEGER" />
    <result column="DNUM" property="num" jdbcType="INTEGER" />
    <result column="AMOUNT_WEIGHT" property="amountWeight" jdbcType="DECIMAL" />
    <result column="MAIL_GOODS" property="mailGoods" jdbcType="VARCHAR" />
    <result column="MAIL_GOODS_NUM" property="mailGoodsNum" jdbcType="INTEGER" />
    <result column="IS_PROTECT_PRICE" property="isProtectPrice" jdbcType="INTEGER" />
    <result column="PROTECT_PRICE" property="protectPrice" jdbcType="DECIMAL" />
    <result column="IS_RECEIPT" property="isReceipt" jdbcType="INTEGER" />
    <result column="MAIL_COMMTENTS" property="mailCommtents" jdbcType="VARCHAR" />
    <result column="SENT_SMS" property="sentSms" jdbcType="BIT" />
    <result column="CODE" property="code" jdbcType="VARCHAR" />
    <result column="INVOICE_AMOUNT" property="invoiceAmount" jdbcType="DECIMAL" />
    <result column="INVOICE_NO" property="invoiceNo" jdbcType="VARCHAR" />
    <result column="WMS_ORDER_NO" property="wmsOrderNo" jdbcType="VARCHAR" />

    <collection property="expressDetail" ofType="com.vedeng.logistics.model.ExpressDetail"
                column="EXPRESS_ID" javaType="ArrayList" select="com.vedeng.logistics.dao.ExpressDetailMapper.selectAllByExpressId">
      <id column="EXPRESS_DETAIL_ID" property="expressDetailId" jdbcType="INTEGER" />
      <result column="EXPRESS_ID" property="expressId" jdbcType="INTEGER" />
      <result column="BUSINESS_TYPE" property="businessType" jdbcType="INTEGER" />
      <result column="RELATED_ID" property="relatedId" jdbcType="INTEGER" />
      <result column="NUM" property="num" jdbcType="INTEGER" />
      <result column="AMOUNT" property="amount" jdbcType="DECIMAL" />
      <result column="HISTORICAL_NUM" property="historicalNum" jdbcType="INTEGER" />
      <result column="NON_ALL_ARRIVAL_REASON" property="nonAllArrivalReason" jdbcType="VARCHAR" />
    </collection>
  </resultMap>

  <select id="selectAllInRelatedKey" resultMap="resultMapByExpress">
    select e.*, bg.BUYORDER_ID
    from T_EXPRESS e
    left join T_EXPRESS_DETAIL ed on e.EXPRESS_ID = ed.EXPRESS_ID
    left join T_BUYORDER_GOODS bg on ed.RELATED_ID = bg.BUYORDER_GOODS_ID
    where e.IS_ENABLE = 1
    and ed.BUSINESS_TYPE = 515
    and e.EXPRESS_ID in
    <foreach collection="expressIdList" item="item" index="index" open="(" close=")" separator=",">
      #{item,jdbcType=INTEGER}
    </foreach>
  </select>
  <select id="selectOneByRelatedKey" resultType="com.vedeng.logistics.model.ExpressDetail">
    select *
    from T_EXPRESS_DETAIL
    where EXPRESS_ID = #{paramsVariable.expressId,jdbcType=INTEGER}
      and RELATED_ID = #{paramsVariable.buyorderGoodsId,jdbcType=INTEGER}
      and BUSINESS_TYPE = 515
  </select>
    <select id="selectAllByExpressId" resultType="com.vedeng.logistics.model.ExpressDetail">
        select * from T_EXPRESS_DETAIL where EXPRESS_ID = #{expressId,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from T_EXPRESS_DETAIL
    where EXPRESS_DETAIL_ID = #{expressDetailId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteSelective" parameterType="com.vedeng.logistics.model.ExpressDetail">
    delete from T_EXPRESS_DETAIL
    where 
    	<if test="expressDetailId != null" >
    		EXPRESS_DETAIL_ID = #{expressDetailId,jdbcType=INTEGER}
        </if>
        <if test="expressId != null" >
    		EXPRESS_ID = #{expressId,jdbcType=INTEGER}
        </if>
        <if test="businessType != null" >
    		BUSINESS_TYPE = #{businessType,jdbcType=INTEGER}
        </if>
  </delete>
  <insert id="insert" parameterType="com.vedeng.logistics.model.ExpressDetail" >
    insert into T_EXPRESS_DETAIL (EXPRESS_DETAIL_ID, EXPRESS_ID, BUSINESS_TYPE, 
      RELATED_ID, NUM, AMOUNT
      )
    values (#{expressDetailId,jdbcType=INTEGER}, #{expressId,jdbcType=INTEGER}, #{businessType,jdbcType=INTEGER}, 
      #{relatedId,jdbcType=INTEGER}, #{num,jdbcType=INTEGER}, #{amount,jdbcType=DECIMAL}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.logistics.model.ExpressDetail" >
    insert into T_EXPRESS_DETAIL
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="expressDetailId != null" >
        EXPRESS_DETAIL_ID,
      </if>
      <if test="expressId != null" >
        EXPRESS_ID,
      </if>
      <if test="businessType != null" >
        BUSINESS_TYPE,
      </if>
      <if test="relatedId != null" >
        RELATED_ID,
      </if>
      <if test="num != null" >
        NUM,
      </if>
      <if test="amount != null" >
        AMOUNT,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="expressDetailId != null" >
        #{expressDetailId,jdbcType=INTEGER},
      </if>
      <if test="expressId != null" >
        #{expressId,jdbcType=INTEGER},
      </if>
      <if test="businessType != null" >
        #{businessType,jdbcType=INTEGER},
      </if>
      <if test="relatedId != null" >
        #{relatedId,jdbcType=INTEGER},
      </if>
      <if test="num != null" >
        #{num,jdbcType=INTEGER},
      </if>
      <if test="amount != null" >
        #{amount,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <!-- 批量保存  -->
  <insert id="insertSelectiveBatch" parameterType="java.util.List"  keyProperty="expressDetailId" useGeneratedKeys="true">
	  insert into T_EXPRESS_DETAIL (
	        EXPRESS_ID,
	        BUSINESS_TYPE,
	        RELATED_ID,
	        NUM,
	        AMOUNT,
	        HISTORICAL_NUM,
	        NON_ALL_ARRIVAL_REASON
	  )
	  values	
	  <foreach item="data" index="index" collection="list" separator=",">
	  <trim prefix="(" suffix=")" suffixOverrides=",">
	        #{data.expressId,jdbcType=INTEGER},
	        #{data.businessType,jdbcType=INTEGER},
	        #{data.relatedId,jdbcType=INTEGER},
	        #{data.num,jdbcType=INTEGER},
	        #{data.amount,jdbcType=DECIMAL},
	        #{data.historicalNum,jdbcType=INTEGER},
	        #{data.nonAllArrivalReason,jdbcType=VARCHAR},
	  </trim>
	</foreach>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.logistics.model.ExpressDetail" >
    update T_EXPRESS_DETAIL
    <set >
      <if test="expressId != null" >
        EXPRESS_ID = #{expressId,jdbcType=INTEGER},
      </if>
      <if test="businessType != null" >
        BUSINESS_TYPE = #{businessType,jdbcType=INTEGER},
      </if>
      <if test="relatedId != null" >
        RELATED_ID = #{relatedId,jdbcType=INTEGER},
      </if>
      <if test="num != null" >
        NUM = #{num,jdbcType=INTEGER},
      </if>
      <if test="amount != null" >
        AMOUNT = #{amount,jdbcType=DECIMAL},
      </if>
    </set>
    where EXPRESS_DETAIL_ID = #{expressDetailId,jdbcType=INTEGER}
  </update>


  <update id="updateByPrimaryKeySelectiveByExpressId" parameterType="com.vedeng.logistics.model.ExpressDetail" >
    update T_EXPRESS_DETAIL
    <set >
      <if test="amount != null" >
        AMOUNT = #{amount,jdbcType=DECIMAL},
      </if>
    </set>
    where EXPRESS_ID = #{expressId,jdbcType=INTEGER} and  RELATED_ID = #{relatedId,jdbcType=INTEGER}
  </update>

  <update id="updateByPrimaryKey" parameterType="com.vedeng.logistics.model.ExpressDetail" >
    update T_EXPRESS_DETAIL
    set EXPRESS_ID = #{expressId,jdbcType=INTEGER},
      BUSINESS_TYPE = #{businessType,jdbcType=INTEGER},
      RELATED_ID = #{relatedId,jdbcType=INTEGER},
      NUM = #{num,jdbcType=INTEGER},
      AMOUNT = #{amount,jdbcType=DECIMAL}
    where EXPRESS_DETAIL_ID = #{expressDetailId,jdbcType=INTEGER}
  </update>
  <update id="updateExpressDetailNum" parameterType="com.vedeng.logistics.model.ExpressDetail">
    UPDATE T_EXPRESS_DETAIL a
    SET
        a.HISTORICAL_NUM = a.NUM,
        a.NUM = #{expressDetail.num,jdbcType=INTEGER},
        a.NON_ALL_ARRIVAL_REASON = #{expressDetail.nonAllArrivalReason,jdbcType=VARCHAR}
    WHERE
        a.EXPRESS_ID = #{expressDetail.expressId,jdbcType=INTEGER}
     AND a.RELATED_ID = #{expressDetail.relatedId,jdbcType=INTEGER}
     AND a.BUSINESS_TYPE = 515
  </update>

  <select id="queryByRelateAndType" resultType="com.vedeng.logistics.model.ExpressDetail">
    SELECT A.*
    FROM T_EXPRESS_DETAIL A
    LEFT JOIN T_EXPRESS B ON A.EXPRESS_ID = B.EXPRESS_ID
    WHERE A.RELATED_ID = #{relatedId,jdbcType=INTEGER}
      AND A.BUSINESS_TYPE = #{businessType,jdbcType=INTEGER}
      AND B.IS_ENABLE = 1
  </select>

    <select id="getExpressByDetailId" resultType="com.vedeng.logistics.model.Express">
      SELECT
        T2.*
      FROM
        T_EXPRESS_DETAIL T1
          LEFT JOIN T_EXPRESS T2 ON T1.EXPRESS_ID = T2.EXPRESS_ID
      WHERE
        T1.EXPRESS_DETAIL_ID = #{detailId,jdbcType=INTEGER}
      GROUP BY
        T2.EXPRESS_ID
        LIMIT 1
    </select>
</mapper>
