package com.vedeng.erp.kingdee.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import com.vedeng.common.mybatis.jbatis.annotation.GeneratedValue;
import com.vedeng.common.mybatis.jbatis.annotation.Id;
import com.vedeng.common.mybatis.jbatis.annotation.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 金蝶客户
 */
@Getter
@Setter
@ToString
@Table(name = "KING_DEE_CUSTOMER")
public class KingDeeCustomerEntity extends BaseEntity {

    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer id;

    /**
     * 客户编号(使用erp客户id trader customer id)
     */
    private Integer fNumber;

    /**
     * 客户内码 0：表示新增 非0：云星空系统单据ID值
     */
    private String fCustId;

    /**
     * 创建组织id
     */
    private Integer fCreateOrgId;

    /**
     * 使用组织id
     */
    private Integer fUseOrgId;

    /**
     * 客户名称
     */
    private String fName;

    /**
     * 客户简称
     */
    private String fShortName;

    /**
     * 客户类别编码
     */
    private String fCustTypeId;

    /**
     * 通讯地址
     */
    private String fAddress;

    /**
     * 发票抬头
     */
    private String fInvoiceTitle;

    /**
     * 纳税登记号
     */
    private String fTaxRegisterCode;

    /**
     * 开户银行
     */
    private String fInvoiceBankName;

    /**
     * 开票联系电话
     */
    private String fInvoiceTel;

    /**
     * 银行账号
     */
    private String fInvoiceBankAccount;

    /**
     * 开票通讯地址
     */
    private String fInvoiceAddress;

    /**
     * 客户分组编码
     */
    private String fGroup;

    /**
     * 结算币别 默认PRE001
     */
    private String fTradingCurrid;

    /**
     * 结算方式编码
     */
    private String fSettleTypeId;

    /**
     * 销售部门 填写部门编码
     */
    private String fSaldeptId;

    /**
     * 税率编码
     */
    private String fTaxRate;

    /**
     * 贸易条款 这是一个自定义的举例，如果有自定义，需要先确认金蝶单据字段后填写
     */
    private String fQZOKTradeTerms;

    /**
     * 所属医院共体
     */
    private String fqzoksyyygt;

    /**
     * 所属集团
     */
    private String fqzokssjt;

    /**
     * 现用名
     */
    private String fqzokxym;

    /**
     * 客户性质
     */
    private String fqzokkhxztext;

    /**
     * 终端客户分类
     */
    private String fqzokzdkhfltext;

    /**
     * 医疗机构分类
     */
    private String fqzokyljgfltext;

    /**
     * 客户细分类
     */
    private String fqzokkhxfltext;

    /**
     * 医院等级
     */
    private String fqzokyydjtext;

    /**
     * 客户等级文本
     */
    private String fqzokkhdj;

    /**
     * 客户所属省份
     */
    private String fprovince;

    /**
     * 客户银行账户信息
     */
    private String ftBdCustbank;
}