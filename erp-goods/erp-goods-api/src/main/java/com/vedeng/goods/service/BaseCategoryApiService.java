package com.vedeng.goods.service;

import com.github.pagehelper.PageInfo;
import com.vedeng.goods.dto.BaseCategoryDto;
import com.vedeng.goods.dto.CategoryFrontDto;
import com.vedeng.goods.dto.CategoryQueryDto;
import com.vedeng.goods.dto.CategoryResultDto;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/11 16:58
 **/
public interface BaseCategoryApiService {

    List<BaseCategoryDto> findLevelCategory(Integer isDelete, Integer level);

    BaseCategoryDto selectByPrimaryKey(Integer baseCategoryId);

    /**
     * 获取系统内所有未删除的分类
     * @return List<CategoryFrontDto>
     */
    List<CategoryFrontDto> getAllCategory();

    /**
     * 根据三级子id反查到顶级id
     * @param child
     * @return [[1级，2级，3级]]
     */
    List<List<Integer>> bindParentIdByChild(List<Integer> child);

    /**
     * 返回逗号分割的分类集合
     * @param baseCategoryIds
     * @return
     */
    String getCategoryNameByList(List<Integer> baseCategoryIds);

    List<BaseCategoryDto> getCategoryDtoByList(List<Integer> baseCategoryIds);

    /**
     * 根据分类id集合返回分类名称，展示规则:若一个分类下的所有子类都有，则展示父类的名称
     *
     * @param categoryIdList 分类id集合
     * @param existingCategoryList 分类集合
     * @return List<BaseCategoryDto>
     */
    List<BaseCategoryDto> getByIdList(List<Integer> categoryIdList, List<BaseCategoryDto> existingCategoryList);

    /**
     * 同步税收编码
     */
    void synchronousTaxCode(Integer baseCategoryId, String taxCode);

    PageInfo<CategoryResultDto> getCategorybyKeyword(CategoryQueryDto categoryQueryDto);

    Integer getCategoryIdByFullPath(String fullCategoryPath);

    /**
     * 根据分类ID获取完整路径名称
     *
     * @param categoryId 分类ID
     * @return 完整路径名称
     */
    String getFullPathNameById(Integer categoryId);
}
