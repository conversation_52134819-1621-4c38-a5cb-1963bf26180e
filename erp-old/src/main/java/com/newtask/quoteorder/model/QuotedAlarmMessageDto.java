package com.newtask.quoteorder.model;

import com.vedeng.order.enums.QuotedAlarmModeEnum;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.Collections;
import java.util.List;


/**
 * <AUTHOR> [<EMAIL>]
 */
@Data
public class QuotedAlarmMessageDto {

    private Integer quoteOrderId;

    private List<Integer> quotedGoodsIdList;

    /**
     * 报价模式
     *
     * @see QuotedAlarmModeEnum
     */
    private Integer mode;

    private Integer level;


    public static QuotedAlarmMessageDto createWithQuotedAlarmRecord(QuotedAlarmRecord quotedAlarmRecord) {
        QuotedAlarmMessageDto quotedAlarmMessageDto = new QuotedAlarmMessageDto();
        quotedAlarmMessageDto.setQuoteOrderId(quotedAlarmRecord.getQuoteOrderKey());
        quotedAlarmMessageDto.setQuotedGoodsIdList(CollectionUtils.isNotEmpty(quotedAlarmRecord.getQuotedGoodsIdList()) ?
                quotedAlarmRecord.getQuotedGoodsIdList() : Collections.emptyList());
        quotedAlarmMessageDto.setMode(quotedAlarmRecord.getMode());
        quotedAlarmMessageDto.setLevel(quotedAlarmRecord.getCurrentLevel());
        return quotedAlarmMessageDto;
    }

}