<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="编辑基本信息" scope="application"/>
<%@ include file="../common/common.jsp" %>
<%--<%@ include file="supplier_tag.jsp" %>--%>
<script type="text/javascript" src="<%= basePath %>static/js/jquery/ajaxfileupload.js?rnd=${resourceVersionKey}"></script>
<div style="width: 100%;height: 20px"></div>
<form method="post"
      action="${pageContext.request.contextPath}/trader/supplier/saveeditbaseinfosimple.do" id="supplierForm">
    <div class="baseinforcontainer" style="padding-bottom: 15px;">
        <div class="border overflow-hidden">
            <div class="baseinfor f_left">基本信息</div>
        </div>
        <div class="baseinforeditform">
            <ul>
                <li>
                    <div class="infor_name" style="width: 155px;">
                        <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="28px">
                        <span>*</span>
                        <lable>供应商名称</lable>
                    </div>
                    <div class="f_left">
                        <input type="text" class="input-largest errobor" name="traderName"
                               id="traderName" value="${traderSupplier.trader.traderName }"/>
                    </div>
                    &nbsp;&nbsp;
                    <input type="button" class="btn btn-blue btn-small margin-left-20" onclick="getTyWarehouseArea()" value="贝登天眼数据同步">
                </li>
                <li>
                    <div class="infor_name" style="width: 155px;">
                        <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="28px">
                        <span>*</span>
                        <lable>供应商所在地区</lable>
                    </div>
                    <div class="f_left">
                        <select class="input-middle mr6" name="province">
                            <option value="0">请选择</option>
                            <c:if test="${not empty provinceList }">
                                <c:forEach items="${provinceList }" var="province">
                                    <option value="${province.regionId }"
                                            <c:if test="${ not empty provinceRegion &&  province.regionId == provinceRegion.regionId }">selected="selected"</c:if>>${province.regionName }</option>
                                </c:forEach>
                            </c:if>
                        </select> <select class="input-middle mr6" name="city">
                        <option value="0">请选择</option>
                        <c:if test="${not empty cityList }">
                            <c:forEach items="${cityList }" var="city">
                                <option value="${city.regionId }"
                                        <c:if test="${ not empty cityRegion &&  city.regionId == cityRegion.regionId }">selected="selected"</c:if>>${city.regionName }</option>
                            </c:forEach>
                        </c:if>
                    </select> <select class="input-middle mr6" name="zone" id="zone">
                        <option value="0">请选择</option>
                        <c:if test="${not empty zoneList }">
                            <c:forEach items="${zoneList }" var="zone">
                                <option value="${zone.regionId }"
                                        <c:if test="${ not empty zoneRegion &&  zone.regionId == zoneRegion.regionId }">selected="selected"</c:if>>${zone.regionName }</option>
                            </c:forEach>
                        </c:if>
                    </select>
                    </div>
                </li>

                <li>
                    <div class="infor_name" style="width: 125px">
                        <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="28px">
                        <span>*</span>
                        <lable>库房地区</lable>
                    </div>
                    <div class="f_left">
                        <select class="input-middle mr6" name="warehouseProvince">
                            <option value="0">请选择</option>
                            <c:if test="${not empty provinceList }">
                                <c:forEach items="${provinceList }" var="province">
                                    <option value="${province.regionId }"
                                            <c:if test="${ not empty warehouseProvinceRegion &&  province.regionId == warehouseProvinceRegion.regionId }">selected="selected"</c:if>>${province.regionName }</option>
                                </c:forEach>
                            </c:if>
                        </select> <select class="input-middle mr6" name="warehouseCity" id="warehouseCity">
                        <option value="0">请选择</option>
                        <c:if test="${not empty warehouseCityList}">
                            <c:forEach items="${warehouseCityList }" var="city">
                                <option value="${city.regionId }"
                                        <c:if test="${ not empty warehouseCityRegion &&  city.regionId == warehouseCityRegion.regionId }">selected="selected"</c:if>>${city.regionName }</option>
                            </c:forEach>
                        </c:if>
                    </select> <select class="input-middle mr6" name="warehouseAreaId" id="warehouseAreaId">
                        <option value="0">请选择</option>
                        <c:if test="${not empty warehouseZoneList}">
                            <c:forEach items="${warehouseZoneList}" var="zone">
                                <option value="${zone.regionId }"
                                        <c:if test="${ not empty warehouseZoneRegion &&  zone.regionId == warehouseZoneRegion.regionId }">selected="selected"</c:if>>${zone.regionName}</option>
                            </c:forEach>
                        </c:if>
                    </select>
                    </div>

                    &nbsp;&nbsp;&nbsp;
                    <span class="font-grey9" id="referenceInfo">
					</span>
                </li>

                <li>
                    <div class="form-tips" style="width: 125px">
                        <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="28px">
                        <span>*</span>
                        <lable>库房详细地址</lable>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <input type="text" name='warehouseAddress' id='warehouseAddress' value="${traderSupplier.warehouseAddress}" class="input-largest"/>
                        </div>
                        <div id="warehouseAddressError"></div>
                    </div>
                </li>

                <li>
                    <div class="infor_name " style="width: 155px;">
                        <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="28px">
                        <span>*</span>
                        <label>供应商类型</label>
                    </div>
                    <div class="f_left inputradio">
                        <select class="input-middle f_left mr10 mt0" id="traderType" name="traderTypeSup">
                            <option value="0">请选择</option>
                            <option value="1" <c:if test="${traderSupplier.traderType eq 1}">selected</c:if>>生产厂家</option>
                            <option value="2" <c:if test="${traderSupplier.traderType eq 2}">selected</c:if>>渠道商</option>
                        </select>
                    </div>
                </li>


            </ul>
        </div>
        <div class="add-tijiao tcenter">
            <input type="hidden" name="traderSupplier.traderSupplierId" value="${traderSupplier.traderSupplierId }">
            <input type="hidden" name="beforeParams" value='${beforeParams}'/>
            <%--<button type="submit">提交</button>--%>
            <%--<button class="dele" id="close-layer" type="button"--%>
                    <%--onclick="goUrl('${pageContext.request.contextPath}/trader/supplier/baseinfo.do?traderSupplierId=${traderSupplier.traderSupplierId}');">--%>
                <%--取消--%>
            <%--</button>--%>
        </div>
    </div>
    <div class="baseinforcontainer" style="padding-bottom: 15px;">
        <div class="border overflow-hidden">
            <div class="baseinfor f_left">资质</div>
        </div>

        <div class="addElement">
            <div class="add-main">
                <input type="hidden" name="domain" value="${domain}" id="domain">
                <ul class="add-detail add-detail1">
                    <li class="table-large" style="display: none">
                        <div class="infor_name" style="width: 155px;">
                            <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="28px">
                            <span>*</span>
                            <label>三证合一</label>
                        </div>
                        <div class="f_left inputfloat mt3">
                            <input type="radio" name="threeInOne" id="one" value="1" onclick="thOne();" />
                            <label class="mr8">是</label>
                            <input type="radio" name="threeInOne" id="zero" value="0" onclick="thZero();"checked="checked"/>
                            <label>否</label>
                            <div id="threeInOneError"></div>
                        </div>
                    </li>
                    <li>
                        <div class="infor_name sex_name" style="width: 155px;">
                            <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="28px">
                            <span>*</span>
                            <label>营业执照</label>
                        </div>
                        <div class="f_left insertli insertli1">
                            <ul>
                                <li style="margin-bottom:0px;">
                                    <div class="f_left">
                                        <input type="file" class="upload_file" id='file_1' name="lwfile"
                                               style="display: none;" onchange="uploadFile(this,1);">
                                        <input type="text" class="input-middle" id="name_1" readonly="readonly"
                                               placeholder="请上传营业执照" name="busName" onclick="file_1.click();"
                                               value="${business.name}">
                                        <input type="hidden" id="uri_1" name="busUri" value="${business.uri}">
                                        <div class="font-red " style="display: none;">请上传营业执照</div>
                                    </div>
                                    <label class="bt-bg-style bt-middle bg-light-blue ml10" type="file" id="busUpload"
                                           onclick="return $('#file_1').click();">浏览</label>
                                    <!-- 上传成功出现 -->
                                    <c:choose>
                                        <c:when test="${!empty business.uri}">
                                            <i class="iconsuccesss ml7" id="img_icon_1"></i>
                                            <a href="http://${business.domain}${business.uri}" target="_blank"
                                               class="font-blue cursor-pointer mr5 ml10 mt4" id="img_view_1">查看</a>
                                            <span class="font-red cursor-pointer mt4" onclick="del(1)"
                                                  id="img_del_1">删除</span>
                                        </c:when>
                                        <c:otherwise>
                                            <i class="iconsuccesss ml7 none" id="img_icon_1"></i>
                                            <a href="" target="_blank"
                                               class="font-blue cursor-pointer mr5 ml10 mt4 none" id="img_view_1">查看</a>
                                            <span class="font-red cursor-pointer mt4 none" onclick="del(1)"
                                                  id="img_del_1">删除</span>
                                        </c:otherwise>
                                    </c:choose>
                                </li>
                                <li style="margin-bottom:0px;">
                                    <label class="f_left mt4 mr10">设置有效期</label>
                                    <div class="f_left">
                                        <div class='inputfloat'>
                                            <input type="text" class="Wdate input-smaller" placeholder="请选择日期" autocomplete="off"
                                                   onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'busEndTime\')}'})"
                                                   name="busStartTime"
                                                   id="busStartTime"
                                                   value='<date:date value ="${business.begintime} " format="yyyy-MM-dd"/>'/>
                                            <input type="text" class="Wdate input-smaller" placeholder="请选择日期" autocomplete="off"
                                                   onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'busStartTime\')}'})"
                                                   name="busEndTime"
                                                   id="busEndTime"
                                                   value='<date:date value ="${business.endtime} " format="yyyy-MM-dd"/>'/>
                                        </div>
                                        <div class="font-red " style="display: none;">开始时间不能为空</div>
                                    </div>
                                </li>
                                <li class="inputfloat" style="margin-bottom:0px;">
                                    <input class="mt8" type="checkbox" name="isMedical"
                                           <c:if test="${business.isMedical eq 1}">checked="checked"</c:if> value="1">
                                    <label class="mt5">含有医疗器械</label>
                                </li>
                            </ul>
                            <div id="businessError"></div>
                        </div>
                    </li>
                    <li style="display: none">
                        <div class="infor_name sex_name" style="width: 155px;">
                            <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="28px">
                            <label>税务登记证</label>
                        </div>
                        <div class="f_left insertli insertli1" id="tax">
                            <ul>
                                <li>
                                    <div class="f_left">
                                        <input type="file" class="upload_file" id='file_2' name="lwfile"
                                               style="display: none;" onchange="uploadFile(this,2);">
                                        <input type="text" class="input-middle" id="name_2" readonly="readonly"
                                               <c:if test="${business.threeInOne eq 1}">disabled="disabled"</c:if>
                                               placeholder="请上传税务登记证" name="taxName" onclick="file_2.click();"
                                               value="${tax.name}">
                                        <input type="hidden" id="uri_2" name="taxUri" value="${tax.uri}">
                                        <div class="font-red " style="display: none;">请上传税务登记证</div>
                                    </div>
                                    <label class="bt-bg-style bt-middle bg-light-blue ml10 <c:if test='${business.threeInOne eq 1}'>bg-opcity</c:if>"
                                           type="file" id="taxUpload"
                                           <c:if test='${business eq null || business.threeInOne ne 1}'>onclick="return $('#file_2').click();"</c:if>>浏览</label>
                                    <!-- 上传成功出现 -->
                                    <c:choose>
                                        <c:when test="${!empty tax.uri}">
                                            <i class="iconsuccesss ml7" id="img_icon_2"></i>
                                            <a href="http://${tax.domain}${tax.uri}" target="_blank"
                                               class="font-blue cursor-pointer mr5 ml10 mt4" id="img_view_2">查看</a>
                                            <span class="font-red <c:if test="${business eq null || business.threeInOne eq 0}">cursor-pointer</c:if> mt4"
                                                  <c:if test="${business eq null || business.threeInOne eq 0}">onclick="del(2)"</c:if>
                                                  id="img_del_2">删除</span>
                                        </c:when>
                                        <c:otherwise>
                                            <i class="iconsuccesss ml7 none" id="img_icon_2"></i>
                                            <a href="" target="_blank"
                                               class="font-blue cursor-pointer mr5 ml10 mt4 none" id="img_view_2">查看</a>
                                            <span class="font-red <c:if test="${business eq null || business.threeInOne eq 0}">cursor-pointer</c:if> mt4 none"
                                                  <c:if test="${business eq null || business.threeInOne eq 0}">onclick="del(2)"</c:if>
                                                  id="img_del_2">删除</span>
                                        </c:otherwise>
                                    </c:choose>

                                </li>
                                <li>
                                    <label class="f_left mt4 mr10 <c:if test='${business.threeInOne eq 1}'>bg-opcity</c:if>">设置有效期</label>
                                    <div class="f_left">
                                        <div class='inputfloat'>
                                            <input type="text"
                                                   class="Wdate input-smaller <c:if test='${business.threeInOne eq 1}'>bg-opcity</c:if>"
                                                   placeholder="请选择日期" autocomplete="off"
                                                   onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'taxEndTime\')}'})"
                                                   name="taxStartTime"
                                                   id="taxStartTime"
                                                   value='<date:date value ="${tax.begintime} " format="yyyy-MM-dd"/>'
                                                   <c:if test="${business.threeInOne eq 1}">disabled="disabled"</c:if>/>
                                            <input type="text"
                                                   class="Wdate input-smaller <c:if test='${business.threeInOne eq 1}'>bg-opcity</c:if>"
                                                   placeholder="请选择日期" autocomplete="off"
                                                   onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'taxStartTime\')}'})"
                                                   name="taxEndTime"
                                                   id="taxEndTime"
                                                   value='<date:date value ="${tax.endtime} " format="yyyy-MM-dd"/>'
                                                   <c:if test="${business.threeInOne eq 1}">disabled="disabled"</c:if>/>
                                        </div>
                                        <div class="font-red " style="display: none;">开始时间不能为空</div>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </li>
                    <li style="display: none">
                        <div class="infor_name sex_name" style="width: 155px;">
                            <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="28px">
                            <label>组织机构代码证</label>
                        </div>
                        <div class="f_left insertli " id="org">
                            <ul>
                                <li style="margin-bottom:0px;">
                                    <div class="f_left">
                                        <input type="file" class="upload_file" id='file_3' name="lwfile"
                                               style="display: none;" onchange="uploadFile(this,3);">
                                        <input type="text" class="input-middle" id="name_3" readonly="readonly"
                                               <c:if test="${business.threeInOne eq 1}">disabled="disabled"</c:if>
                                               placeholder="请上传组织机构代码证" name="orgName" onclick="file_3.click();"
                                               value="${orga.name}">
                                        <input type="hidden" id="uri_3" name="orgaUri" value="${orga.uri}">
                                        <div class="font-red " style="display: none;">请上传组织机构代码证</div>
                                    </div>
                                    <label class="bt-bg-style bt-middle bg-light-blue ml10
                                    	<c:if test='${business.threeInOne eq 1}'>bg-opcity</c:if>" type="file"
                                           id="orgUpload"
                                           <c:if test='${business eq null || business.threeInOne ne 1}'>onclick="return $('#file_3').click();"</c:if>>浏览</label>
                                    <!-- 上传成功出现 -->
                                    <c:choose>
                                        <c:when test="${!empty orga.uri}">
                                            <i class="iconsuccesss ml7" id="img_icon_3"></i>
                                            <a href="http://${orga.domain}${orga.uri}" target="_blank"
                                               class="font-blue cursor-pointer mr5 ml10 mt4" id="img_view_3">查看</a>
                                            <span class="font-red <c:if test="${business eq null || business.threeInOne eq 0}">cursor-pointer</c:if> mt4"
                                                  <c:if test="${business eq null || business.threeInOne eq 0}">onclick="del(3)"</c:if>
                                                  id="img_del_3">删除</span>
                                        </c:when>
                                        <c:otherwise>
                                            <i class="iconsuccesss ml7 none" id="img_icon_3"></i>
                                            <a href="" target="_blank"
                                               class="font-blue <c:if test="${business.threeInOne eq 0}">cursor-pointer</c:if> mr5 ml10 mt4 none"
                                               id="img_view_3">查看</a>
                                            <span class="font-red cursor-pointer mt4 none"
                                                  <c:if test="${business eq null || business.threeInOne eq 0}">onclick="del(3)"</c:if>
                                                  id="img_del_3">删除</span>
                                        </c:otherwise>
                                    </c:choose>

                                </li>
                                <li style="margin-bottom:0px;">
                                    <label class="f_left mt4 mr10 <c:if test='${business.threeInOne eq 1}'>bg-opcity</c:if>">设置有效期</label>
                                    <div class="f_left">
                                        <div class='inputfloat'>
                                            <input class="Wdate input-smaller <c:if test='${business.threeInOne eq 1}'>bg-opcity</c:if>"
                                                   type="text" placeholder="请选择日期" autocomplete="off"
                                                   onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'orgaEndTime\')}'})"
                                                   name="orgaStartTime"
                                                   id="orgaStartTime"
                                                   value='<date:date value ="${orga.begintime} " format="yyyy-MM-dd"/>'
                                                   <c:if test="${business.threeInOne eq 1}">disabled="disabled"</c:if>/>
                                            <input class="Wdate input-smaller <c:if test='${business.threeInOne eq 1}'>bg-opcity</c:if>"
                                                   type="text" placeholder="请选择日期" autocomplete="off"
                                                   onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'orgaStartTime\')}'})"
                                                   name="orgaEndTime"
                                                   id="orgaEndTime"
                                                   value='<date:date value ="${orga.endtime} " format="yyyy-MM-dd"/>'
                                                   <c:if test="${business.threeInOne eq 1}">disabled="disabled"</c:if>/>
                                        </div>
                                        <div class="font-red" style="display: none;">开始时间不能为空</div>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </li>
                    <li class="table-large">
                        <div class="infor_name mt0" style="width: 155px;">
                            <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="28px">
                            <label>是否多证合一</label>
                        </div>
                        <div class="f_left inputfloat">
                            <input type="radio" disabled="disabled" name="medicalQualification" id="med1" value="1" onclick="medOne();"
                                   <c:if test="${twoMedical.medicalQualification eq 1}">checked="checked"</c:if>/>
                            <label class="mr8">是</label>
                            <input type="radio" disabled="disabled" name="medicalQualification" id="med0" value="0" onclick="medZero();"
                                   <c:if test="${twoMedical.medicalQualification ne 1}">checked="checked"</c:if>/>
                            <label>否</label>
                        </div>
                        <div class="font-grey9" style="margin-top:7px;">
                            营业执照和二类备案凭证合一
                        </div>
                    </li>
                    <li>
                        <div class="infor_name sex_name" style="width: 200px;">
                            <span id="twoMedicalSpan">
                                <c:if test="${twoMedical.medicalQualification eq 1}">*</c:if>
                            </span>
                            <label id="twoMedicalLab">
                                <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="28px">
                                <c:choose>
                                    <c:when test="${twoMedical.medicalQualification eq 1}">
                                        多证合一辅助证明
                                    </c:when>
                                    <c:otherwise>
                                        医疗器械二类备案凭证
                                    </c:otherwise>
                                </c:choose>
                            </label>
                        </div>
                        <div class="f_left insertli ">
                            <ul id="two_medical">
                                <li style="margin-bottom:0;">
                                    <c:choose>
                                        <c:when test="${!empty twoMedicalList }">
                                            <c:forEach items="${twoMedicalList }" var="twoMedical" varStatus="st">
                                                <c:if test="${st.index == 0}">
                                                    <c:set var="beginTime" value="${twoMedical.begintime}"></c:set>
                                                    <c:set var="endTime" value="${twoMedical.endtime}"></c:set>
                                                    <c:set var="sn" value="${twoMedical.sn}"></c:set>
                                                </c:if>
                                                <div class="mb8">
                                                    <div class="pos_rel f_left ">
                                                        <input type="file" class="upload_file" name="lwfile"
                                                               id="file_4_${st.index}" style="display: none;"
                                                               onchange="uploadFile(this,4);"/>
                                                        <c:choose>
                                                            <c:when test="${st.index == 0 }">
                                                                <input type="text" class="input-middle" id="name_4"
                                                                       readonly="readonly"
                                                                       placeholder="请上传二类备案凭证" name="twoName"
                                                                       onclick="file_4_${st.index}.click();"
                                                                       value="${twoMedical.name}">
                                                                <input type="hidden" id="uri_4_${st.index}"
                                                                       name="twoUri" value="${twoMedical.uri}">
                                                            </c:when>
                                                            <c:otherwise>
                                                                <input type="text" class="input-middle"
                                                                       id="name_4_${st.index}" readonly="readonly"
                                                                       placeholder="请上传二类备案凭证" name="name_4"
                                                                       onclick="file_4_${st.index}.click();"
                                                                       value="${twoMedical.name}">
                                                                <input type="hidden" id="uri_4_${st.index}" name="uri_4"
                                                                       value="${twoMedical.uri}">
                                                            </c:otherwise>
                                                        </c:choose>
                                                        <label class="bt-bg-style bt-middle bg-light-blue ml10"
                                                               id="twoUpload"
                                                               onclick="return $('#file_4_${st.index}').click();">浏览</label>
                                                    </div>

                                                    <c:choose>
                                                        <c:when test="${twoMedical.uri ne null && twoMedical.uri ne ''}">
                                                            <div class="f_left ">
                                                                <i class="iconsuccesss ml7" id="img_icon_4"></i>
                                                                <a href="http://${twoMedical.domain}${twoMedical.uri}"
                                                                   target="_blank"
                                                                   class="font-blue cursor-pointer mr5 ml10 mt4"
                                                                   id="img_view_4">查看</a>
                                                                <c:choose>
                                                                    <c:when test="${st.index == 0 }">
                                                                        <span class="font-red cursor-pointer mt4"
                                                                              onclick="del(4)" id="img_del_4">删除</span>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <span class="font-red cursor-pointer mt4"
                                                                              onclick="delAttachment(this)"
                                                                              id="img_del_4">删除</span>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </div>
                                                        </c:when>
                                                        <c:otherwise>
                                                            <div class="f_left ">
                                                                <i class="iconsuccesss ml7 none" id="img_icon_4"></i>
                                                                <a href="http://${twoMedical.domain}${twoMedical.uri}"
                                                                   target="_blank"
                                                                   class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                                   id="img_view_4">查看</a>
                                                                <c:choose>
                                                                    <c:when test="${st.index == 0 }">
                                                                        <span class="font-red cursor-pointer mt4 none"
                                                                              onclick="del(4)" id="img_del_4">删除</span>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <span class="font-red cursor-pointer mt4 none"
                                                                              onclick="delAttachment(this)"
                                                                              id="img_del_4">删除</span>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </div>
                                                        </c:otherwise>
                                                    </c:choose>
                                                    <div class="clear"></div>
                                                </div>
                                            </c:forEach>
                                        </c:when>
                                        <c:otherwise>
                                            <div class="mb8">
                                                <div class="f_left">
                                                    <input type="file" class="upload_file" name="lwfile" id="file_4"
                                                           style="display: none;" onchange="uploadFile(this,4);"/>
                                                    <input type="text" class="input-middle" id="name_4"
                                                           readonly="readonly"
                                                           placeholder="请上传二类备案凭证" name="twoName"
                                                           onclick="file_4.click();" value="${twoMedical.name}">
                                                    <input type="hidden" id="uri_4" name="twoUri"
                                                           value="${twoMedical.uri}">
                                                    <div class="font-red " style="display: none;">请上传二类备案凭证</div>
                                                </div>
                                                <label class="bt-bg-style bt-middle bg-light-blue ml10" id="twoUpload"
                                                       onclick="return $('#file_4').click();">浏览</label>
                                                <!-- 上传成功出现 -->
                                                <c:choose>
                                                    <c:when test="${!empty twoMedical.uri}">
                                                        <i class="iconsuccesss ml7" id="img_icon_4"></i>
                                                        <a href="http://${twoMedical.domain}${twoMedical.uri}"
                                                           target="_blank" class="font-blue cursor-pointer mr5 ml10 mt4"
                                                           id="img_view_4">查看</a>
                                                        <span class="font-red cursor-pointer mt4" onclick="del(4)"
                                                              id="img_del_4">删除</span>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <i class="iconsuccesss ml7 none" id="img_icon_4"></i>
                                                        <a href="" target="_blank"
                                                           class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                           id="img_view_4">查看</a>
                                                        <span class="font-red cursor-pointer mt4 none" onclick="del(4)"
                                                              id="img_del_4">删除</span>
                                                    </c:otherwise>
                                                </c:choose>
                                                <div class="clear"></div>
                                                <div id="twoMedicalError"></div>
                                            </div>
                                        </c:otherwise>
                                    </c:choose>

                                    <div class=" clear" id="conadd4">
                                        <span class="bt-border-style bt-small border-blue "
                                              onclick="con_add(4);">继续添加</span>
                                    </div>

                                    <div class="font-grey9" style="margin-top:7px;">
                                        1，图片格式只能JPG、PNG、JPEG、BMP等格式<br>
                                        2，图片大小不超过5M<br>
                                        <div id="medicalQualificationFont" <c:if test="${twoMedical.medicalQualification eq 0}">style="display: none"</c:if>>
                                             3，请上传多证合一辅助证明（必填项）
                                        </div>
                                    </div>
                                </li>
                                <li style="margin-bottom:0;">
                                    <label class='f_left mt4 mr10'>设置有效期</label>
                                    <div class="f_left ">
                                        <div class='inputfloat'>
                                            <input class="Wdate input-smaller" type="text" placeholder="请选择日期" autocomplete="off"
                                                   onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'twoEndTime\')}'})"
                                                   name="twoStartTime"
                                                   id="twoStartTime"
                                                   value='<date:date value ="${beginTime} " format="yyyy-MM-dd"/>'/>
                                            <input class="Wdate input-smaller" type="text" placeholder="请选择日期" autocomplete="off"
                                                   onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'twoStartTime\')}'})"
                                                   name="twoEndTime"
                                                   id="twoEndTime"
                                                   value='<date:date value ="${endTime} " format="yyyy-MM-dd"/>'/>
                                        </div>
                                        <div class="font-red " style="display: none;">开始时间不能为空</div>
                                    </div>
                                </li>
                                <li class="specialli" style="margin-bottom:0;">
                                    <label class="f_left mt4 mr10 <c:if test="${twoMedical.medicalQualification eq 1}">bg-opcity</c:if>" id="twoSnLab">
                                        许可证编号
                                    </label>
                                    <div class="f_left ">
                                        <input <c:if test="${twoMedical.medicalQualification eq 1}">disabled="disabled"</c:if> value="${sn}"
                                               type="text" name="twoSn" class="input-middle" id="twoSn"/>
                                        <div class="font-red " style="display: none;"></div>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </li>

                    <li class="">
                        <div class="infor_name sex_name" style="width: 155px;">
                            <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="28px">
                            <label>销售人员授权书</label>
                        </div>
                        <div class="f_left insertli">
                            <ul id="saleAuth_1">
                                <li>
                                    <div class="f_left">
                                        <input type="file" class="upload_file" name="lwfile" id="file_8"
                                               style="display: none;" onchange="uploadFile(this,8);"/>
                                        <input type="text" class="input-middle" id="name_8" readonly="readonly"
                                               placeholder="请上传销售人员授权书" name="saleAuthBookName"
                                               onclick="file_8.click();" value="${saleAuth.name}">
                                        <input type="hidden" id="uri_8" name="saleAuthBookUri" value="${saleAuth.uri}">
                                        <div id="sale_auto_book" class="font-red " style="display: none;">请上传销售人员授权书
                                        </div>
                                    </div>
                                    <label class="bt-bg-style bt-middle bg-light-blue ml10"
                                           onclick="return $('#file_8').click();">浏览</label>
                                    <!-- 上传成功出现 -->
                                    <c:choose>
                                        <c:when test="${!empty saleAuth.uri}">
                                            <i class="iconsuccesss ml7" id="img_icon_8"></i>
                                            <a href="http://${saleAuth.domain}${saleAuth.uri}" target="_blank"
                                               class="font-blue cursor-pointer mr5 ml10 mt4" id="img_view_8">查看</a>
                                            <span class="font-red cursor-pointer mt4" onclick="del(8)"
                                                  id="img_del_8">删除</span>
                                        </c:when>
                                        <c:otherwise>
                                            <i class="iconsuccesss ml7 none" id="img_icon_8"></i>
                                            <a href="" target="_blank"
                                               class="font-blue cursor-pointer mr5 ml10 mt4 none" id="img_view_8">查看</a>
                                            <span class="font-red cursor-pointer mt4 none" onclick="del(8)"
                                                  id="img_del_8">删除</span>
                                        </c:otherwise>
                                    </c:choose>
                                </li>
                                <li>
                                    <label class="f_left mt4 mr10">设置有效期</label>
                                    <div class="f_left">
                                        <div class='inputfloat'>
                                            <input class="Wdate input-smaller" type="text" placeholder="请选择日期"
                                                   id="saleAuthBookStartTime" autocomplete="off"
                                                   onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'saleAuthBookEndTime\')}'})"
                                                   name="saleAuthBookStartTime"
                                                   value='<date:date value ="${saleAuth.begintime} " format="yyyy-MM-dd"/>'/>
                                            <input class="Wdate input-smaller" type="text" placeholder="请选择日期"
                                                   id="saleAuthBookEndTime" autocomplete="off"
                                                   onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'saleAuthBookStartTime\')}'})"
                                                   name="saleAuthBookEndTime"
                                                   value='<date:date value ="${saleAuth.endtime} " format="yyyy-MM-dd"/>'/>
                                        </div>
                                        <div id="sale_time_div" class="font-red" style="display: none;">开始时间不能为空</div>
                                    </div>

                                </li>
                                <li>
                                    <div class="clear"></div>
                                    <div id="salesAuthError"></div>
                                </li>
                            </ul>
                        </div>
                    </li>
                    <li class="" style="margin-bottom:14px; display: none">
                        <div class="infor_name sex_name" style="width: 155px;">
                            <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="28px">
                            <label>授权销售人信息</label>
                        </div>
                        <div class="f_left insertli">
                            <ul id="saleAuth_2">
                                <li class="specialli" style="margin-bottom:0px;">
                                    <label class="f_left mt4 mr10">职位</label>
                                    <div class="f_left ">
                                        <input type="text" name="authPost" class="input-middle"
                                               value="${saleAuth.authPost}" maxlength="100"/>
                                        <div class="font-red" style="display: none;"></div>
                                    </div>
                                    <div class="clear"></div>
                                </li>
                                <li class="specialli" style="margin-bottom:0px;">
                                    <label class="f_left mt4 mr10">姓名</label>
                                    <div class="f_left ">
                                        <input type="text" name="authUserName" class="input-middle"
                                               value="${saleAuth.authUserName}" maxlength="100"/>
                                        <div class="font-red" style="display: none;"></div>
                                    </div>
                                    <div class="clear"></div>
                                </li>
                                <li class="specialli" style="margin-bottom:0px;">
                                    <label class="f_left mt4 mr10">联系方式</label>
                                    <div class="f_left ">
                                        <input type="text" name="authContactInfo" class="input-middle"
                                               value="${saleAuth.authContactInfo}" maxlength="200"/>
                                        <div class="font-red" style="display: none;"></div>
                                    </div>
                                    <div class="clear"></div>
                                </li>
                            </ul>
                        </div>
                    </li>
                    <li class="" style="display: none">
                        <div class="infor_name sex_name" style="width: 155px;">
                            <label>品牌授权书</label>
                        </div>
                        <div class="f_left insertli">
                            <ul id="brandBook">
                                <li>
                                    <c:choose>
                                        <c:when test="${!empty brandBookList}">
                                            <c:forEach items="${brandBookList }" var="brandBook" varStatus="st">
                                                <c:if test="${st.index == 0}">
                                                    <c:set var="brankBeginTime" value="${brandBook.begintime}"></c:set>
                                                    <c:set var="brankEndTime" value="${brandBook.endtime}"></c:set>
                                                </c:if>
                                                <div class="mb8">
                                                    <div class="pos_rel f_left ">

                                                        <input type="file" class="upload_file" name="lwfile"
                                                               id="file_9_${st.index}" style="display: none;"
                                                               onchange="uploadFile(this,9);"/>
                                                        <c:choose>
                                                            <c:when test="${st.index == 0}">
                                                                <input type="text" class="input-middle" id="name_9"
                                                                       readonly="readonly"
                                                                       placeholder="请上传品牌授权书" name="brandBookName"
                                                                       onclick="file_9_${st.index}.click();"
                                                                       value="${brandBook.name}">
                                                                <input type="hidden" id="uri_9_${st.index}"
                                                                       name="brandBookUri" value="${brandBook.uri}">
                                                            </c:when>
                                                            <c:otherwise>
                                                                <input type="text" class="input-middle"
                                                                       id="name_9_${st.index}" readonly="readonly"
                                                                       placeholder="请上传品牌授权书" name="name_9"
                                                                       onclick="file_9_${st.index}.click();"
                                                                       value="${brandBook.name}">
                                                                <input type="hidden" id="uri_9_${st.index}" name="uri_9"
                                                                       value="${brandBook.uri}">
                                                            </c:otherwise>
                                                        </c:choose>
                                                        <label class="bt-bg-style bt-middle bg-light-blue ml10"
                                                               onclick="return $('#file_9_${st.index}').click();"
                                                               style="margin:0 12px 0 10px">浏览</label>

                                                        <div><span id="brand_book" class="font-red "
                                                                   style="display: none;">请上传品牌授权书</span></div>
                                                    </div>

                                                    <c:choose>
                                                        <c:when test="${brandBook.uri ne null && brandBook.uri ne ''}">
                                                            <div class="f_left ">
                                                                <i class="iconsuccesss ml7" id="img_icon_9"></i>
                                                                <a href="http://${brandBook.domain}${brandBook.uri}"
                                                                   target="_blank"
                                                                   class="font-blue cursor-pointer mr5 ml10 mt4"
                                                                   id="img_view_9">查看</a>
                                                                <c:choose>
                                                                    <c:when test="${st.index == 0}">
                                                                        <span class="font-red cursor-pointer mt4"
                                                                              onclick="del(9)" id="img_del_9">删除</span>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <span class="font-red cursor-pointer mt4"
                                                                              onclick="delAttachment(this)"
                                                                              id="img_del_9">删除</span>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </div>
                                                        </c:when>
                                                        <c:otherwise>
                                                            <div class="f_left ">
                                                                <i class="iconsuccesss ml7 none" id="img_icon_9"></i>
                                                                <a href="http://${brandBook.domain}${brandBook.uri}"
                                                                   target="_blank"
                                                                   class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                                   id="img_view_9">查看</a>
                                                                <c:choose>
                                                                    <c:when test="${st.index == 0}">
                                                                        <span class="font-red cursor-pointer mt4 none"
                                                                              onclick="del(9)" id="img_del_9">删除</span>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <span class="font-red cursor-pointer mt4 none"
                                                                              onclick="delAttachment(this)"
                                                                              id="img_del_9">删除</span>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </div>
                                                        </c:otherwise>
                                                    </c:choose>
                                                    <div class="clear"></div>
                                                </div>
                                            </c:forEach>
                                        </c:when>

                                        <c:otherwise>
                                            <div class=" mb8">
                                                <div class="f_left">

                                                    <input type="file" class="upload_file" name="lwfile" id="file_9"
                                                           style="display: none;" onchange="uploadFile(this,9);"/>
                                                    <input type="text" class="input-middle" id="name_9"
                                                           readonly="readonly"
                                                           placeholder="请上传品牌授权书" name="brandBookName"
                                                           onclick="file_9.click();" value="${brandBook.name}">
                                                    <input type="hidden" id="uri_9" name="brandBookUri"
                                                           value="${brandBook.uri}">
                                                    <label class="bt-bg-style bt-middle bg-light-blue ml10"
                                                           onclick="return $('#file_9').click();">浏览</label>

                                                    <div><span id="brand_book" class="font-red " style="display: none;">请上传品牌授权书</span>
                                                    </div>

                                                </div>
                                                <!-- 上传成功出现 -->
                                                <div class="f_left">
                                                    <i class="iconsuccesss ml7 none" id="img_icon_9"></i>
                                                    <a href="" target="_blank"
                                                       class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                       id="img_view_9">查看</a>
                                                    <span class="font-red cursor-pointer mt4 none" onclick="del(9)"
                                                          id="img_del_9">删除</span>
                                                </div>

                                                <div class="clear"></div>
                                            </div>
                                        </c:otherwise>
                                    </c:choose>

                                    <div class=" clear" id="conadd9">
                                        <span class="bt-border-style bt-small border-blue "
                                              onclick="con_add(9);">继续添加</span>
                                    </div>

                                    <div class="font-grey9" style="margin-top:7px;">
                                        1，图片格式只能JPG、PNG、JPEG、BMP、PDF等格式<br>
                                        2，图片大小不超过5M
                                    </div>
                                </li>
                                <li>
                                    <label class="f_left mt4 mr10">设置有效期</label>
                                    <div class="f_left">
                                        <div class='inputfloat'>
                                            <input class="Wdate input-smaller" type="text" placeholder="请选择日期"
                                                   id="brandBookStartTime" autocomplete="off"
                                                   onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'brandBookEndTime\')}'})"
                                                   name="brandBookStartTime"
                                                   value='<date:date value ="${brankBeginTime} " format="yyyy-MM-dd"/>'/>

                                            <input class="Wdate input-smaller" type="text" placeholder="请选择日期"
                                                   id="brandBookEndTime" autocomplete="off"
                                                   onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'brandBookStartTime\')}'})"
                                                   name="brandBookEndTime"
                                                   value='<date:date value ="${brankEndTime} " format="yyyy-MM-dd"/>'/>
                                        </div>
                                        <div id="brandBook_time_div" class="font-red" style="display: none;">开始时间不能为空
                                        </div>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </li>

                    <li class="">
                        <div class="infor_name sex_name" style="width: 155px;">
                            <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="28px">
                            <label>随货同行单模板</label>
                        </div>
                        <div class="f_left insertli">
                            <ul id="goodsWithTem">
                                <li>
                                    <c:choose>
                                        <c:when test="${!empty goodWithTemList}">
                                            <c:forEach items="${goodWithTemList}" var="goodWithTem" varStatus="st">
                                                <div class="clear mb8">
                                                    <div class="pos_rel f_left mb8">
                                                        <input type="file" class="upload_file" name="lwfile"
                                                               id="file_103_${st.index}" style="display: none;"
                                                               onchange="uploadFile(this,103);"/>
                                                        <c:choose>
                                                            <c:when test="${st.index == 0}">
                                                                <input type="text" class="input-middle" id="name_103"
                                                                       readonly="readonly"
                                                                       placeholder="请上传随货同行单模板" name="goodsWithTemName"
                                                                       onclick="file_103_${st.index}.click();"
                                                                       value="${goodWithTem.name}">
                                                                <input type="hidden" id="uri_103_${st.index}"
                                                                       name="goodsWithTemUri" value="${goodWithTem.uri}">
                                                            </c:when>
                                                            <c:otherwise>
                                                                <input type="text" class="input-middle"
                                                                       id="name_103_${st.index}" readonly="readonly"
                                                                       placeholder="请上传随货同行单模板" name="name_103"
                                                                       onclick="file_103_${st.index}.click();"
                                                                       value="${goodWithTem.name}">
                                                                <input type="hidden" id="uri_103_${st.index}"
                                                                       name="uri_103" value="${goodWithTem.uri}">
                                                            </c:otherwise>
                                                        </c:choose>
                                                        <label class="bt-bg-style bt-middle bg-light-blue ml103"
                                                               onclick="return $('#file_103_${st.index}').click();">浏览</label>
                                                    </div>
                                                    <c:choose>
                                                        <c:when test="${goodWithTem.uri ne null && goodWithTem.uri ne ''}">
                                                            <div class="f_left ">
                                                                <i class="iconsuccesss ml7" id="img_icon_103"></i>
                                                                <a href="http://${goodWithTem.domain}${goodWithTem.uri}"
                                                                   target="_blank"
                                                                   class="font-blue cursor-pointer mr5 ml10 mt4"
                                                                   id="img_view_103">查看</a>
                                                                <c:choose>
                                                                    <c:when test="${st.index == 0}">
                                                                        <span class="font-red cursor-pointer mt4"
                                                                              onclick="del(103)"
                                                                              id="img_del_103">删除</span>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <span class="font-red cursor-pointer mt4"
                                                                              onclick="delAttachment(this)"
                                                                              id="img_del_103">删除</span>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </div>
                                                        </c:when>
                                                        <c:otherwise>
                                                            <div class="f_left ">
                                                                <i class="iconsuccesss ml7 none" id="img_icon_103"></i>
                                                                <a href="http://${goodWithTem.domain}${goodWithTem.uri}"
                                                                   target="_blank"
                                                                   class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                                   id="img_view_103">查看</a>
                                                                <c:choose>
                                                                    <c:when test="${st.index == 0}">
                                                                        <span class="font-red cursor-pointer mt4 none"
                                                                              onclick="del(103)"
                                                                              id="img_del_103">删除</span>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <span class="font-red cursor-pointer mt4 none"
                                                                              onclick="delAttachment(this)"
                                                                              id="img_del_103">删除</span>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </div>
                                                        </c:otherwise>
                                                    </c:choose>
                                                </div>
                                            </c:forEach>
                                        </c:when>

                                        <c:otherwise>
                                            <div class="mb8">
                                                <div class="f_left">
                                                    <input type="file" class="upload_file" name="lwfile" id="file_103"
                                                           style="display: none;" onchange="uploadFile(this,103);"/>
                                                    <input type="text" class="input-middle" id="name_103"
                                                           readonly="readonly"
                                                           placeholder="请上传随货同行单模板" name="goodsWithTemName"
                                                           onclick="file_103.click();" value="${goodWithTem.name}">
                                                    <input type="hidden" id="uri_103" name="goodsWithTemUri"
                                                           value="${goodWithTem.uri}">
                                                    <label class="bt-bg-style bt-middle bg-light-blue ml10"
                                                           onclick="return $('#file_103').click();">浏览</label>
                                                </div>
                                                <!-- 上传成功出现 -->
                                                <c:choose>
                                                    <c:when test="${!empty goodWithTem.uri}">
                                                        <i class="iconsuccesss ml7" id="img_icon_103"></i>
                                                        <a href="http://${goodWithTem.domain}${goodWithTem.uri}" target="_blank"
                                                           class="font-blue cursor-pointer mr5 ml10 mt4"
                                                           id="img_view_103">查看</a>
                                                        <span class="font-red cursor-pointer mt4" onclick="del(3)"
                                                              id="img_del_103">删除</span>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <i class="iconsuccesss ml7 none" id="img_icon_103"></i>
                                                        <a href="" target="_blank"
                                                           class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                           id="img_view_103">查看</a>
                                                        <span class="font-red cursor-pointer mt4 none" onclick="del(130)"
                                                              id="img_del_130">删除</span>
                                                    </c:otherwise>
                                                </c:choose>
                                                <div class="clear"></div>
                                            </div>
                                        </c:otherwise>
                                    </c:choose>

                                    <div class="mt8 clear" id="conadd103">
                                        <span class="bt-border-style bt-small border-blue "
                                              onclick="con_add(103);">继续添加</span>
                                    </div>

                                    <div class="font-grey9" style="margin-top:7px;">
                                        1，图片格式只能JPG、PNG、JPEG、BMP、PDF等格式<br>
                                        2，图片大小不超过5M
                                    </div>
                                </li>
                                <li>
                                    <div class="clear"></div>
                                    <div id="goodsWithError"></div>
                                </li>
                            </ul>
                        </div>
                    </li>

                    <li class="">
                        <div class="infor_name sex_name" style="width: 155px;">
                            <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="28px">
                            <label>质量保证协议</label>
                        </div>
                        <div class="f_left insertli">
                            <ul id="qualityAssurance">
                                <li>
                                    <c:choose>
                                        <c:when test="${!empty qualityAssuranceList}">
                                            <c:forEach items="${qualityAssuranceList }" var="qualityAssurance" varStatus="st">
                                                <div class="clear mb8">
                                                    <div class="pos_rel f_left mb8">
                                                        <input type="file" class="upload_file" name="lwfile"
                                                               id="file_104_${st.index}" style="display: none;"
                                                               onchange="uploadFile(this,104);"/>
                                                        <c:choose>
                                                            <c:when test="${st.index == 0}">
                                                                <input type="text" class="input-middle" id="name_104"
                                                                       readonly="readonly"
                                                                       placeholder="请上传质量保证协议" name="qualityAssuranceName"
                                                                       onclick="file_104_${st.index}.click();"
                                                                       value="${qualityAssurance.name}">
                                                                <input type="hidden" id="uri_104_${st.index}"
                                                                       name="qualityAssuranceUri" value="${qualityAssurance.uri}">
                                                            </c:when>
                                                            <c:otherwise>
                                                                <input type="text" class="input-middle"
                                                                       id="name_104_${st.index}" readonly="readonly"
                                                                       placeholder="请上传质量保证协议" name="name_104"
                                                                       onclick="file_104_${st.index}.click();"
                                                                       value="${qualityAssurance.name}">
                                                                <input type="hidden" id="uri_104_${st.index}"
                                                                       name="uri_104" value="${qualityAssurance.uri}">
                                                            </c:otherwise>
                                                        </c:choose>
                                                        <label class="bt-bg-style bt-middle bg-light-blue ml104"
                                                               onclick="return $('#file_104_${st.index}').click();">浏览</label>
                                                    </div>
                                                    <c:choose>
                                                        <c:when test="${qualityAssurance.uri ne null && qualityAssurance.uri ne ''}">
                                                            <div class="f_left ">
                                                                <i class="iconsuccesss ml7" id="img_icon_104"></i>
                                                                <a href="http://${qualityAssurance.domain}${qualityAssurance.uri}"
                                                                   target="_blank"
                                                                   class="font-blue cursor-pointer mr5 ml10 mt4"
                                                                   id="img_view_104">查看</a>
                                                                <c:choose>
                                                                    <c:when test="${st.index == 0}">
                                                                        <span class="font-red cursor-pointer mt4"
                                                                              onclick="del(104)"
                                                                              id="img_del_104">删除</span>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <span class="font-red cursor-pointer mt4"
                                                                              onclick="delAttachment(this)"
                                                                              id="img_del_104">删除</span>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </div>
                                                        </c:when>
                                                        <c:otherwise>
                                                            <div class="f_left ">
                                                                <i class="iconsuccesss ml7 none" id="img_icon_104"></i>
                                                                <a href="http://${qualityAssurance.domain}${qualityAssurance.uri}"
                                                                   target="_blank"
                                                                   class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                                   id="img_view_104">查看</a>
                                                                <c:choose>
                                                                    <c:when test="${st.index == 0}">
                                                                        <span class="font-red cursor-pointer mt4 none"
                                                                              onclick="del(104)"
                                                                              id="img_del_104">删除</span>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <span class="font-red cursor-pointer mt4 none"
                                                                              onclick="delAttachment(this)"
                                                                              id="img_del_104">删除</span>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </div>
                                                        </c:otherwise>
                                                    </c:choose>
                                                </div>
                                            </c:forEach>
                                        </c:when>

                                        <c:otherwise>
                                            <div class="mb8">
                                                <div class="f_left">
                                                    <input type="file" class="upload_file" name="lwfile" id="file_104"
                                                           style="display: none;" onchange="uploadFile(this,104);"/>
                                                    <input type="text" class="input-middle" id="name_104"
                                                           readonly="readonly"
                                                           placeholder="请上传质量保证协议" name="qualityAssuranceName"
                                                           onclick="file_104.click();" value="${qualityAssurance.name}">
                                                    <input type="hidden" id="uri_104" name="qualityAssuranceUri"
                                                           value="${qualityAssurance.uri}">
                                                    <label class="bt-bg-style bt-middle bg-light-blue ml10"
                                                           onclick="return $('#file_104').click();">浏览</label>
                                                </div>
                                                <!-- 上传成功出现 -->
                                                <c:choose>
                                                    <c:when test="${!empty qualityAssurance.uri}">
                                                        <i class="iconsuccesss ml7" id="img_icon_104"></i>
                                                        <a href="http://${qualityAssurance.domain}${qualityAssurance.uri}" target="_blank"
                                                           class="font-blue cursor-pointer mr5 ml10 mt4"
                                                           id="img_view_104">查看</a>
                                                        <span class="font-red cursor-pointer mt4" onclick="del(3)"
                                                              id="img_del_104">删除</span>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <i class="iconsuccesss ml7 none" id="img_icon_104"></i>
                                                        <a href="" target="_blank"
                                                           class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                           id="img_view_104">查看</a>
                                                        <span class="font-red cursor-pointer mt4 none" onclick="del(130)"
                                                              id="img_del_130">删除</span>
                                                    </c:otherwise>
                                                </c:choose>
                                                <div class="clear"></div>
                                            </div>
                                        </c:otherwise>
                                    </c:choose>

                                    <div class="mt8 clear" id="conadd104">
                                        <span class="bt-border-style bt-small border-blue "
                                              onclick="con_add(104);">继续添加</span>
                                    </div>

                                    <div class="font-grey9" style="margin-top:7px;">
                                        1，图片格式只能JPG、PNG、JPEG、BMP、PDF等格式<br>
                                        2，图片大小不超过5M
                                    </div>
                                </li>
                                <li style="margin-bottom:0px;">
                                    <label class="f_left mt4 mr10">设置有效期</label>
                                    <div class="f_left">
                                        <div class='inputfloat'>
                                            <input type="text" class="Wdate input-smaller" placeholder="请选择日期"
                                                   onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'qualityAssuranceEndTime\')}'})"
                                                   name="qualityAssuranceStartTime" autocomplete="off"
                                                   id="qualityAssuranceStartTime"
                                                   value='<date:date value ="${qualityAssurance.begintime} " format="yyyy-MM-dd"/>'/>
                                            <input type="text" class="Wdate input-smaller" placeholder="请选择日期"
                                                   onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'qualityAssuranceStartTime\')}'})"
                                                   name="qualityAssuranceEndTime" autocomplete="off"
                                                   id="qualityAssuranceEndTime"
                                                   value='<date:date value ="${qualityAssurance.endtime} " format="yyyy-MM-dd"/>'/>
                                        </div>
                                        <div id="qualityAssuranc_time_div" class="font-red " style="display: none;">开始时间不能为空</div>
                                    </div>
                                </li>
                                <li>
                                    <div class="clear"></div>
                                    <div id="qualityError"></div>
                                </li>
                            </ul>
                        </div>
                    </li>

                    <li class="">
                        <div class="infor_name sex_name" style="width: 155px;">
                            <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="28px">
                            <label>售后服务承诺书</label>
                        </div>
                        <div class="f_left insertli">
                            <ul id="afterSalesBook">
                                <li>
                                    <c:choose>
                                        <c:when test="${!empty afterSalesBookList}">
                                            <c:forEach items="${afterSalesBookList}" var="afterSalesBook" varStatus="st">
                                                <div class="clear mb8">
                                                    <div class="pos_rel f_left mb8">
                                                        <input type="file" class="upload_file" name="lwfile"
                                                               id="file_105_${st.index}" style="display: none;"
                                                               onchange="uploadFile(this,105);"/>
                                                        <c:choose>
                                                            <c:when test="${st.index == 0}">
                                                                <input type="text" class="input-middle" id="name_105"
                                                                       readonly="readonly"
                                                                       placeholder="请上传售后服务承诺书" name="afterSalesBookName"
                                                                       onclick="file_105_${st.index}.click();"
                                                                       value="${afterSalesBook.name}">
                                                                <input type="hidden" id="uri_105_${st.index}"
                                                                       name="afterSalesBookUri" value="${afterSalesBook.uri}">
                                                            </c:when>
                                                            <c:otherwise>
                                                                <input type="text" class="input-middle"
                                                                       id="name_105_${st.index}" readonly="readonly"
                                                                       placeholder="请上传售后服务承诺书" name="name_105"
                                                                       onclick="file_105_${st.index}.click();"
                                                                       value="${afterSalesBook.name}">
                                                                <input type="hidden" id="uri_105_${st.index}"
                                                                       name="uri_105" value="${afterSalesBook.uri}">
                                                            </c:otherwise>
                                                        </c:choose>
                                                        <label class="bt-bg-style bt-middle bg-light-blue ml105"
                                                               onclick="return $('#file_105_${st.index}').click();">浏览</label>
                                                    </div>
                                                    <c:choose>
                                                        <c:when test="${afterSalesBook.uri ne null && afterSalesBook.uri ne ''}">
                                                            <div class="f_left ">
                                                                <i class="iconsuccesss ml7" id="img_icon_105"></i>
                                                                <a href="http://${afterSalesBook.domain}${afterSalesBook.uri}"
                                                                   target="_blank"
                                                                   class="font-blue cursor-pointer mr5 ml10 mt4"
                                                                   id="img_view_105">查看</a>
                                                                <c:choose>
                                                                    <c:when test="${st.index == 0}">
                                                                        <span class="font-red cursor-pointer mt4"
                                                                              onclick="del(105)"
                                                                              id="img_del_105">删除</span>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <span class="font-red cursor-pointer mt4"
                                                                              onclick="delAttachment(this)"
                                                                              id="img_del_105">删除</span>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </div>
                                                        </c:when>
                                                        <c:otherwise>
                                                            <div class="f_left ">
                                                                <i class="iconsuccesss ml7 none" id="img_icon_105"></i>
                                                                <a href="http://${afterSalesBook.domain}${afterSalesBook.uri}"
                                                                   target="_blank"
                                                                   class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                                   id="img_view_105">查看</a>
                                                                <c:choose>
                                                                    <c:when test="${st.index == 0}">
                                                                        <span class="font-red cursor-pointer mt4 none"
                                                                              onclick="del(105)"
                                                                              id="img_del_105">删除</span>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <span class="font-red cursor-pointer mt4 none"
                                                                              onclick="delAttachment(this)"
                                                                              id="img_del_105">删除</span>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </div>
                                                        </c:otherwise>
                                                    </c:choose>
                                                </div>
                                            </c:forEach>
                                        </c:when>

                                        <c:otherwise>
                                            <div class="mb8">
                                                <div class="f_left">
                                                    <input type="file" class="upload_file" name="lwfile" id="file_105"
                                                           style="display: none;" onchange="uploadFile(this,105);"/>
                                                    <input type="text" class="input-middle" id="name_105"
                                                           readonly="readonly"
                                                           placeholder="请上传售后服务承诺书" name="afterSalesBookName"
                                                           onclick="file_105.click();" value="${afterSalesBook.name}">
                                                    <input type="hidden" id="uri_105" name="afterSalesBookUri"
                                                           value="${afterSalesBook.uri}">
                                                    <label class="bt-bg-style bt-middle bg-light-blue ml10"
                                                           onclick="return $('#file_105').click();">浏览</label>
                                                </div>
                                                <!-- 上传成功出现 -->
                                                <c:choose>
                                                    <c:when test="${!empty afterSalesBook.uri}">
                                                        <i class="iconsuccesss ml7" id="img_icon_105"></i>
                                                        <a href="http://${afterSalesBook.domain}${afterSalesBook.uri}" target="_blank"
                                                           class="font-blue cursor-pointer mr5 ml10 mt4"
                                                           id="img_view_105">查看</a>
                                                        <span class="font-red cursor-pointer mt4" onclick="del(3)"
                                                              id="img_del_105">删除</span>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <i class="iconsuccesss ml7 none" id="img_icon_105"></i>
                                                        <a href="" target="_blank"
                                                           class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                           id="img_view_105">查看</a>
                                                        <span class="font-red cursor-pointer mt4 none" onclick="del(130)"
                                                              id="img_del_130">删除</span>
                                                    </c:otherwise>
                                                </c:choose>
                                                <div class="clear"></div>
                                            </div>
                                        </c:otherwise>
                                    </c:choose>

                                    <div class="mt8 clear" id="conadd105">
                                        <span class="bt-border-style bt-small border-blue "
                                              onclick="con_add(105);">继续添加</span>
                                    </div>

                                    <div class="font-grey9" style="margin-top:7px;">
                                        1，图片格式只能JPG、PNG、JPEG、BMP、PDF等格式<br>
                                        2，图片大小不超过5M
                                    </div>
                                </li>
                                <li style="margin-bottom:0px;">
                                    <label class="f_left mt4 mr10">设置有效期</label>
                                    <div class="f_left">
                                        <div class='inputfloat'>
                                            <input type="text" class="Wdate input-smaller" placeholder="请选择日期"
                                                   onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'afterSalesBookEndTime\')}'})"
                                                   name="afterSalesBookStartTime" autocomplete="off"
                                                   id="afterSalesBookStartTime"
                                                   value='<date:date value ="${afterSalesBook.begintime} " format="yyyy-MM-dd"/>'/>
                                            <input type="text" class="Wdate input-smaller" placeholder="请选择日期"
                                                   onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'afterSalesBookStartTime\')}'})"
                                                   name="afterSalesBookEndTime" autocomplete="off"
                                                   id="afterSalesBookEndTime"
                                                   value='<date:date value ="${afterSalesBook.endtime} " format="yyyy-MM-dd"/>'/>
                                        </div>
                                        <div id="afterSalesBook_time_div" class="font-red " style="display: none;">开始时间不能为空</div>
                                    </div>
                                </li>
                                <li>
                                    <div class="clear"></div>
                                    <div id="afterSalesError"></div>
                                </li>
                            </ul>
                        </div>
                    </li>

                </ul>
                <div class="font-grey9 ml120 line20">
                    友情提醒
                    <br/>1、结束日期可以不填写，代表资质没有结束日期；
                    <br/>2、三证合一和医疗资质合一的客户无需再设置其他信息；
                </div>

                <input type="hidden" name="busTraderCertificateId" value="${business.traderCertificateId}">
                <input type="hidden" name="taxTraderCertificateId" value="${tax.traderCertificateId}">
                <input type="hidden" name="orgaTraderCertificateId" value="${orga.traderCertificateId}">
                <input type="hidden" name="twoTraderCertificateId" value="${twoMedical.traderCertificateId}">
                <input type="hidden" name="threeTraderCertificateId" value="${threeMedical.traderCertificateId}">
                <input type="hidden" name="productTraderCertificateId" value="${product.traderCertificateId}">
                <input type="hidden" name="traderId" value="${traderSupplier1.traderId}">
                <input type="hidden" name="traderSupplierId" value="${traderSupplier1.traderSupplierId}">
                <input type="hidden" name="traderType" value="2">
                <input type="hidden" name="beforeParams" value='${beforeParams}'>
            </div>
        </div>

    </div>
    <div class="add-tijiao tcenter mt20">
        <button type="submit" id='submit'>保存</button>
    </div>
</form>
<div style="width: 100%;height:80px"></div>
<script type="text/javascript">
    function checkContactWay(contactWay,errorMsg){

        if(contactWay.value == ""){
            return;
        }

        var re = /^(\d{3,4}-?)?\d{7,9}(-?\d{2,6})?$|^$/;
        if(!re.test(contactWay.value)){
            layer.alert(errorMsg);
        }

    }
</script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/js/todolist/edit_baseinfo.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/js/region/index.js?rnd=${resourceVersionKey}"></script>
<%@ include file="../common/footer.jsp" %>
