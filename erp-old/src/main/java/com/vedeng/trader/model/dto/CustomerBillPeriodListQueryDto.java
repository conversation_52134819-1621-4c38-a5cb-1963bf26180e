package com.vedeng.trader.model.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * 账期列表检索DTO
 */
@Data
public class CustomerBillPeriodListQueryDto {

    /**
     * 逾期状态 @OverdueStateEnum
     */
    private Integer overdueState;

    /**
     * 订单ID
     */
    private List<Long> orderIds;

    /**
     * 账期类型，@CustomerBillPeriodTypeEnum
     */
    private Integer billPeriodType;
    /**
     * 账期是否归还  全部 0   是 1   否 2
     */
    private Integer isReturn;
}