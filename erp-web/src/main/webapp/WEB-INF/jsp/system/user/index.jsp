<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="用户管理" scope="application" />	
<%@ include file="../../common/common.jsp"%>
<script src="/webjars/ezadmin/plugins/layui/layui.js"></script>

<link href="/webjars/ezadmin/plugins/layui/css/layui.css?v=2.7.6" rel="stylesheet">

<link href="/webjars/ezadmin/plugins/viewer/viewer.min.css" rel="stylesheet">
<%--
<link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
--%>
<link href="/webjars/ezadmin/layui/css/ezlist.css?v=1711100573488" rel="stylesheet">
<link href="/webjars/ezadmin/plugins/cascader/cascader.css" rel="stylesheet">
<script src="/webjars/ezadmin/plugins/cascader/cascader.js?1=1" type="text/javascript" ></script>
<script src="/webjars/ezadmin/plugins/pingyin/pinyin_dict_firstletter.js" ></script>
<script src="/webjars/ezadmin/plugins/pingyin/pinyin_dict_notone.js" ></script>
<script src="/webjars/ezadmin/plugins/pingyin/pinyinUtil.js" ></script>

<style>
	/*.el-cascader-node{
		height: 30px !important;
	}*/
	.el-input__inner{height:30px ;line-height:30px;padding-left: 5px;}
	.el-cascader{
		width: 200px;
	}
	/*.el-input input{
		height: 30px !important;
	}*/
	li i{
		height: auto;
		background: none;
	}

</style>
<div class="content">
	<div class="searchfunc">
		<form action="${pageContext.request.contextPath}/system/user/index.do" method="post" id="search">
			<input type="hidden" name="flag" value="1">
			<ul style="display: flex;    flex-wrap: wrap;">
				<li><label class="infor_name">员工姓名</label> <input type="text"
					class="input-middle" name="realName" id="realName"
					value="${user.realName }"></li>
				<li><label class="infor_name">用户名</label> <input type="text"
					class="input-middle" name="username" id="username"
					value="${user.username }"></li>
				<li><label class="infor_name">分机号</label> <input type="text"
					class="input-middle" name="ccNum" id="ccNum"
					value="${user.ccNum }"></li>
				<%--<li><label class="infor_name">主叫号码</label> <input type="text"
																 class="input-middle" name="ttNumber" id="ttNumber"
																 value="${user.ttNumber }"></li>--%>
				<c:if test="${curr_user.isAdmin == 2}">
				<li><label class="infor_name">分公司</label> <select
					class="input-middle f_left" name="companyId">
						<option value="0">请选择</option>
						<c:forEach items="${companyList }" var="company">
							<option value="${company.companyId }"
								<c:if test="${user.companyId != null and user.companyId == company.companyId}">selected="selected"</c:if>>
								${company.companyName }</option>
						</c:forEach>
				</select></li>
				</c:if>
				<c:if test="${curr_user.isAdmin != 2}">
				<li>
					<%--<div style="display: flex;  flex-wrap: nowrap;">--%>
						<label class="infor_name">所属部门</label>
							<%--
                                                        <input type="hidden" id="orgId" name="orgId" value="${position.orgId }"/>
                            --%>
							<%--<select class="input-middle f_left" name="orgId" onchange="initPosit();">
								<option value="0">请选择</option>
								<c:forEach items="${orgList }" var="org">
									<option value="${org.orgId }"  <c:if test="${position.orgId != null and position.orgId == org.orgId}">selected="selected"</c:if>>
									${org.orgName }
									</option>
								</c:forEach>
							</select>--%>

						<div class="layui-input-inline">
							<input value="${user.orgId }" type="hidden" name="orgId" id="orgId" class="hidden-reset ez-laycascader" ez_url="/system/org/orgList.do" ez_value="orgId" ez_label="orgName" ez_children="child" autocomplete="off">
						</div>
					<%--</div>--%>
					<%--<label class="infor_name">所属部门</label>

					<select
					class="input-middle f_left" name="orgId" onchange="initPosit();">
						<option value="0">请选择</option>
						<c:forEach items="${orgList }" var="org">
							<option value="${org.orgId }"
								<c:if test="${user.orgId != null and user.orgId == org.orgId}">selected="selected"</c:if>>
								${org.orgName }</option>
						</c:forEach>
					</select>--%>
				</li>
				<li><label class="infor_name">职位</label> <select
					class="input-middle f_left" name="positionId">
						<option selected="selected" value="0">请选择</option>
						<c:if test="${positList != null }">
							<c:forEach items="${positList }" var="posit">
								<option value="${posit.positionId }"
									<c:if test="${user.positionId != null and user.positionId == posit.positionId}">selected="selected"</c:if>>${posit.positionName }</option>
							</c:forEach>
						</c:if>
				</select></li>
				</c:if>
				<li><label class="infor_name">状态</label> <select
					class="input-middle f_left" name="isDisabled">
						<option selected="selected" value="-1">请选择</option>
						<option value="0"
							<c:if test="${user.isDisabled != null and user.isDisabled=='0'}">selected="selected"</c:if>>启用</option>
						<option value="1"
							<c:if test="${user.isDisabled != null and user.isDisabled=='1'}">selected="selected"</c:if>>禁用</option>
				</select></li>
				<li><label class="infor_name">是否为贝登员工</label>
					<select class="input-middle f_left" name="staff">
						<option value="">请选择</option>
						<option value="1" <c:if test="${user.staff == '1'}">selected="selected"</c:if>>是</option>
						<option value="0" <c:if test="${user.staff == '0'}">selected="selected"</c:if>>否</option>
					</select>
				</li>
				<li><label class="infor_name">所属公司</label>
					<input type="text" class="input-middle" name="belongCompanyName" id="belongCompanyName" value="${user.belongCompanyName}">
				</li>
				<li><label class="infor_name">可登录系统</label>
					<select class="input-middle f_left" name="systems">
						<option value="">请选择</option>
						<c:forEach items="${platformList}" var="each">
							<option value="${each.platformId}"
									<c:if test="${user.systems == each.platformId}">selected="selected"</c:if>
							>${each.platformName}</option>
						</c:forEach>
					</select>
				</li>
				<li><label class="infor_name">数据授权</label>
					<select class="input-middle f_left" name="orgIdsList">
						<option value="">请选择</option>
						<option value="1" <c:if test="${user.orgIdsList == '1'}">selected="selected"</c:if>>部门权限</option>
						<option value="2" <c:if test="${user.orgIdsList == '2'}">selected="selected"</c:if>>默认</option>
					</select>
				</li>
			</ul>
			<div class="tcenter">
				<span class="bg-light-blue bt-bg-style bt-small" onclick="search();" id="searchSpan">搜索</span> 
				<span class="bt-small bg-light-blue bt-bg-style" onclick="reset();">重置</span>
<%--				<c:if test="${editStatus == 'false'}">--%>
<%--					<c:choose>--%>
<%--						<c:when test="${curr_user.isAdmin == 2}">--%>
<%--							<span class="bt-small bg-light-blue bt-bg-style pop-new-data" layerParams='{"width":"600px","height":"300px","title":"新增管理员","link":"./addmanager.do"}'>新增管理员</span>--%>
<%--						</c:when>--%>
<%--						<c:otherwise>--%>
<%--							<span class="bg-light-blue bt-bg-style bt-small addtitle" tabTitle='{"num":"addsystemuser","link":"./system/user/modifyuser.do","title":"新增员工"}'>新增员工</span>--%>
<%--						</c:otherwise>--%>
<%--					</c:choose>--%>
<%--				</c:if>--%>
				<span class="pop-new-data bt-small bt-bg-style bg-light-blue" layerParams='{"width":"550px","height":"250px","title":"列表配置","link":"./define.do"}'>列表配置</span>
				<c:if test="${isLineAdmin}">
					<span class="pop-new-data bt-small bt-bg-style bg-light-blue" layerParams='{"width":"550px","height":"250px","title":"部门线路号码配置","link":"./callLineInit.do"}'>部门线路号码配置</span>
				</c:if>
			</div>
		</form>
	</div>
	<div  class="normal-list-page">
		<table
			class="table table-bordered table-striped table-condensed table-centered">
			<thead>
				<tr>
					<th class="sorts">序号</th>
					<th>所属公司</th>
					<th>可登录系统</th>
					<th>员工姓名</th>
					<th>用户名</th>
					<c:if test="${curr_user.isAdmin == 2}">
					<th>分公司</th>
					<th>管理员</th>
					</c:if>
					<th <define:define bussiness="/system/user/index.do" field="orgName" />>所属部门</th>
					<th <define:define bussiness="/system/user/index.do" field="positionName" />>职位</th>
					<th <define:define bussiness="/system/user/index.do" field="pUsername" />>直接上级</th>
					<th <define:define bussiness="/system/user/index.do" field="number" />>工号</th>
					<th <define:define bussiness="/system/user/index.do" field="ccNumber" />>分机号</th>
 					<th <define:define bussiness="/system/user/index.do" field="isDisabled" />>状态</th>
					<th <define:define bussiness="/system/user/index.do" field="addTime" />>创建时间</th>
 					<c:if test="${isLineAdmin}">
						<th>电信线路号码</th>
						<th>移动线路号码</th>
						<th>联通线路号码</th>
						<th>客户专线</th>
					</c:if>
					<th class="wid15">操作</th>
				</tr>
			</thead>

			<tbody class="employeestate">
				<c:if test="${not empty users}">
					<c:forEach items="${users}" var="user" varStatus="status">
						<tr>
							<td>${status.count}</td>
							<td>${user.belongCompanyName}</td>
							<td>${user.systems}</td>
							<td>${user.userDetail.realName }</td>
							<td>${user.username}</td>
							<c:if test="${curr_user.isAdmin == 2}">
								<td>${user.companyName}</td>
								<td>
									<c:choose>
										<c:when test="${user.isAdmin == 2 }">超级管理员</c:when>
										<c:when test="${user.isAdmin == 1 }">管理员</c:when>
										<c:otherwise>用户</c:otherwise>
									</c:choose>
								</td>
							</c:if>
							<td <define:define bussiness="/system/user/index.do" field="orgName" /> title="${user.orgName}">${user.orgName}</td>
							<td <define:define bussiness="/system/user/index.do" field="positionName" /> title="${user.positionName}">${user.positionName}</td>
							<td <define:define bussiness="/system/user/index.do" field="pUsername" />>${user.pUsername}</td>
							<td <define:define bussiness="/system/user/index.do" field="number" />>${user.number }</td>
							<td <define:define bussiness="/system/user/index.do" field="ccNumber" />>${user.userDetail.ccNumber }</td>
 							<td <define:define bussiness="/system/user/index.do" field="isDisabled" />>
								<c:choose>
									<c:when test="${user.isDisabled==1}">
										<span class="offstate">禁用</span>
									</c:when>
									<c:otherwise>
										<span class="onstate">启用</span>
									</c:otherwise>
								</c:choose>
							</td>
							<td <define:define bussiness="/system/user/index.do" field="addTime" />><date:date value="${user.addTime} " /></td>
 							<c:if test="${isLineAdmin}">
								<td>${user.telecomLine}</td>
								<td>${user.mobileLine}</td>
								<td>${user.unicomLine}</td>
								<td>${user.customerLine}</td>
							</c:if>
							<td>
								<span class="edit-user addtitle" tabTitle='{"num":"viewuser${user.userId}","link":"./system/user/viewuser.do?userId=${user.userId}","title":"查看员工"}'>查看</span>

								<c:if test="${editStatus == 'false'}">
									<c:choose>
										<c:when test="${curr_user.isAdmin == 2 && user.isAdmin != 0}">
											<span class="edit-user pop-new-data" layerParams='{"width":"600px","height":"300px","title":"编辑管理员","link":"./editmanager.do?userId=${user.userId}"}'>编辑</span>
										</c:when>
										<c:otherwise>
											<c:if test="${user.isAdmin == 0 }">
												<span class="edit-user addtitle" tabTitle='{"num":"modifyuser${user.userId}","link":"./system/user/modifyuser.do?userId=${user.userId}","title":"编辑员工"}'>编辑</span>
                                                <shiro:hasPermission name="/ezadmin/form/form-modifyUserPers">
												    <span class="pop-new-data bt-small bt-bg-style bg-light-blue" layerParams='{"width":"700px","height":"500px","title":"${user.username}数据授权","link":"/ezadmin/form/form-modifyUserPers?ID=${user.userId}"}'>数据授权</span>
                                                </shiro:hasPermission>
											<%--	<a href="#" onclick="javascript:modifyUserPers(${user.userId});" >数据授权</a>--%>
												<%--<span class="edit-user addtitle" tabTitle='{"num":"modifyuser${user.userId}","link":"./system/user/modifyuser.do?userId=${user.userId}","title":"数据授权"}'>数据授权</span>--%>

											</c:if>
										</c:otherwise>
									</c:choose>
									<c:if test="${user.isAdmin == 0 || curr_user.isAdmin == 2}">
										<c:choose>
											<c:when test="${user.isDisabled == 1}">
												<span class="edit-user" onclick="setDisabled(${user.userId},0);">启用</span>
											</c:when>
											<c:otherwise>
												<span class="forbid clcforbid" onclick="setDisabled(${user.userId},1);">禁用</span>
											</c:otherwise>
										</c:choose>
									</c:if>
								</c:if>
							</td>
						</tr>
					</c:forEach>
				</c:if>
			</tbody>
		</table>
		<c:if test="${empty users }">
			<!-- 查询无结果弹出 -->
			<div class="noresult">查询无结果！请尝试使用其他搜索条件。</div>
		</c:if>
		<tags:page page="${page}" />
	</div>
</div>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/js/user/index.js?rnd=${resourceVersionKey}"></script>
<script  type="text/javascript" >
	$(document).ready(function() {
		$(".ez-laycascader").each(function(){
			var _this=$(this);
			renderCascader(_this);
		})
	})
	function istrue(c){
		return  (c||'true')=='true'||(c||'1')=='1';
	}
	function createParentChildStructure(data) {
		// 解析 JSON 字符串
		// const data = JSON.parse(jsonData);

		// 创建一个字典来保存所有节点，按照它们的 K 值索引
		const dataMap = data.reduce((map, node) => {
			map[node.orgId] = { ...node, child: [] };
		return map;
	}, {});
		// 最终的父节点数组
		const result = [];

		// 遍历所有节点，构建父子关系
		data.forEach(node => {
			// 如果 level 值为 "3"，则是顶层节点，添加到结果数组中
			if (node.level === "3" || node.level == 3) {
			result.push(dataMap[node.orgId]);
		} else if (node.parentId in dataMap) {
			// 如果 P 值在 dataMap 中，则找到其父节点，并将当前节点添加为子节点
			dataMap[node.parentId].child.push(dataMap[node.orgId]);
		}
	});

		return result;
	}
	function renderCascader(cas){
		layui.use('layCascader', function () {
			try {
				var layCascader = layui.layCascader;
				var _this = $(cas);
				var url = _this.attr("ez_url");
				var ezurl = _this.attr("ezlist_url");
				var value = _this.attr("ez_value") || 'VALUE';
				var label = _this.attr("ez_label") || 'LABEL';
				var children = _this.attr("ez_children") || 'CHILDREN';
				var multiple = istrue(_this.attr("multi"));
				var itemsJson = _this.attr("itemsJson");
				// debugger
				var itemPlaceholder = _this.attr("placeholder") || '请选择';
				var paramValue = [] ;
				if(_this.val().length >0){
					paramValue=parseInt(_this.val());
				}else{

				}
				//("["+_this.val()+"]"):'[]';
				var collapseTags = istrue(_this.attr("collapsetags"));
				var showAllLevels = istrue(_this.attr("showalllevels"));

				$.get(url || ezurl, function (data) {
					var jsonData = JSON.parse(data);
					// var res = data.data;
					//console.log(jsonData.data);
					var res  = createParentChildStructure(jsonData.data);
					var prop = {};
					prop.value = value;
					prop.label = label;
					prop.children = children;
					prop.multiple = false;
					prop.checkStrictly = true;

					//console.log(prop);
					if (ezurl) {
						res = flatToTree(res, 0);
					}
					var org = layCascader({
						name:"orgId",
						elem: _this[0],
						props: prop,
						filterable: true,
						filterMethod: function (node, val) {//重写搜索方法。
							if (val == node.data[label]) {//把value相同的搜索出来

								return true;
							}
							if ((node.data[label] + node.data[label]).indexOf(val) != -1) {//名称中包含的搜索出来
								return true;
							}
							//  console.log(node.data.orgName+node.data.orgNames+'##'+(node.data.orgId+'').indexOf(val));
							return !ezpingyin(val, (node.data[label] + node.data[label]), (node.data[value] + ''));
						},
						clearable: true,
						placeholder: itemPlaceholder,
						collapseTags: collapseTags,
						showAllLevels: showAllLevels,
						value: paramValue,
						options: res,
						showLastLevels: false, // 显示完整路径
						multiple: false // 单选模式
					});
					org.change(function (value, node) {
						//layer.msg('value:' + value + ',Node:' + JSON.stringify(node.data));
						$("#orgId").val(value);
						initPositForCasader();
					});
				})

			}catch (e) {
				console.log(e);
			}

		})

	}

</script>
<%@ include file="../../common/footer.jsp"%>
