package com.vedeng.aftersales.dao;

import com.vedeng.aftersales.model.AfterSaleSupplyPolicy;
import com.vedeng.aftersales.model.dto.AfterSaleSupplyPolicyDto;
import com.vedeng.aftersales.model.dto.CopySupplyAfterSalePolicyDto;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface AfterSaleSupplyPolicyMapper {
    int deleteByPrimaryKey(Long supplyPolicyId);

    int insertSelective(AfterSaleSupplyPolicy record);

    AfterSaleSupplyPolicy selectByPrimaryKey(Long supplyPolicyId);

    int updateByPrimaryKeySelective(AfterSaleSupplyPolicy record);

    int updateByPrimaryKey(AfterSaleSupplyPolicy record);

    List<AfterSaleSupplyPolicyDto> getSupplyAfterSalePolicyListBySkuNo(String skuNo);

    List<AfterSaleSupplyPolicyDto> referenceSupplyAfterSalePolicyListPage(Map<String, Object> map);

    AfterSaleSupplyPolicyDto findSupplyAfterSalePolicy(Long supplyPolicyId);

    List<CopySupplyAfterSalePolicyDto> querySupplyAfterSalePolicyListPage(Map<String, Object> map);

    List<AfterSaleSupplyPolicyDto> findSupplyAfterSalePolicyByTraderId(Long traderId);

    AfterSaleSupplyPolicy getBySkuNoAndTraderId(@Param("traderId") Long traderId,@Param("skuNo") String skuNo);

    Map<String, BigDecimal> findLastestYearNumAndAmout(@Param("skuNo") String skuNo, @Param("traderId") Long traderId);

    List<AfterSaleSupplyPolicyDto> findSupplyPolicyBySkuNoAndServiceType(@Param("skuNo") String skuNo,@Param("serviceType") Integer serviceType);
}