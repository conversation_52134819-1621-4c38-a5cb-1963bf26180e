package com.vedeng.erp.kingdee.batch.writer;

import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.dto.KingDeeBuyOrderContractDto;
import com.vedeng.erp.kingdee.dto.KingDeeSaleorderContractDto;
import com.vedeng.erp.kingdee.service.KingDeeBuyOrderContractApiService;
import com.vedeng.erp.kingdee.service.KingDeeSaleorderContractApiService;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class BuyOrderContractWriter extends BaseWriter<KingDeeBuyOrderContractDto> {

    @Autowired
    private KingDeeBuyOrderContractApiService kingDeeBuyOrderContractApiService;
    @Override
    public void doWrite(KingDeeBuyOrderContractDto item, JobParameters params, ExecutionContext stepContext) throws Exception {
        item.setKingDeeBizEnums(KingDeeBizEnums.saveBuyOrderContract);
        kingDeeBuyOrderContractApiService.register(item,true);
    }
}
