package com.vedeng.erp.kingdee.repository.mappers;

import com.vedeng.erp.kingdee.domain.entity.KingDeeMaterialEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

public interface KingDeeMaterialMapper {
    /**
     * delete by primary key
     *
     * @param kingDeeMaterialEntityId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer kingDeeMaterialEntityId);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(KingDeeMaterialEntity record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(KingDeeMaterialEntity record);

    /**
     * select by primary key
     *
     * @param kingDeeMaterialEntityId primary key
     * @return object by primary key
     */
    KingDeeMaterialEntity selectByPrimaryKey(Integer kingDeeMaterialEntityId);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(KingDeeMaterialEntity record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(KingDeeMaterialEntity record);

    /**
     * 根据物料编码查询物料信息
     *
     * @param fNumber 物料编码
     * @return KingDeeMaterialEntity
     */
    KingDeeMaterialEntity getByfNumber(@Param("fNumber") String fNumber);
}