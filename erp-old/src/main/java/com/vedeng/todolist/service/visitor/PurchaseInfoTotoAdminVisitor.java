package com.vedeng.todolist.service.visitor;

import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.Organization;
import com.vedeng.common.page.Page;
import com.vedeng.erp.buyorder.dto.BuyOrderSaleOrderGoodsDetailDto;
import com.vedeng.erp.buyorder.service.BuyorderExpenseApiService;
import com.vedeng.goods.model.vo.GoodsVo;
import com.vedeng.goods.model.vo.ProductManagerAndAssistantIdVo;
import com.vedeng.goods.service.VgoodsService;
import com.vedeng.order.dao.OrderAssistantRelationMapper;
import com.vedeng.order.model.OrderAssistantRelationDo;
import com.vedeng.order.model.Spu;
import com.vedeng.order.model.vo.SaleorderGoodsVo;
import com.vedeng.order.model.vo.SaleorderVo;
import com.vedeng.order.service.BuyorderService;
import com.vedeng.order.service.SaleorderService;
import com.vedeng.todolist.dto.PurchaseInfoTotoDto;
import com.wms.service.context.ThreadLocalContext;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 管理者页面->采购任务
 */
@Service
public class PurchaseInfoTotoAdminVisitor{

    @Resource
    private BuyorderService buyorderService;

    @Resource
    private OrderAssistantRelationMapper orderAssistantRelationMapper;

    @Resource
    private UserMapper userMapper;

    @Autowired
    private VgoodsService vGoodsService;

    @Autowired
    private BuyorderExpenseApiService buyorderExpenseApiService;

    @Autowired
    @Qualifier("saleorderService")
    private SaleorderService saleorderService;

    public PurchaseInfoTotoDto visitor(List<Organization> orgaList) {
        PurchaseInfoTotoDto purchaseInfoTotoDto = new PurchaseInfoTotoDto();
        List<Integer> orgaIdList = orgaList.stream().map(orga->orga.getOrgId()).collect(Collectors.toList());

        List<Integer> orderAssitIdList = userMapper.getOrderAsisitIdByOrgId(orgaIdList);
        List<OrderAssistantRelationDo> orderAssistantRelationDoList = null;

        if(CollectionUtils.isNotEmpty(orderAssitIdList)){
            orderAssistantRelationDoList = orderAssistantRelationMapper.getBingdedInfoByOrderAssIds(orderAssitIdList);
        }

        GoodsVo goodsVo =  new GoodsVo();
        goodsVo.setComponentId(2);

        if(CollectionUtils.isEmpty(orderAssistantRelationDoList)){
            return purchaseInfoTotoDto;
        }
        for(OrderAssistantRelationDo orderAssistantRelationDo : orderAssistantRelationDoList){
            ProductManagerAndAssistantIdVo productManagerAndAssistantIdVo = new ProductManagerAndAssistantIdVo();
            productManagerAndAssistantIdVo.setProductManagerUserId(orderAssistantRelationDo.getProductManagerUserId());
            productManagerAndAssistantIdVo.setProductAssistantUserId(orderAssistantRelationDo.getProductAssitantUserId());
            goodsVo.getProductManagerAndAssistantIdVoList().add(productManagerAndAssistantIdVo);
        }
        Page page= Page.newBuilder(1, Integer.MAX_VALUE, null);

        Map<String, Object> map = buyorderService.getUnPurchasingOrderList(goodsVo, page);
        if(map == null){
            return null;
        }

        if (map != null) {
            List<SaleorderVo> list = (List<SaleorderVo>) map.get("list");
            List<Integer> saleorderIds = new ArrayList<>();
            //根据订单号查询归属人
            for (SaleorderVo saleorderVo : list) {
                List<SaleorderGoodsVo> sgvList = saleorderVo.getSgvList();
                if (sgvList != null) {
                    Spu spu = null;
                    for (SaleorderGoodsVo sgv : sgvList) {
                        spu = saleorderService.getSpu(sgv.getSku());
                        if (spu == null) {
                            sgv.setAssignmentManagerId(null);
                            sgv.setAssignmentAssistantId(null);
                        } else {
                            sgv.setAssignmentManagerId(spu.getAssignmentManagerId());
                            sgv.setAssignmentAssistantId(spu.getAssignmentAssistantId());
                        }
                        saleorderIds.add(sgv.getSaleorderGoodsId());
                    }
                }
            }
            //计算虚拟商品需采购数量并排除需采为0,并重新计算待采数量
            Integer buySum = (Integer) map.get("buySum");
            List<BuyOrderSaleOrderGoodsDetailDto> buyOrderSaleOrderGoodsDetailDtoList = buyorderExpenseApiService.waitBuyExpenseNeed(saleorderIds);
            for (int i = list.size() - 1; i >= 0; i--) {
                List<SaleorderGoodsVo> sgvList = list.get(i).getSgvList();
                Integer proBuySum = list.get(i).getProBuySum();
                if (sgvList.size()>0){
                    for (int j = sgvList.size() - 1; j >= 0; j--) {
                        SaleorderGoodsVo sgv = sgvList.get(j);
                        if(sgv.getIsVirture()==2){
                            for (BuyOrderSaleOrderGoodsDetailDto goodsDetailDto : buyOrderSaleOrderGoodsDetailDtoList) {
                                if (sgv.getSaleorderGoodsId().equals(goodsDetailDto.getSaleorderGoodsId())){
                                    buySum -= sgv.getNum();
                                    proBuySum -= sgv.getNum();
                                    if (goodsDetailDto.getNum() > 0){
                                        buySum += goodsDetailDto.getNum();
                                        sgv.setNeedBuyNum(goodsDetailDto.getNum());
                                        proBuySum += goodsDetailDto.getNum();
                                    }else {
                                        sgvList.remove(j);
                                    }
                                    break;
                                }
                            }
                        }
                    }
                    if (sgvList.size() == 0){
                        list.remove(i);
                    }else {
                        list.get(i).setProBuySum(proBuySum);
                    }
                }
            }

            //新商品流切换 VDERP-1970 add by brianna 2020/3/16 start
            if (!CollectionUtils.isEmpty(list)) {

                List<Integer> skuIds = new ArrayList<>();

                list.stream().forEach(saleOrder -> {
                    if (!CollectionUtils.isEmpty(saleOrder.getSgvList())) {
                        saleOrder.getSgvList().stream().forEach(saleOrderGoods -> {
                            skuIds.add(saleOrderGoods.getGoodsId());
                        });
                    }
                });

                List<Map<String, Object>> skuTipsMap = this.vGoodsService.skuTipList(skuIds);

                Map<String, Map<String, Object>> newSkuInfosMap = skuTipsMap.stream()
                        .collect(Collectors.toMap(key -> key.get("SKU_NO").toString(), v -> v, (k, v) -> v));

            }
            List<SaleorderVo> saleorderList = list;
            List<SaleorderGoodsVo> saleOrderGoodsList = new ArrayList<>();
            saleorderList.stream().forEach(saleOrder -> {
                saleOrderGoodsList.addAll(saleOrder.getSgvList());
            });

            long adventTask = saleOrderGoodsList.stream().filter(saleOrderGood-> saleOrderGood.getAging() != null && saleOrderGood.getAging() == 1).count();
            long overdueTask = saleOrderGoodsList.stream().filter(saleOrderGood->saleOrderGood.getAging() != null && saleOrderGood.getAging() == 2).count();

            purchaseInfoTotoDto.setTotalTasks(adventTask + overdueTask);
            purchaseInfoTotoDto.setAdventTask(adventTask);
            purchaseInfoTotoDto.setOverdueTask(overdueTask);
            purchaseInfoTotoDto.setOrderAssitIdList(orderAssitIdList.stream().map(String::valueOf).collect(Collectors.joining(",")));
        }

        return purchaseInfoTotoDto;

    }


}
