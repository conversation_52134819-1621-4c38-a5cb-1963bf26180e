<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.dao.GoodsAttachmentGenerateExtendMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.goods.model.GoodsAttachmentGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jun 05 11:49:39 CST 2019.
    -->
    <id column="GOODS_ATTACHMENT_ID" jdbcType="INTEGER" property="goodsAttachmentId" />
    <result column="GOODS_ID" jdbcType="INTEGER" property="goodsId" />
    <result column="ATTACHMENT_TYPE" jdbcType="INTEGER" property="attachmentType" />
    <result column="DOMAIN" jdbcType="VARCHAR" property="domain" />
    <result column="URI" jdbcType="VARCHAR" property="uri" />
    <result column="ALT" jdbcType="VARCHAR" property="alt" />
    <result column="SORT" jdbcType="INTEGER" property="sort" />
    <result column="IS_DEFAULT" jdbcType="TINYINT" property="isDefault" />
    <result column="STATUS" jdbcType="TINYINT" property="status" />
    <result column="SYN_SUCCESS" jdbcType="INTEGER" property="synSuccess" />
    <result column="ORIGINAL_FILEPATH" jdbcType="VARCHAR" property="originalFilepath" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jun 05 11:49:39 CST 2019.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jun 05 11:49:39 CST 2019.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jun 05 11:49:39 CST 2019.
    -->
    GOODS_ATTACHMENT_ID, GOODS_ID, ATTACHMENT_TYPE, `DOMAIN`, URI, ALT, SORT, IS_DEFAULT, 
    `STATUS`,SYN_SUCCESS,ORIGINAL_FILEPATH
  </sql>
  <select id="getNoAttachmentsSkuNumber" parameterType="com.vedeng.goods.model.GoodsAttachment" resultType="com.vedeng.goods.model.vo.GoodsAttachmentVo">
    SELECT
        COUNT(c.GOODS_ATTACHMENT_ID) AS IMG_NUM,
        b.SKU_ID AS GOODS_ID
    FROM
        V_CORE_SPU a
    LEFT JOIN V_CORE_SKU b ON a.SPU_ID = b.SPU_ID
    LEFT JOIN T_GOODS_ATTACHMENT c ON b.SKU_ID = c.GOODS_ID
    AND c.ATTACHMENT_TYPE = #{attachmentType,jdbcType=INTEGER} AND c.`STATUS` = #{status,jdbcType=TINYINT}
    WHERE
        b.`STATUS` != 4
    AND a.SPU_ID = #{goodsId,jdbcType=INTEGER}
    GROUP BY
        b.SKU_ID
    HAVING
        COUNT(c.GOODS_ATTACHMENT_ID) = 0
  </select>
  <update id="deleteByGoodsIdAndType" parameterType="com.vedeng.goods.model.GoodsAttachment">
    UPDATE T_GOODS_ATTACHMENT
    SET `STATUS` = #{status,jdbcType=TINYINT}
    WHERE GOODS_ID = #{goodsId,jdbcType=INTEGER}
    AND ATTACHMENT_TYPE = #{attachmentType,jdbcType=INTEGER}
  </update>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into T_GOODS_ATTACHMENT (GOODS_ID, ATTACHMENT_TYPE, `DOMAIN`,
      URI, SORT, `STATUS`)
    values
      <foreach collection="list" item="goodsAttachment" separator=",">
        (#{goodsAttachment.goodsId,jdbcType=INTEGER}, #{goodsAttachment.attachmentType,jdbcType=INTEGER}, #{goodsAttachment.domain,jdbcType=VARCHAR},
        #{goodsAttachment.uri,jdbcType=VARCHAR},  #{goodsAttachment.sort,jdbcType=INTEGER}, #{goodsAttachment.status,jdbcType=TINYINT})
      </foreach>
  </insert>
  <select id="getGoodsAttachment" parameterType="com.vedeng.goods.model.GoodsAttachment" resultType="com.vedeng.goods.model.GoodsAttachment">
    SELECT
      GOODS_ID, ATTACHMENT_TYPE, `DOMAIN`,
      URI, ALT, SORT, IS_DEFAULT, `STATUS`,SYN_SUCCESS,ORIGINAL_FILEPATH
    FROM
      T_GOODS_ATTACHMENT
    WHERE `STATUS` = #{status,jdbcType=TINYINT}
    AND GOODS_ID = #{goodsId,jdbcType=INTEGER}
    AND ATTACHMENT_TYPE = #{attachmentType,jdbcType=INTEGER}
    ORDER BY SORT
  </select>
</mapper>