<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchAfterSalesDtoMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.batch.dto.BatchAfterSalesDto">
        <!--@Table T_AFTER_SALES-->
        <id column="AFTER_SALES_ID" jdbcType="INTEGER" property="afterSalesId" />
        <result column="AFTER_SALES_NO" jdbcType="VARCHAR" property="afterSalesNo" />
        <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId" />
        <result column="SUBJECT_TYPE" jdbcType="INTEGER" property="subjectType" />
        <result column="TYPE" jdbcType="INTEGER" property="type" />
        <result column="ORDER_ID" jdbcType="INTEGER" property="orderId" />
        <result column="ORDER_NO" jdbcType="VARCHAR" property="orderNo" />
        <result column="SERVICE_USER_ID" jdbcType="INTEGER" property="serviceUserId" />
        <result column="VALID_STATUS" jdbcType="INTEGER" property="validStatus" />
        <result column="VALID_TIME" jdbcType="BIGINT" property="validTime" />
        <result column="STATUS" jdbcType="INTEGER" property="status" />
        <result column="ATFER_SALES_STATUS" jdbcType="INTEGER" property="atferSalesStatus" />
        <result column="ATFER_SALES_STATUS_RESON" jdbcType="INTEGER" property="atferSalesStatusReson" />
        <result column="ATFER_SALES_STATUS_USER" jdbcType="INTEGER" property="atferSalesStatusUser" />
        <result column="ATFER_SALES_STATUS_COMMENTS" jdbcType="VARCHAR" property="atferSalesStatusComments" />
        <result column="FIRST_VALID_STATUS" jdbcType="INTEGER" property="firstValidStatus" />
        <result column="FIRST_VALID_TIME" jdbcType="BIGINT" property="firstValidTime" />
        <result column="FIRST_VALID_USER" jdbcType="INTEGER" property="firstValidUser" />
        <result column="FIRST_VALID_COMMENTS" jdbcType="VARCHAR" property="firstValidComments" />
        <result column="SOURCE" jdbcType="INTEGER" property="source" />
        <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
        <result column="CREATOR" jdbcType="INTEGER" property="creator" />
        <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
        <result column="UPDATER" jdbcType="INTEGER" property="updater" />
        <result column="IS_OUT_AFTER" jdbcType="INTEGER" property="isOutAfter" />
        <result column="INVOICE_SEND_STATUS" jdbcType="INTEGER" property="invoiceSendStatus" />
        <result column="INVOICE_ARRIVAL_STATUS" jdbcType="INTEGER" property="invoiceArrivalStatus" />
        <result column="UPDATE_DATA_TIME" jdbcType="TIMESTAMP" property="updateDataTime" />
        <result column="IS_LIGHTNING" jdbcType="INTEGER" property="isLightning" />
        <result column="CREATE_TYPE" jdbcType="TINYINT" property="createType" />
        <result column="CREATE_FRONT_END_USER" jdbcType="VARCHAR" property="createFrontEndUser" />
        <result column="CLOSE_FRONT_END_MOBILE" jdbcType="VARCHAR" property="closeFrontEndMobile" />
        <result column="VERIFIES_NOT_PASS_REASON" jdbcType="VARCHAR" property="verifiesNotPassReason" />
        <result column="HANDLE_STATUS" jdbcType="INTEGER" property="handleStatus" />
        <result column="INVOICE_REFUND_STATUS" jdbcType="INTEGER" property="invoiceRefundStatus" />
        <result column="AMOUNT_REFUND_STATUS" jdbcType="INTEGER" property="amountRefundStatus" />
        <result column="AMOUNT_COLLECTION_STATUS" jdbcType="INTEGER" property="amountCollectionStatus" />
        <result column="AMOUNT_PAY_STATUS" jdbcType="INTEGER" property="amountPayStatus" />
        <result column="INVOICE_MAKEOUT_STATUS" jdbcType="INTEGER" property="invoiceMakeoutStatus" />
        <result column="IS_NEW" jdbcType="INTEGER" property="isNew" />
        <result column="DELIVERY_DIRECT_AFTER_SALES_ID" jdbcType="INTEGER" property="deliveryDirectAfterSalesId" />
        <result column="TRADER_ID" property="traderId" />
        <result column="TRADER_NAME" property="traderName" />
        <result column="TRADER_SUPPLIER_ID" property="traderSupplierId" />
        <result column="TRADER_CUSTOMER_ID" property="traderCustomerId" />
        <result column="DELIVERY_DIRECT" property="deliveryDirect" />

        <collection ofType="com.vedeng.erp.kingdee.batch.dto.BatchAfterSalesGoodsDto" property="batchAfterSalesGoodsDtoList">
            <id column="AFTER_SALES_GOODS_ID" jdbcType="INTEGER" property="afterSalesGoodsId" />
            <result column="AFTER_SALES_ID" jdbcType="INTEGER" property="afterSalesId" />
            <result column="ORDER_DETAIL_ID" jdbcType="INTEGER" property="orderDetailId" />
            <result column="GOODS_TYPE" jdbcType="INTEGER" property="goodsType" />
            <result column="GOODS_ID" jdbcType="INTEGER" property="goodsId" />
            <result column="NUM" jdbcType="INTEGER" property="num" />
            <result column="PRICE" jdbcType="DECIMAL" property="price" />
            <result column="DELIVERY_DIRECT" jdbcType="INTEGER" property="deliveryDirect" />
            <result column="ARRIVAL_NUM" jdbcType="INTEGER" property="arrivalNum" />
            <result column="ARRIVAL_TIME" jdbcType="BIGINT" property="arrivalTime" />
            <result column="ARRIVAL_USER_ID" jdbcType="INTEGER" property="arrivalUserId" />
            <result column="ARRIVAL_STATUS" jdbcType="INTEGER" property="arrivalStatus" />
            <result column="DELIVERY_NUM" jdbcType="INTEGER" property="deliveryNum" />
            <result column="DELIVERY_STATUS" jdbcType="INTEGER" property="deliveryStatus" />
            <result column="DELIVERY_TIME" jdbcType="BIGINT" property="deliveryTime" />
            <result column="SKU_REFUND_AMOUNT" jdbcType="DECIMAL" property="skuRefundAmount" />
            <result column="SKU_OLD_REFUND_AMOUNT" jdbcType="DECIMAL" property="skuOldRefundAmount" />
            <result column="IS_ACTION_GOODS" jdbcType="INTEGER" property="isActionGoods" />
            <result column="UPDATE_DATA_TIME" jdbcType="TIMESTAMP" property="updateDataTime" />
            <result column="FACTORY_CODE" jdbcType="VARCHAR" property="factoryCode" />
            <result column="GOOD_CREATE_TIME" jdbcType="BIGINT" property="goodCreateTime" />
            <result column="GOOD_VAILD_TIME" jdbcType="BIGINT" property="goodVaildTime" />
            <result column="RKNUM" jdbcType="INTEGER" property="rknum" />
            <result column="AFTER_INVOICE_NUM" jdbcType="DECIMAL" property="afterInvoiceNum" />
            <result column="SKU_NO" property="sku" />
        </collection>
    </resultMap>

    <sql id="Base_Column_List">
        AFTER_SALES_ID,
        AFTER_SALES_NO,
        COMPANY_ID,
        SUBJECT_TYPE,
        `TYPE`,
        ORDER_ID,
        ORDER_NO,
        SERVICE_USER_ID,
        VALID_STATUS,
        VALID_TIME,
        `STATUS`,
        ATFER_SALES_STATUS,
        ATFER_SALES_STATUS_RESON,
        ATFER_SALES_STATUS_USER,
        ATFER_SALES_STATUS_COMMENTS,
        FIRST_VALID_STATUS,
        FIRST_VALID_TIME,
        FIRST_VALID_USER,
        FIRST_VALID_COMMENTS,
        `SOURCE`,
        ADD_TIME,
        CREATOR,
        MOD_TIME,
        UPDATER,
        IS_OUT_AFTER,
        INVOICE_SEND_STATUS,
        INVOICE_ARRIVAL_STATUS,
        UPDATE_DATA_TIME,
        IS_LIGHTNING,
        CREATE_TYPE,
        CREATE_FRONT_END_USER,
        CLOSE_FRONT_END_MOBILE,
        VERIFIES_NOT_PASS_REASON,
        HANDLE_STATUS,
        INVOICE_REFUND_STATUS,
        AMOUNT_REFUND_STATUS,
        AMOUNT_COLLECTION_STATUS,
        AMOUNT_PAY_STATUS,
        INVOICE_MAKEOUT_STATUS,
        IS_NEW,
        DELIVERY_DIRECT_AFTER_SALES_ID
    </sql>


    <select id="findByAfterSalesNoAndSubjectType" resultMap="BaseResultMap">
        select TAS.*,
               TASG.*,
               TR.TRADER_NAME,
               TR.TRADER_ID,
               TR.DELIVERY_STATUS,
               TTS.TRADER_SUPPLIER_ID,
               VCS.SKU_NO
        from T_AFTER_SALES TAS
                 left join T_AFTER_SALES_GOODS TASG on TAS.AFTER_SALES_ID = TASG.AFTER_SALES_ID
                 left join V_CORE_SKU VCS on TASG.GOODS_ID = VCS.SKU_ID
                 left join T_BUYORDER TR on TR.BUYORDER_ID = TAS.ORDER_ID
                 left join T_TRADER_SUPPLIER TTS on TR.TRADER_ID = TTS.TRADER_ID
        where TAS.AFTER_SALES_NO = #{afterSalesNo,jdbcType=VARCHAR}
          and TAS.SUBJECT_TYPE = #{subjectType,jdbcType=INTEGER}
          and TAS.COMPANY_ID = 1
    </select>

<!--auto generated by MybatisCodeHelper on 2022-12-12-->
    <select id="findByAfterSalesId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_AFTER_SALES
        where AFTER_SALES_ID=#{afterSalesId,jdbcType=INTEGER}
    </select>

    <select id="findSaleByAfterSalesNoAndSubjectType" resultMap="BaseResultMap">
        select TAS.*,
               TASG.*,
               TS.TRADER_NAME,
               TS.TRADER_ID,
               TS.DELIVERY_STATUS,
               TS.ARRIVAL_STATUS,
               TTC.TRADER_CUSTOMER_ID,
               VCS.SKU_NO
        from T_AFTER_SALES TAS
                 left join T_AFTER_SALES_GOODS TASG on TAS.AFTER_SALES_ID = TASG.AFTER_SALES_ID
                 left join V_CORE_SKU VCS on TASG.GOODS_ID = VCS.SKU_ID
                 left join T_SALEORDER TS on TS.SALEORDER_ID = TAS.ORDER_ID
                 left join T_TRADER_CUSTOMER TTC on TS.TRADER_ID = TTC.TRADER_ID
        where TAS.AFTER_SALES_NO = #{afterSalesNo,jdbcType=VARCHAR}
          and TAS.SUBJECT_TYPE = #{subjectType,jdbcType=INTEGER}
          and TAS.COMPANY_ID = 1
    </select>

    <!--auto generated by MybatisCodeHelper on 2023-02-18-->
    <select id="findByAfterSalesIdAndSubjectType" resultMap="BaseResultMap">
        select
        TAS.*,
        TASG.*,
        TS.TRADER_NAME,
        TS.TRADER_ID,
        TS.DELIVERY_STATUS,
        TS.ARRIVAL_STATUS,
        TTC.TRADER_CUSTOMER_ID,
        VCS.SKU_NO
        from
        T_AFTER_SALES TAS
        left join T_AFTER_SALES_GOODS TASG on TAS.AFTER_SALES_ID = TASG.AFTER_SALES_ID
        left join V_CORE_SKU VCS on TASG.GOODS_ID = VCS.SKU_ID
        left join T_SALEORDER TS on TS.SALEORDER_ID = TAS.ORDER_ID
        left join T_TRADER_CUSTOMER TTC on TS.TRADER_ID = TTC.TRADER_ID
        where TAS.AFTER_SALES_ID=#{afterSalesId,jdbcType=INTEGER}
        and TAS.SUBJECT_TYPE=#{subjectType,jdbcType=INTEGER}
        and TAS.COMPANY_ID = 1
    </select>

    <select id="findSaleBackBySaleorderId" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            T_AFTER_SALES
        <where>
            <if test="afterSalesId != null">
                AND AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
            </if>
            <if test="afterSalesNo != null">
                AND AFTER_SALES_NO = #{afterSalesNo,jdbcType=VARCHAR}
            </if>
            <if test="orderId != null">
                AND ORDER_ID = #{orderId,jdbcType=INTEGER}
            </if>
            <if test="type != null">
                AND TYPE =#{type,jdbcType=INTEGER}
            </if>
            <if test="atferSalesStatus != null">
                AND ATFER_SALES_STATUS = #{atferSalesStatus,jdbcType=INTEGER}
            </if>
            <if test="beginTime != null">
                AND VALID_TIME <![CDATA[>=]]> #{beginTime,jdbcType=BIGINT}
            </if>
            <if test="endTime != null">
                AND VALID_TIME <![CDATA[<=]]> #{endTime,jdbcType=BIGINT}
            </if>
        </where>
    </select>

<!--auto generated by MybatisCodeHelper on 2023-04-26-->
    <select id="findByOrderIdAndTypeAndSubjectType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_AFTER_SALES
        where ORDER_ID=#{orderId,jdbcType=INTEGER} and `TYPE`=#{type,jdbcType=INTEGER} and
        SUBJECT_TYPE=#{subjectType,jdbcType=INTEGER}
    </select>

<!--auto generated by MybatisCodeHelper on 2023-05-06-->
    <select id="findByAfterSalesNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_AFTER_SALES
        where AFTER_SALES_NO=#{afterSalesNo,jdbcType=VARCHAR}
    </select>
    <select id="getAfterTraderSuplier" resultType="com.vedeng.erp.kingdee.batch.dto.BatchAfterSalesDto">
        SELECT
            AF.AFTER_SALES_NO,
            E.`NAME` ,
            TR.TRADER_ID,
            TR.TRADER_NAME,
            TS.TRADER_SUPPLIER_ID
        FROM
            T_AFTER_SALES AF
                LEFT JOIN T_AFTER_SALES_INSTALLSTION AFG ON AFG.AFTER_SALES_ID = AF.AFTER_SALES_ID
                LEFT JOIN T_ENGINEER E ON AFG.ENGINEER_ID = E.ENGINEER_ID
                LEFT JOIN T_TRADER TR ON TR.TRADER_NAME=E.`NAME`
                LEFT JOIN T_TRADER_SUPPLIER TS ON TS.TRADER_ID=TR.TRADER_ID
        WHERE
            AF.AFTER_SALES_NO = #{afterSalesNo}
    </select>

    <select id="getTraderCustomerIdByAfterSalesId" resultType="java.lang.Integer">
        select ttc.TRADER_CUSTOMER_ID
        from T_AFTER_SALES tas
                 left join T_AFTER_SALES_DETAIL tasd on tas.AFTER_SALES_ID = tasd.AFTER_SALES_ID
                 left join T_TRADER_CUSTOMER ttc on tasd.TRADER_ID = ttc.TRADER_ID
        where tas.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
    </select>

    <select id="queryAfterSaleAdjustPriceNew" resultMap="BaseResultMap">
        SELECT TAS.*
        FROM T_AFTER_SALES TAS
                 left join T_SALEORDER TS on TAS.ORDER_ID = TS.SALEORDER_ID
                 left join T_SALEORDER_GOODS TSG on TS.SALEORDER_ID = TSG.SALEORDER_ID and TSG.IS_DELETE = 0
                 left join V_CORE_SKU VCS on TSG.GOODS_ID = VCS.SKU_ID
                 left join T_SALE_SETTLEMENT_ADJUSTMENT TSSA on TSSA.AFTER_SALE_ID = TAS.AFTER_SALES_ID and TSSA.ADJUSTMENT_TYPE = 2
        where TAS.TYPE = 539
          and TAS.ATFER_SALES_STATUS = 2
          and TS.SALEORDER_ID is not null
          and TSG.SALEORDER_GOODS_ID is not null
          and TSG.SKU not in ('V127063', 'V251526', 'V256675', 'V253620', 'V251462', 'V140633')
          and TSG.PRICE = 0
          and VCS.IS_VIRTURE_SKU != 1
          and TSSA.SALE_SETTLEMENT_ADJUSTMENT_ID is null
          and TAS.ADD_TIME >= 1609430400000
        <if test="beginTime != null">
            AND TAS.MOD_TIME <![CDATA[>=]]> #{beginTime,jdbcType=BIGINT}
        </if>
        <if test="endTime != null">
            AND TAS.MOD_TIME <![CDATA[<=]]> #{endTime,jdbcType=BIGINT}
        </if>
        group by TAS.AFTER_SALES_ID
        order by TAS.AFTER_SALES_ID desc
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>

<!--auto generated by MybatisCodeHelper on 2023-11-30-->
    <select id="selectBySubjectTypeAndOrderNo" resultType="java.lang.Integer">
        select
        AFTER_SALES_ID
        from T_AFTER_SALES
        where SUBJECT_TYPE=#{subjectType,jdbcType=INTEGER} and ORDER_NO=#{orderNo,jdbcType=VARCHAR}
    </select>


    <select id="getNonGiftAfterOrders" resultType="java.lang.Integer">
        select TASG.AFTER_SALES_GOODS_ID
        from  T_AFTER_SALES_GOODS TASG
        left join T_SALEORDER_GOODS TSG  on TASG.ORDER_DETAIL_ID = TSG.SALEORDER_GOODS_ID
        where TSG.IS_GIFT = 0
        and TASG.AFTER_SALES_GOODS_ID in
        <foreach collection="afterSalesGoodsIds" item="detailGoodsId" index="index" open="(" close=")" separator=",">
            #{detailGoodsId,jdbcType=INTEGER}
        </foreach>
    </select>

</mapper>
