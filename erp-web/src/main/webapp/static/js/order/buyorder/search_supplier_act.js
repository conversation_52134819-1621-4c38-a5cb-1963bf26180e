$(function() {
	$("#search").click(function(){
		checkLogin();
		var name=$("#supplierName").val();
		if(name == ''){
			$("#supplierName").addClass("errorbor")
			$("#none").removeClass("none");
			return  false;
		}else{
			$("#supplierName").removeClass("errorbor")
			$("#none").addClass("none");
		}
		$("#myform1").submit();
	});

});

function unableChoose(){
	layer.alert("请先审核通过该供应商，再选择");
}
function addSelectSupplier(traderSupplierName,traderSupplierId,traderId,supplierComments,periodBalance,periodDay,callbackFuntion,isGift,validRebateCharge,certificateOverdue){
	checkLogin();
	//如果有回调函数就调用
	//供应商禁止选择'南京ZP'
	if (traderSupplierName == '南京ZP' && isGift == 1){
		window.parent.$("#searchTraderNameAct").parent("div").siblings("div").removeClass("none").html("请选择真实的供应商！");
		window.parent.$("#searchTraderNameAct").addClass("errorbor");
		window.parent.$("#searchTraderNameAct").val('');
		var index = parent.layer.getFrameIndex(window.name); //获取当前窗体索引
		parent.layer.close(index);
		return;
	}
	$("#searchTraderNameAct",window.parent.document).hide();
	$("#traderNameAct",window.parent.document).val(traderSupplierName);
	$("#traderSupplierIdAct",window.parent.document).val(traderSupplierId);
	$("#traderIdAct",window.parent.document).val(traderId);
	$("#nameAct",window.parent.document).html(traderSupplierName);
	if (certificateOverdue){
		$("#certificateOverdueAct",window.parent.document).show();
	}
	$("#nameAct",window.parent.document).removeClass("none");
	$("#errorMesAct",window.parent.document).addClass("none");
	$("#researchAct",window.parent.document).removeClass("none");
	
		$.ajax({
			url:page_url+'/order/buyorder/getContactsAddress.do',
			data:{"traderId":traderId,"traderType":2},
			type:"POST",
			dataType : "json",
			async: false,
			success:function(data)
			{
				if(data.code ==0){
					$("#contactAct",window.parent.document).removeClass("none");
					var contactSelect = $("select[name='traderContactStrAct']",window.parent.document);
					contactSelect.empty();
					var contact = "<option value=''>请选择</option>";
					for(var i=0; i<data.data.length;i++){
						if(data.data[i].isDefault==1){
							contact += "<option selected='selected' value='"+data.data[i].traderContactId+"|"
										+data.data[i].name+"|"+data.data[i].mobile+"|"+data.data[i].telephone+"'>"+data.data[i].name+"/"+data.data[i].mobile+"/"+data.data[i].telephone+"</option>";
						}else{
							contact += "<option value='"+data.data[i].traderContactId+"|"
										+data.data[i].name+"|"+data.data[i].mobile+"|"+data.data[i].telephone+"'>"+data.data[i].name+"/"+data.data[i].mobile+"/"+data.data[i].telephone+"</option>";
						}
						
					}
					contactSelect.append(contact);
				}
			},
			error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部pride。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		});
	
	var index = parent.layer.getFrameIndex(window.name); //获取当前窗体索引
    parent.layer.close(index);

}

