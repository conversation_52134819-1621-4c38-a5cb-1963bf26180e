<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.mapper.UnitKingDeeExtMapper">

<select id="getAllUnitKingDeeList" resultType="com.vedeng.goods.dto.UnitKingDee">
  select * from T_UNIT_KING_DEE where IS_DELETE = 0
  <if test="name != null and name != ''">
    and (UNIT_KING_DEE_NO like concat("%",#{name,jdbcType=VARCHAR},"%") or UNIT_KING_DEE_NAME like concat("%",#{name,jdbcType=VARCHAR},"%"))
  </if>
  order by ADD_TIME DESC
  </select>

  <insert id="batchInsert" keyColumn="UNIT_KING_DEE_ID" keyProperty="unitKingDeeId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_UNIT_KING_DEE
    (UNIT_KING_DEE_NO, UNIT_KING_DEE_NAME, IS_DELETE, ADD_TIME, CREATOR, CREATOR_NAME,
    MOD_TIME, UPDATER, UPDATER_NAME, REMARK, UPDATE_REMARK)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.unitKingDeeNo,jdbcType=VARCHAR}, #{item.unitKingDeeName,jdbcType=VARCHAR},
      #{item.isDelete,jdbcType=BOOLEAN}, #{item.addTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER},
      #{item.creatorName,jdbcType=VARCHAR}, #{item.modTime,jdbcType=TIMESTAMP}, #{item.updater,jdbcType=INTEGER},
      #{item.updaterName,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, #{item.updateRemark,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>
</mapper>