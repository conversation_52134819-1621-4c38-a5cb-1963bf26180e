package com.vedeng.goods.feign.saleorder;

import com.vedeng.common.feign.annotations.FeignApi;
import com.vedeng.common.feign.constants.ServerConstants;
import com.vedeng.erp.aftersale.dto.AfterSaleServiceStandardInfoDto;
import feign.Param;
import feign.RequestLine;

/**
 * 远程调用erp-saleorder
 *
 * <AUTHOR>
 */
@FeignApi(serverName = ServerConstants.ERP_SERVER)
public interface RemoteAftersaleInfoApiService {

    /**
     * 根据SKU_ID获取售后政策
     *
     * @param skuNo
     * @return
     */
    @RequestLine("GET /order/aftersales/getEffectAfterSalePolicy.do?skuNo={skuNo}")
    AfterSaleServiceStandardInfoDto getEffectAfterSalePolicy(@Param("skuNo") String skuNo);


}
