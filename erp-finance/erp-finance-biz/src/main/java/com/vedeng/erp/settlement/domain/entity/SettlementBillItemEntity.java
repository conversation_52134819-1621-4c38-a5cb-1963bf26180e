package com.vedeng.erp.settlement.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * 财务结算单明细
 */
@Getter
@Setter
public class SettlementBillItemEntity extends BaseEntity {
    /**
     * 主键
     */
    private Integer settleItemBillId;

    /**
     * 结算单ID
     */
    private Integer settleBillId;

    /**
     * 结算单号
     */
    private String settleBillNo;

    /**
     * 交易方向1收入2支出3转移4转入5转出
     */
    private Integer traderDirection;

    /**
     * 结算类型
     * 1.返利
     * 2.余额
     * 3.银行
     * 4.信用额度
     */
    private Integer settlementType;

    /**
     * 业务单据类型
     * saleOrder.销售单
     * buyOrder.采购单
     * buyOrderExpense.采购费用单
     * buyOrderAfterSale.采购售后单
     * buyOrderExpenseAfterSale.采购费用售后单
     */
    private String businessType;

    /**
     * 业务单号
     */
    private String businessNo;

    /**
     * 业务单ID
     */
    private Integer businessId;

    /**
     * 业务明细单ID
     */
    private Integer businessItemId;

    /**
     * 结算产品名称
     */
    private String productName;

    /**
     * 单价
     */
    private BigDecimal price;

    /**
     * 数量
     */
    private BigDecimal number;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 发票状态0：无发票1：部分有票2：全部有票
     */
    private Integer invoiceStatus;

    /**
     * 结算状态0：未结算1：部分结算2：已结算
     */
    private Integer settlementStatus;

    /**
     * 是否删除
     */
    private Integer isDelete;
}