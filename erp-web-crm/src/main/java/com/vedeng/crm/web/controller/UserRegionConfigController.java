package com.vedeng.crm.web.controller;

import com.vedeng.common.core.annotation.MenuDesc;
import com.vedeng.common.core.base.BaseController;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.stereotype.Controller;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 人员关系配置
 * @date 2025/3/26 8:55
 */
@Controller
@RequestMapping("/role/userRegionConfig")
public class UserRegionConfigController extends BaseController {


    @RequestMapping(value = "/index")
    @MenuDesc(menuValue = "C05", menuDesc = "销售关系配置")
    public String index() {

        return "/vue/view/crm/profile/role/userRegionConfig/index";
    }
}
