package com.vedeng.api.standard.adapter.saleorder.dto;

import lombok.Data;

import java.io.Serializable;


@Data
public abstract class SaleOrderRequest implements Serializable {
    /**
     * 销售订单ID
     */
    private Integer saleOrderId;
    /**
     * 销售订单号
     */
    private String saleOrderNo;
    /**
     * 获取操作类型
     * 子类需要实现此方法来标识自己的操作类型
     */
    public abstract String getOperationType();

    /**
     * 验证请求数据的有效性
     * 子类可以重写此方法来实现特定的验证逻辑
     */
    public void validate() {
        // 基础验证逻辑
        // 子类可以重写添加特定验证
    }

}
