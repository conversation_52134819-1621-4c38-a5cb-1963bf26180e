void function(){
    new Vue({
        el: '#page-container',
        data () {
            return {
                loadingEl: null,
                // 筛选项
                filter_dep: '',   // 部门名称
                filter_group: '', // 小组名称
                filter_sale: '',  // AED销售
                filter_saleName: '',
                saleRemoteInfo: {
                    url: '/broadcast/user/getAllAedUserList.do',
                    paramsType: 'url',
                    hasPage: true,
                    paramsKey: 'username', // 接口入参
                    parseLabel: 'username', // 面板label
                    parseValue: 'aedUserId', // 面板value
                }, // AED销售下拉接口参数
                list: [],
                total: 0,
                pageSize: 20,
                pageNum: 1,
                // 新建/编辑小组
                isShowEditGroup: false,
                editData: null, // 编辑对象记录
                deptRemoteInfo: {
                    url: '/broadcast/common/listDept.do?parentId=0',  // application/x-www-form-urlencoded
                    paramsType: 'url',
                    hasPage: false,
                    paramsKey: 'deptName', // 接口入参
                    parseLabel: 'label', // 面板label
                    parseValue: 'value', // 面板value
                },
                deptId: '',
                defaultDeptName: '', // 用于编辑回显
                groupName: '',
                sales: [],
                defaultSales: [], // 用于编辑回显
            }
        },
        watch: {
            isShowEditGroup (newV) {
                if (!newV) {
                    this.editData = null;
                    this.deptId = '';
                    this.defaultDeptName = '';
                    this.groupName = '';
                    this.sales = [];
                    this.defaultSales = [];
                }
            }
        },
        mounted() {
            this.getList();
        },
        methods: {
            getList() {
                this.showloading();
                axios.post('/broadcast/dept/getDeptListPage.do', {
                    "pageNum": this.pageNum,
                    "pageSize": this.pageSize,
                    "param": {
                        "deptName": this.filter_dep,
                        "groupName": this.filter_group,
                        "aedUserId": this.filter_sale
                    }
                }).then(({data}) => {
                    this.hideloading();
                    console.log('data:', data);
                    if (data.success) {
                        this.list = data.data.list || [];
                        this.total = data.data.total || [];
                    }
                })
            },
            handleSizeChange(val) {
                console.log(`每页 ${val} 条`);
                this.pageSize = val;
                this.getList();
            },
            handleCurrentChange(val) {
                console.log(`当前页: ${val}`);
                this.pageNum = val;
                this.getList();
            },
            handlerSearch() {
                this.pageNum = 1;
                console.log('query', this.filter_dep, this.filter_group, this.filter_sale);
                this.getList();
            },
            handlerReset() {
                this.filter_dep = '';
                this.filter_group = '';
                this.filter_sale = '';
                this.filter_saleName = '';
                this.getList();
            },
            handlerChange(item) {
                this.filter_saleName = item.selected.label;
            },

            showEditGroup(type = 'add') { // type： add新建 edit编辑
                this.isShowEditGroup = true;

                let checkData = {
                    deptId: {
                        required: '请选择部门'
                    },
                    groupName: {
                        required: '请填写小组'
                    },
                    // sales: {
                    //     required: '请选择AED销售'
                    // },
                }
                if (type == 'edit') {
                    delete checkData.deptId
                }
                this.$form.rules(checkData, 'editGroup', this)
            },
            addRow () {
                this.editData = null;
                this.deptId = '';
                this.defaultDeptName = '';
                this.groupName = '';
                this.sales = [];
                this.defaultSales = [];
                this.showEditGroup();
            },
            editRow(item) {
                console.log(item);
                this.editData = item;
                this.deptId = item.deptId;
                this.defaultDeptName = item.deptName;
                this.groupName = item.groupName;

                // AED销售回显
                let labels = item.aedUsernames && item.aedUsernames.split(',') || [];
                let values = item.aedUserIds && item.aedUserIds.split(',') || [];
                let arr = [];
                values.forEach((item, index) => {
                    arr.push({ label: labels[index], value: values[index] });
                })
                this.defaultSales = arr;
                this.sales = values;
                this.showEditGroup('edit');
            },
            delRow(item) {
                console.log(item);
                let _this = this;
                this.$popup.warn({
                    message: '确定删除？',
                    buttons: [{
                        txt: '是',
                        btnClass: 'delete',
                        callback() {
                            axios.get(`/broadcast/dept/delete.do?id=${item.id}`).then(({data}) => {
                                console.log('del group:', data);
                                if (data.code === 0) {
                                    _this.$message({
                                        message: data.message,
                                        type: 'success'
                                    });
                                    _this.getList();
                                } else {
                                    _this.$message({
                                        message: data.message || '删除失败',
                                        type: 'error'
                                    });
                                }
                            });
                        }
                    }, { txt: '否' }]
                })
            },


            confirm() {
                console.log('确认');
                if (this.$form.validForm('editGroup')) {
                    this.showloading();

                    let reqData = {
                        "deptId": this.deptId,
                        "groupName": this.groupName,
                        "aedUserIds": this.sales.join(',')
                    };
                    if (this.editData) {
                        reqData['id'] = this.editData.id
                    }
                    axios.post('/broadcast/dept/saveOrUpdate.do', reqData).then(({data}) => {
                        this.hideloading();
                        console.log('data:', data);

                        if (data.code === 0) {
                            this.$message({
                                message: data.message,
                                type: 'success'
                            });
                            this.isShowEditGroup = false;
                            this.getList();
                        } else {
                            this.$message({
                                message: data.message || '保存失败',
                                type: 'error'
                            });
                        }
                    })
                }
            },


            showloading() {
                this.loadingEl = this.$loading({
                    lock: true,
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)'
                });
            },
            hideloading() {
                this.loadingEl && this.loadingEl.close();
            },
        }
    })
}.call(this);