package com.newtask;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.util.StringUtil;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.service.BuyorderService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @date $
 */
@Component
@JobHandler(value="BuyorderDeliverStatusTask")
public class BuyorderDeliverStatusTask extends AbstractJobHandler {

    @Autowired
    private BuyorderService buyorderService;

    @Autowired
    private BuyorderMapper buyorderMapper;

    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        XxlJobLogger.log("XXL-JOB, Hello World.");
        updateBuyorderDeliverStatus(s);
        return SUCCESS;
    }

    private void updateBuyorderDeliverStatus(String s) {
        if(StringUtil.isBlank(s)){
          List<Integer> buyorderIdList = buyorderMapper.getAllBuyorderId();
            for (Integer buyorderId : buyorderIdList) {
                buyorderService.updateBuyorderDeliveryStatus(buyorderId);
            }
            XxlJobLogger.log("更新了"+buyorderIdList.size()+"条");
        }else{
            Integer buyorderId = Integer.valueOf(s);
            buyorderService.updateBuyorderDeliveryStatus(buyorderId);
            XxlJobLogger.log("更新成功id:{}",buyorderId);
        }
    }
}
