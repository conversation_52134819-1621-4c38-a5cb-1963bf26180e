// 排除数字0 获取值
const getVal = (val) => {
    if (val == 0) return val;
    if (val) {
        return val;
    } else {
        return '';
    }
};

void function () {
    new Vue({
        el: '#page-container',
        data: {
            isEdit: false, // true:编辑状态  false:新建
            businessId: '', // 业务id - 仅编辑
            preSaleDataType: '', //业务类型 线索:1, preSaleDataEnum:BUSSINESS_LEADS 商机:2, preSaleDataEnum:BUSSINESS_CHANCE
            canAjax: true,
            fixedTop: layout_hidden_value ? 0 : 50, //表头悬浮高度，根据有没有头部判断
            pageLoading: true, // 页面加载状态
            /* Card1 */
            // 客户名称
            traderName: '',
            traderId: '',
            tycFlag: 'N', //是否是天眼查带入
            // 联系人
            traderContactName: '', // 联系人
            traderContactId: '', // 联系人id
            // 手机号
            mobile: '',
            phoneMsg: '',
            // 固话
            telephone: '',
            telephoneMsg: '',
            //其他联系方式
            otherContactInfo: '',
            // 终端名称
            terminalTraderName: '',
            isFromTyc: false, // 是否客户带入 - 如是,入参不需要带“关联终端信息”
            remoteInfo: {
                url: '/terminal/public/search',
                hasPage: true,
                paramsKey: 'terminalTraderName', // 接口入参
                parseLabel: 'terminalName', // 面板label
                parseValue: 'terminalName', // 面板value
                parseLabelRight: 'hosModelName', // 面板right值
            },
            orderTerminalDto: '', // 关联终端信息 - 联想选择的终端，整个带到入参
            // 终端性质
            radioList1: [],
            terminalTraderNature: '',
            // 终端地区
            addressData: [],
            area: [],
            areaNames: [],
            /* Card2 */
            // 商机类型
            radioList2: [],
            businessType: '',
            amount: '', // 预计成交金额
            orderTimeDate: '', // 预计成单日期
            productCommentsSale: '', // 产品需求
            // 商机标签
            checkboxList1: [],
            tagIds: [],
            // 客情关系 (1决策人，2使用人，多选逗号分隔)
            checkboxList2: [
                { label: '决策人', value: '1' },
                { label: '使用人', value: '2' }
            ],
            customerRelationship: [],
            // 采购方式
            radioList3: [],
            purchasingType: '',
            // 招投标阶段
            radioList4: [],
            biddingPhase: '',
            // 招标参数
            radioList5: [
                { label: '可调整', value: 1 },
                { label: '不可调整', value: 2 }
            ],
            biddingParameter: '',
            comments: '', // 备注

            /* Card3 */
            businessLeadsId: '', // 线索id
            hasShowCard3: true, // 展示跟进记录卡片
            // 跟进类型
            // FollowUpTypeS: [], // 字典
            followUpType: '',
            beginTimeDate: '', // 跟进时间  
            contentSuffix: '', // 跟进内容
            nextContactDate: '', // 待跟进时间
            noneNextDate: false, // 暂无
            nextContactContent: '',  // 待跟进事项
            isBusinessAllInput: false, //线索编辑新增判断商机字段是否全部填写
            isBusiness: false, //当前页面是否已经是商机
            clueType: '392', //来源 新建默认是销售392，编辑的只做存取 391:总机 394:商城
            //总机线索字段(当前页面只做存取，不做修改)start 
            sendVx: '',
            inquiry: '',
            inquiryName: '',
            source: '',
            sourceName: '',
            communication: '',
            communicationName: '',
            CategoryContent: '',
            leadsArea: [], //总机创建线索的地区
            leadsAreaNames: [], //总机创建线索的地区名称
            entrances: '',
            entrancesName: '',
            functions: '',
            functionsName: '',
            //end
            SysOptions: [], //询价行为枚举
            Sources: [], //渠道类型枚举
            SourceNames: [], //渠道名称枚举
            belonger: '',
            belongerId: '', //归属人
            leadsNo: '', //线索编号
            bussinessChanceNo: '', //商机编号
            addTime: '', //创建时间
            visitId: '', //拜访计划创建商机
            defaultAiData: {}, //编辑时回显ai数据
            aiValue: {
                keywords: [],
                categoryIds: []
            },
            isAiloading: false,
            defaultBelongerId: '', //编辑的时候修改客户名称为文本 需要将归属销售重置为进页面的时候的归属销售
            defaultBelonger: '', 
        },
        async created() {
            GLOBAL.showGlobalLoading();


            this.businessId = GLOBAL.getQuery('id');
            this.businessId && (this.isEdit = true);

            this.visitId = GLOBAL.getQuery('visitid');

            this.preSaleDataType = GLOBAL.getQuery('type');
            this.isBusiness = this.preSaleDataType == 2 ? true : false;

            /* 线索转商机 START */
            // this.businessLeadsId = document.getElementById('businessLeadsId').value || ''; // 线索id
            // let followUp = document.getElementById('followUp').value || 'N'; // 线索是否有跟进记录
            // if (this.businessLeadsId && followUp == 'Y') { // 线索转商机-有跟进记录 则不展示跟进记录
            //     this.hasShowCard3 = false;
            // } else if (this.isEdit) {
            //     this.hasShowCard3 = false;
            // } else {
            //     this.hasShowCard3 = true;
            // }

            // 线索转商机-自动带入 客户名称/手机/固话/联系人
            // if (this.businessLeadsId) {
            //     await this.$axios.post(`/crm/businessLeads/profile/getOne?id=${this.businessLeadsId}`)
            //         .then(({data})=> {
            //             if (data.success) {
            //                 let leadsInfo = data.data || {};
            //                 this.traderId = leadsInfo.traderId || '';
            //                 this.traderName = leadsInfo.traderName || '';
            //                 this.traderContactName = leadsInfo.contact || '';
            //                 this.traderContactId = leadsInfo.traderContactId || '';
            //                 this.mobile = leadsInfo.phone || '';
            //                 this.telephone = leadsInfo.telephone || '';
            //             }
            //         }).catch(err=> {
            //             console.log('leads deatail err', err);
            //         })
            // }
            /* 线索转商机 END */


            // 终端性质
            await this.$axios.post('/crm/sysOption/public/getByParentId?parentId=5600').then(({ data }) => {
                if (data.success) {
                    let arr = data.data || [];
                    this.radioList1 = arr.map(m1 => {
                        return {
                            label: m1.title,
                            value: m1.sysOptionDefinitionId
                        }
                    })
                }
            });

            // 地址数据
            await this.$axios.get('/crm/common/profile/getRegionAll').then(({ data }) => {
                if (data.success) {
                    this.addressData = data.data || [];
                }
            });

            // 商机类型-字典
            await this.$axios.post('/crm/sysOption/public/getByParentId?parentId=5700').then(({ data }) => {
                if (data.success) {
                    let arr = data.data || [];
                    this.radioList2 = arr.map(m1 => {
                        return {
                            label: m1.title,
                            value: m1.sysOptionDefinitionId
                        }
                    })
                }
            });

            // 商机标签-字典
            await this.$axios.get('/crm/businessChance/profile/findAllTag').then(({ data }) => {
                if (data.success) {
                    let arr = data.data || [];
                    this.checkboxList1 = arr.map(m1 => {
                        return {
                            label: m1.name,
                            value: m1.id
                        }
                    })
                }
            });

            // 采购方式-字典
            await this.$axios.post('/crm/sysOption/public/getByParentId?parentId=404').then(({ data }) => {
                if (data.success) {
                    let arr = data.data || [];
                    this.radioList3 = arr.map(m1 => {
                        return {
                            label: m1.title,
                            value: m1.sysOptionDefinitionId
                        }
                    })
                }
            });

            // 招投标阶段-字典
            await this.$axios.get('/crm/sysOption/public/getByParentId?parentId=5800').then(({ data }) => {
                if (data.success) {
                    let arr = data.data || [];
                    this.radioList4 = arr.map(m1 => {
                        return {
                            label: m1.title,
                            value: m1.sysOptionDefinitionId
                        }
                    })
                }
            });

            // if (this.hasShowCard3) {
            //     // 跟进类型字典
            //     await this.$axios.post('/crm/sysOption/public/getByParentId?parentId=5900').then(({data})=> {
            //         if (data.success) {
            //             let res = data.data || [];
            //             this.FollowUpTypeS = res.map(item => {
            //                 return {
            //                     label: item.title,
            //                     value: item.sysOptionDefinitionId
            //                 }
            //             })
            //         }
            //     })
            // }

            // 编辑
            if (this.businessId) {
                let { data: resData } = await this.$axios.get(`/crm/presales/profile/detail?dataId=${this.businessId}&type=${this.preSaleDataType}`)
                if (resData.success) {
                    this.initForm(resData.data);
                    this.checkBusinessInput();
                }
            } else if(this.visitId) {
                this.geiVisitInfo();
            } else {
                this.pageLoading = false;
                GLOBAL.hideGlobalLoading();
            }

            if (this.isEdit && this.clueType == 391 && !this.isBusiness && GLOBAL.auth('C0102')) {
                // 询价行为
                const { data: result1 } = await this.$axios.post(`/crm/sysOption/public/getByParentId?parentId=391`);
                if (result1.success) {
                    let arr = result1.data || [];
                    this.SysOptions = arr.map(m1 => {
                        return {
                            label: m1.title,
                            value: m1.sysOptionDefinitionId
                        }
                    })
                    console.log('询价行为字典:', this.SysOptions)
                }

                // 渠道类型枚举
                const { data: result2 } = await this.$axios.post('/crm/businessLeads/profile/getCascaderChannelOptions');
                if (result2.success) {
                    let res = result2.data || [];
                    res.forEach(item => {
                        if (!(item.children && item.children.length)) {
                            item.disabled = true;
                        } else if (this.source && item.value == this.source) {
                            this.SourceNames = item.children || [];
                        }
                    })

                    this.Sources = res;
                }
            }

            this.formBindValid();

            // if (!this.isEdit && this.hasShowCard3) {
            //     this.beginTimeDate = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
            // }
        },
        mounted() {

        },
        methods: {
            formBindValid() {
                const _this = this;
                let rules = {
                    traderName: {
                        required: '请输入客户名称'
                    },
                    amount: {
                        custom: {
                            valid(value) {
                                if (!value) {
                                    return true;
                                }

                                if (!/^\d{1,8}(\.\d{1,2})?$/.test(value)) {
                                    return false;
                                } else {
                                    if (parseFloat(value) > 0) {
                                        return true;
                                    } else {
                                        return false;
                                    }
                                }
                            },
                            message: '最多支持8位数+2位小数，仅限正数',
                        },
                    },
                }

                if (this.isBusiness) {
                    rules = {
                        ...rules,
                        traderContactName: {
                            required: '请输入联系人'
                        },
                        businessType: {
                            required: '请选择商机类型'
                        },
                        amount: {
                            required: '请输入预计成单金额',
                            custom: {
                                valid(value) {
                                    if (!value) {
                                        return true;
                                    }
                                    if (!/^\d{1,8}(\.\d{1,2})?$/.test(value)) {
                                        return false;
                                    } else {
                                        if (parseFloat(value) > 0) {
                                            return true;
                                        } else {
                                            return false;
                                        }
                                    }
                                },
                                message: '最多支持8位数+2位小数，仅限正数',
                            },
                        },
                        orderTimeDate: {
                            required: '请选择预计成单日期',
                        },
                        productCommentsSale: {
                            required: '请输入产品需求',
                        },
                    }
                }

                if (!this.isEdit) {
                    rules = {
                        ...rules,
                        // followUpType: {
                        //     required: '请选择跟进类型'
                        // },
                        // beginTimeDate: {
                        //     required: '请选择跟进时间'
                        // },
                        // contentSuffix: {
                        //     required: '请输入跟进内容',
                        // },
                        nextContactDate: {
                            custom: {
                                valid(value) {
                                    if (!_this.noneNextDate && !value.trim()) {
                                        return false;
                                    } else {
                                        return true;
                                    }
                                },
                                message: '请选择待跟进时间',
                            },
                        },
                        nextContactContent: {
                            custom: {
                                valid(value) {
                                    if (!_this.noneNextDate && !value.trim()) {
                                        return false;
                                    } else {
                                        return true;
                                    }
                                },
                                message: '请输入待跟进事项',
                            },
                        }
                    }
                }

                if (this.isEdit && this.clueType == 391 && !this.isBusiness && GLOBAL.auth('C0102')) {
                    console.log('---------------------')
                    rules = {
                        ...rules,
                        inquiry: {
                            required: '请选择询价行为'
                        },
                        source: {
                            required: '请选择渠道类型'
                        },
                        communication: {
                            required: '请选择渠道名称'
                        },
                        // CategoryContent: {
                        //     required: '请选择三级分类'
                        // },
                        leadsArea: {
                            required: '请选择省市区'
                        },
                    }
                }

                // 归属销售valid初始化
                this.$form && this.$form.rules(rules, 'AddBusinessChance', this);
            },
            // 回显表单 - 仅编辑
            initForm(res) {
                console.log('回显 res:', res);

                this.clueType = res.clueType;

                // card1 ---
                this.traderName = res.traderName || '';
                this.traderId = res.traderId || '';
                this.traderContactName = res.contact; // 联系人
                this.traderContactId = res.traderContactId;  // 联系人id
                this.mobile = res.phone || '';
                this.telephone = res.telephone || '';
                this.otherContactInfo = res.otherContactInfo || '';
                this.tycFlag = res.tycFlag || 'N';

                // 终端名称
                this.orderTerminalDto = res.orderTerminalDto || ''; // 关联终端信息 - 联想选择的终端，整个带到入参
                this.terminalTraderName = res.terminalTraderName || '';
                this.terminalTraderNature = res.terminalTraderNature || '';
                if (res.customerNature == 466 && res.traderId) { // 有traderId且终端 禁改终端名称
                    this.isFromTyc = true;
                }

                // 终端地区
                if (res.terminalTraderRegion) {
                    this.area = res.terminalTraderRegion.split(',') || [];
                }

                this.belonger = res.belonger || '';
                this.belongerId = res.belongerId || '';
                this.defaultBelongerId = res.belongerId || '';
                this.defaultBelonger = res.belonger || '';
                this.leadsNo = res.leadsNo || '';
                this.bussinessChanceNo = res.bussinessChanceNo || '';
                this.addTime = res.addTime;

                /* Card2 */
                this.businessType = res.businessType || '';
                this.amount = res.amount || '';
                this.orderTimeDate = res.orderTime || '';
                this.productCommentsSale = res.goodsInfo || '';
                // 标签
                if (res.tagIds) {
                    this.tagIds = res.tagIds.split(',');
                }
                // 客情关系
                if (res.customerRelationship) {
                    this.customerRelationship = res.customerRelationship.split(',') || [];
                }
                this.purchasingType = res.purchasingType || '';
                this.biddingPhase = res.biddingPhase || '';
                this.biddingParameter = res.biddingParameter || '';
                this.comments = res.remark || '';

                //总机线索字段
                this.inquiry = getVal(res.inquiry);
                this.inquiryName = res.inquiryName || '';
                this.source = getVal(res.source);
                this.sourceName = res.sourceName || '';
                this.communication = getVal(res.communication);
                this.communicationName = res.communicationName || '';
                // this.CategoryContent = res.content || '';
                this.entrances = res.entrances || '';
                this.entrancesName = res.entrancesName || '';
                this.functions = res.functions || '';
                this.functionsName = res.functionsName || '';
                this.leadsArea = res.provinceId ? [res.provinceId, res.cityId || '', res.countyId || ''] : [];
                this.leadsAreaNames = res.province ? [res.province, res.city, res.county] : [];

                //ai字段
                this.getAiData();

                if (!layout_hidden_value) {
                    document.title = (this.traderName || '') + (this.isBusiness ? '商机编辑' : '线索编辑');
                }
                this.pageLoading = false;
                GLOBAL.hideGlobalLoading();
            },
            getAiData() {
                this.isAiloading = true;
                this.$axios.post('/crm/category/getByBusiness?businessId=' + this.businessId + '&businessType=' + (this.isBusiness ? 1 : 0)).then(({ data }) => {
                    if(data.success) {
                        this.isAiloading = false;
                        if(data.data.matchedCategories && data.data.matchedCategories.length) {
                            let list = [];
                            let ids = [];

                            data.data.matchedCategories.forEach(item => {
                                list.push({
                                    label: item.category,
                                    value: item.subCategoryId
                                })

                                ids.push(item.subCategoryId);
                            })

                            this.defaultAiData = {
                                keywords: data.data.keywords || [],
                                list: list,
                                word: this.productCommentsSale
                            }

                            this.aiValue = {
                                keywords: data.data.keywords || [],
                                categoryIds: ids
                            }
                        }
                    }
                })
            },
            geiVisitInfo() {
                
                this.$axios.get('/crm/visitrecord/profile/detail?id=' + this.visitId).then(({data}) => {
                    GLOBAL.hideGlobalLoading();

                    if(data.success) {
                        let detail = data.data;
                        this.traderName = detail.customerName || '';
                        this.tycFlag = detail.visitCustomerVo ? detail.visitCustomerVo.tycFlag : '';
                        this.traderContactName = detail.contactName || '';
                        this.mobile = detail.contactMobile || '';
                        this.telephone = detail.contactTele || '';
                    }

                    this.pageLoading = false;
                })
            },
            /* 客户名称 */
            handlerTrader(data) {
                console.log('handler Trader:', data);
                this.traderId = data.traderId || '';
                this.tycFlag = data.tycFlag || 'N';

                if (this.traderId) { // 已建档且性质为终端：自动带入终端名称和地区 // 465分销 466终端
                    if (data.customerNature == 466 && data.traderName) {
                        this.terminalTraderName = data.traderName || '';
                        this.isFromTyc = true;
                    } else {
                        this.terminalTraderName = '';
                        this.isFromTyc = false;
                    }
                    if (data.customerNature == 466 && data.areaIds) { // 终端
                        this.area = data.areaIds.split(',');
                    }

                    if(data.saleId) {
                        this.belongerId = data.saleId || '';
                        this.belonger = data.saleName || '';
                    }
                } else { // 清空
                    if (this.isFromTyc) {
                        this.terminalTraderName = '';
                        this.isFromTyc = false;
                    }

                    this.belongerId = this.defaultBelongerId;
                    this.belonger = this.defaultBelonger;
                }
            },
            // 手机
            handlerPhone(val) {
                console.log('handlerPhone', val);
                if (val.choosed) {
                    this.traderContactName = val.traderContactName || '';
                    this.traderContactId = val.traderContactId || '';

                    if (this.traderContactName) {
                        this.$form.validEl('AddBusinessChance_traderContactName');
                    }
                } else {
                    this.traderContactId = '';
                }

                if (this.mobile && this.mobile.length == 11) {
                    this.phone_Blur();
                }
            },
            //其他联系方式
            handlerMoreContact() {
                if (this.mobile || this.telephone || this.otherContactInfo) {
                    if (this.phoneMsg == '手机、固话和其他联系方式至少填写一项') {
                        this.phoneMsg = '';
                    }
                }

                console.log(this.otherContactInfo)
            },
            // 联系人
            handlerContact(val) {
                if (val.choosed) {
                    this.traderContactId = val.traderContactId || '';
                    this.mobile = val.mobile || '';

                    if (this.mobile) {
                        this.phone_Blur();
                    }
                    this.$form.validEl('AddBusinessChance_traderContactName');
                } else {
                    this.traderContactId = '';
                }
            },
            // 终端名称
            handlerTerminalName(val) {
                console.log('handlerTerminalName:', val);
                if (val.isChoosed) {
                    this.orderTerminalDto = val;
                } else {
                    this.orderTerminalDto = '';
                }

                if(val.areaId) {
                    this.area = [val.provinceId, val.cityId, val.areaId];
                }

                this.isFromTyc = false;
            },
            remoteDataParse(data) {
                return data.list || [];
            },
            // 终端地区
            handleArea(val) {
                let area = [];
                let areaNames = [];

                val.forEach(item => {
                    if (item.value) {
                        area.push(item.value);
                        areaNames.push(item.label);
                    }
                })

                this.area = area;
                this.areaNames = areaNames;
            },
            // 商机标签
            handlerTags(val) {
                console.log(val);
            },
            // 客情关系
            handlerCustomerRelationship(val) {
                console.log(val);
            },
            // 采购方式
            handlerPurchasing() {
                if (this.purchasingType != 406) {
                    this.biddingPhase = '';
                    this.biddingParameter = '';
                }
            },
            // 暂无
            handlerNext(val) {
                if (this.noneNextDate) {
                    this.nextContactDate = '';
                    this.nextContactContent = '';
                }
                this.$form.validEl('AddBusinessChance_nextContactDate');
                this.$form.validEl('AddBusinessChance_nextContactContent');
            },

            phone_Blur() {
                if (this.mobile && this.mobile.length != 11) {
                    this.phoneMsg = '请输入11位手机号码';
                    return false;
                } else {
                    if (!(this.mobile || this.telephone || this.otherContactInfo)) {
                        this.phoneMsg = '手机、固话和其他联系方式至少填写一项'
                    } else {
                        this.phoneMsg = '';
                    }
                }
            },
            telephone_Blur() {
                if (this.mobile || this.telephone || this.otherContactInfo) {
                    if (this.phoneMsg == '手机、固话和其他联系方式至少填写一项') {
                        this.phoneMsg = '';
                    }
                }
            },
            // 验证表单
            checkForm() {
                let error = 0;
                if (!this.$form.validForm('AddBusinessChance')) {
                    error++;
                }
                if (!(this.mobile || this.telephone || this.otherContactInfo)) {
                    this.phoneMsg = '手机、固话和其他联系方式至少填写一项';
                    error++;
                }

                if (this.mobile && this.mobile.length != 11) {
                    this.phoneMsg = '请输入11位手机号码';
                    error++;
                }

                if (error) {
                    return false;
                }

                this.phoneMsg = '';
                return true;
            },

            // 提交
            submit() {
                if (!this.checkForm()) return;

                let reqData = {
                    clueType: this.clueType,

                    traderName: this.traderName, // 客户名称
                    traderId: this.traderId,     // 客户ID
                    tycFlag: this.tycFlag,
                    phone: this.mobile,       // 手机号
                    telephone: this.telephone, // 固话
                    otherContactInfo: this.otherContactInfo,
                    contact: this.traderContactName,  // 联系人
                    traderContactId: this.traderContactId,      // 联系人id
                    terminalTraderName: this.terminalTraderName,      // 终端名称
                    terminalTraderNature: this.terminalTraderNature,  // 终端性质
                    terminalTraderRegion: this.area.join(','), // 终端区域
                    terminalTraderRegionStr: this.areaNames.join(','), // 终端区域
                    // card2
                    businessType: this.businessType,  // 商机类型
                    amount: this.amount,              // 预计成单金额
                    orderTime: this.orderTimeDate, // 预计成单日期
                    goodsInfo: this.productCommentsSale, // 产品需求
                    tagIds: this.tagIds.join(','), // 商机标签
                    customerRelationship: this.customerRelationship.join(','), // 客情关系
                    purchasingType: this.purchasingType, // 采购方式
                    biddingPhase: this.biddingPhase, // 招投标阶段
                    biddingParameter: this.biddingParameter, // 招标参数
                    remark: this.comments, // 备注
                    provinceId: this.leadsArea[0] || '',
                    cityId: this.leadsArea[1] || '',
                    countyId: this.leadsArea[2] || '',
                    province: this.leadsAreaNames[0] || '',
                    city: this.leadsAreaNames[1] || '',
                    county: this.leadsAreaNames[2] || '',
                    inquiry: this.inquiry || '',
                    source: this.source || '',
                    communication: this.communication || '',
                    // content: this.CategoryContent || '',
                    belongerId: this.belongerId,
                    belonger: this.belonger,
                    entrances: this.entrances,
                    functions: this.functions,
                    visitId: this.visitId,
                    keywords: this.aiValue.keywords.join(','),
                    categoryIds: this.aiValue.categoryIds.join(','),
                }

                // 非客户带入
                if (!this.isFromTyc) {
                    // 终端信息
                    if (this.orderTerminalDto) {
                        reqData['orderTerminalDto'] = this.orderTerminalDto;
                    }
                }
                // 线索转商机 带线索id
                // if (this.businessLeadsId) {
                //     reqData['businessLeadsId'] = this.businessLeadsId;
                // }

                if (!this.isEdit) {
                    let communicateRecordDto = {
                        nextContactDate: this.nextContactDate,
                        nextContactContent: this.nextContactContent.trim(),
                    }
                    reqData['communicateRecordDto'] = communicateRecordDto
                }

                if (this.canAjax) {
                    this.canAjax = false;

                    if (this.isEdit) {
                        this.axiosUpdate(reqData);
                    } else {
                        this.axiosCreated(reqData);
                    }
                }
            },

            // 编辑
            async axiosUpdate(reqData) {
                reqData['preSalesInfoType'] = {
                    dataId: this.businessId,
                    preSaleDataEnum: this.preSaleDataType == 1 ? 'BUSSINESS_LEADS' : 'BUSSINESS_CHANCE'
                };
                const { data } = await this.$axios.post(`/crm/presales/profile/update`, reqData);
                if (data.success) {
                    this.$message.success("保存成功");
                    setTimeout(() => {
                        if (data.data.preSaleDataEnum === 'BUSSINESS_CHANCE') {
                            window.location.href = `/crm/businessChance/profile/detail?id=${data.data.dataId}`;
                        } else {
                            window.location.href = `/crm/businessLeads/profile/detail?id=${data.data.dataId}`;
                        }
                    }, 2000)
                } else {
                    this.$message.warn(data.message || '网络异常');
                    this.canAjax = true;
                }
            },
            // 新建
            async axiosCreated(reqData) {
                const { data } = await this.$axios.post(`/crm/presales/profile/add`, reqData);
                if (data.success) {
                    this.$message.success("创建成功");
                    setTimeout(() => {
                        if (data.data.preSaleDataEnum === 'BUSSINESS_CHANCE') {
                            window.location.href = `/crm/businessChance/profile/detail?id=${data.data.dataId}`;
                        } else {
                            window.location.href = `/crm/businessLeads/profile/detail?id=${data.data.dataId}`;
                        }
                    }, 2000)
                } else {
                    this.$message.warn(data.message || '网络异常');
                    this.canAjax = true;
                }
            },
            checkBusinessInput() {
                if (this.businessType && this.productCommentsSale && this.amount && this.orderTimeDate) {
                    this.isBusinessAllInput = true;
                } else {
                    this.isBusinessAllInput = false;
                }

                this.$refs.aicategory && this.$refs.aicategory.getAiInfo(this.productCommentsSale);
            },
            // 渠道类型change
            changeSource(val) {
                console.log('changeSource:', val);
                this.SourceNames = val.children || [];
                this.communication = '';
            },
            //线索地区修改
            handleLeadsArea(val) {
                let area = [];
                let areaNames = [];

                val.forEach(item => {
                    if (item.value) {
                        area.push(item.value);
                        areaNames.push(item.label);
                    }
                })

                this.leadsArea = area;
                this.leadsAreaNames = areaNames;
            },
            handlerAiCategoryChange(data) {
                console.log(data)

                this.aiValue = data;
            }
        }
    })
}.call(this);