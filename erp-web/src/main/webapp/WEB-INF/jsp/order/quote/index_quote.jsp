<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="报价列表" scope="application" />
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript" src='<%=basePath%>static/js/order/quote/index_quote.js?rnd=${resourceVersionKey}'></script>
<script src="/webjars/ezadmin/plugins/layui/layui.js"></script>

<link href="/webjars/ezadmin/plugins/layui/css/layui.css?v=2.7.6" rel="stylesheet">

<link href="/webjars/ezadmin/plugins/viewer/viewer.min.css" rel="stylesheet">
<%--
<link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
--%>
<link href="/webjars/ezadmin/layui/css/ezlist.css?v=1711100573488" rel="stylesheet">
<link href="/webjars/ezadmin/plugins/cascader/cascader.css" rel="stylesheet">
<script src="/webjars/ezadmin/plugins/cascader/cascader.js?1=1" type="text/javascript" ></script>
<script src="/webjars/ezadmin/plugins/pingyin/pinyin_dict_firstletter.js" ></script>
<script src="/webjars/ezadmin/plugins/pingyin/pinyin_dict_notone.js" ></script>
<script src="/webjars/ezadmin/plugins/pingyin/pinyinUtil.js" ></script>

<style>
	/*.el-cascader-node{
		height: 30px !important;
	}*/
	.el-input__inner{height:30px ;line-height:30px;padding-left: 5px;}
	.el-cascader{
		width: 180px;
	}
	/*.el-input input{
		height: 30px !important;
	}*/
	li i{
		height: auto;
		background: none;
	}
	a, a:hover, a:focus {
		text-decoration: none;
		outline-style: none;
		color: #3384ef;
		cursor: pointer;
	}

</style>
	<div class="main-container">
		<div class="list-pages-search">
		<form method="post" id="search" action="<%=basePath%>order/quote/index.do">
			<ul style="display: flex;    flex-wrap: wrap;">
				<li>
					<label class="infor_name">报价单号</label> 
					<input type="text" class="input-middle" name="quoteorderNo" id="quoteorderNo" value="${quote.quoteorderNo}" />
				</li>
				<li>
					<label class="infor_name">生效状态</label> 
					<select class="input-middle" name="validStatus" id="validStatus">
						<option value="">全部</option>
						<option <c:if test="${quote.validStatus eq 0}">selected</c:if> value="0">未生效</option>
						<option <c:if test="${quote.validStatus eq 1}">selected</c:if> value="1">已生效</option>
					</select>
				</li>
				<li>
					<label class="infor_name">审核状态</label>
					<select class="input-middle" name="verifyStatus">
						<option value="">全部</option>
						<option <c:if test="${quote.verifyStatus eq 3}">selected</c:if> value="3">待审核</option>
						<option <c:if test="${quote.verifyStatus eq 0}">selected</c:if> value="0">审核中</option>
						<option <c:if test="${quote.verifyStatus eq 1}">selected</c:if> value="1">审核通过</option>
						<option <c:if test="${quote.verifyStatus eq 2}">selected</c:if> value="2">审核不通过</option>
					</select>
				</li>
				<li>
					<label class="infor_name">跟单状态</label> 
					<select class="input-middle" name="followOrderStatus" id="followOrderStatus">
						<option value="-1">全部</option>
						<option	<c:if test="${quote.followOrderStatus eq 0}">selected</c:if> value="0">跟单中</option>
						<option <c:if test="${quote.followOrderStatus eq 1}">selected</c:if> value="1">已成单</option>
						<option	<c:if test="${quote.followOrderStatus eq 2}">selected</c:if> value="2">已失单</option>
					</select>
				</li>
				<li>
					<label class="infor_name">客户名称</label> 
					<input type="text" class="input-middle" name="traderName" id="traderName" value="${quote.traderName}" />
				</li>
				<li>
					<label class="infor_name">客户性质</label> 
					<select class="input-middle" name="customerNature" id="customerNature">
						<option value="">全部</option>
						<c:forEach items="${customerNatureList}" var="cnl">
							<option value="${cnl.sysOptionDefinitionId}"
								<c:if test="${quote.customerNature eq cnl.sysOptionDefinitionId}">selected="selected"</c:if>>${cnl.title}</option>
						</c:forEach>
					</select>
				</li>

				<li>
				<div style="display: flex;  flex-wrap: nowrap;">
					<label class="infor_name">销售部门</label>
					<div class="layui-input-inline">
						<input value="${quote.orgId }" type="hidden" name="orgId" id="orgId" class="hidden-reset ez-laycascader" ez_url="/system/org/orgList.do" ez_value="orgId" ez_label="orgName" ez_children="child" autocomplete="off">
					</div>
				</div>
				</li>
				<%-- <li>
					<label class="infor_name">销售人员</label>
					<select class="input-middle" name="userId" id="userId">
						<option value="">全部</option>
						<c:forEach var="list" items="${quoteUserList}" varStatus="status">
							<option value="${list.userId}" <c:if test="${quote.userId eq list.userId}">selected="selected"</c:if>>${list.username}</option>
						</c:forEach>
					</select>
				</li> --%>
				<li>
					<label class="infor_name">订货号</label> 
					<input type="text" class="input-middle" name="sku" id="sku" value="${quote.sku}" />
				</li>
				<li>
					<label class="infor_name">产品名称</label> 
					<input type="text" class="input-middle" name="goodsName" id="goodsName" value="${quote.goodsName}" />
				</li>
				<li>
					<label class="infor_name">品牌</label> 
					<input type="text" class="input-middle" name="brandName" id="brandName" value="${quote.brandName}" />
				</li>
				<li>
					<label class="infor_name">型号</label> 
					<input type="text" class="input-middle" name="model" id="model" value="${quote.model}" />
				</li>
				<li>
					<label class="infor_name">联系人信息</label> 
					<input type="text" class="input-middle" name="traderContact" id="traderContact" placeholder="联系人姓名/电话/手机" value="${quote.traderContact}" />
				</li>
				<li>
					<label class="infor_name">有沟通</label> 
					<select class="input-middle" name="haveCommunicate" id="haveCommunicate">
						<option value="">全部</option>
						<option <c:if test="${quote.haveCommunicate eq 0}">selected</c:if> value="0">无</option>
						<option <c:if test="${quote.haveCommunicate eq 1}">selected</c:if> value="1">有</option>
					</select>
				</li>
				<li>
					<label class="infor_name">咨询答复</label> 
					<select class="input-middle" name="consultStatus" id="consultStatus">
						<option value="">全部</option>
						<option <c:if test="${quote.consultStatus eq 1}">selected</c:if> value="1">待供应链答复</option>
						<option <c:if test="${quote.consultStatus eq 2}">selected</c:if> value="2">待主管答复</option>
						<option <c:if test="${quote.consultStatus eq 3}">selected</c:if> value="3">已处理</option>
					</select>
				</li>
				<li>
					<label class="infor_name">报价来源</label>
					<select class="input-middle" name="sourceQuae" id="sourceQuae">
						<option value="">全部</option>
						<option value="" <c:if test="${quote.sourceQuae ne 'VD'}">selected</c:if>>订单</option>
						<option value="VD" <c:if test="${quote.sourceQuae eq 'VD'}">selected</c:if>>商机</option>

					</select>
				</li>
				<li>
					<label class="infor_name">归属销售</label> 
					<select class="input-middle" name="optUserId" id="optUserId">
						<option value="">全部</option>
						<c:forEach items="${userList}" var="list">
							<option value="${list.userId}"
								<c:if test="${quote.optUserId eq list.userId}">selected="selected"</c:if>>${list.username}</option>
						</c:forEach>
					</select>
				</li>
				<li>
					<label class="infor_name">商机来源</label> 
					<select class="input-middle" name="source" id="source">
						<option value="">全部</option>
						<c:forEach items="${bussSource}" var="buss">
							<option value="${buss.sysOptionDefinitionId}"
								<c:if test="${quote.source eq buss.sysOptionDefinitionId}">selected="selected"</c:if>>${buss.title}</option>
						</c:forEach>
					</select>
				</li>

				<li>
					<div class="infor_name specialinfor">
						<select name="timeType" id="timeType">
							<option value="3" <c:if test="${quote.timeType eq 3}">selected</c:if>>创建时间</option>
							<option value="1" <c:if test="${quote.timeType eq 1}">selected</c:if>>生效时间</option>
							<option value="2" <c:if test="${quote.timeType eq 2}">selected</c:if>>沟通时间</option>
							<option value="4" <c:if test="${quote.timeType eq 4}">selected</c:if>>成单时间</option>
							<option value="5" <c:if test="${quote.timeType eq 5}">selected</c:if>>失单时间</option>
						</select>
					</div> 
					<input class="Wdate f_left input-smaller96 m0" type="text" placeholder="请选择日期" autocomplete="off" onClick="WdatePicker()" name="beginTime" value="${beginTime}">
					<div class="f_left ml1 mr1 mt4">-</div> 
					<input class="Wdate f_left input-smaller96" type="text" placeholder="请选择日期" autocomplete="off" onClick="WdatePicker()" name="endTime" value="${endTime}">
				</li>
				<li>
					<label class="infor_name">丢单原因</label>
					<select class="input-middle" name="closeReasonId" id="closeReasonId">
						<option value="">全部</option>
						<c:forEach items="${firstCloseReason}" var="buss">
							<option  value="${buss.sysOptionDefinitionId}" style="color: #999;font-weight: bold;"   <c:if test="${quote.closeReasonId eq buss.sysOptionDefinitionId}">selected="selected"</c:if>>${buss.title}</option>
							<c:if test="${buss.sysOptionDefinitionId eq 1601}">
								<c:forEach items="${saleCloseReason}" var="item">
									<option value="${item.sysOptionDefinitionId}" <c:if test="${quote.closeReasonId eq item.sysOptionDefinitionId}">selected="selected"</c:if>>${item.title}</option>
								</c:forEach>
							</c:if>
							<c:if test="${buss.sysOptionDefinitionId eq 1602}">
								<c:forEach items="${traderCloseReason}" var="item">
									<option value="${item.sysOptionDefinitionId}" <c:if test="${quote.closeReasonId eq item.sysOptionDefinitionId}">selected="selected"</c:if>>${item.title}</option>
								</c:forEach>
							</c:if>
							<c:if test="${buss.sysOptionDefinitionId eq 1603}">
								<c:forEach items="${goodsCloseReason}" var="item">
									<option value="${item.sysOptionDefinitionId}" <c:if test="${quote.closeReasonId eq item.sysOptionDefinitionId}">selected="selected"</c:if>>${item.title}</option>
								</c:forEach>
							</c:if>
						</c:forEach>
					</select>
				</li>

				<li>
					<label class="infor_name">是否含授权申请</label>
					<select class="input-middle" name="authorizationApply" id="authorizationApply">
						<option value="">全部</option>
						<option value="1" <c:if test="${quote.authorizationApply == 1}">selected</c:if>>是</option>
						<option value="2" <c:if test="${quote.authorizationApply == 2}">selected</c:if>>否</option>

					</select>
				</li>
				<li>
					<label class="infor_name">跟进预警</label>
					<select class="input-middle" name="salesmanAlarmLevel">
						<option value="" selected>请选择</option>
						<option value="1" <c:if test="${quote.salesmanAlarmLevel eq 1}">selected</c:if>>第一级预警</option>
						<option value="2" <c:if test="${quote.salesmanAlarmLevel eq 2}">selected</c:if>>第二级预警</option>
					</select>
				</li>
				<li>
					<label class="infor_name">是否科研特麦帮</label>
					<select class="input-middle" name="tmh">
						<option value="">全部</option>
						<option value="true" <c:if test="${quote.tmh == true}">selected</c:if>>是</option>
						<option value="false" <c:if test="${quote.tmh == false}">selected</c:if>>否</option>
					</select>
				</li>
				<%--<li>
					<div style="display: flex;  flex-wrap: nowrap;">
						<label class="infor_name">销售部门</label>
						<div class="layui-input-inline">
							<input value="${quote.orgId }" type="hidden" name="orgId" id="orgId" class="ez-laycascader" ez_url="/system/org/orgList.do" ez_value="orgId" ez_label="orgName" ez_children="child" autocomplete="off">
						</div>
					</div>
					&lt;%&ndash;<label class="infor_name">销售部门</label>
					<select class="input-middle" name="orgId" id="orgId">
						<!-- onchange="getDeptUser(this);" -->
						<option value="">全部</option>
						<c:forEach items="${orgList}" var="org">
							<option value="${org.orgId}"
								<c:if test="${quote.orgId eq org.orgId}">selected="selected"</c:if>>${org.orgName}</option>
						</c:forEach>
					</select>&ndash;%&gt;
				</li>--%>
			</ul>
			<div class="tcenter">
				<span class="confSearch bt-small bt-bg-style bg-light-blue"	onclick="search();" id="searchSpan">搜索</span> 
				<span class="bt-small bg-light-blue bt-bg-style mr20" onclick="reset();">重置</span>
				<!-- <span class="bt-small bg-light-blue bt-bg-style" onclick="exportQuote()">导出列表</span>  -->
				<!-- <span class="bt-small bg-light-blue bt-bg-style" onclick="exportQuoteDetail()">导出报价明细</span>  -->
				<!-- <span class="bt-small bg-light-blue bt-bg-style addtitle"
					tabtitle="{&quot;num&quot;:&quot;user&quot;,&quot;link&quot;:&quot;./order/quote/addQuote.do?traderId=586&bussinessChanceId=100&quot;,&quot;title&quot;:&quot;新增报价&quot;}">测试新增报价</span> -->
			</div>
		</form>
	</div>
	<div class="list-page">
		<div class="fixdiv">
			<div class="superdiv">
				<table
					class="table table-bordered table-striped table-condensed table-centered">
					<thead>
						<tr>
							<th class="wid6">序号</th>
							<th class="wid12">报价单号</th>
							<th class="wid10">报价来源</th>
							<th class="wid10">是否科研特麦帮</th>
							<th class="wid14">创建时间</th>
							<th class="wid20">客户名称</th>
							<th class="wid8">客户性质</th>
							<th class="wid8">新/老客户</th>
							<th class="wid8">客户等级</th>
							<th class="table-smallest6">报价金额</th>
							<th class="table-smallest10">销售部门</th>
							<th class="table-smallest5">销售人员</th>
							<th class="table-smallest5">归属销售</th>
							<th class="wid10">沟通次数</th>
							<th class="wid10">生效状态</th>
							<th class="wid10">审核状态</th>
							<th class="wid10">跟单状态</th>
							<th class="wid10">跟进预警</th>
							<th class="wid10">咨询答复</th>
							<th class="wid10">丢单原因</th>
						</tr>
					</thead>
					<tbody>
						<c:forEach var="list" items="${quoteList}" varStatus="num">
							<tr>
								<td>${num.count}</td>
								<td class="text-left">
									<div class="font-blue">
										<c:choose>
											<c:when test="${list.followOrderStatus eq 0}">
												<span class="greencircle"></span>
											</c:when>
											<c:when test="${list.followOrderStatus eq 1}">
												<span class="bluecircle"></span>
											</c:when>
											<c:when test="${list.followOrderStatus eq 2}">
												<span class="greycircle"></span>
											</c:when>
											<c:otherwise>
												<span class="orangecircle"></span>
											</c:otherwise>
										</c:choose>
										<c:choose>
											<c:when test="${list.validStatus eq 0}">

												<c:set var="shenhe" value="0"></c:set>
												<c:if test="${list.verifyStatus!=null && null!=list.verifyUsernameList}">
													<c:forEach items="${list.verifyUsernameList}" var="verifyUsernameInfo">
														<c:if test="${verifyUsernameInfo == loginUser.username}">
															<c:set var="shenhe" value="1"></c:set>
														</c:if>
													</c:forEach>
												</c:if>
												<a class="addtitle"
												   tabtitle="{&quot;num&quot;:&quot;viewQuote2<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>&quot;,&quot;link&quot;:&quot;./order/quote/getQuoteDetail.do?quoteorderId=${list.quoteorderId}&viewType=2&quot;,&quot;title&quot;:&quot;编辑报价&quot;}">${list.quoteorderNo}</a>${list.verifyStatus != 1 && shenhe == 1 ?"<font color='red'>[审]</font>":""}
											</c:when>
											<c:otherwise>
												<a class="addtitle"
													tabtitle="{&quot;num&quot;:&quot;viewQuote3<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>&quot;,&quot;link&quot;:&quot;./order/quote/getQuoteDetail.do?quoteorderId=${list.quoteorderId}&viewType=3&quot;,&quot;title&quot;:&quot;报价信息&quot;}">${list.quoteorderNo}</a>
											</c:otherwise>
										</c:choose>
									</div>
								</td>
								<td><c:if test="${fn:substring(list.quoteorderNo,0,2) == 'VD'}">商机</c:if>
									<c:if test="${fn:substring(list.quoteorderNo,0,2) == 'BD'}">订单</c:if>
								</td>
								<td>
									<c:choose>
										<c:when test="${list.tmh == true}">是</c:when>
										<c:otherwise>否</c:otherwise>
									</c:choose>
								</td>
								<td><date:date value="${list.addTime}" /></td>
								<td>
									<div class="font-blue">
										<a class="addtitle" href="javascript:void(0);"
											tabtitle='{"num":"view_quote_customer${list.traderId}",
											"link":"./trader/customer/baseinfo.do?traderId=${list.traderId}","title":"客户信息"}'>
											<c:if test="${not empty traderGroupMap && not empty traderGroupMap[list.traderId]}">
												<span style="color: red">【${traderGroupMap[list.traderId].traderGroupName}】</span>
											</c:if>
											${list.traderName}

										</a>
									</div>
								</td>
								<td>${list.customerNatureStr}</td>
								<td><c:choose>
										<c:when test="${list.isNewCustomer eq 0}">
											否
										</c:when>
										<c:when test="${list.isNewCustomer eq 1}">
											是
										</c:when>
										<c:otherwise>
											--
										</c:otherwise>
									</c:choose></td>
								<td>${list.customerLevel}</td>
								<td>${list.totalAmount}</td>
								<td>${list.salesDeptName}</td>
								<td>${list.salesName}</td>
								<td>${list.optUserName}</td>
								<td>${list.communicateNum}</td>
								<td>
									<c:choose>
										<c:when test="${list.validStatus eq 0}">
											<span style="color: red">未生效</span>
										</c:when>
										<c:when test="${list.validStatus eq 1}">
											已生效
										</c:when>
										<c:otherwise>
											--
										</c:otherwise>
									</c:choose>
								</td>
								<td>
									<c:choose>
										<c:when test="${list.verifyStatus eq 0}">
											审核中
										</c:when>
										<c:when test="${list.verifyStatus eq 1}">
											审核通过
										</c:when>
										<c:when test="${list.verifyStatus eq 2}">
											审核不通过
										</c:when>
										<c:otherwise>
											待审核
										</c:otherwise>
									</c:choose>
								</td>
								<td>
									<c:choose>
										<c:when test="${list.followOrderStatus eq 0}">
											<span style="color: red">跟单中</span>
										</c:when>
										<c:when test="${list.followOrderStatus eq 1}">
											已成单
										</c:when>
										<c:when test="${list.followOrderStatus eq 2}">
											已失单
										</c:when>
										<c:otherwise>
											--
										</c:otherwise>
									</c:choose>
								</td>
									<%--跟进预警--%>
								<td>
									<c:choose>
										<c:when test="${list.quotedAlarmMode eq 1 and list.salesmanAlarmLevel eq 1}">
											第一级预警
										</c:when>
										<c:when test="${list.quotedAlarmMode eq 1 and list.salesmanAlarmLevel eq 2}">
											第二级预警
										</c:when>
										<c:otherwise>
											--
										</c:otherwise>
									</c:choose>
								</td>
								<td>
									<c:if test="${list.consultStatus eq 1 || list.consultStatus eq 2 || list.consultStatus eq 4 || list.consultStatus eq 5}">
										<span style="color: red">待供应链处理</span>
									</c:if>
									<c:if test="${list.consultStatus eq 1 || list.consultStatus eq 2}">
										，
									</c:if>
									<c:if test="${list.consultStatus eq 1 || list.consultStatus eq 2 || list.consultStatus eq 3}">
										<span style="color: red">待主管处理</span>
									</c:if>
									<c:if test="${list.consultStatus eq 6}">
										已处理
									</c:if>
								</td>
								<td>
									<c:choose>
										<c:when test="${list.followOrderStatus ==2 && list.closeReasonComment != null}">
												${list.closeReasonComment}
										</c:when>
										<c:otherwise>
											-
										</c:otherwise>
									</c:choose>
								</td>
							</tr>
						</c:forEach>
						<c:if test="${empty quoteList}">
							<tr>
								<td colspan="16">
									<!-- 查询无结果弹出 --> 查询无结果！请尝试使用其他搜索条件。
								</td>
							</tr>
						</c:if>
					</tbody>
				</table>
			</div>
		</div>
		<tags:page page="${page}" />
	</div>
	</div>
<script  type="text/javascript" >
	$(document).ready(function() {
		$(".ez-laycascader").each(function(){
			var _this=$(this);
			renderCascader(_this);
		})
	})
	function istrue(c){
		return  (c||'true')=='true'||(c||'1')=='1';
	}
	function createParentChildStructure(data) {
		// 解析 JSON 字符串
		// const data = JSON.parse(jsonData);

		// 创建一个字典来保存所有节点，按照它们的 K 值索引
		const dataMap = data.reduce((map, node) => {
			map[node.orgId] = { ...node, child: [] };
		return map;
	}, {});
		// 最终的父节点数组
		const result = [];

		// 遍历所有节点，构建父子关系
		data.forEach(node => {
			// 如果 level 值为 "3"，则是顶层节点，添加到结果数组中
			if (node.level === "3" || node.level == 3) {
			result.push(dataMap[node.orgId]);
		} else if (node.parentId in dataMap) {
			// 如果 P 值在 dataMap 中，则找到其父节点，并将当前节点添加为子节点
			dataMap[node.parentId].child.push(dataMap[node.orgId]);
		}
	});

		return result;
	}
	function renderCascader(cas){
		layui.use('layCascader', function () {
			try {
				var layCascader = layui.layCascader;
				var _this = $(cas);
				var url = _this.attr("ez_url");
				var ezurl = _this.attr("ezlist_url");
				var value = _this.attr("ez_value") || 'VALUE';
				var label = _this.attr("ez_label") || 'LABEL';
				var children = _this.attr("ez_children") || 'CHILDREN';
				var multiple = istrue(_this.attr("multi"));
				var itemsJson = _this.attr("itemsJson");
				// debugger
				var itemPlaceholder = _this.attr("placeholder") || '请选择';
				var paramValue = [] ;
				if(_this.val().length >0){
					paramValue=parseInt(_this.val());
				}else{

				}
				//("["+_this.val()+"]"):'[]';
				var collapseTags = istrue(_this.attr("collapsetags"));
				var showAllLevels = istrue(_this.attr("showalllevels"));

				$.get(url || ezurl, function (data) {
					var jsonData = JSON.parse(data);
					// var res = data.data;
					//console.log(jsonData.data);
					var res  = createParentChildStructure(jsonData.data);
					var prop = {};
					prop.value = value;
					prop.label = label;
					prop.children = children;
					prop.multiple = false;
					prop.checkStrictly = true;

					//console.log(prop);
					if (ezurl) {
						res = flatToTree(res, 0);
					}
					var org = layCascader({
						name:"orgId",
						elem: _this[0],
						props: prop,
						filterable: true,
						filterMethod: function (node, val) {//重写搜索方法。
							if (val == node.data[label]) {//把value相同的搜索出来

								return true;
							}
							if ((node.data[label] + node.data[label]).indexOf(val) != -1) {//名称中包含的搜索出来
								return true;
							}
							//  console.log(node.data.orgName+node.data.orgNames+'##'+(node.data.orgId+'').indexOf(val));
							return !ezpingyin(val, (node.data[label] + node.data[label]), (node.data[value] + ''));
						},
						clearable: true,
						placeholder: itemPlaceholder,
						collapseTags: collapseTags,
						showAllLevels: showAllLevels,
						value: paramValue,
						options: res,
						showLastLevels: false, // 显示完整路径
						multiple: false // 单选模式
					});
					org.change(function (value, node) {
						//layer.msg('value:' + value + ',Node:' + JSON.stringify(node.data));
						$("#orgId").val(value);
						//initPosit();
					});
				})

			}catch (e) {
				console.log(e);
			}

		})

	}

</script>
<%@ include file="../../common/footer.jsp"%>
