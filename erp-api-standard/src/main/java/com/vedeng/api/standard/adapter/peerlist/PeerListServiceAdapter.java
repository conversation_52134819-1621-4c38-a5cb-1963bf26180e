package com.vedeng.api.standard.adapter.peerlist;

import cn.hutool.core.bean.BeanUtil;
import com.vedeng.api.standard.adapter.peerlist.dto.*;
import com.vedeng.api.standard.core.AbstractServiceAdapter;
import com.vedeng.api.standard.core.ApiRequest;
import com.vedeng.api.standard.core.exception.ApiStandardException;
import com.vedeng.api.standard.template.BusinessTemplate;
import com.vedeng.api.standard.template.ParameterConfig;
import com.vedeng.api.standard.validation.ValidationContextKeys;
import com.vedeng.api.standard.validation.rules.AddPeerListEnableRule;
import com.vedeng.erp.buyorder.domain.vo.PurchaseDeliveryDirectBatchInfoVo;
import com.vedeng.erp.buyorder.dto.BuyOrderApiDto;
import com.vedeng.erp.buyorder.service.BuyorderApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 直发同行单服务适配器
 * 实现直发同行单相关的标准化API接口
 *
 * <AUTHOR>
 */
@Component("peerListServiceAdapter")
public class PeerListServiceAdapter extends AbstractServiceAdapter {

    @Autowired
    private BusinessTemplate businessTemplate;

    @Override
    public void preProcess(String action, ApiRequest apiRequest) {
        super.preProcess(action, apiRequest);
    }

    @Override
    public Object postProcess(String action, ApiRequest request, Object result) {
        return super.postProcess(action, request, result);
    }

    @Override
    public String getModuleName() {
        return "peerlist";
    }

    /**
     * 注册操作处理器
     */
    @Override
    protected void registerOperationHandlers() {
        // 注册支持的操作处理器
        registerThrowingHandler("create", this::executeCreateOperation);
        registerThrowingHandler("query", this::executeQueryOperation);
        registerThrowingHandler("queryStockRecords", this::executeQueryStockRecordsOperation);
        
        logger.info("直发同行单服务适配器注册完成，支持{}个操作", getHandlerCount());
    }

    /**
     * 获取不需要身份认证的操作列表
     *
     * 采购单模块中，查询操作通常不需要身份认证，
     * 允许外部系统或匿名用户查询采购单信息
     *
     * @return 不需要认证的操作名称数组
     */
    @Override
    public String[] getNoAuthActions() {
        return new String[]{"query","queryStockRecords"};
    }
    
    /**
     * 执行创建操作
     */
    private Object executeCreateOperation(ApiRequest request) {
        try {
            // 直接创建并转换请求对象
            PeerListCreateRequest createRequest = new PeerListCreateRequest();
            BeanUtil.fillBeanWithMap(request.getData(), createRequest, true);
            createRequest.validate();
            
            // 调用业务模板执行创建操作
            return businessTemplate.<PeerListCreateRequest, PeerListResponse>executeCreate(request)
                    .requestType(PeerListCreateRequest.class)
                    .responseType(PeerListResponse.class)
                    .validationRules(AddPeerListEnableRule.class)
                    .controller("newBuyorderPeerListController", "saveDetails")
                    .withHttpParameters(
                            ParameterConfig.fromValidationContext(ValidationContextKeys.PURCHASE_DELIVERY_DIRECT_BATCH_INFO_VO, PurchaseDeliveryDirectBatchInfoVo.class)
                    )
                    .execute();
            
        } catch (Exception e) {
            logger.error("执行直发同行单创建操作失败: requestId={}", request.getRequestId(), e);
            throw ApiStandardException.serviceExecutionError("直发同行单创建失败: " + e.getMessage(), e);
        }
    }
    
    
    /**
     * 执行查询同行单操作
     */
    private Object executeQueryOperation(ApiRequest request) {
        try {
            // 转换请求对象
            PeerListQueryRequest queryRequest = new PeerListQueryRequest();
            BeanUtil.fillBeanWithMap(request.getData(), queryRequest, true);
            queryRequest.validate();
            
            logger.info("查询直发同行单参数转换成功，准备调用Controller: buyorderId={}", queryRequest.getBuyorderId());
            
            // 调用业务模板执行查询操作
            return businessTemplate.<PeerListQueryRequest, PeerListQueryResponse>executeQuery(request)
                    .requestType(PeerListQueryRequest.class)
                    .responseType(PeerListQueryResponse.class)
                    .controller("newBuyorderPeerListServiceImpl", "queryPeerList")
                    .withoutHttpParameters(
                            ParameterConfig.of(Integer.class, queryRequest.getBuyorderId())
                    )
                    .execute();
            
        } catch (Exception e) {
            logger.error("执行直发同行单查询操作失败: requestId={}", request.getRequestId(), e);
            throw ApiStandardException.serviceExecutionError("直发同行单查询失败: " + e.getMessage(), e);
        }
    }
    
    @Autowired
    private BuyorderApiService buyorderApiService;
    
    /**
     * 执行查询采购单相关出入库记录并组装同行单创建入参的操作
     */
    private Object executeQueryStockRecordsOperation(ApiRequest request) {
        try {
            // 转换请求对象
            PeerListStockQueryRequest queryRequest = new PeerListStockQueryRequest();
            BeanUtil.fillBeanWithMap(request.getData(), queryRequest, true);
            queryRequest.validate();

            BuyOrderApiDto apiDto = buyorderApiService.getBuyorderByBuyorderNo(queryRequest.getBuyOrderNo());
            Integer buyorderId = apiDto.getBuyorderId();
            logger.info("查询采购单出入库记录参数转换成功，准备调用Controller: buyorderId={}", buyorderId);
            
            if (Objects.equals(apiDto.getDeliveryDirect(),0) && Objects.equals(queryRequest.getIsFirst(),1)){
                logger.info("这是第一个节点且为直发，查询queryStockRecords，入参：DeliveryDirect:{},IsFirst:{}",apiDto.getDeliveryDirect(),queryRequest.getIsFirst());
                // 调用业务模板执行查询操作
                return businessTemplate.<PeerListStockQueryRequest, PurchaseDeliveryDirectBatchInfoVo>executeQuery(request)
                        .requestType(PeerListStockQueryRequest.class)
                        .responseType(PurchaseDeliveryDirectBatchInfoVo.class)
                        .controller("newBuyorderPeerListServiceImpl", "queryStockRecords")
                        .withoutHttpParameters(
                                ParameterConfig.of(Integer.class, buyorderId))
                        .execute();
            }else {
                logger.info("不是第一个节点或不是直发，直接查询同行单queryPeerRecords,入参：DeliveryDirect:{},IsFirst:{}",apiDto.getDeliveryDirect(),queryRequest.getIsFirst());
                // 查询当前系统
                return businessTemplate.<PeerListStockQueryRequest, PurchaseDeliveryDirectBatchInfoVo>executeQuery(request)
                        .requestType(PeerListStockQueryRequest.class)
                        .responseType(PurchaseDeliveryDirectBatchInfoVo.class)
                        .controller("newBuyorderPeerListServiceImpl", "queryPeerRecords")
                        .withoutHttpParameters(
                                ParameterConfig.of(Integer.class, buyorderId))
                        .execute();
            }
          
        } catch (Exception e) {
            logger.error("执行查询采购单出入库记录失败: requestId={}", request.getRequestId(), e);
            throw ApiStandardException.serviceExecutionError("查询采购单出入库记录失败: " + e.getMessage(), e);
        }
    }
} 
