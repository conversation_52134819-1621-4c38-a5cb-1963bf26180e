package com.report.dao;

import com.newtask.data.dto.WarehouseOperateDataDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import javax.inject.Named;
import java.util.List;
import java.util.Map;

/**
 *从库读取数据
 */
@Named("saleorderDataSelectMapper")
public interface SaleorderDataSelectMapper {
    List<Integer> getSaleorderLeftAmountPeriodIdsList(@Param("startTime") Long startTime, @Param("endTime") Long endTime);

    List<Map<String, Object>> findLeftAmountPeriodBySaleorderId(@Param("bizIds") List<Integer> bizIds);

    /**
     * 数据查询方法不对，优化逻辑 2022/418
     * @return
     */
    List<Integer> getSaleorderIdsPreTime(@Param("startTime") Long startTime, @Param("endTime") Long endTime);

    List<Map<String, Object>> findAutoCheckBySaleorderId(@Param("bizIds") List<Integer> bizIds);

    List<Integer> SaleorderCommunicateNumIdsList(@Param("startTime") Long startTime, @Param("endTime") Long endTime);

    List<Map<String, Object>> findCommunicateNumBySaleorderId(@Param("bizIds") List<Integer> bizIds);

    List<WarehouseOperateDataDto> getWarehouseGoodsOperateChangedData(@Param("num")int num);

    List<Integer> getBuyStatusChangedGoodsSecondQuery(@Param("warehouseGoodsOperateChangedData")List<Integer> warehouseGoodsOperateChangedData);

}
