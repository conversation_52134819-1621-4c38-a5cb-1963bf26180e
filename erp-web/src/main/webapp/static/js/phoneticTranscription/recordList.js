function del(id) {

    var msg = "确定删除该记录？";
    layer.confirm(msg, {
        btn: ['确定','取消'] //按钮
    }, function(){
        $.ajax({
            type: "POST",
            url: "./delRecord.do",
            data: {'id':id},
            dataType:'json',
            success: function(data){
                if (data.code == 0){
                    parent.layer.close(index);
                    //alert(data.message);
                    self.location.reload();
                }else {
                    layer.alert(data.message)
                }
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });
    })

}

function resetForm() {
    $("form").find("input[type='text']").val('');
    $.each($("form select"),function(i,n){
        $(this).children("option:first").prop("selected",true);
    });
    $("#search").submit();
}