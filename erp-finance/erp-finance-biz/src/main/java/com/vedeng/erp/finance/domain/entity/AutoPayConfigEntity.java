package com.vedeng.erp.finance.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 自动付款配置
 * @date 2024/4/22 9:11
 */
@Getter
@Setter
public class AutoPayConfigEntity extends BaseEntity {


    /**
     * 主键
     */
    private Integer autoPayConfigId;

    /**
     * 启用付款申请自动制单
     */
    private Integer enableAutoPay;

    /**
     * 启用采购合同审核状态E
     */
    private Integer enableContractAudit;

    /**
     * 启用供应商白名单
     */
    private Integer enableSupplierWhitelist;

    /**
     * 供应商白名单
     */
    private String supplierWhitelist;

    /**
     * 启用推送金蝶付款单自动审核
     */
    private Integer enableKingDeeAutoAudit;

    /**
     * 付款限额：时间规则
     */
    private String payApplyTime;

    /**
     * 付款限额
     */
    private String payLimit;

    /**
     * 付款银行：时间规则
     */
    private String payBankTime;

    /**
     * 付款银行
     */
    private String payBank;

}
