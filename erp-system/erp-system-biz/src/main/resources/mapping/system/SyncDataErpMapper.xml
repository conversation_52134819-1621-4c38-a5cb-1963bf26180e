<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.system.mapper.SyncDataErpMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.erp.system.domain.entity.SyncDataErpEntity">
        <id column="ID" property="id" />
        <result column="BUSINESS_NO" property="businessNo" />
        <result column="BUSINESS_TYPE" property="businessType" />
        <result column="TARGET_ERP" property="targetErp" />
        <result column="REQUEST_CONTENT" property="requestContent" />
        <result column="PROCESS_STATUS" property="processStatus" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="CREATE_USER" property="createUser" />
        <result column="UPDATE_USER" property="updateUser" />
        <result column="IS_DELETED" property="isDeleted" />
        <result column="REMARK" property="remark"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID, BUSINESS_NO, BUSINESS_TYPE, TARGET_ERP, REQUEST_CONTENT, PROCESS_STATUS,
        CREATE_TIME, UPDATE_TIME, CREATE_USER, UPDATE_USER, IS_DELETED,REMARK
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from T_SYNC_DATA_ERP
        where ID = #{id}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from T_SYNC_DATA_ERP
        where ID = #{id}
    </delete>

    <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.system.domain.entity.SyncDataErpEntity" useGeneratedKeys="true">
        insert into T_SYNC_DATA_ERP (BUSINESS_NO, BUSINESS_TYPE, TARGET_ERP,
            REQUEST_CONTENT, PROCESS_STATUS, CREATE_TIME, UPDATE_TIME,
            CREATE_USER, UPDATE_USER, IS_DELETED,REMARK)
        values (#{businessNo}, #{businessType}, #{targetErp},
            #{requestContent}, #{processStatus}, #{createTime}, #{updateTime},
            #{createUser}, #{updateUser}, #{isDeleted},#{remark})
    </insert>

    <insert id="insertSelective" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.system.domain.entity.SyncDataErpEntity" useGeneratedKeys="true">
        insert into T_SYNC_DATA_ERP
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="businessNo != null">
                BUSINESS_NO,
            </if>
            <if test="businessType != null">
                BUSINESS_TYPE,
            </if>
            <if test="targetErp != null">
                TARGET_ERP,
            </if>
            <if test="requestContent != null">
                REQUEST_CONTENT,
            </if>
            <if test="processStatus != null">
                PROCESS_STATUS,
            </if>
            <if test="createTime != null">
                CREATE_TIME,
            </if>
            <if test="updateTime != null">
                UPDATE_TIME,
            </if>
            <if test="createUser != null">
                CREATE_USER,
            </if>
            <if test="updateUser != null">
                UPDATE_USER,
            </if>
            <if test="isDeleted != null">
                IS_DELETED,
            </if>
            <if test="remark != null">
                REMARK,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="businessNo != null">
                #{businessNo},
            </if>
            <if test="businessType != null">
                #{businessType},
            </if>
            <if test="targetErp != null">
                #{targetErp},
            </if>
            <if test="requestContent != null">
                #{requestContent},
            </if>
            <if test="processStatus != null">
                #{processStatus},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="createUser != null">
                #{createUser},
            </if>
            <if test="updateUser != null">
                #{updateUser},
            </if>
            <if test="isDeleted != null">
                #{isDeleted},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.system.domain.entity.SyncDataErpEntity">
        update T_SYNC_DATA_ERP
        <set>
            <if test="businessNo != null">
                BUSINESS_NO = #{businessNo},
            </if>
            <if test="businessType != null">
                BUSINESS_TYPE = #{businessType},
            </if>
            <if test="targetErp != null">
                TARGET_ERP = #{targetErp},
            </if>
            <if test="requestContent != null">
                REQUEST_CONTENT = #{requestContent},
            </if>
            <if test="processStatus != null">
                PROCESS_STATUS = #{processStatus},
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime},
            </if>
            <if test="createUser != null">
                CREATE_USER = #{createUser},
            </if>
            <if test="updateUser != null">
                UPDATE_USER = #{updateUser},
            </if>
            <if test="isDeleted != null">
                IS_DELETED = #{isDeleted},
            </if>
            <if test="remark != null">
                REMARK = #{remark},
            </if>
        </set>
        where ID = #{id}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.system.domain.entity.SyncDataErpEntity">
        update T_SYNC_DATA_ERP
        set BUSINESS_NO = #{businessNo},
            BUSINESS_TYPE = #{businessType},
            TARGET_ERP = #{targetErp},
            REQUEST_CONTENT = #{requestContent},
            PROCESS_STATUS = #{processStatus},
            CREATE_TIME = #{createTime},
            UPDATE_TIME = #{updateTime},
            CREATE_USER = #{createUser},
            UPDATE_USER = #{updateUser},
            IS_DELETED = #{isDeleted},
            REMARK = #{remark}
        where ID = #{id}
    </update>

    <select id="selectByProcessStatus" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from T_SYNC_DATA_ERP
        where PROCESS_STATUS = #{processStatus} AND BUSINESS_TYPE = #{businessType,jdbcType=VARCHAR}
        and IS_DELETED = 0
    </select>

    <select id="selectByBusinessNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from T_SYNC_DATA_ERP
        where BUSINESS_NO = #{businessNo}
        and IS_DELETED = 0
    </select>
</mapper> 