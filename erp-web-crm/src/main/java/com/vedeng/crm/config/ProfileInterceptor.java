package com.vedeng.crm.config;

import com.alibaba.fastjson.JSON;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.core.annotation.MenuDesc;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.exception.UserLimitException;
import com.vedeng.common.core.utils.ErpSpringBeanUtil;
import com.vedeng.erp.system.service.UserApiService;
import com.vedeng.infrastructure.feign.uac.UacWxUserInfoApiService;
import com.vedeng.uac.api.dto.PermissionDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.method.HandlerMethod;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.util.*;

import java.lang.reflect.Method;
import java.util.stream.Collectors;


/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/7/20
 */
@Slf4j
public class ProfileInterceptor  implements HandlerInterceptor {

    //private UserApiService userApiService = ApplicationContextProvider.getContext().getBean(UserApiService.class);
    UacWxUserInfoApiService uacWxUserInfoApiService = ApplicationContextProvider.getContext().getBean(UacWxUserInfoApiService.class);//
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
//        return true;
        return checkPerimission(request,response,handler);
    }


    public boolean checkPerimission(HttpServletRequest request, HttpServletResponse response,  Object handler) throws Exception {
        if (handler instanceof HandlerMethod) {
            HandlerMethod handlerMethod = (HandlerMethod) handler;
            Method method = handlerMethod.getMethod();
            // 获取方法上的 @MenuDesc 注解
            MenuDesc menuDesc = method.getAnnotation(MenuDesc.class);
            if (menuDesc == null) {
                return true;
            }
            CurrentUser currentUser = CurrentUser.getCurrentUser();
            Set<String> perimissionList = currentUser.getPermissions();

            log.info("Intercepted request to: " + request.getRequestURI());
            if (currentUser != null) { // || currentUser.getId() <=1) {
                //取url上的注解
                String value = menuDesc.menuValue();
                if (value== null && value.isEmpty()) {
                    return true;
                }
                //将 value以逗号分割
                String[] values = value.split(",");
                if(perimissionList == null || perimissionList.isEmpty()){
                    //抛出自定义权限异常
                    throw new UserLimitException("您暂时还未开通此权限，请联系研发部开通权限。");
                }
                //判断perimissionList是否包含values中的任意一个
                for (String v : values) {
                    if (perimissionList.contains(v)) {
                        return true;
                    }
                }
                try{
                    //如果没检查到权限，则重新从uac再拉取一遍权限校验
                    RestfulResult<List<PermissionDTO>> result =  uacWxUserInfoApiService.getPermissionByAccount(currentUser.getId(),22);
                    if(result!=null && result.getData()!=null && result.getData().size()>0){
                        List<PermissionDTO> permissionDTOList = result.getData();
                        Set<String> permissionNowList =    Optional.ofNullable(permissionDTOList).orElse(Collections.emptyList())
                                .stream()
                                .filter(Objects::nonNull).map(PermissionDTO::getUri)
                                .collect(Collectors.toSet());

                        for (String v : values) {
                            if (permissionNowList.contains(v)) {
                                //如果发现此时,用户的权限刷新了
                                HttpSession session = request.getSession();
                                currentUser.setPermissions(permissionNowList);
                                session.setAttribute(ErpConstant.CURRENT_USER, currentUser);
                                return true;
                            }
                        }

                    }
                }catch (Exception e){
                    log.error("实时刷新权限失败",e);
                }

                //response.sendRedirect("/login");
                //抛出自定义权限异常
                throw new UserLimitException("您暂时还未开通此权限，请联系研发部开通权限。");
            }

            log.info(JSON.toJSONString(currentUser));
            return true; // 返回true表示继续处理请求，返回false表示中断请求处理
        }
        return true;
    }

}
