package com.vedeng.order.model.vo;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import com.vedeng.order.model.BussinessChance;
import com.vedeng.order.model.Quoteorder;
import com.vedeng.system.model.Tag;
import lombok.Data;

@Data
public class BussinessChanceVo extends BussinessChance {

	/**
	 * @Fields serialVersionUID : TODO
	 */
	private static final long serialVersionUID = 1L;

	private String sourceName;// 询价来源

	//咨询入口名称
	private String entranceName;

	private String inquiryName;

	/**
	 * 功能
	 */
	private String functionName;

	private String communicationName;// 询价方式

	private String saleUser;// 销售人员
	
	private String saleDeptName;//归属销售部门

	private Integer quoteorderId;//报价主键

	private String quoteorderNo;// 报价单号
	
	private Integer paymentType;//付款方式

	private Long quoteorderAddTime;// 报价时间

	private BigDecimal quoteorderTotalAmount;// 报价金额
	
	private Integer quoteorderStatus;//报价单状态
	
	private String saleorderId;//销售订单主键

	private String saleorderNo;// 销售订单单号

	private Long saleorderAddTime;// 销售订单时间

	private BigDecimal saleorderTotalAmount;// 销售订单金额

	private Integer saleorderstatus;// 销售订单状态

	private Integer timeType;// 时间类型

	private String starttime;// 开始时间

	private String endtime;// 结束时间

	private Integer province;// 省

	private Integer city;// 市

	private Integer zone;// 区

	private String closedComments;// 关闭原因

	private List<Integer> creatorId;// 创建人集合

	private Long starttimeLong;//

	private Long endtimeLong;//

	private String typeName;// 商机类型

	private String goodsCategoryName;// 商品类型

	private List<Integer> bussinessChanceIds;// 商机ID集合
	
	private List<String> bussinessChanceNos;// 商机编号集合

	private String attachmentName;// 附件名称

	private String attachmentUri;//

	private String attachmentDomain;//
	
	private String closeReason;//关闭原因
	
	private String areas;//所属地区
	
	private String creatorName;//创建人名称
	
	private String salerName;//分配销售人名称
	
	private List<Integer> userIds;//归属人集合
	
	private List<Integer> traderIds;//客户ID集合
	
	private Integer quoteValidStatus;//报价审核状态
	
	private String assignTimeStr;//
	
	private String quoteorderAddTimeStr;//
	
	private String saleorderAddTimeStr;//
	
	private String firstViewTimeStr;//
	
	private String addTimeStr;//
	
	private String respondTime;//
	
	private String lastVerifyUsermae;//最后审核人
	    
	private String verifyUsername;//当前审核人
	    
	private Integer verifyStatus;//审核状态
	
	private List<String> verifyUsernameList;//当前审核人
	
	private Integer currUserId;//当前登录人员的id
	
	private String nextContactDate;//下次沟通时间
	
    private String bussinessLevelStr;//商机等级
    
    private String bussinessStageStr;//商机阶段
    
    private String enquiryTypeStr;//询价类型
    
    private String orderRateStr;//成单几率
    
    private String flag;//是否用感叹号标记（1是0否）
    
    private String cancelReasonStr; //作废原因
    
    private String cdstarttime;//预计成单开始时间
    
    private String cdendtime;//预计成单结束时间
    
    private Long cdstarttimeLong;//预计成单开始时间
    
    private Long cdendtimeLong;//预计成单结束时间
    
    private String isPayment;//是否已成单未结款商机1是0否
    
    private BigDecimal totalAmount;//商机预计金额之和
    
    private Integer isRest;//是否重置筛选项

	//商机分配状态
	private Integer assignStatus;

	/**
	 * 客户分类，1：新客户
	 */
	private Integer traderCategory;

	/**
	 * 客户性质，465分销，466终端
	 */
	private Integer customerNature;

	/**
	 * 下次联系时间开始时间
	 */
	private String nextStartTime;

	/**
	 * 下次联系时间截止时间
	 */
	private String nextEndTime;

	/**
	 * 预计成单时间
	 */
	private Long orderTime;

	/**
	 * 下次沟通内容
	 */
	private String nextContactContent;
	/**
	 * '商机精准度 0无法判断 1不精准 2一般精准 3高精准'
	 */
	private Integer businessChanceAccuracy;

	/**
	 * '商机精准度 0无法判断 1不精准 2一般精准 3高精准 显示'
	 */
	private String businessChanceAccuracyShow;

	private String contactContent;

	private String orgName;//部门名称

	private List<MergeChanceGoods> oldChanceList;

	private Quoteorder quoteorder;

	private Integer bussinessChance;//商机次数

	private List<Tag> tags;

	/**
	 * 搜索类型，0：按人员查，1：按部门查
	 */
	private Integer searchType;

	/**
	 * 按根据searchType，查询userId或者orgId;
	 */
	private Integer searchId;

	private List<Integer> levelList;

	private Integer jumpType;

	/**
	 * 是否是商机关联查询
	 */
	private Integer isForLink;

	// 新查询数据
	private Integer newType;
	private Integer newSource;
	private Integer newCommunication;

	private String trackId;

	/**
	 * VDERP-15213 询价行为 查询项特殊处理
	 */
	private List<Integer> inquirySpecial;
}
