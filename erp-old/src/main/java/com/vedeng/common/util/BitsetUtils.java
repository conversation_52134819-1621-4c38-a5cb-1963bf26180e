package com.vedeng.common.util;

import java.util.*;

public class BitsetUtils {
    private static final int MAX_BIT_SET_COUNT = 10000000;

    /**
     * 集合转化为BitSet
     * @param collection 集合
     * @return BitSet结果
     */
    public static BitSet collection2BitSet(Collection<Integer> collection){
        BitSet bitSet = new BitSet(MAX_BIT_SET_COUNT);
        for (Integer integer : collection) {
            bitSet.set(integer);
        }
        return bitSet;
    }


    /**
     * BitSet结构序列化为字符串
     * @param bitSet 源BitSet
     * @return 字符串结果
     */
    public String bitSet2String(BitSet bitSet){
        long[] longArray = bitSet.toLongArray();
        StringBuilder builder = new StringBuilder();
        for (long l : longArray) {
            builder.append(l).append(",");
        }
        String res = builder.toString();
        return res.substring(0,res.length()-1);
    }


    /**
     * BitSet由字符串反序列化为BitSet
     * @param s 字符串
     * @return BitSet结果
     */
    public BitSet string2BitSet(String s){

        String[] stringArray = s.split(",");
        long[] longArray = new long[stringArray.length];
        for (int i = 0; i < stringArray.length; i++) {
            longArray[i] = Long.parseLong(stringArray[i]);
        }
        return BitSet.valueOf(longArray);
    }


    /**
     * 判断某个数字是否在BitSet集合中
     * @param index 数字
     * @param bitSet 集合
     * @return 是否存在
     */
    public Boolean isInBitSet(Integer index, BitSet bitSet){
        return bitSet.get(index);
    }


    /**
     * bitSet转为List
     * @param bitSet 源数据
     * @return list结果
     */
    public static List<Integer> bitSet2List(BitSet bitSet){
        List<Integer> list = new ArrayList<>();
        String s = bitSet.toString();
        if(StringUtil.isBlank(s)||s.length()<3){
            return new ArrayList<>();
        }
        String[] stringArray = s.substring(1,s.length()-1).split(",");
        if (stringArray.length > 0){
            for (String item : stringArray){
                list.add(Integer.valueOf(item.trim()));
            }
        }
        return list;
    }


    /**
     * 分页获取集合数据
     * @param bitSet bitSet
     * @param currentPage 当前页，从1开始
     * @param size 单页条数
     * @return 结果
     */
    public List<Integer> bitSet2ListByPage(BitSet bitSet, Integer currentPage, Integer size){
        List<Integer> list = new ArrayList<>();
        String s = bitSet.toString();
        String[] stringArray = s.substring(1,s.length()-1).split(",");
        if (stringArray.length > 0){
            for (int i = (currentPage - 1) * size; i < currentPage * size; i++) {
                if (i < stringArray.length){
                    list.add(Integer.valueOf(stringArray[i].trim()));
                }
            }
        }
        return list;
    }
}
