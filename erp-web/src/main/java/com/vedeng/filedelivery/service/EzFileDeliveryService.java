package com.vedeng.filedelivery.service;

import java.util.List;
import java.util.Map;

import com.vedeng.oa.service.filedelivery.vo.FileDeliveryAddressVO;
import top.ezadmin.web.EzResult;

public interface EzFileDeliveryService {
    public String searchCondition(Map<String, Object> request) ;
    
    /**
     * 保存地址信息EZ
     * @param request
     * @return
     */
    public EzResult saveAddress(Map<String, Object> request);

    public Object hasPermission( String url);
}
