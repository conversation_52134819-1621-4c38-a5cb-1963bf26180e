package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.processor.BaseProcessor;
import com.vedeng.erp.kingdee.batch.dto.BatchBankBillDto;
import com.vedeng.erp.kingdee.dto.KingDeeFileDataDto;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveBillDto;
import com.vedeng.erp.kingdee.service.KingDeeFileDataService;
import com.vedeng.erp.kingdee.service.KingDeeReceiveBillService;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;

@Slf4j
@Service
public class ReceiveBillFillProcessor extends BaseProcessor<BatchBankBillDto, KingDeeFileDataDto> {
    public static final String ZERO = "0";

    @Autowired
    private KingDeeReceiveBillService kingDeeReceiveBillService;

    @Autowired
    private KingDeeFileDataService kingDeeFileDataService;

    @Override
    public KingDeeFileDataDto doProcess(BatchBankBillDto batchBankBillDto, JobParameters params, ExecutionContext stepContext) throws Exception {
        KingDeeFileDataDto query = KingDeeFileDataDto.builder()
                .formId(KingDeeFormConstant.RECEIVE_BILL)
                .erpId(batchBankBillDto.getBankBillId().toString())
                .url(batchBankBillDto.getReceiptUrl())
                .build();
        List<KingDeeFileDataDto> existFile = kingDeeFileDataService.getByBusinessIdAndUri(query);
        if (!CollectionUtils.isEmpty(existFile)) {
            log.info("当前附件已经推送过金蝶，{}", JSON.toJSONString(query));
            return null;
        }

        KingDeeReceiveBillDto kingDeeReceiveBillDto = new KingDeeReceiveBillDto();
        if (KingDeeConstant.ZERO.equals(batchBankBillDto.getStatus()) && batchBankBillDto.getMatchedAmount().compareTo(BigDecimal.ZERO) != 0) {
            kingDeeReceiveBillDto.setFBillNo("C_" + batchBankBillDto.getBankBillId());
        } else {
            kingDeeReceiveBillDto.setFBillNo("B_" + batchBankBillDto.getBankBillId());
        }
        kingDeeReceiveBillService.query(kingDeeReceiveBillDto);
        if (kingDeeReceiveBillDto.getFId() == null || ZERO.equals(kingDeeReceiveBillDto.getFId())) {
            log.info("上传出库单附件,金蝶收款单未推送金蝶：{}", JSON.toJSONString(batchBankBillDto));
            return null;
        }

        return KingDeeFileDataDto.builder()
                .fileName(batchBankBillDto.getBankBillId() + ".pdf")
                .aliasFileName(batchBankBillDto.getBankBillId() + ".pdf")
                .billNo(kingDeeReceiveBillDto.getFBillNo())
                .formId(KingDeeFormConstant.RECEIVE_BILL)
                .isLast(true)
                .fId(kingDeeReceiveBillDto.getFId())
                .url(batchBankBillDto.getReceiptUrl())
                .erpId(batchBankBillDto.getBankBillId().toString())
                .businessId(kingDeeReceiveBillDto.getFormId() + batchBankBillDto.getBankBillId().toString())
                .build();
    }
}
