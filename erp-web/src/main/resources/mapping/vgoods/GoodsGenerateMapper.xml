<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.dao.GoodsGenerateMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.goods.model.GoodsGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:18:06 CST 2019.
    -->
    <id column="GOODS_ID" jdbcType="INTEGER" property="goodsId" />
    <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId" />
    <result column="PARENT_ID" jdbcType="INTEGER" property="parentId" />
    <result column="CATEGORY_ID" jdbcType="INTEGER" property="categoryId" />
    <result column="BRAND_ID" jdbcType="INTEGER" property="brandId" />
    <result column="IS_ON_SALE" jdbcType="TINYINT" property="isOnSale" />
    <result column="IS_DISCARD" jdbcType="TINYINT" property="isDiscard" />
    <result column="SKU" jdbcType="VARCHAR" property="sku" />
    <result column="GOODS_NAME" jdbcType="VARCHAR" property="goodsName" />
    <result column="ALIAS_NAME" jdbcType="VARCHAR" property="aliasName" />
    <result column="MODEL" jdbcType="VARCHAR" property="model" />
    <result column="MATERIAL_CODE" jdbcType="VARCHAR" property="materialCode" />
    <result column="BASE_UNIT_ID" jdbcType="INTEGER" property="baseUnitId" />
    <result column="CHANGE_NUM" jdbcType="INTEGER" property="changeNum" />
    <result column="UNIT_ID" jdbcType="INTEGER" property="unitId" />
    <result column="GROSS_WEIGHT" jdbcType="DECIMAL" property="grossWeight" />
    <result column="NET_WEIGHT" jdbcType="DECIMAL" property="netWeight" />
    <result column="GOODS_LENGTH" jdbcType="DECIMAL" property="goodsLength" />
    <result column="GOODS_WIDTH" jdbcType="DECIMAL" property="goodsWidth" />
    <result column="GOODS_HEIGHT" jdbcType="DECIMAL" property="goodsHeight" />
    <result column="PACKAGE_LENGTH" jdbcType="DECIMAL" property="packageLength" />
    <result column="PACKAGE_WIDTH" jdbcType="DECIMAL" property="packageWidth" />
    <result column="PACKAGE_HEIGHT" jdbcType="DECIMAL" property="packageHeight" />
    <result column="GOODS_TYPE" jdbcType="INTEGER" property="goodsType" />
    <result column="GOODS_LEVEL" jdbcType="INTEGER" property="goodsLevel" />
    <result column="MANAGE_CATEGORY" jdbcType="INTEGER" property="manageCategory" />
    <result column="MANAGE_CATEGORY_LEVEL" jdbcType="INTEGER" property="manageCategoryLevel" />
    <result column="PURCHASE_REMIND" jdbcType="VARCHAR" property="purchaseRemind" />
    <result column="LICENSE_NUMBER" jdbcType="VARCHAR" property="licenseNumber" />
    <result column="FIRST_ENGAGE_ID" jdbcType="INTEGER" property="firstEngageId" />
    <result column="RECORD_NUMBER" jdbcType="VARCHAR" property="recordNumber" />
    <result column="REGISTRATION_NUMBER" jdbcType="VARCHAR" property="registrationNumber" />
    <result column="BEGINTIME" jdbcType="BIGINT" property="begintime" />
    <result column="ENDTIME" jdbcType="BIGINT" property="endtime" />
    <result column="AUTHORIZATION_CERTIFICATE_URL" jdbcType="VARCHAR" property="authorizationCertificateUrl" />
    <result column="OTHER_QUALIFICATION_URL" jdbcType="VARCHAR" property="otherQualificationUrl" />
    <result column="COLOR_PAGE_URL" jdbcType="VARCHAR" property="colorPageUrl" />
    <result column="TECHNICAL_PARAMETER_URL" jdbcType="VARCHAR" property="technicalParameterUrl" />
    <result column="INSTRUCTIONS_URL" jdbcType="VARCHAR" property="instructionsUrl" />
    <result column="BIDDING_DATA_URL" jdbcType="VARCHAR" property="biddingDataUrl" />
    <result column="PACKING_LIST" jdbcType="VARCHAR" property="packingList" />
    <result column="TOS" jdbcType="VARCHAR" property="tos" />
    <result column="TAX_CATEGORY_NO" jdbcType="VARCHAR" property="taxCategoryNo" />
    <result column="MANUFACTURER" jdbcType="VARCHAR" property="manufacturer" />
    <result column="PRODUCTION_LICENSE" jdbcType="VARCHAR" property="productionLicense" />
    <result column="DISCARD_REASON" jdbcType="VARCHAR" property="discardReason" />
    <result column="DISCARD_TIME" jdbcType="BIGINT" property="discardTime" />
    <result column="SUPPLY_MODEL" jdbcType="VARCHAR" property="supplyModel" />
    <result column="STANDARD_CATEGORY_ID" jdbcType="INTEGER" property="standardCategoryId" />
    <result column="STANDARD_CATEGORY_LEVEL" jdbcType="INTEGER" property="standardCategoryLevel" />
    <result column="SPEC" jdbcType="VARCHAR" property="spec" />
    <result column="PRODUCT_ADDRESS" jdbcType="VARCHAR" property="productAddress" />
    <result column="STORAGE_REQUIREMENTS" jdbcType="TINYINT" property="storageRequirements" />
    <result column="SOURCE" jdbcType="TINYINT" property="source" />
    <result column="IS_RECOMMED" jdbcType="TINYINT" property="isRecommed" />
    <result column="REGISTER_NAME" jdbcType="VARCHAR" property="registerName" />
    <result column="HREF" jdbcType="VARCHAR" property="href" />
    <result column="JX_MARKET_PRICE" jdbcType="DECIMAL" property="jxMarketPrice" />
    <result column="JX_SALE_PRICE" jdbcType="DECIMAL" property="jxSalePrice" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="TO_SKU_FLAG" jdbcType="INTEGER" property="toSkuFlag" />
    <result column="IS_NO_REASON_RETURN" jdbcType="TINYINT" property="isNoReasonReturn" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.vedeng.goods.model.GoodsGenerateWithBLOBs">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:18:06 CST 2019.
    -->
    <result column="TECHNICAL_PARAMETER" jdbcType="LONGVARCHAR" property="technicalParameter" />
    <result column="PERFORMANCE_PARAMETER" jdbcType="LONGVARCHAR" property="performanceParameter" />
    <result column="SPEC_PARAMETER" jdbcType="LONGVARCHAR" property="specParameter" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:18:06 CST 2019.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:18:06 CST 2019.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:18:06 CST 2019.
    -->
    GOODS_ID, COMPANY_ID, PARENT_ID, CATEGORY_ID, BRAND_ID, IS_ON_SALE, IS_DISCARD, SKU, 
    GOODS_NAME, ALIAS_NAME, MODEL, MATERIAL_CODE, BASE_UNIT_ID, CHANGE_NUM, UNIT_ID, 
    GROSS_WEIGHT, NET_WEIGHT, GOODS_LENGTH, GOODS_WIDTH, GOODS_HEIGHT, PACKAGE_LENGTH, 
    PACKAGE_WIDTH, PACKAGE_HEIGHT, GOODS_TYPE, GOODS_LEVEL, MANAGE_CATEGORY, MANAGE_CATEGORY_LEVEL, 
    PURCHASE_REMIND, LICENSE_NUMBER, FIRST_ENGAGE_ID, RECORD_NUMBER, REGISTRATION_NUMBER, 
    BEGINTIME, ENDTIME, AUTHORIZATION_CERTIFICATE_URL, OTHER_QUALIFICATION_URL, COLOR_PAGE_URL, 
    TECHNICAL_PARAMETER_URL, INSTRUCTIONS_URL, BIDDING_DATA_URL, PACKING_LIST, TOS, TAX_CATEGORY_NO, 
    MANUFACTURER, PRODUCTION_LICENSE, DISCARD_REASON, DISCARD_TIME, SUPPLY_MODEL, STANDARD_CATEGORY_ID, 
    STANDARD_CATEGORY_LEVEL, SPEC, PRODUCT_ADDRESS, STORAGE_REQUIREMENTS, `SOURCE`, IS_RECOMMED, 
    REGISTER_NAME, HREF, JX_MARKET_PRICE, JX_SALE_PRICE, ADD_TIME, CREATOR, MOD_TIME, 
    UPDATER, TO_SKU_FLAG, IS_NO_REASON_RETURN
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:18:06 CST 2019.
    -->
    TECHNICAL_PARAMETER, PERFORMANCE_PARAMETER, SPEC_PARAMETER
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.vedeng.goods.model.GoodsGenerateExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:18:06 CST 2019.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from T_GOODS
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.vedeng.goods.model.GoodsGenerateExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:18:06 CST 2019.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from T_GOODS
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:18:06 CST 2019.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from T_GOODS
    where GOODS_ID = #{goodsId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:18:06 CST 2019.
    -->
    delete from T_GOODS
    where GOODS_ID = #{goodsId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.vedeng.goods.model.GoodsGenerateExample">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:18:06 CST 2019.
    -->
    delete from T_GOODS
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.vedeng.goods.model.GoodsGenerateWithBLOBs">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:18:06 CST 2019.
    -->
    <selectKey keyProperty="goodsId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_GOODS (COMPANY_ID, PARENT_ID, CATEGORY_ID, 
      BRAND_ID, IS_ON_SALE, IS_DISCARD, 
      SKU, GOODS_NAME, ALIAS_NAME, 
      MODEL, MATERIAL_CODE, BASE_UNIT_ID, 
      CHANGE_NUM, UNIT_ID, GROSS_WEIGHT, 
      NET_WEIGHT, GOODS_LENGTH, GOODS_WIDTH, 
      GOODS_HEIGHT, PACKAGE_LENGTH, PACKAGE_WIDTH, 
      PACKAGE_HEIGHT, GOODS_TYPE, GOODS_LEVEL, 
      MANAGE_CATEGORY, MANAGE_CATEGORY_LEVEL, PURCHASE_REMIND, 
      LICENSE_NUMBER, FIRST_ENGAGE_ID, RECORD_NUMBER, 
      REGISTRATION_NUMBER, BEGINTIME, ENDTIME, 
      AUTHORIZATION_CERTIFICATE_URL, OTHER_QUALIFICATION_URL, 
      COLOR_PAGE_URL, TECHNICAL_PARAMETER_URL, INSTRUCTIONS_URL, 
      BIDDING_DATA_URL, PACKING_LIST, TOS, 
      TAX_CATEGORY_NO, MANUFACTURER, PRODUCTION_LICENSE, 
      DISCARD_REASON, DISCARD_TIME, SUPPLY_MODEL, 
      STANDARD_CATEGORY_ID, STANDARD_CATEGORY_LEVEL, 
      SPEC, PRODUCT_ADDRESS, STORAGE_REQUIREMENTS, 
      `SOURCE`, IS_RECOMMED, REGISTER_NAME, 
      HREF, JX_MARKET_PRICE, JX_SALE_PRICE, 
      ADD_TIME, CREATOR, MOD_TIME, 
      UPDATER, TO_SKU_FLAG, IS_NO_REASON_RETURN, 
      TECHNICAL_PARAMETER, PERFORMANCE_PARAMETER, 
      SPEC_PARAMETER)
    values (#{companyId,jdbcType=INTEGER}, #{parentId,jdbcType=INTEGER}, #{categoryId,jdbcType=INTEGER}, 
      #{brandId,jdbcType=INTEGER}, #{isOnSale,jdbcType=TINYINT}, #{isDiscard,jdbcType=TINYINT}, 
      #{sku,jdbcType=VARCHAR}, #{goodsName,jdbcType=VARCHAR}, #{aliasName,jdbcType=VARCHAR}, 
      #{model,jdbcType=VARCHAR}, #{materialCode,jdbcType=VARCHAR}, #{baseUnitId,jdbcType=INTEGER}, 
      #{changeNum,jdbcType=INTEGER}, #{unitId,jdbcType=INTEGER}, #{grossWeight,jdbcType=DECIMAL}, 
      #{netWeight,jdbcType=DECIMAL}, #{goodsLength,jdbcType=DECIMAL}, #{goodsWidth,jdbcType=DECIMAL}, 
      #{goodsHeight,jdbcType=DECIMAL}, #{packageLength,jdbcType=DECIMAL}, #{packageWidth,jdbcType=DECIMAL}, 
      #{packageHeight,jdbcType=DECIMAL}, #{goodsType,jdbcType=INTEGER}, #{goodsLevel,jdbcType=INTEGER}, 
      #{manageCategory,jdbcType=INTEGER}, #{manageCategoryLevel,jdbcType=INTEGER}, #{purchaseRemind,jdbcType=VARCHAR}, 
      #{licenseNumber,jdbcType=VARCHAR}, #{firstEngageId,jdbcType=INTEGER}, #{recordNumber,jdbcType=VARCHAR}, 
      #{registrationNumber,jdbcType=VARCHAR}, #{begintime,jdbcType=BIGINT}, #{endtime,jdbcType=BIGINT}, 
      #{authorizationCertificateUrl,jdbcType=VARCHAR}, #{otherQualificationUrl,jdbcType=VARCHAR}, 
      #{colorPageUrl,jdbcType=VARCHAR}, #{technicalParameterUrl,jdbcType=VARCHAR}, #{instructionsUrl,jdbcType=VARCHAR}, 
      #{biddingDataUrl,jdbcType=VARCHAR}, #{packingList,jdbcType=VARCHAR}, #{tos,jdbcType=VARCHAR}, 
      #{taxCategoryNo,jdbcType=VARCHAR}, #{manufacturer,jdbcType=VARCHAR}, #{productionLicense,jdbcType=VARCHAR}, 
      #{discardReason,jdbcType=VARCHAR}, #{discardTime,jdbcType=BIGINT}, #{supplyModel,jdbcType=VARCHAR}, 
      #{standardCategoryId,jdbcType=INTEGER}, #{standardCategoryLevel,jdbcType=INTEGER}, 
      #{spec,jdbcType=VARCHAR}, #{productAddress,jdbcType=VARCHAR}, #{storageRequirements,jdbcType=TINYINT}, 
      #{source,jdbcType=TINYINT}, #{isRecommed,jdbcType=TINYINT}, #{registerName,jdbcType=VARCHAR}, 
      #{href,jdbcType=VARCHAR}, #{jxMarketPrice,jdbcType=DECIMAL}, #{jxSalePrice,jdbcType=DECIMAL}, 
      #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=BIGINT}, 
      #{updater,jdbcType=INTEGER}, #{toSkuFlag,jdbcType=INTEGER}, #{isNoReasonReturn,jdbcType=TINYINT}, 
      #{technicalParameter,jdbcType=LONGVARCHAR}, #{performanceParameter,jdbcType=LONGVARCHAR}, 
      #{specParameter,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.goods.model.GoodsGenerateWithBLOBs">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:18:06 CST 2019.
    -->
    <selectKey keyProperty="goodsId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_GOODS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        COMPANY_ID,
      </if>
      <if test="parentId != null">
        PARENT_ID,
      </if>
      <if test="categoryId != null">
        CATEGORY_ID,
      </if>
      <if test="brandId != null">
        BRAND_ID,
      </if>
      <if test="isOnSale != null">
        IS_ON_SALE,
      </if>
      <if test="isDiscard != null">
        IS_DISCARD,
      </if>
      <if test="sku != null">
        SKU,
      </if>
      <if test="goodsName != null">
        GOODS_NAME,
      </if>
      <if test="aliasName != null">
        ALIAS_NAME,
      </if>
      <if test="model != null">
        MODEL,
      </if>
      <if test="materialCode != null">
        MATERIAL_CODE,
      </if>
      <if test="baseUnitId != null">
        BASE_UNIT_ID,
      </if>
      <if test="changeNum != null">
        CHANGE_NUM,
      </if>
      <if test="unitId != null">
        UNIT_ID,
      </if>
      <if test="grossWeight != null">
        GROSS_WEIGHT,
      </if>
      <if test="netWeight != null">
        NET_WEIGHT,
      </if>
      <if test="goodsLength != null">
        GOODS_LENGTH,
      </if>
      <if test="goodsWidth != null">
        GOODS_WIDTH,
      </if>
      <if test="goodsHeight != null">
        GOODS_HEIGHT,
      </if>
      <if test="packageLength != null">
        PACKAGE_LENGTH,
      </if>
      <if test="packageWidth != null">
        PACKAGE_WIDTH,
      </if>
      <if test="packageHeight != null">
        PACKAGE_HEIGHT,
      </if>
      <if test="goodsType != null">
        GOODS_TYPE,
      </if>
      <if test="goodsLevel != null">
        GOODS_LEVEL,
      </if>
      <if test="manageCategory != null">
        MANAGE_CATEGORY,
      </if>
      <if test="manageCategoryLevel != null">
        MANAGE_CATEGORY_LEVEL,
      </if>
      <if test="purchaseRemind != null">
        PURCHASE_REMIND,
      </if>
      <if test="licenseNumber != null">
        LICENSE_NUMBER,
      </if>
      <if test="firstEngageId != null">
        FIRST_ENGAGE_ID,
      </if>
      <if test="recordNumber != null">
        RECORD_NUMBER,
      </if>
      <if test="registrationNumber != null">
        REGISTRATION_NUMBER,
      </if>
      <if test="begintime != null">
        BEGINTIME,
      </if>
      <if test="endtime != null">
        ENDTIME,
      </if>
      <if test="authorizationCertificateUrl != null">
        AUTHORIZATION_CERTIFICATE_URL,
      </if>
      <if test="otherQualificationUrl != null">
        OTHER_QUALIFICATION_URL,
      </if>
      <if test="colorPageUrl != null">
        COLOR_PAGE_URL,
      </if>
      <if test="technicalParameterUrl != null">
        TECHNICAL_PARAMETER_URL,
      </if>
      <if test="instructionsUrl != null">
        INSTRUCTIONS_URL,
      </if>
      <if test="biddingDataUrl != null">
        BIDDING_DATA_URL,
      </if>
      <if test="packingList != null">
        PACKING_LIST,
      </if>
      <if test="tos != null">
        TOS,
      </if>
      <if test="taxCategoryNo != null">
        TAX_CATEGORY_NO,
      </if>
      <if test="manufacturer != null">
        MANUFACTURER,
      </if>
      <if test="productionLicense != null">
        PRODUCTION_LICENSE,
      </if>
      <if test="discardReason != null">
        DISCARD_REASON,
      </if>
      <if test="discardTime != null">
        DISCARD_TIME,
      </if>
      <if test="supplyModel != null">
        SUPPLY_MODEL,
      </if>
      <if test="standardCategoryId != null">
        STANDARD_CATEGORY_ID,
      </if>
      <if test="standardCategoryLevel != null">
        STANDARD_CATEGORY_LEVEL,
      </if>
      <if test="spec != null">
        SPEC,
      </if>
      <if test="productAddress != null">
        PRODUCT_ADDRESS,
      </if>
      <if test="storageRequirements != null">
        STORAGE_REQUIREMENTS,
      </if>
      <if test="source != null">
        `SOURCE`,
      </if>
      <if test="isRecommed != null">
        IS_RECOMMED,
      </if>
      <if test="registerName != null">
        REGISTER_NAME,
      </if>
      <if test="href != null">
        HREF,
      </if>
      <if test="jxMarketPrice != null">
        JX_MARKET_PRICE,
      </if>
      <if test="jxSalePrice != null">
        JX_SALE_PRICE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="toSkuFlag != null">
        TO_SKU_FLAG,
      </if>
      <if test="isNoReasonReturn != null">
        IS_NO_REASON_RETURN,
      </if>
      <if test="technicalParameter != null">
        TECHNICAL_PARAMETER,
      </if>
      <if test="performanceParameter != null">
        PERFORMANCE_PARAMETER,
      </if>
      <if test="specParameter != null">
        SPEC_PARAMETER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="parentId != null">
        #{parentId,jdbcType=INTEGER},
      </if>
      <if test="categoryId != null">
        #{categoryId,jdbcType=INTEGER},
      </if>
      <if test="brandId != null">
        #{brandId,jdbcType=INTEGER},
      </if>
      <if test="isOnSale != null">
        #{isOnSale,jdbcType=TINYINT},
      </if>
      <if test="isDiscard != null">
        #{isDiscard,jdbcType=TINYINT},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="aliasName != null">
        #{aliasName,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="materialCode != null">
        #{materialCode,jdbcType=VARCHAR},
      </if>
      <if test="baseUnitId != null">
        #{baseUnitId,jdbcType=INTEGER},
      </if>
      <if test="changeNum != null">
        #{changeNum,jdbcType=INTEGER},
      </if>
      <if test="unitId != null">
        #{unitId,jdbcType=INTEGER},
      </if>
      <if test="grossWeight != null">
        #{grossWeight,jdbcType=DECIMAL},
      </if>
      <if test="netWeight != null">
        #{netWeight,jdbcType=DECIMAL},
      </if>
      <if test="goodsLength != null">
        #{goodsLength,jdbcType=DECIMAL},
      </if>
      <if test="goodsWidth != null">
        #{goodsWidth,jdbcType=DECIMAL},
      </if>
      <if test="goodsHeight != null">
        #{goodsHeight,jdbcType=DECIMAL},
      </if>
      <if test="packageLength != null">
        #{packageLength,jdbcType=DECIMAL},
      </if>
      <if test="packageWidth != null">
        #{packageWidth,jdbcType=DECIMAL},
      </if>
      <if test="packageHeight != null">
        #{packageHeight,jdbcType=DECIMAL},
      </if>
      <if test="goodsType != null">
        #{goodsType,jdbcType=INTEGER},
      </if>
      <if test="goodsLevel != null">
        #{goodsLevel,jdbcType=INTEGER},
      </if>
      <if test="manageCategory != null">
        #{manageCategory,jdbcType=INTEGER},
      </if>
      <if test="manageCategoryLevel != null">
        #{manageCategoryLevel,jdbcType=INTEGER},
      </if>
      <if test="purchaseRemind != null">
        #{purchaseRemind,jdbcType=VARCHAR},
      </if>
      <if test="licenseNumber != null">
        #{licenseNumber,jdbcType=VARCHAR},
      </if>
      <if test="firstEngageId != null">
        #{firstEngageId,jdbcType=INTEGER},
      </if>
      <if test="recordNumber != null">
        #{recordNumber,jdbcType=VARCHAR},
      </if>
      <if test="registrationNumber != null">
        #{registrationNumber,jdbcType=VARCHAR},
      </if>
      <if test="begintime != null">
        #{begintime,jdbcType=BIGINT},
      </if>
      <if test="endtime != null">
        #{endtime,jdbcType=BIGINT},
      </if>
      <if test="authorizationCertificateUrl != null">
        #{authorizationCertificateUrl,jdbcType=VARCHAR},
      </if>
      <if test="otherQualificationUrl != null">
        #{otherQualificationUrl,jdbcType=VARCHAR},
      </if>
      <if test="colorPageUrl != null">
        #{colorPageUrl,jdbcType=VARCHAR},
      </if>
      <if test="technicalParameterUrl != null">
        #{technicalParameterUrl,jdbcType=VARCHAR},
      </if>
      <if test="instructionsUrl != null">
        #{instructionsUrl,jdbcType=VARCHAR},
      </if>
      <if test="biddingDataUrl != null">
        #{biddingDataUrl,jdbcType=VARCHAR},
      </if>
      <if test="packingList != null">
        #{packingList,jdbcType=VARCHAR},
      </if>
      <if test="tos != null">
        #{tos,jdbcType=VARCHAR},
      </if>
      <if test="taxCategoryNo != null">
        #{taxCategoryNo,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="productionLicense != null">
        #{productionLicense,jdbcType=VARCHAR},
      </if>
      <if test="discardReason != null">
        #{discardReason,jdbcType=VARCHAR},
      </if>
      <if test="discardTime != null">
        #{discardTime,jdbcType=BIGINT},
      </if>
      <if test="supplyModel != null">
        #{supplyModel,jdbcType=VARCHAR},
      </if>
      <if test="standardCategoryId != null">
        #{standardCategoryId,jdbcType=INTEGER},
      </if>
      <if test="standardCategoryLevel != null">
        #{standardCategoryLevel,jdbcType=INTEGER},
      </if>
      <if test="spec != null">
        #{spec,jdbcType=VARCHAR},
      </if>
      <if test="productAddress != null">
        #{productAddress,jdbcType=VARCHAR},
      </if>
      <if test="storageRequirements != null">
        #{storageRequirements,jdbcType=TINYINT},
      </if>
      <if test="source != null">
        #{source,jdbcType=TINYINT},
      </if>
      <if test="isRecommed != null">
        #{isRecommed,jdbcType=TINYINT},
      </if>
      <if test="registerName != null">
        #{registerName,jdbcType=VARCHAR},
      </if>
      <if test="href != null">
        #{href,jdbcType=VARCHAR},
      </if>
      <if test="jxMarketPrice != null">
        #{jxMarketPrice,jdbcType=DECIMAL},
      </if>
      <if test="jxSalePrice != null">
        #{jxSalePrice,jdbcType=DECIMAL},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="toSkuFlag != null">
        #{toSkuFlag,jdbcType=INTEGER},
      </if>
      <if test="isNoReasonReturn != null">
        #{isNoReasonReturn,jdbcType=TINYINT},
      </if>
      <if test="technicalParameter != null">
        #{technicalParameter,jdbcType=LONGVARCHAR},
      </if>
      <if test="performanceParameter != null">
        #{performanceParameter,jdbcType=LONGVARCHAR},
      </if>
      <if test="specParameter != null">
        #{specParameter,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vedeng.goods.model.GoodsGenerateExample" resultType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:18:06 CST 2019.
    -->
    select count(*) from T_GOODS
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:18:06 CST 2019.
    -->
    update T_GOODS
    <set>
      <if test="record.goodsId != null">
        GOODS_ID = #{record.goodsId,jdbcType=INTEGER},
      </if>
      <if test="record.companyId != null">
        COMPANY_ID = #{record.companyId,jdbcType=INTEGER},
      </if>
      <if test="record.parentId != null">
        PARENT_ID = #{record.parentId,jdbcType=INTEGER},
      </if>
      <if test="record.categoryId != null">
        CATEGORY_ID = #{record.categoryId,jdbcType=INTEGER},
      </if>
      <if test="record.brandId != null">
        BRAND_ID = #{record.brandId,jdbcType=INTEGER},
      </if>
      <if test="record.isOnSale != null">
        IS_ON_SALE = #{record.isOnSale,jdbcType=TINYINT},
      </if>
      <if test="record.isDiscard != null">
        IS_DISCARD = #{record.isDiscard,jdbcType=TINYINT},
      </if>
      <if test="record.sku != null">
        SKU = #{record.sku,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsName != null">
        GOODS_NAME = #{record.goodsName,jdbcType=VARCHAR},
      </if>
      <if test="record.aliasName != null">
        ALIAS_NAME = #{record.aliasName,jdbcType=VARCHAR},
      </if>
      <if test="record.model != null">
        MODEL = #{record.model,jdbcType=VARCHAR},
      </if>
      <if test="record.materialCode != null">
        MATERIAL_CODE = #{record.materialCode,jdbcType=VARCHAR},
      </if>
      <if test="record.baseUnitId != null">
        BASE_UNIT_ID = #{record.baseUnitId,jdbcType=INTEGER},
      </if>
      <if test="record.changeNum != null">
        CHANGE_NUM = #{record.changeNum,jdbcType=INTEGER},
      </if>
      <if test="record.unitId != null">
        UNIT_ID = #{record.unitId,jdbcType=INTEGER},
      </if>
      <if test="record.grossWeight != null">
        GROSS_WEIGHT = #{record.grossWeight,jdbcType=DECIMAL},
      </if>
      <if test="record.netWeight != null">
        NET_WEIGHT = #{record.netWeight,jdbcType=DECIMAL},
      </if>
      <if test="record.goodsLength != null">
        GOODS_LENGTH = #{record.goodsLength,jdbcType=DECIMAL},
      </if>
      <if test="record.goodsWidth != null">
        GOODS_WIDTH = #{record.goodsWidth,jdbcType=DECIMAL},
      </if>
      <if test="record.goodsHeight != null">
        GOODS_HEIGHT = #{record.goodsHeight,jdbcType=DECIMAL},
      </if>
      <if test="record.packageLength != null">
        PACKAGE_LENGTH = #{record.packageLength,jdbcType=DECIMAL},
      </if>
      <if test="record.packageWidth != null">
        PACKAGE_WIDTH = #{record.packageWidth,jdbcType=DECIMAL},
      </if>
      <if test="record.packageHeight != null">
        PACKAGE_HEIGHT = #{record.packageHeight,jdbcType=DECIMAL},
      </if>
      <if test="record.goodsType != null">
        GOODS_TYPE = #{record.goodsType,jdbcType=INTEGER},
      </if>
      <if test="record.goodsLevel != null">
        GOODS_LEVEL = #{record.goodsLevel,jdbcType=INTEGER},
      </if>
      <if test="record.manageCategory != null">
        MANAGE_CATEGORY = #{record.manageCategory,jdbcType=INTEGER},
      </if>
      <if test="record.manageCategoryLevel != null">
        MANAGE_CATEGORY_LEVEL = #{record.manageCategoryLevel,jdbcType=INTEGER},
      </if>
      <if test="record.purchaseRemind != null">
        PURCHASE_REMIND = #{record.purchaseRemind,jdbcType=VARCHAR},
      </if>
      <if test="record.licenseNumber != null">
        LICENSE_NUMBER = #{record.licenseNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.firstEngageId != null">
        FIRST_ENGAGE_ID = #{record.firstEngageId,jdbcType=INTEGER},
      </if>
      <if test="record.recordNumber != null">
        RECORD_NUMBER = #{record.recordNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.registrationNumber != null">
        REGISTRATION_NUMBER = #{record.registrationNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.begintime != null">
        BEGINTIME = #{record.begintime,jdbcType=BIGINT},
      </if>
      <if test="record.endtime != null">
        ENDTIME = #{record.endtime,jdbcType=BIGINT},
      </if>
      <if test="record.authorizationCertificateUrl != null">
        AUTHORIZATION_CERTIFICATE_URL = #{record.authorizationCertificateUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.otherQualificationUrl != null">
        OTHER_QUALIFICATION_URL = #{record.otherQualificationUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.colorPageUrl != null">
        COLOR_PAGE_URL = #{record.colorPageUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.technicalParameterUrl != null">
        TECHNICAL_PARAMETER_URL = #{record.technicalParameterUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.instructionsUrl != null">
        INSTRUCTIONS_URL = #{record.instructionsUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.biddingDataUrl != null">
        BIDDING_DATA_URL = #{record.biddingDataUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.packingList != null">
        PACKING_LIST = #{record.packingList,jdbcType=VARCHAR},
      </if>
      <if test="record.tos != null">
        TOS = #{record.tos,jdbcType=VARCHAR},
      </if>
      <if test="record.taxCategoryNo != null">
        TAX_CATEGORY_NO = #{record.taxCategoryNo,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturer != null">
        MANUFACTURER = #{record.manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="record.productionLicense != null">
        PRODUCTION_LICENSE = #{record.productionLicense,jdbcType=VARCHAR},
      </if>
      <if test="record.discardReason != null">
        DISCARD_REASON = #{record.discardReason,jdbcType=VARCHAR},
      </if>
      <if test="record.discardTime != null">
        DISCARD_TIME = #{record.discardTime,jdbcType=BIGINT},
      </if>
      <if test="record.supplyModel != null">
        SUPPLY_MODEL = #{record.supplyModel,jdbcType=VARCHAR},
      </if>
      <if test="record.standardCategoryId != null">
        STANDARD_CATEGORY_ID = #{record.standardCategoryId,jdbcType=INTEGER},
      </if>
      <if test="record.standardCategoryLevel != null">
        STANDARD_CATEGORY_LEVEL = #{record.standardCategoryLevel,jdbcType=INTEGER},
      </if>
      <if test="record.spec != null">
        SPEC = #{record.spec,jdbcType=VARCHAR},
      </if>
      <if test="record.productAddress != null">
        PRODUCT_ADDRESS = #{record.productAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.storageRequirements != null">
        STORAGE_REQUIREMENTS = #{record.storageRequirements,jdbcType=TINYINT},
      </if>
      <if test="record.source != null">
        `SOURCE` = #{record.source,jdbcType=TINYINT},
      </if>
      <if test="record.isRecommed != null">
        IS_RECOMMED = #{record.isRecommed,jdbcType=TINYINT},
      </if>
      <if test="record.registerName != null">
        REGISTER_NAME = #{record.registerName,jdbcType=VARCHAR},
      </if>
      <if test="record.href != null">
        HREF = #{record.href,jdbcType=VARCHAR},
      </if>
      <if test="record.jxMarketPrice != null">
        JX_MARKET_PRICE = #{record.jxMarketPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.jxSalePrice != null">
        JX_SALE_PRICE = #{record.jxSalePrice,jdbcType=DECIMAL},
      </if>
      <if test="record.addTime != null">
        ADD_TIME = #{record.addTime,jdbcType=BIGINT},
      </if>
      <if test="record.creator != null">
        CREATOR = #{record.creator,jdbcType=INTEGER},
      </if>
      <if test="record.modTime != null">
        MOD_TIME = #{record.modTime,jdbcType=BIGINT},
      </if>
      <if test="record.updater != null">
        UPDATER = #{record.updater,jdbcType=INTEGER},
      </if>
      <if test="record.toSkuFlag != null">
        TO_SKU_FLAG = #{record.toSkuFlag,jdbcType=INTEGER},
      </if>
      <if test="record.isNoReasonReturn != null">
        IS_NO_REASON_RETURN = #{record.isNoReasonReturn,jdbcType=TINYINT},
      </if>
      <if test="record.technicalParameter != null">
        TECHNICAL_PARAMETER = #{record.technicalParameter,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.performanceParameter != null">
        PERFORMANCE_PARAMETER = #{record.performanceParameter,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.specParameter != null">
        SPEC_PARAMETER = #{record.specParameter,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:18:06 CST 2019.
    -->
    update T_GOODS
    set GOODS_ID = #{record.goodsId,jdbcType=INTEGER},
      COMPANY_ID = #{record.companyId,jdbcType=INTEGER},
      PARENT_ID = #{record.parentId,jdbcType=INTEGER},
      CATEGORY_ID = #{record.categoryId,jdbcType=INTEGER},
      BRAND_ID = #{record.brandId,jdbcType=INTEGER},
      IS_ON_SALE = #{record.isOnSale,jdbcType=TINYINT},
      IS_DISCARD = #{record.isDiscard,jdbcType=TINYINT},
      SKU = #{record.sku,jdbcType=VARCHAR},
      GOODS_NAME = #{record.goodsName,jdbcType=VARCHAR},
      ALIAS_NAME = #{record.aliasName,jdbcType=VARCHAR},
      MODEL = #{record.model,jdbcType=VARCHAR},
      MATERIAL_CODE = #{record.materialCode,jdbcType=VARCHAR},
      BASE_UNIT_ID = #{record.baseUnitId,jdbcType=INTEGER},
      CHANGE_NUM = #{record.changeNum,jdbcType=INTEGER},
      UNIT_ID = #{record.unitId,jdbcType=INTEGER},
      GROSS_WEIGHT = #{record.grossWeight,jdbcType=DECIMAL},
      NET_WEIGHT = #{record.netWeight,jdbcType=DECIMAL},
      GOODS_LENGTH = #{record.goodsLength,jdbcType=DECIMAL},
      GOODS_WIDTH = #{record.goodsWidth,jdbcType=DECIMAL},
      GOODS_HEIGHT = #{record.goodsHeight,jdbcType=DECIMAL},
      PACKAGE_LENGTH = #{record.packageLength,jdbcType=DECIMAL},
      PACKAGE_WIDTH = #{record.packageWidth,jdbcType=DECIMAL},
      PACKAGE_HEIGHT = #{record.packageHeight,jdbcType=DECIMAL},
      GOODS_TYPE = #{record.goodsType,jdbcType=INTEGER},
      GOODS_LEVEL = #{record.goodsLevel,jdbcType=INTEGER},
      MANAGE_CATEGORY = #{record.manageCategory,jdbcType=INTEGER},
      MANAGE_CATEGORY_LEVEL = #{record.manageCategoryLevel,jdbcType=INTEGER},
      PURCHASE_REMIND = #{record.purchaseRemind,jdbcType=VARCHAR},
      LICENSE_NUMBER = #{record.licenseNumber,jdbcType=VARCHAR},
      FIRST_ENGAGE_ID = #{record.firstEngageId,jdbcType=INTEGER},
      RECORD_NUMBER = #{record.recordNumber,jdbcType=VARCHAR},
      REGISTRATION_NUMBER = #{record.registrationNumber,jdbcType=VARCHAR},
      BEGINTIME = #{record.begintime,jdbcType=BIGINT},
      ENDTIME = #{record.endtime,jdbcType=BIGINT},
      AUTHORIZATION_CERTIFICATE_URL = #{record.authorizationCertificateUrl,jdbcType=VARCHAR},
      OTHER_QUALIFICATION_URL = #{record.otherQualificationUrl,jdbcType=VARCHAR},
      COLOR_PAGE_URL = #{record.colorPageUrl,jdbcType=VARCHAR},
      TECHNICAL_PARAMETER_URL = #{record.technicalParameterUrl,jdbcType=VARCHAR},
      INSTRUCTIONS_URL = #{record.instructionsUrl,jdbcType=VARCHAR},
      BIDDING_DATA_URL = #{record.biddingDataUrl,jdbcType=VARCHAR},
      PACKING_LIST = #{record.packingList,jdbcType=VARCHAR},
      TOS = #{record.tos,jdbcType=VARCHAR},
      TAX_CATEGORY_NO = #{record.taxCategoryNo,jdbcType=VARCHAR},
      MANUFACTURER = #{record.manufacturer,jdbcType=VARCHAR},
      PRODUCTION_LICENSE = #{record.productionLicense,jdbcType=VARCHAR},
      DISCARD_REASON = #{record.discardReason,jdbcType=VARCHAR},
      DISCARD_TIME = #{record.discardTime,jdbcType=BIGINT},
      SUPPLY_MODEL = #{record.supplyModel,jdbcType=VARCHAR},
      STANDARD_CATEGORY_ID = #{record.standardCategoryId,jdbcType=INTEGER},
      STANDARD_CATEGORY_LEVEL = #{record.standardCategoryLevel,jdbcType=INTEGER},
      SPEC = #{record.spec,jdbcType=VARCHAR},
      PRODUCT_ADDRESS = #{record.productAddress,jdbcType=VARCHAR},
      STORAGE_REQUIREMENTS = #{record.storageRequirements,jdbcType=TINYINT},
      `SOURCE` = #{record.source,jdbcType=TINYINT},
      IS_RECOMMED = #{record.isRecommed,jdbcType=TINYINT},
      REGISTER_NAME = #{record.registerName,jdbcType=VARCHAR},
      HREF = #{record.href,jdbcType=VARCHAR},
      JX_MARKET_PRICE = #{record.jxMarketPrice,jdbcType=DECIMAL},
      JX_SALE_PRICE = #{record.jxSalePrice,jdbcType=DECIMAL},
      ADD_TIME = #{record.addTime,jdbcType=BIGINT},
      CREATOR = #{record.creator,jdbcType=INTEGER},
      MOD_TIME = #{record.modTime,jdbcType=BIGINT},
      UPDATER = #{record.updater,jdbcType=INTEGER},
      TO_SKU_FLAG = #{record.toSkuFlag,jdbcType=INTEGER},
      IS_NO_REASON_RETURN = #{record.isNoReasonReturn,jdbcType=TINYINT},
      TECHNICAL_PARAMETER = #{record.technicalParameter,jdbcType=LONGVARCHAR},
      PERFORMANCE_PARAMETER = #{record.performanceParameter,jdbcType=LONGVARCHAR},
      SPEC_PARAMETER = #{record.specParameter,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:18:06 CST 2019.
    -->
    update T_GOODS
    set GOODS_ID = #{record.goodsId,jdbcType=INTEGER},
      COMPANY_ID = #{record.companyId,jdbcType=INTEGER},
      PARENT_ID = #{record.parentId,jdbcType=INTEGER},
      CATEGORY_ID = #{record.categoryId,jdbcType=INTEGER},
      BRAND_ID = #{record.brandId,jdbcType=INTEGER},
      IS_ON_SALE = #{record.isOnSale,jdbcType=TINYINT},
      IS_DISCARD = #{record.isDiscard,jdbcType=TINYINT},
      SKU = #{record.sku,jdbcType=VARCHAR},
      GOODS_NAME = #{record.goodsName,jdbcType=VARCHAR},
      ALIAS_NAME = #{record.aliasName,jdbcType=VARCHAR},
      MODEL = #{record.model,jdbcType=VARCHAR},
      MATERIAL_CODE = #{record.materialCode,jdbcType=VARCHAR},
      BASE_UNIT_ID = #{record.baseUnitId,jdbcType=INTEGER},
      CHANGE_NUM = #{record.changeNum,jdbcType=INTEGER},
      UNIT_ID = #{record.unitId,jdbcType=INTEGER},
      GROSS_WEIGHT = #{record.grossWeight,jdbcType=DECIMAL},
      NET_WEIGHT = #{record.netWeight,jdbcType=DECIMAL},
      GOODS_LENGTH = #{record.goodsLength,jdbcType=DECIMAL},
      GOODS_WIDTH = #{record.goodsWidth,jdbcType=DECIMAL},
      GOODS_HEIGHT = #{record.goodsHeight,jdbcType=DECIMAL},
      PACKAGE_LENGTH = #{record.packageLength,jdbcType=DECIMAL},
      PACKAGE_WIDTH = #{record.packageWidth,jdbcType=DECIMAL},
      PACKAGE_HEIGHT = #{record.packageHeight,jdbcType=DECIMAL},
      GOODS_TYPE = #{record.goodsType,jdbcType=INTEGER},
      GOODS_LEVEL = #{record.goodsLevel,jdbcType=INTEGER},
      MANAGE_CATEGORY = #{record.manageCategory,jdbcType=INTEGER},
      MANAGE_CATEGORY_LEVEL = #{record.manageCategoryLevel,jdbcType=INTEGER},
      PURCHASE_REMIND = #{record.purchaseRemind,jdbcType=VARCHAR},
      LICENSE_NUMBER = #{record.licenseNumber,jdbcType=VARCHAR},
      FIRST_ENGAGE_ID = #{record.firstEngageId,jdbcType=INTEGER},
      RECORD_NUMBER = #{record.recordNumber,jdbcType=VARCHAR},
      REGISTRATION_NUMBER = #{record.registrationNumber,jdbcType=VARCHAR},
      BEGINTIME = #{record.begintime,jdbcType=BIGINT},
      ENDTIME = #{record.endtime,jdbcType=BIGINT},
      AUTHORIZATION_CERTIFICATE_URL = #{record.authorizationCertificateUrl,jdbcType=VARCHAR},
      OTHER_QUALIFICATION_URL = #{record.otherQualificationUrl,jdbcType=VARCHAR},
      COLOR_PAGE_URL = #{record.colorPageUrl,jdbcType=VARCHAR},
      TECHNICAL_PARAMETER_URL = #{record.technicalParameterUrl,jdbcType=VARCHAR},
      INSTRUCTIONS_URL = #{record.instructionsUrl,jdbcType=VARCHAR},
      BIDDING_DATA_URL = #{record.biddingDataUrl,jdbcType=VARCHAR},
      PACKING_LIST = #{record.packingList,jdbcType=VARCHAR},
      TOS = #{record.tos,jdbcType=VARCHAR},
      TAX_CATEGORY_NO = #{record.taxCategoryNo,jdbcType=VARCHAR},
      MANUFACTURER = #{record.manufacturer,jdbcType=VARCHAR},
      PRODUCTION_LICENSE = #{record.productionLicense,jdbcType=VARCHAR},
      DISCARD_REASON = #{record.discardReason,jdbcType=VARCHAR},
      DISCARD_TIME = #{record.discardTime,jdbcType=BIGINT},
      SUPPLY_MODEL = #{record.supplyModel,jdbcType=VARCHAR},
      STANDARD_CATEGORY_ID = #{record.standardCategoryId,jdbcType=INTEGER},
      STANDARD_CATEGORY_LEVEL = #{record.standardCategoryLevel,jdbcType=INTEGER},
      SPEC = #{record.spec,jdbcType=VARCHAR},
      PRODUCT_ADDRESS = #{record.productAddress,jdbcType=VARCHAR},
      STORAGE_REQUIREMENTS = #{record.storageRequirements,jdbcType=TINYINT},
      `SOURCE` = #{record.source,jdbcType=TINYINT},
      IS_RECOMMED = #{record.isRecommed,jdbcType=TINYINT},
      REGISTER_NAME = #{record.registerName,jdbcType=VARCHAR},
      HREF = #{record.href,jdbcType=VARCHAR},
      JX_MARKET_PRICE = #{record.jxMarketPrice,jdbcType=DECIMAL},
      JX_SALE_PRICE = #{record.jxSalePrice,jdbcType=DECIMAL},
      ADD_TIME = #{record.addTime,jdbcType=BIGINT},
      CREATOR = #{record.creator,jdbcType=INTEGER},
      MOD_TIME = #{record.modTime,jdbcType=BIGINT},
      UPDATER = #{record.updater,jdbcType=INTEGER},
      TO_SKU_FLAG = #{record.toSkuFlag,jdbcType=INTEGER},
      IS_NO_REASON_RETURN = #{record.isNoReasonReturn,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.goods.model.GoodsGenerateWithBLOBs">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:18:06 CST 2019.
    -->
    update T_GOODS
    <set>
      <if test="companyId != null">
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="parentId != null">
        PARENT_ID = #{parentId,jdbcType=INTEGER},
      </if>
      <if test="categoryId != null">
        CATEGORY_ID = #{categoryId,jdbcType=INTEGER},
      </if>
      <if test="brandId != null">
        BRAND_ID = #{brandId,jdbcType=INTEGER},
      </if>
      <if test="isOnSale != null">
        IS_ON_SALE = #{isOnSale,jdbcType=TINYINT},
      </if>
      <if test="isDiscard != null">
        IS_DISCARD = #{isDiscard,jdbcType=TINYINT},
      </if>
      <if test="sku != null">
        SKU = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        GOODS_NAME = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="aliasName != null">
        ALIAS_NAME = #{aliasName,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        MODEL = #{model,jdbcType=VARCHAR},
      </if>
      <if test="materialCode != null">
        MATERIAL_CODE = #{materialCode,jdbcType=VARCHAR},
      </if>
      <if test="baseUnitId != null">
        BASE_UNIT_ID = #{baseUnitId,jdbcType=INTEGER},
      </if>
      <if test="changeNum != null">
        CHANGE_NUM = #{changeNum,jdbcType=INTEGER},
      </if>
      <if test="unitId != null">
        UNIT_ID = #{unitId,jdbcType=INTEGER},
      </if>
      <if test="grossWeight != null">
        GROSS_WEIGHT = #{grossWeight,jdbcType=DECIMAL},
      </if>
      <if test="netWeight != null">
        NET_WEIGHT = #{netWeight,jdbcType=DECIMAL},
      </if>
      <if test="goodsLength != null">
        GOODS_LENGTH = #{goodsLength,jdbcType=DECIMAL},
      </if>
      <if test="goodsWidth != null">
        GOODS_WIDTH = #{goodsWidth,jdbcType=DECIMAL},
      </if>
      <if test="goodsHeight != null">
        GOODS_HEIGHT = #{goodsHeight,jdbcType=DECIMAL},
      </if>
      <if test="packageLength != null">
        PACKAGE_LENGTH = #{packageLength,jdbcType=DECIMAL},
      </if>
      <if test="packageWidth != null">
        PACKAGE_WIDTH = #{packageWidth,jdbcType=DECIMAL},
      </if>
      <if test="packageHeight != null">
        PACKAGE_HEIGHT = #{packageHeight,jdbcType=DECIMAL},
      </if>
      <if test="goodsType != null">
        GOODS_TYPE = #{goodsType,jdbcType=INTEGER},
      </if>
      <if test="goodsLevel != null">
        GOODS_LEVEL = #{goodsLevel,jdbcType=INTEGER},
      </if>
      <if test="manageCategory != null">
        MANAGE_CATEGORY = #{manageCategory,jdbcType=INTEGER},
      </if>
      <if test="manageCategoryLevel != null">
        MANAGE_CATEGORY_LEVEL = #{manageCategoryLevel,jdbcType=INTEGER},
      </if>
      <if test="purchaseRemind != null">
        PURCHASE_REMIND = #{purchaseRemind,jdbcType=VARCHAR},
      </if>
      <if test="licenseNumber != null">
        LICENSE_NUMBER = #{licenseNumber,jdbcType=VARCHAR},
      </if>
      <if test="firstEngageId != null">
        FIRST_ENGAGE_ID = #{firstEngageId,jdbcType=INTEGER},
      </if>
      <if test="recordNumber != null">
        RECORD_NUMBER = #{recordNumber,jdbcType=VARCHAR},
      </if>
      <if test="registrationNumber != null">
        REGISTRATION_NUMBER = #{registrationNumber,jdbcType=VARCHAR},
      </if>
      <if test="begintime != null">
        BEGINTIME = #{begintime,jdbcType=BIGINT},
      </if>
      <if test="endtime != null">
        ENDTIME = #{endtime,jdbcType=BIGINT},
      </if>
      <if test="authorizationCertificateUrl != null">
        AUTHORIZATION_CERTIFICATE_URL = #{authorizationCertificateUrl,jdbcType=VARCHAR},
      </if>
      <if test="otherQualificationUrl != null">
        OTHER_QUALIFICATION_URL = #{otherQualificationUrl,jdbcType=VARCHAR},
      </if>
      <if test="colorPageUrl != null">
        COLOR_PAGE_URL = #{colorPageUrl,jdbcType=VARCHAR},
      </if>
      <if test="technicalParameterUrl != null">
        TECHNICAL_PARAMETER_URL = #{technicalParameterUrl,jdbcType=VARCHAR},
      </if>
      <if test="instructionsUrl != null">
        INSTRUCTIONS_URL = #{instructionsUrl,jdbcType=VARCHAR},
      </if>
      <if test="biddingDataUrl != null">
        BIDDING_DATA_URL = #{biddingDataUrl,jdbcType=VARCHAR},
      </if>
      <if test="packingList != null">
        PACKING_LIST = #{packingList,jdbcType=VARCHAR},
      </if>
      <if test="tos != null">
        TOS = #{tos,jdbcType=VARCHAR},
      </if>
      <if test="taxCategoryNo != null">
        TAX_CATEGORY_NO = #{taxCategoryNo,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        MANUFACTURER = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="productionLicense != null">
        PRODUCTION_LICENSE = #{productionLicense,jdbcType=VARCHAR},
      </if>
      <if test="discardReason != null">
        DISCARD_REASON = #{discardReason,jdbcType=VARCHAR},
      </if>
      <if test="discardTime != null">
        DISCARD_TIME = #{discardTime,jdbcType=BIGINT},
      </if>
      <if test="supplyModel != null">
        SUPPLY_MODEL = #{supplyModel,jdbcType=VARCHAR},
      </if>
      <if test="standardCategoryId != null">
        STANDARD_CATEGORY_ID = #{standardCategoryId,jdbcType=INTEGER},
      </if>
      <if test="standardCategoryLevel != null">
        STANDARD_CATEGORY_LEVEL = #{standardCategoryLevel,jdbcType=INTEGER},
      </if>
      <if test="spec != null">
        SPEC = #{spec,jdbcType=VARCHAR},
      </if>
      <if test="productAddress != null">
        PRODUCT_ADDRESS = #{productAddress,jdbcType=VARCHAR},
      </if>
      <if test="storageRequirements != null">
        STORAGE_REQUIREMENTS = #{storageRequirements,jdbcType=TINYINT},
      </if>
      <if test="source != null">
        `SOURCE` = #{source,jdbcType=TINYINT},
      </if>
      <if test="isRecommed != null">
        IS_RECOMMED = #{isRecommed,jdbcType=TINYINT},
      </if>
      <if test="registerName != null">
        REGISTER_NAME = #{registerName,jdbcType=VARCHAR},
      </if>
      <if test="href != null">
        HREF = #{href,jdbcType=VARCHAR},
      </if>
      <if test="jxMarketPrice != null">
        JX_MARKET_PRICE = #{jxMarketPrice,jdbcType=DECIMAL},
      </if>
      <if test="jxSalePrice != null">
        JX_SALE_PRICE = #{jxSalePrice,jdbcType=DECIMAL},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="toSkuFlag != null">
        TO_SKU_FLAG = #{toSkuFlag,jdbcType=INTEGER},
      </if>
      <if test="isNoReasonReturn != null">
        IS_NO_REASON_RETURN = #{isNoReasonReturn,jdbcType=TINYINT},
      </if>
      <if test="technicalParameter != null">
        TECHNICAL_PARAMETER = #{technicalParameter,jdbcType=LONGVARCHAR},
      </if>
      <if test="performanceParameter != null">
        PERFORMANCE_PARAMETER = #{performanceParameter,jdbcType=LONGVARCHAR},
      </if>
      <if test="specParameter != null">
        SPEC_PARAMETER = #{specParameter,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where GOODS_ID = #{goodsId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.vedeng.goods.model.GoodsGenerateWithBLOBs">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:18:06 CST 2019.
    -->
    update T_GOODS
    set COMPANY_ID = #{companyId,jdbcType=INTEGER},
      PARENT_ID = #{parentId,jdbcType=INTEGER},
      CATEGORY_ID = #{categoryId,jdbcType=INTEGER},
      BRAND_ID = #{brandId,jdbcType=INTEGER},
      IS_ON_SALE = #{isOnSale,jdbcType=TINYINT},
      IS_DISCARD = #{isDiscard,jdbcType=TINYINT},
      SKU = #{sku,jdbcType=VARCHAR},
      GOODS_NAME = #{goodsName,jdbcType=VARCHAR},
      ALIAS_NAME = #{aliasName,jdbcType=VARCHAR},
      MODEL = #{model,jdbcType=VARCHAR},
      MATERIAL_CODE = #{materialCode,jdbcType=VARCHAR},
      BASE_UNIT_ID = #{baseUnitId,jdbcType=INTEGER},
      CHANGE_NUM = #{changeNum,jdbcType=INTEGER},
      UNIT_ID = #{unitId,jdbcType=INTEGER},
      GROSS_WEIGHT = #{grossWeight,jdbcType=DECIMAL},
      NET_WEIGHT = #{netWeight,jdbcType=DECIMAL},
      GOODS_LENGTH = #{goodsLength,jdbcType=DECIMAL},
      GOODS_WIDTH = #{goodsWidth,jdbcType=DECIMAL},
      GOODS_HEIGHT = #{goodsHeight,jdbcType=DECIMAL},
      PACKAGE_LENGTH = #{packageLength,jdbcType=DECIMAL},
      PACKAGE_WIDTH = #{packageWidth,jdbcType=DECIMAL},
      PACKAGE_HEIGHT = #{packageHeight,jdbcType=DECIMAL},
      GOODS_TYPE = #{goodsType,jdbcType=INTEGER},
      GOODS_LEVEL = #{goodsLevel,jdbcType=INTEGER},
      MANAGE_CATEGORY = #{manageCategory,jdbcType=INTEGER},
      MANAGE_CATEGORY_LEVEL = #{manageCategoryLevel,jdbcType=INTEGER},
      PURCHASE_REMIND = #{purchaseRemind,jdbcType=VARCHAR},
      LICENSE_NUMBER = #{licenseNumber,jdbcType=VARCHAR},
      FIRST_ENGAGE_ID = #{firstEngageId,jdbcType=INTEGER},
      RECORD_NUMBER = #{recordNumber,jdbcType=VARCHAR},
      REGISTRATION_NUMBER = #{registrationNumber,jdbcType=VARCHAR},
      BEGINTIME = #{begintime,jdbcType=BIGINT},
      ENDTIME = #{endtime,jdbcType=BIGINT},
      AUTHORIZATION_CERTIFICATE_URL = #{authorizationCertificateUrl,jdbcType=VARCHAR},
      OTHER_QUALIFICATION_URL = #{otherQualificationUrl,jdbcType=VARCHAR},
      COLOR_PAGE_URL = #{colorPageUrl,jdbcType=VARCHAR},
      TECHNICAL_PARAMETER_URL = #{technicalParameterUrl,jdbcType=VARCHAR},
      INSTRUCTIONS_URL = #{instructionsUrl,jdbcType=VARCHAR},
      BIDDING_DATA_URL = #{biddingDataUrl,jdbcType=VARCHAR},
      PACKING_LIST = #{packingList,jdbcType=VARCHAR},
      TOS = #{tos,jdbcType=VARCHAR},
      TAX_CATEGORY_NO = #{taxCategoryNo,jdbcType=VARCHAR},
      MANUFACTURER = #{manufacturer,jdbcType=VARCHAR},
      PRODUCTION_LICENSE = #{productionLicense,jdbcType=VARCHAR},
      DISCARD_REASON = #{discardReason,jdbcType=VARCHAR},
      DISCARD_TIME = #{discardTime,jdbcType=BIGINT},
      SUPPLY_MODEL = #{supplyModel,jdbcType=VARCHAR},
      STANDARD_CATEGORY_ID = #{standardCategoryId,jdbcType=INTEGER},
      STANDARD_CATEGORY_LEVEL = #{standardCategoryLevel,jdbcType=INTEGER},
      SPEC = #{spec,jdbcType=VARCHAR},
      PRODUCT_ADDRESS = #{productAddress,jdbcType=VARCHAR},
      STORAGE_REQUIREMENTS = #{storageRequirements,jdbcType=TINYINT},
      `SOURCE` = #{source,jdbcType=TINYINT},
      IS_RECOMMED = #{isRecommed,jdbcType=TINYINT},
      REGISTER_NAME = #{registerName,jdbcType=VARCHAR},
      HREF = #{href,jdbcType=VARCHAR},
      JX_MARKET_PRICE = #{jxMarketPrice,jdbcType=DECIMAL},
      JX_SALE_PRICE = #{jxSalePrice,jdbcType=DECIMAL},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      TO_SKU_FLAG = #{toSkuFlag,jdbcType=INTEGER},
      IS_NO_REASON_RETURN = #{isNoReasonReturn,jdbcType=TINYINT},
      TECHNICAL_PARAMETER = #{technicalParameter,jdbcType=LONGVARCHAR},
      PERFORMANCE_PARAMETER = #{performanceParameter,jdbcType=LONGVARCHAR},
      SPEC_PARAMETER = #{specParameter,jdbcType=LONGVARCHAR}
    where GOODS_ID = #{goodsId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.goods.model.GoodsGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:18:06 CST 2019.
    -->
    update T_GOODS
    set COMPANY_ID = #{companyId,jdbcType=INTEGER},
      PARENT_ID = #{parentId,jdbcType=INTEGER},
      CATEGORY_ID = #{categoryId,jdbcType=INTEGER},
      BRAND_ID = #{brandId,jdbcType=INTEGER},
      IS_ON_SALE = #{isOnSale,jdbcType=TINYINT},
      IS_DISCARD = #{isDiscard,jdbcType=TINYINT},
      SKU = #{sku,jdbcType=VARCHAR},
      GOODS_NAME = #{goodsName,jdbcType=VARCHAR},
      ALIAS_NAME = #{aliasName,jdbcType=VARCHAR},
      MODEL = #{model,jdbcType=VARCHAR},
      MATERIAL_CODE = #{materialCode,jdbcType=VARCHAR},
      BASE_UNIT_ID = #{baseUnitId,jdbcType=INTEGER},
      CHANGE_NUM = #{changeNum,jdbcType=INTEGER},
      UNIT_ID = #{unitId,jdbcType=INTEGER},
      GROSS_WEIGHT = #{grossWeight,jdbcType=DECIMAL},
      NET_WEIGHT = #{netWeight,jdbcType=DECIMAL},
      GOODS_LENGTH = #{goodsLength,jdbcType=DECIMAL},
      GOODS_WIDTH = #{goodsWidth,jdbcType=DECIMAL},
      GOODS_HEIGHT = #{goodsHeight,jdbcType=DECIMAL},
      PACKAGE_LENGTH = #{packageLength,jdbcType=DECIMAL},
      PACKAGE_WIDTH = #{packageWidth,jdbcType=DECIMAL},
      PACKAGE_HEIGHT = #{packageHeight,jdbcType=DECIMAL},
      GOODS_TYPE = #{goodsType,jdbcType=INTEGER},
      GOODS_LEVEL = #{goodsLevel,jdbcType=INTEGER},
      MANAGE_CATEGORY = #{manageCategory,jdbcType=INTEGER},
      MANAGE_CATEGORY_LEVEL = #{manageCategoryLevel,jdbcType=INTEGER},
      PURCHASE_REMIND = #{purchaseRemind,jdbcType=VARCHAR},
      LICENSE_NUMBER = #{licenseNumber,jdbcType=VARCHAR},
      FIRST_ENGAGE_ID = #{firstEngageId,jdbcType=INTEGER},
      RECORD_NUMBER = #{recordNumber,jdbcType=VARCHAR},
      REGISTRATION_NUMBER = #{registrationNumber,jdbcType=VARCHAR},
      BEGINTIME = #{begintime,jdbcType=BIGINT},
      ENDTIME = #{endtime,jdbcType=BIGINT},
      AUTHORIZATION_CERTIFICATE_URL = #{authorizationCertificateUrl,jdbcType=VARCHAR},
      OTHER_QUALIFICATION_URL = #{otherQualificationUrl,jdbcType=VARCHAR},
      COLOR_PAGE_URL = #{colorPageUrl,jdbcType=VARCHAR},
      TECHNICAL_PARAMETER_URL = #{technicalParameterUrl,jdbcType=VARCHAR},
      INSTRUCTIONS_URL = #{instructionsUrl,jdbcType=VARCHAR},
      BIDDING_DATA_URL = #{biddingDataUrl,jdbcType=VARCHAR},
      PACKING_LIST = #{packingList,jdbcType=VARCHAR},
      TOS = #{tos,jdbcType=VARCHAR},
      TAX_CATEGORY_NO = #{taxCategoryNo,jdbcType=VARCHAR},
      MANUFACTURER = #{manufacturer,jdbcType=VARCHAR},
      PRODUCTION_LICENSE = #{productionLicense,jdbcType=VARCHAR},
      DISCARD_REASON = #{discardReason,jdbcType=VARCHAR},
      DISCARD_TIME = #{discardTime,jdbcType=BIGINT},
      SUPPLY_MODEL = #{supplyModel,jdbcType=VARCHAR},
      STANDARD_CATEGORY_ID = #{standardCategoryId,jdbcType=INTEGER},
      STANDARD_CATEGORY_LEVEL = #{standardCategoryLevel,jdbcType=INTEGER},
      SPEC = #{spec,jdbcType=VARCHAR},
      PRODUCT_ADDRESS = #{productAddress,jdbcType=VARCHAR},
      STORAGE_REQUIREMENTS = #{storageRequirements,jdbcType=TINYINT},
      `SOURCE` = #{source,jdbcType=TINYINT},
      IS_RECOMMED = #{isRecommed,jdbcType=TINYINT},
      REGISTER_NAME = #{registerName,jdbcType=VARCHAR},
      HREF = #{href,jdbcType=VARCHAR},
      JX_MARKET_PRICE = #{jxMarketPrice,jdbcType=DECIMAL},
      JX_SALE_PRICE = #{jxSalePrice,jdbcType=DECIMAL},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      TO_SKU_FLAG = #{toSkuFlag,jdbcType=INTEGER},
      IS_NO_REASON_RETURN = #{isNoReasonReturn,jdbcType=TINYINT}
    where GOODS_ID = #{goodsId,jdbcType=INTEGER}
  </update>

  <update id="updateGoodsDiscard">
    UPDATE T_GOODS
    SET IS_DISCARD = #{isDiscard,jdbcType=INTEGER}
    WHERE SKU = #{sku,jdbcType=VARCHAR}
  </update>
</mapper>