package com.vedeng.trader.model;

import java.io.Serializable;

/**
 * T_R_TRADER_LABEL_J_TRADER
 * <AUTHOR>
public class RTraderLabelJTrader implements Serializable {
    private Long rTraderLabelJTraderId;

    /**
     * 客户标签id
     */
    private Integer traderLabelId;

    /**
     * 客户id
     */
    private Integer traderId;

    private static final long serialVersionUID = 1L;

    public Long getrTraderLabelJTraderId() {
        return rTraderLabelJTraderId;
    }

    public void setrTraderLabelJTraderId(Long rTraderLabelJTraderId) {
        this.rTraderLabelJTraderId = rTraderLabelJTraderId;
    }

    public Integer getTraderLabelId() {
        return traderLabelId;
    }

    public void setTraderLabelId(Integer traderLabelId) {
        this.traderLabelId = traderLabelId;
    }

    public Integer getTraderId() {
        return traderId;
    }

    public void setTraderId(Integer traderId) {
        this.traderId = traderId;
    }
}