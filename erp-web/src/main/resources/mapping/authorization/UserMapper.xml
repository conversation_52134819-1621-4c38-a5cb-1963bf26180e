<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.authorization.dao.UserMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.authorization.model.User" >
    <id column="USER_ID" property="userId" jdbcType="INTEGER" />
    <result column="COMPANY_ID" property="companyId" jdbcType="INTEGER" />
    <result column="USERNAME" property="username" jdbcType="VARCHAR" />
    <result column="NUMBER" property="number" jdbcType="VARCHAR" />
    <result column="PASSWORD" property="password" jdbcType="VARCHAR" />
    <result column="SALT" property="salt" jdbcType="VARCHAR" />
    <result column="PARENT_ID" property="parentId" jdbcType="INTEGER" />
    <result column="IS_DISABLED" property="isDisabled" jdbcType="INTEGER" />
    <result column="DISABLED_REASON" property="disabledReason" jdbcType="VARCHAR" />
    <result column="LAST_LOGIN_TIME" property="lastLoginTime" jdbcType="BIGINT" />
    <result column="LAST_LOGIN_IP" property="lastLoginIp" jdbcType="VARCHAR" />
    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
    <result column="IS_ADMIN" property="isAdmin" jdbcType="INTEGER" />
    <result column="STAFF" property="staff" jdbcType="INTEGER" />
    <result column="SYSTEM" property="systems" jdbcType="VARCHAR" />
    <result column="USER_BELONG_COMPANY_ID" property="userBelongCompanyId" jdbcType="INTEGER" />

    <result column="TRADER_ID" property="traderId" jdbcType="INTEGER" />
    <result column="COMPANY_NAME" property="companyName" jdbcType="VARCHAR" />
	  <result column="ORG_IDS_LIST" property="orgIdsList" jdbcType="VARCHAR" />
    <result column="CC_NUMBER" property="ccNumber" jdbcType="VARCHAR" />
    <result column="REAL_NAME" property="realName" jdbcType="VARCHAR" />
	  <result column="TT_NUMBER" property="ttNumber" jdbcType="VARCHAR" />
	  <result column="TELECOM_LINE" property="telecomLine" jdbcType="VARCHAR" />
	  <result column="MOBILE_LINE" property="mobileLine" jdbcType="VARCHAR" />
	  <result column="UNICOM_LINE" property="unicomLine" jdbcType="VARCHAR" />
	  <result column="CUSTOMER_LINE" property="customerLine" jdbcType="VARCHAR" />
  </resultMap>

  <sql id="Base_Column_List" >
    USER_ID, COMPANY_ID, USERNAME, NUMBER, PASSWORD, SALT, PARENT_ID, IS_ADMIN, IS_DISABLED, DISABLED_REASON,
    LAST_LOGIN_TIME, LAST_LOGIN_IP, ADD_TIME, CREATOR, MOD_TIME, UPDATER,STAFF,SYSTEM,USER_BELONG_COMPANY_ID
    ,TT_NUMBER,ORG_IDS_LIST
  </sql>

	<select id="getUserByNameAndPositionType" resultMap="BaseResultMap">

		Select u.USER_ID,u.USERNAME from T_USER u
		LEFT JOIN T_R_USER_POSIT pu on u.USER_ID=pu.USER_ID
		LEFT JOIN T_POSITION p on pu.POSITION_ID=p.POSITION_ID
		where p.TYPE=#{positionType} and u.USERNAME=#{userName}
		and u.company_id=1 and u.IS_DISABLED=0
		limit 1
	</select>

	<select id="getUserByIdAndPositionType" resultMap="BaseResultMap">

		Select u.USER_ID,u.USERNAME,u.PARENT_ID from T_USER u
		LEFT JOIN T_R_USER_POSIT pu on u.USER_ID=pu.USER_ID
		LEFT JOIN T_POSITION p on pu.POSITION_ID=p.POSITION_ID
		where p.TYPE=#{positionType} and u.USER_ID=#{userId}
		limit 1
	</select>
  <!-- 添加与员工 -->
  <insert id="insert" parameterType="com.vedeng.authorization.model.User" >
    insert into T_USER
    <trim prefix="(" suffix=")" suffixOverrides="," >
		<if test="userId != null">
			USER_ID,
		</if>
		<if test="companyId != null">
			COMPANY_ID,
		</if>
		<if test="username != null">
			USERNAME,
		</if>
		<if test="number != null">
			NUMBER,
		</if>
		<if test="password != null">
			PASSWORD,
		</if>
		<if test="salt != null">
			SALT,
		</if>
		<if test="parentId != null">
			PARENT_ID,
		</if>
		<if test="isAdmin != null">
			IS_ADMIN,
		</if>
		<if test="isDisabled != null">
			IS_DISABLED,
		</if>
		<if test="disabledReason != null">
			DISABLED_REASON,
		</if>
		<if test="lastLoginTime != null">
			LAST_LOGIN_TIME,
		</if>
		<if test="lastLoginIp != null">
			LAST_LOGIN_IP,
		</if>
		<if test="addTime != null">
			ADD_TIME,
		</if>
		<if test="creator != null">
			CREATOR,
		</if>
		<if test="modTime != null">
			MOD_TIME,
		</if>
		<if test="updater != null">
			UPDATER,
		</if>
		<if test="staff != null">
			STAFF,
		</if>
		<if test="systems != null">
			SYSTEM,
		</if>
		<if test="userBelongCompanyId != null">
			USER_BELONG_COMPANY_ID,
		</if>
		<if test="ttNumber != null">
			TT_NUMBER,
		</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
		<if test="userId != null">
			#{userId,jdbcType=INTEGER},
		</if>
		<if test="companyId != null">
			#{companyId,jdbcType=INTEGER},
		</if>
		<if test="username != null">
			#{username,jdbcType=VARCHAR},
		</if>
		<if test="number != null">
			#{number,jdbcType=VARCHAR},
		</if>
		<if test="password != null">
			#{password,jdbcType=VARCHAR},
		</if>
		<if test="salt != null">
			#{salt,jdbcType=VARCHAR},
		</if>
		<if test="parentId != null">
			#{parentId,jdbcType=INTEGER},
		</if>
		<if test="isAdmin != null">
			#{isAdmin,jdbcType=INTEGER},
		</if>
		<if test="isDisabled != null">
			#{isDisabled,jdbcType=INTEGER},
		</if>
		<if test="disabledReason != null">
			#{disabledReason,jdbcType=VARCHAR},
		</if>
		<if test="lastLoginTime != null">
			#{lastLoginTime,jdbcType=BIGINT},
		</if>
		<if test="lastLoginIp != null">
			#{lastLoginIp,jdbcType=VARCHAR},
		</if>
		<if test="addTime != null">
			#{addTime,jdbcType=BIGINT},
		</if>
		<if test="creator != null">
			#{creator,jdbcType=INTEGER},
		</if>
		<if test="modTime != null">
			#{modTime,jdbcType=BIGINT},
		</if>
		<if test="updater != null">
			#{updater,jdbcType=INTEGER},
		</if>
		<if test="staff != null">
			#{staff,jdbcType=INTEGER},
		</if>
		<if test="systems != null">
			#{systems,jdbcType=VARCHAR},
		</if>
		<if test="userBelongCompanyId != null">
			#{userBelongCompanyId,jdbcType=INTEGER},
		</if>
		<if test="ttNumber != null">
			#{ttNumber,jdbcType=VARCHAR},
		</if>
    </trim>

    <selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="userId">
		SELECT LAST_INSERT_ID() AS userId
    </selectKey>
  </insert>
  <!-- 编辑员工 -->
  <update id="update" parameterType="com.vedeng.authorization.model.User" >
    update T_USER
    <set >
		<if test="companyId != null">
			COMPANY_ID = #{companyId,jdbcType=INTEGER},
		</if>
		<if test="username != null">
			USERNAME = #{username,jdbcType=VARCHAR},
		</if>
		<if test="number != null">
			NUMBER = #{number,jdbcType=VARCHAR},
		</if>
		<if test="password != null and password != ''">
			PASSWORD = #{password,jdbcType=VARCHAR},
		</if>
		<if test="salt != null">
			SALT = #{salt,jdbcType=VARCHAR},
		</if>
		<if test="parentId != null">
			PARENT_ID = #{parentId,jdbcType=INTEGER},
		</if>
		<if test="isAdmin != null">
			IS_ADMIN = #{isAdmin,jdbcType=INTEGER},
		</if>
		<if test="isDisabled != null">
			IS_DISABLED = #{isDisabled,jdbcType=INTEGER},
		</if>
		<if test="disabledReason != null">
			DISABLED_REASON = #{disabledReason,jdbcType=VARCHAR},
		</if>
		<if test="lastLoginTime != null">
			LAST_LOGIN_TIME = #{lastLoginTime,jdbcType=BIGINT},
		</if>
		<if test="lastLoginIp != null">
			LAST_LOGIN_IP = #{lastLoginIp,jdbcType=VARCHAR},
		</if>
		<if test="addTime != null">
			ADD_TIME = #{addTime,jdbcType=BIGINT},
		</if>
		<if test="creator != null">
			CREATOR = #{creator,jdbcType=INTEGER},
		</if>
		<if test="modTime != null">
			MOD_TIME = #{modTime,jdbcType=BIGINT},
		</if>
		<if test="updater != null">
			UPDATER = #{updater,jdbcType=INTEGER},
		</if>
		<if test="staff != null">
			STAFF = #{staff,jdbcType=INTEGER},
		</if>
		<if test="systems != null">
			SYSTEM = #{systems,jdbcType=VARCHAR},
		</if>
		<if test="userBelongCompanyId != null">
			USER_BELONG_COMPANY_ID = #{userBelongCompanyId,jdbcType=VARCHAR},
		</if>
		<if test="ttNumber != null">
			tt_Number = #{ttNumber,jdbcType=VARCHAR},
		</if>
		<if test="ttNumber != null">
			TELECOM_LINE = #{telecomLine,jdbcType=VARCHAR},
		</if>
		<if test="ttNumber != null">
			MOBILE_LINE = #{mobileLine,jdbcType=VARCHAR},
		</if>
		<if test="ttNumber != null">
			UNICOM_LINE = #{unicomLine,jdbcType=VARCHAR},
		</if>
		<if test="ttNumber != null">
			CUSTOMER_LINE = #{customerLine,jdbcType=VARCHAR},
		</if>
    </set>
    where USER_ID = #{userId,jdbcType=INTEGER}
  </update>

  <resultMap type="com.vedeng.authorization.model.User" id="getByUsernameMap" extends="BaseResultMap">
  	<association property="userDetail" javaType="com.vedeng.authorization.model.UserDetail">
  		<id column="USER_DETAIL_ID" property="userDetailId" jdbcType="INTEGER" />
	    <result column="REAL_NAME" property="realName" jdbcType="VARCHAR" />
	    <result column="EMAIL" property="email" jdbcType="VARCHAR" />
	    <result column="SEX" property="sex" jdbcType="INTEGER" />
	    <result column="BIRTHDAY" property="birthday" jdbcType="DATE" />
	    <result column="MOBILE" property="mobile" jdbcType="VARCHAR" />
	    <result column="TELEPHONE" property="telephone" jdbcType="VARCHAR" />
	    <result column="FAX" property="fax" jdbcType="VARCHAR" />
	    <result column="QQ" property="qq" jdbcType="VARCHAR" />
	    <result column="CC_NUMBER" property="ccNumber" jdbcType="VARCHAR" />
  	</association>
  </resultMap>
  <select id="getByUsername" resultMap="getByUsernameMap">
    select
	    a.USER_ID, a.COMPANY_ID, a.USERNAME, a.NUMBER, a.PASSWORD, a.SALT, a.PARENT_ID, a.IS_ADMIN,a.IS_DISABLED, a.DISABLED_REASON,
	    a.LAST_LOGIN_TIME, a.LAST_LOGIN_IP, a.ADD_TIME, a.CREATOR, a.MOD_TIME, a.UPDATER,a.STAFF,a.SYSTEM,a.USER_BELONG_COMPANY_ID,
	    b.USER_DETAIL_ID, b.REAL_NAME, b.EMAIL, b.SEX, b.BIRTHDAY, b.MOBILE, b.TELEPHONE, b.FAX,
	    b.QQ, b.CC_NUMBER,a.TT_NUMBER,a.ORG_IDS_LIST
    from
    	T_USER a
    left join
    	T_USER_DETAIL b on a.USER_ID=b.USER_ID
    where
    	a.USERNAME = #{username,jdbcType=VARCHAR}


   		and a.COMPANY_ID = 1
		limit 1
  </select>
	<select id="getByUsernameEnable" resultMap="getByUsernameMap">
		select
			a.USER_ID, a.COMPANY_ID, a.USERNAME, a.NUMBER, a.PASSWORD, a.SALT, a.PARENT_ID, a.IS_ADMIN,a.IS_DISABLED, a.DISABLED_REASON,
			a.LAST_LOGIN_TIME, a.LAST_LOGIN_IP, a.ADD_TIME, a.CREATOR, a.MOD_TIME, a.UPDATER,a.STAFF,a.SYSTEM,a.USER_BELONG_COMPANY_ID,
			b.USER_DETAIL_ID, b.REAL_NAME, b.EMAIL, b.SEX, b.BIRTHDAY, b.MOBILE, b.TELEPHONE, b.FAX,
			b.QQ, b.CC_NUMBER,a.TT_NUMBER,a.ORG_IDS_LIST
		from
			T_USER a
				left join
			T_USER_DETAIL b on a.USER_ID=b.USER_ID
		where
			a.USERNAME = #{username,jdbcType=VARCHAR}
		  and a.COMPANY_ID = 1
		  order by a.IS_DISABLED
			limit 1
	</select>


  <!-- selectByPrimaryKey -->
  <resultMap type="com.vedeng.authorization.model.User" id="selectByPrimaryKeyResultMap" extends="BaseResultMap">
  	<result column="POSITION_NAME" property="positionName"/>
  	<result column="ORG_NAME" property="orgName"/>
  	<result column="ORG_ID" property="orgId"/>
  	<result column="P_USERNAME" property="pUsername"/>
  	<result column="COMPANY_NAME" property="companyName"/>
    <result column="ORG_IDS_LIST" property="orgIdsList"/>

  	<association property="userDetail" javaType="com.vedeng.authorization.model.UserDetail">
  		<result column="REAL_NAME" property="realName"/>
  		<result column="EMAIL" property="email" />
	    <result column="SEX" property="sex" />
	    <result column="BIRTHDAY" property="birthday" />
	    <result column="MOBILE" property="mobile" />
	    <result column="TELEPHONE" property="telephone" />
	    <result column="FAX" property="fax" />
	    <result column="QQ" property="qq" />
	    <result column="CC_NUMBER" property="ccNumber" />
  	</association>
  	<association property="userAddress" javaType="com.vedeng.authorization.model.UserAddress">
  		<result column="AREA_ID" property="areaId" jdbcType="INTEGER" />
	    <result column="ADDRESS" property="address" jdbcType="VARCHAR" />
  	</association>
  </resultMap>

  <select id="selectByPrimaryKey" resultMap="selectByPrimaryKeyResultMap" parameterType="java.lang.Integer" >
    select
    a.USER_ID, a.COMPANY_ID, a.USERNAME, a.NUMBER, a.SALT, a.PARENT_ID, a.IS_ADMIN,a.IS_DISABLED,a.ADD_TIME,
    a.LAST_LOGIN_TIME,a.DISABLED_REASON,a.PASSWORD,a.STAFF,a.SYSTEM,a.USER_BELONG_COMPANY_ID,a.tt_Number,
    a.TELECOM_LINE,a.MOBILE_LINE,a.UNICOM_LINE,a.CUSTOMER_LINE,
    b.REAL_NAME,b.EMAIL,b.SEX,b.BIRTHDAY,b.MOBILE,b.TELEPHONE,b.FAX,b.QQ,b.CC_NUMBER,
    c.AREA_ID,c.ADDRESS,
    group_concat(e.ORG_NAME) as ORG_NAME,e.ORG_ID,
  	group_concat(d.POSITION_NAME) as POSITION_NAME,
  	f.USERNAME as P_USERNAME,
  	g.COMPANY_NAME,a.ORG_IDS_LIST
    from T_USER a
    left join T_USER_DETAIL b on a.USER_ID=b.USER_ID
    left join T_USER_ADDRESS c on c.USER_ID=a.USER_ID
    left join
  		T_R_USER_POSIT r_u_p
  	on
  		r_u_p.USER_ID = a.USER_ID
  	left join
  		T_POSITION d
  	on
  		d.POSITION_ID = r_u_p.POSITION_ID
  	left join
  		T_ORGANIZATION e
  	on
  		d.ORG_ID=e.ORG_ID
  	left join
  	 	T_USER f
  	on
  		a.PARENT_ID = f.USER_ID
  	left join
  		T_COMPANY g
  	on
  		a.COMPANY_ID=g.COMPANY_ID
    where a.USER_ID = #{userId,jdbcType=INTEGER}
    group by
  		a.USER_ID
  </select>

  <!-- querylistPage -->
  <resultMap type="com.vedeng.authorization.model.User" id="userResultMap" extends="BaseResultMap">
  	<result column="POSITION_NAME" property="positionName"/>
  	<result column="ORG_NAME" property="orgName"/>
  	<result column="COMPANY_NAME" property="companyName"/>
  	<result column="P_USERNAME" property="pUsername"/>
	  <result column="TT_NUMBER" property="ttNumber"/>
	  <result column="TELECOM_LINE" property="telecomLine"/>
	  <result column="MOBILE_LINE" property="mobileLine"/>
	  <result column="UNICOM_LINE" property="unicomLine"/>
	  <result column="CUSTOMER_LINE" property="customerLine"/>
  	<association property="userDetail" javaType="com.vedeng.authorization.model.UserDetail">
  		<id column="USER_DETAIL_ID" property="userDetailId"/>
  		<result column="REAL_NAME" property="realName"/>
  		<result column="EMAIL" property="email" />
	    <result column="SEX" property="sex" />
	    <result column="BIRTHDAY" property="birthday" />
	    <result column="MOBILE" property="mobile" />
	    <result column="TELEPHONE" property="telephone" />
	    <result column="FAX" property="fax" />
	    <result column="QQ" property="qq" />
	    <result column="CC_NUMBER" property="ccNumber" />
  	</association>
  </resultMap>
  <select id="querylistPage" resultMap="userResultMap" parameterType="java.util.Map">
  	select
	  	a.USER_ID, a.COMPANY_ID, a.USERNAME, a.NUMBER, a.PASSWORD, a.SALT, a.PARENT_ID, a.IS_ADMIN,a.IS_DISABLED, a.DISABLED_REASON,
    	a.LAST_LOGIN_TIME, a.LAST_LOGIN_IP, a.ADD_TIME, a.CREATOR, a.MOD_TIME, a.UPDATER,a.STAFF,a.SYSTEM,a.USER_BELONG_COMPANY_ID,
  	       a.TELECOM_LINE,a.MOBILE_LINE,a.UNICOM_LINE,a.CUSTOMER_LINE,
	  	b.USER_DETAIL_ID,b.REAL_NAME,b.CC_NUMBER,
	  	group_concat(d.ORG_NAME) as ORG_NAME,
	  	group_concat(c.POSITION_NAME) as POSITION_NAME,
	  	e.COMPANY_NAME,f.USERNAME AS P_USERNAME,a.tt_number
  	from
  		T_USER a
  	left join
  		T_USER_DETAIL b
  	on
  		a.USER_ID=b.USER_ID
  	left join
  		T_R_USER_POSIT r_u_p
  	on
  		r_u_p.USER_ID = a.USER_ID
  	left join
  		T_POSITION c
  	on
  		c.POSITION_ID = r_u_p.POSITION_ID
  	left join
  		T_ORGANIZATION d
  	on
  		d.ORG_ID=c.ORG_ID
  	left join
  		T_COMPANY e
  	on	a.COMPANY_ID = e.COMPANY_ID
  	LEFT JOIN T_USER f on a.PARENT_ID = f.USER_ID
  	<where>
  		1 = 1
  		<if test="user.realName != null and user.realName != ''">
       	and b.REAL_NAME like CONCAT('%',#{user.realName},'%' )
   		</if>
   		<if test="user.username != null and user.username != ''">
       	and a.USERNAME like CONCAT('%',#{user.username},'%' )
   		</if>
   		<if test="user.orgId != null and user.orgId != 0" >
   		and d.ORG_ID=#{user.orgId}
   		</if>
   		<if test="user.positionId != null and user.positionId != 0" >
   		and c.POSITION_ID=#{user.positionId}
   		</if>
   		<if test="user.isDisabled != null and user.isDisabled != -1">
       	and a.IS_DISABLED = #{user.isDisabled}
   		</if>
   		<if test="user.companyId != null and user.companyId != 0">
   		and a.COMPANY_ID = #{user.companyId}
   		</if>
		<if test="user.staff != null">
			and a.STAFF = #{user.staff}
		</if>
		<if test="user.ccNum != null and user.ccNum != ''">
			and b.CC_NUMBER = #{user.ccNum}
		</if>
		<if test="user.systems != null and user.systems != ''">
			and find_in_set(#{user.systems},a.SYSTEM)
		</if>
		<if test="user.userBelongCompanyId != null and user.userBelongCompanyId != ''">
			and a.USER_BELONG_COMPANY_ID = #{user.userBelongCompanyId}
		</if>
		<if test="user.ttNumber != null and user.ttNumber != ''">
			and a.tt_Number = #{user.ttNumber}
		</if>
		<if test="user.orgIdsList != null and user.orgIdsList != ''" >
			<if test="'1'.equals(user.orgIdsList) or user.orgIdsList == 1" >
				and a.ORG_IDS_LIST IS NOT NULL AND a.ORG_IDS_LIST !=''
			</if>
			<if test="'2'.equals(user.orgIdsList) or user.orgIdsList == 2" >
				and (a.ORG_IDS_LIST IS NULL OR a.ORG_IDS_LIST ='')
			</if>
		</if>
  	</where>
  	group by
  		a.USER_ID
  	order by
  		a.IS_DISABLED,a.MOD_TIME desc
  </select>

  <!-- getAllUser -->
  <select id="getAllUser" resultMap="BaseResultMap" parameterType="com.vedeng.authorization.model.User">
  	select
  	<include refid="Base_Column_List" />
  	from T_USER a
  	<where>
  		1=1
  		<if test="companyId != null and companyId != 0">
   		and a.COMPANY_ID = #{companyId}
   		</if>
   		<if test="isDisabled != null">
   		and a.IS_DISABLED = #{isDisabled}
   		</if>
  	</where>

  	order by
  		a.USERNAME asc
  </select>

  <!-- getUserByPositId -->
  <select id="getUserByPositId" resultMap="BaseResultMap" parameterType="java.lang.Integer">
  	select
  		a.USER_ID,a.USERNAME
  	from
  		T_USER a
  	left join
  		T_R_USER_POSIT b on a.USER_ID=b.USER_ID
  	<where>
  		b.POSITION_ID = #{positionId,jdbcType=INTEGER}
  	</where>
  </select>

  <!-- getUserByRoleId -->
  <select id="getUserByRoleId" resultMap="BaseResultMap" parameterType="java.lang.Integer">
  	select
  		a.USER_ID,a.USERNAME
  	from
  		T_USER a
  	left join
  		T_R_USER_ROLE b on a.USER_ID=b.USER_ID
  	<where>
  		b.ROLE_ID = #{roleId,jdbcType=INTEGER}
  	</where>
  </select>

  <!-- getUser -->
  <select id="getUser" resultMap="BaseResultMap" parameterType="com.vedeng.authorization.model.User">
  	select
  	<include refid="Base_Column_List" />
  	from T_USER a
  	<where>
  		1=1
   		and a.USER_ID = #{userId,jdbcType=INTEGER}
  		<if test="companyId != null and companyId != 0">
   		and a.COMPANY_ID = #{companyId,jdbcType=INTEGER}
   		</if>
   		<if test="username != null and username != ''">
   		and a.USERNAME = #{username,jdbcType=VARCHAR}
   		</if>
   		<if test="number != null and number != ''">
   		and a.NUMBER = #{number,jdbcType=VARCHAR}
   		</if>
  	</where>
  </select>

    <!-- getUserByPositType -->
  <select id="getUserByPositType" resultMap="BaseResultMap">
  	select
  		 DISTINCT a.USER_ID, a.COMPANY_ID, a.USERNAME, a.NUMBER, a.PASSWORD, a.SALT, a.PARENT_ID, a.IS_ADMIN,a.IS_DISABLED, a.DISABLED_REASON,
    	a.LAST_LOGIN_TIME, a.LAST_LOGIN_IP, a.ADD_TIME, a.CREATOR, a.MOD_TIME, a.UPDATER,
    	c.CC_NUMBER
  	from
  		T_USER a
  	left join
  		T_R_USER_POSIT b on a.USER_ID=b.USER_ID
  	left join
  		T_POSITION d ON d.POSITION_ID = b.POSITION_ID
  	left join
		T_USER_DETAIL c on a.USER_ID = c.USER_ID
  	<where>
  		1=1
  		<if test="positionType!=null">
  			and d.TYPE = #{positionType,jdbcType=INTEGER}
  		</if>
  		<if test="companyId != null and companyId != 0">
   			and a.COMPANY_ID = #{companyId,jdbcType=INTEGER}
   		</if>
  		ORDER BY a.USERNAME
  	</where>
  </select>
	<select id="getActiveUserByPositType" resultMap="BaseResultMap">
		select
		DISTINCT a.USER_ID, a.COMPANY_ID, a.USERNAME, a.NUMBER, a.PASSWORD, a.SALT, a.PARENT_ID, a.IS_ADMIN,a.IS_DISABLED, a.DISABLED_REASON,
		a.LAST_LOGIN_TIME, a.LAST_LOGIN_IP, a.ADD_TIME, a.CREATOR, a.MOD_TIME, a.UPDATER,
		c.CC_NUMBER
		from
		T_USER a
		left join
		T_R_USER_POSIT b on a.USER_ID=b.USER_ID
		left join
		T_POSITION d ON d.POSITION_ID = b.POSITION_ID
		left join
		T_USER_DETAIL c on a.USER_ID = c.USER_ID
		<where>
			1=1
			<if test="positionType!=null">
				and d.TYPE = #{positionType,jdbcType=INTEGER}
			</if>

				and a.COMPANY_ID =1
		 		AND IS_DISABLED=0
			ORDER BY a.USERNAME
		</where>
	</select>

	<select id="getUserByPositTypesAndOrgIdList" resultMap="BaseResultMap" parameterType="com.vedeng.authorization.model.vo.UserQueryVo">
		select
			DISTINCT a.USER_ID,  a.USERNAME, a.PARENT_ID
		from
		T_USER a
		left join
		T_R_USER_POSIT b on a.USER_ID=b.USER_ID
		left join
		T_POSITION d ON d.POSITION_ID = b.POSITION_ID
		left join
		T_USER_DETAIL c on a.USER_ID = c.USER_ID
		<where>
			1=1

			<if test="positionTypes != null and positionTypes.size > 0">
				and d.TYPE IN
				<foreach item="positionType" index="index" collection="positionTypes" open="(" separator="," close=")">
					#{positionType}
				</foreach>
			</if>
			<if test="companyId != null">
				and a.COMPANY_ID = #{companyId,jdbcType=INTEGER}
			</if>
			<if test="orgIdList != null and orgIdList.size > 0">
				and d.ORG_ID in
				<foreach item="orgItem" index="index" collection="orgIdList" open="(" separator="," close=")">
					#{orgItem}
				</foreach>
			</if>
			<if test="isDisabled != null">
				and a.IS_DISABLED = #{isDisabled,jdbcType=INTEGER}
			</if>
			ORDER BY a.USERNAME
		</where>
	</select>

  <select id="getUserByPositTypes" resultMap="BaseResultMap" parameterType="com.vedeng.authorization.model.User">
  	select
  		 DISTINCT a.USER_ID, a.COMPANY_ID, a.USERNAME, a.NUMBER, a.PASSWORD, a.SALT, a.PARENT_ID, a.IS_DISABLED, a.DISABLED_REASON,
    	a.LAST_LOGIN_TIME, a.LAST_LOGIN_IP, a.ADD_TIME, a.CREATOR, a.MOD_TIME, a.UPDATER,
    	c.CC_NUMBER
  	from
  		T_USER a
  	left join
  		T_R_USER_POSIT b on a.USER_ID=b.USER_ID
  	left join
  		T_POSITION d ON d.POSITION_ID = b.POSITION_ID
  	left join
		T_USER_DETAIL c on a.USER_ID = c.USER_ID
  	<where>
  		1=1

  		<if test="positionTypes != null and positionTypes.size > 0">
  			and d.TYPE IN
			<foreach item="positionType" index="index" collection="positionTypes" open="(" separator="," close=")">
			  #{positionType}
			</foreach>
  		</if>
  		<if test="companyId != null">
   			and a.COMPANY_ID = #{companyId,jdbcType=INTEGER}
   		</if>
   		<if test="isDisabled != null">
   			and a.IS_DISABLED = #{isDisabled,jdbcType=INTEGER}
   		</if>
		group by a.USER_ID
  		ORDER BY a.USERNAME

  	</where>
  </select>

  <select id="getUserByTraderId" resultMap="BaseResultMap" parameterType="java.lang.Integer">
  	select
  		 DISTINCT a.USER_ID, a.COMPANY_ID,a.PARENT_ID, a.USERNAME, a.NUMBER, a.PASSWORD, a.SALT, a.PARENT_ID, a.IS_DISABLED, a.DISABLED_REASON,
    	a.LAST_LOGIN_TIME, a.LAST_LOGIN_IP, a.ADD_TIME, a.CREATOR, a.MOD_TIME, a.UPDATER
  	from
  		T_USER a
  		left join T_R_TRADER_J_USER tr on tr.USER_ID =a.USER_ID
  		where 1=1
  			and tr.TRADER_ID = #{traderId,jdbcType=INTEGER}
  			and tr.TRADER_TYPE = #{traderType,jdbcType=INTEGER} limit 1
  </select>

  <select id="getUserIdListByTraderId" parameterType="java.lang.Integer" resultType="java.lang.Integer">
  	select DISTINCT a.USER_ID
  	from
  		T_USER a
  		left join T_R_TRADER_J_USER tr on tr.USER_ID =a.USER_ID
  		where 1=1
  			and tr.TRADER_ID = #{traderId,jdbcType=INTEGER}
  			and tr.TRADER_TYPE = #{traderType,jdbcType=INTEGER}
  </select>

  <resultMap type="com.vedeng.authorization.model.User" id="userPositionsMap" extends="BaseResultMap">
  	<collection property="positions" javaType="list" ofType="com.vedeng.authorization.model.Position">
  		<id column="POSITION_ID" property="positionId"/>
  		<result column="ORG_ID" property="orgId"/>
  		<result column="POSITION_NAME" property="positionName" />
	    <result column="TYPE" property="type" />
	    <result column="LEVEL" property="level" />
  	</collection>
  </resultMap>
  <select id="getUserByUserIds" resultMap="userPositionsMap">
  	select
  	A.*,C.*,D.REAL_NAME
  	from T_USER A
  	LEFT JOIN T_R_USER_POSIT B ON A.USER_ID = B.USER_ID
	LEFT JOIN T_POSITION C ON B.POSITION_ID = C.POSITION_ID
	LEFT JOIN T_USER_DETAIL D ON A.USER_ID = D.USER_ID
  	where
  		A.USER_ID in
  		<foreach item="userId" index="index" collection="list" open="(" separator="," close=")">
		  #{userId}
		</foreach>
  	ORDER BY A.USERNAME
  </select>

	<select id="getUserByUserNames" resultMap="BaseResultMap">
		select
		A.*
		from T_USER A
		where
		A.USERNAME in
		<foreach item="userName" index="index" collection="userNameList" open="(" separator="," close=")">
			#{userName,jdbcType=VARCHAR}
		</foreach>
		ORDER BY A.USERNAME
	</select>




  	<select id="getUserListByOrgIdList"  resultMap="BaseResultMap">
		SELECT A.USER_ID, A.COMPANY_ID, A.USERNAME, A.NUMBER, A.PASSWORD, A.SALT, A.PARENT_ID, A.IS_DISABLED, A.DISABLED_REASON,
    		A.LAST_LOGIN_TIME, A.LAST_LOGIN_IP, A.ADD_TIME, A.CREATOR, A.MOD_TIME, A.UPDATER
		FROM T_USER A
			LEFT JOIN T_R_USER_POSIT B ON A.USER_ID = B.USER_ID
			LEFT JOIN T_POSITION C ON B.POSITION_ID = C.POSITION_ID
		WHERE C.ORG_ID IN
		<foreach item="orgId" index="index" collection="orgIds" open="(" separator="," close=")">
		  #{orgId}
		</foreach>
			AND A.IS_DISABLED = 0 and A.COMPANY_ID = #{companyId,jdbcType=INTEGER} order by A.USERNAME
	</select>

	<select id="getUserListByOrgIdListAndPostionType"  resultMap="BaseResultMap">
		SELECT A.USER_ID, A.COMPANY_ID, A.USERNAME, A.NUMBER, A.PASSWORD, A.SALT, A.PARENT_ID, A.IS_DISABLED, A.DISABLED_REASON,
    		A.LAST_LOGIN_TIME, A.LAST_LOGIN_IP, A.ADD_TIME, A.CREATOR, A.MOD_TIME, A.UPDATER
		FROM T_USER A
			LEFT JOIN T_R_USER_POSIT B ON A.USER_ID = B.USER_ID
			LEFT JOIN T_POSITION C ON B.POSITION_ID = C.POSITION_ID AND C.TYPE = #{type}
		WHERE C.ORG_ID IN
		<foreach item="orgId" index="index" collection="orgIds" open="(" separator="," close=")">
		  #{orgId}
		</foreach>
			and A.COMPANY_ID = #{companyId,jdbcType=INTEGER} order by A.USERNAME
	</select>

	<select id="getUserListByOrgId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
		SELECT  A.USER_ID, A.USERNAME
		FROM T_USER A
			LEFT JOIN T_R_USER_POSIT B ON A.USER_ID = B.USER_ID
			LEFT JOIN T_POSITION C ON B.POSITION_ID = C.POSITION_ID
		WHERE C.ORG_ID = #{orgId,jdbcType=INTEGER}
	</select>



	<!--待采购列表-->

	<select id="getUserListByOrgIdcg" parameterType="java.lang.Integer" resultMap="BaseResultMap">
		SELECT DISTINCT A.USER_ID, A.USERNAME
		FROM T_USER A
			LEFT JOIN T_R_USER_POSIT B ON A.USER_ID = B.USER_ID
			LEFT JOIN T_POSITION C ON B.POSITION_ID = C.POSITION_ID
			LEFT JOIN T_R_USER_ROLE T ON A.USER_ID = T.USER_ID
		WHERE C.ORG_ID = #{orgId,jdbcType=INTEGER} and A.IS_DISABLED <![CDATA[ <> ]]> 1 and T.ROLE_ID IN ('7','8','9')
	</select>

	<select id="getUserIdListByOrgId" parameterType="java.lang.Integer" resultType="java.lang.Integer">
		SELECT A.USER_ID
		FROM T_USER A
			LEFT JOIN T_R_USER_POSIT B ON A.USER_ID = B.USER_ID
			LEFT JOIN T_POSITION C ON B.POSITION_ID = C.POSITION_ID
		WHERE C.ORG_ID = #{orgId,jdbcType=INTEGER}
	</select>

	<select id="queryUserListByOrgId" parameterType="java.lang.Integer" resultType="java.lang.String">
		SELECT A.USER_ID
		FROM T_USER A
			LEFT JOIN T_R_USER_POSIT B ON A.USER_ID = B.USER_ID
			LEFT JOIN T_POSITION C ON B.POSITION_ID = C.POSITION_ID
		WHERE C.ORG_ID = #{orgId,jdbcType=INTEGER}
	</select>

	<select id="getUserNameByTraderId" parameterType="java.lang.Integer" resultType="java.lang.String">
		SELECT B.USERNAME
		FROM T_R_TRADER_J_USER A LEFT JOIN T_USER B ON A.USER_ID = B.USER_ID
		WHERE A.TRADER_ID = #{traderId,jdbcType=INTEGER} AND A.TRADER_TYPE = 1
		LIMIT 1
	</select>

	<select id="getUserListByPositType" resultType="com.vedeng.authorization.model.User">
		SELECT A.*
		FROM T_USER A
		     LEFT JOIN T_R_USER_POSIT B ON A.USER_ID = B.USER_ID
		     LEFT JOIN T_POSITION C ON B.POSITION_ID = C.POSITION_ID
		WHERE C.TYPE = #{typeId,jdbcType=INTEGER}
		<if test="companyId != null and companyId != 0">
			and A.COMPANY_ID = #{companyId}
		</if>
	</select>

	<select id="getAllUserList"  parameterType="java.lang.Integer" resultType="com.vedeng.authorization.model.User">
		SELECT A.*,C.CC_NUMBER
		FROM T_USER A
		left join
			T_USER_DETAIL C on A.USER_ID = C.USER_ID
		WHERE A.COMPANY_ID = #{companyId,jdbcType=INTEGER}
		order by A.USERNAME asc
	</select>

	<select id="getUserByCcNumber" resultMap="BaseResultMap">
		select
			a.USER_ID,a.USERNAME
		from
			T_USER a
		left join
			T_USER_DETAIL b on a.USER_ID = b.USER_ID
		where
			b.CC_NUMBER = #{ccNumber}
		  and a.IS_DISABLED=0
		limit 1
	</select>

	<select id="getTraderIdListByUserList" resultType="java.lang.Integer">
		SELECT A.TRADER_ID
		FROM T_R_TRADER_J_USER A
		WHERE A.USER_ID IN
			<foreach collection="userList" item="user" open="(" close=")" separator=",">
				#{user.userId,jdbcType=VARCHAR}
			</foreach>
			 AND A.TRADER_TYPE IN (#{traderType,jdbcType=VARCHAR})
	</select>

	<select id="getTraderIdsByUserList" resultType="java.lang.Integer">
		SELECT A.TRADER_ID
		FROM T_R_TRADER_J_USER A
		WHERE A.USER_ID IN
			<foreach collection="userList" item="user" open="(" close=")" separator=",">
				#{user.userId,jdbcType=VARCHAR}
			</foreach>
			 AND A.TRADER_TYPE = #{traderType,jdbcType=BIT}
	</select>

	<select id="getUserNameByUserId" parameterType="java.lang.Integer" resultType="java.lang.String">
		SELECT A.USERNAME FROM T_USER A WHERE A.USER_ID = #{userId,jdbcType=INTEGER}
	</select>

	<select id="getUserByTraderIdList" parameterType="java.util.List" resultType="com.vedeng.authorization.model.User">
		SELECT B.*,A.TRADER_ID
		FROM T_R_TRADER_J_USER A INNER JOIN T_USER B ON A.USER_ID = B.USER_ID
		<where>
			1=1
				AND A.TRADER_TYPE = #{traderType,jdbcType=BIT}
			<if test="traderIdList != null">
				AND A.TRADER_ID IN
				<foreach item="traderId" index="index" collection="traderIdList" open="(" separator="," close=")">
					#{traderId,jdbcType=INTEGER}
				</foreach>
			</if>
		</where>
	</select>

	<resultMap type="com.vedeng.authorization.model.User" id="getUserParentInfoResult" extends="BaseResultMap">
		<result column="P_USERNAME" property="pUsername"/>
		<result column="P_USER_ID" property="pUserId"/>
	</resultMap>
	<select id="getUserParentInfo" resultMap="getUserParentInfoResult">
		select
			a.USER_ID, a.COMPANY_ID, a.USERNAME,a.PARENT_ID,b.USERNAME as P_USERNAME,b.USER_ID as P_USER_ID
		from
			T_USER a
		left join
			T_USER b
		on
			a.PARENT_ID = b.USER_ID
		where
			a.USERNAME = #{username,jdbcType=VARCHAR}
			and a.IS_DISABLED=0
   		and
   			a.COMPANY_ID = #{companyId,jdbcType=INTEGER}
		limit 1
	</select>

	<select id="getUserParentInfoByUserId" resultMap="getUserParentInfoResult">
		select
			a.USER_ID, a.COMPANY_ID, a.USERNAME,a.PARENT_ID,b.USERNAME as P_USERNAME,b.USER_ID as P_USER_ID
		from
			T_USER a
		left join
			T_USER b
		on
			a.PARENT_ID = b.USER_ID
		where
			a.USER_ID = #{userId,jdbcType=INTEGER}
	</select>

	<select id="getUserByOrgIdsAndPositLevel" resultMap="BaseResultMap">
		select
			a.USER_ID, a.COMPANY_ID, a.USERNAME,a.IS_DISABLED
		from
			T_USER a
		left join
			T_R_USER_POSIT b
		on
			a.USER_ID = b.USER_ID
		left join
			T_POSITION c
		on
			b.POSITION_ID = c.POSITION_ID
		where
			1=1
			<if test="level != null">
				and c.LEVEL = #{level,jdbcType=INTEGER}
			</if>
			<if test="orgIds != null">
				and	c.ORG_ID in
				<foreach collection="orgIds" item="orgId" index="index"
		           open="(" close=")" separator=",">
		           #{orgId}
		       </foreach>
			</if>
	</select>

	<select id="getUserInfoByTraderId" resultType="com.vedeng.authorization.model.User">
		select
			b.USER_ID,b.USERNAME,d.ORG_ID,e.ORG_NAME
		from
			T_R_TRADER_J_USER a
		left join
			T_USER b
		on
			a.USER_ID = b.USER_ID
		left join
			T_R_USER_POSIT c
		on
			b.USER_ID = c.USER_ID
		left join
			T_POSITION d
		on
			d.POSITION_ID = c.POSITION_ID
		left join
			T_ORGANIZATION e
		on
			d.ORG_ID = e.ORG_ID
		where
			a.TRADER_ID = #{traderId}
			and
			a.TRADER_TYPE = #{traderType}
			limit 1
	</select>
	<!--所属类型TRADER_TYPE 1::经销商（包含终端）2:供应商-->
    <select id="getUserInfoByMobile" resultType="com.vedeng.authorization.model.User">
	SELECT
	B.`TRADER_ID`,
	C.`USER_ID`,
	D.`USERNAME`,
	F.`ORG_ID`,
	G.`ORG_NAME`
	FROM
	T_WEB_ACCOUNT A
	LEFT JOIN T_TRADER_CONTACT B
	ON A.`TRADER_CONTACT_ID` = B.`TRADER_CONTACT_ID`
	LEFT JOIN T_R_TRADER_J_USER C
	ON B.`TRADER_ID` = C.`TRADER_ID`
	LEFT JOIN T_USER D
	ON D.`USER_ID` = C.`USER_ID`
	LEFT JOIN T_R_USER_POSIT E
	ON E.`USER_ID` = D.`USER_ID`
	LEFT JOIN T_POSITION F
	ON F.`POSITION_ID` = E.`POSITION_ID`
	LEFT JOIN T_ORGANIZATION G
	ON F.`ORG_ID` = G.`ORG_ID`
	WHERE A.`MOBILE` = #{traderContactMobile}

	AND C.TRADER_TYPE = #{traderType}
	LIMIT 1
	</select>
	 <select id="getBDUserInfoByMobile" resultType="com.vedeng.authorization.model.User">
	SELECT
	A.`TRADER_ID`,
	A.`USER_ID`,
	D.`USERNAME`,
	F.`ORG_ID`,
	G.`ORG_NAME`
	FROM
	T_WEB_ACCOUNT A
	LEFT JOIN T_USER D
	ON D.`USER_ID` = A.`USER_ID`
	LEFT JOIN T_R_USER_POSIT E
	ON E.`USER_ID` = D.`USER_ID`
	LEFT JOIN T_POSITION F
	ON F.`POSITION_ID` = E.`POSITION_ID`
	LEFT JOIN T_ORGANIZATION G
	ON F.`ORG_ID` = G.`ORG_ID`
	WHERE A.`MOBILE` = #{phone}

	LIMIT 1
	</select>
	<select id="getUserInfoById" resultType="com.vedeng.authorization.model.User">
	SELECT
	B.`TRADER_ID`,
	C.`USER_ID`,
	D.`USERNAME`,
	F.`ORG_ID`,
	G.`ORG_NAME`
	FROM
	T_WEB_ACCOUNT A
	LEFT JOIN T_TRADER_CONTACT B
	ON A.`TRADER_CONTACT_ID` = B.`TRADER_CONTACT_ID`
	LEFT JOIN T_R_TRADER_J_USER C
	ON B.`TRADER_ID` = C.`TRADER_ID`
	LEFT JOIN T_USER D
	ON D.`USER_ID` = C.`USER_ID`
	LEFT JOIN T_R_USER_POSIT E
	ON E.`USER_ID` = D.`USER_ID`
	LEFT JOIN T_POSITION F
	ON F.`POSITION_ID` = E.`POSITION_ID`
	LEFT JOIN T_ORGANIZATION G
	ON F.`ORG_ID` = G.`ORG_ID`
	WHERE A.`ERP_ACCOUNT_ID`= #{erpAccountId}
	AND C.TRADER_TYPE = #{traderType}
	LIMIT 1
	</select>
	<select id="getBDUserInfoById" resultType="com.vedeng.authorization.model.User">
	SELECT
	B.`TRADER_ID`,
	A.`USER_ID`,
	D.`USERNAME`,
	F.`ORG_ID`,
	G.`ORG_NAME`
	FROM
	T_WEB_ACCOUNT A
	LEFT JOIN T_TRADER_CONTACT B
	ON A.`TRADER_CONTACT_ID` = B.`TRADER_CONTACT_ID`
	LEFT JOIN T_USER D
	ON D.`USER_ID` = A.`USER_ID`
	LEFT JOIN T_R_USER_POSIT E
	ON E.`USER_ID` = D.`USER_ID`
	LEFT JOIN T_POSITION F
	ON F.`POSITION_ID` = E.`POSITION_ID`
	LEFT JOIN T_ORGANIZATION G
	ON F.`ORG_ID` = G.`ORG_ID`
	WHERE A.`ERP_ACCOUNT_ID`= #{erpAccountId}
	LIMIT 1
	</select>
	<select id="getTraderUserAndOrgList" parameterType="java.util.List" resultType="com.vedeng.authorization.model.User">
		SELECT A.USER_ID,
		       B.USERNAME,
		       E.ORG_NAME,
		       A.TRADER_ID
		FROM T_R_TRADER_J_USER A
		     INNER JOIN T_USER B ON A.USER_ID = B.USER_ID
		     LEFT JOIN T_R_USER_POSIT C ON B.USER_ID = C.USER_ID
		     LEFT JOIN T_POSITION D ON C.POSITION_ID = D.POSITION_ID
		     LEFT JOIN T_ORGANIZATION E ON D.ORG_ID = E.ORG_ID
		WHERE A.TRADER_ID IN
			<foreach collection="traderIdList" item="traderId" open="(" close=")" separator=",">
				#{traderId,jdbcType=INTEGER}
			</foreach>
			AND A.TRADER_TYPE = 1
	</select>

	<!-- 根据部门IDlist查询部门信息 -->
	<select id="getOrgNameByOrgIdList" resultType="com.vedeng.authorization.model.Organization" parameterType="java.util.List">
		SELECT *
	  	FROM T_ORGANIZATION
	  	<where>
        	ORG_ID IN
        	<foreach collection="orgIdList" item="orgId" open="(" close=")" separator=",">
	        	#{orgId,jdbcType=INTEGER}
        	</foreach>
	  	</where>
	</select>
	<resultMap type="com.vedeng.authorization.model.vo.UserVo" id="userOrgListMap" extends="BaseResultMap">
		<result column="ORG_NAME" property="orgName"/>
	</resultMap>
	<select id="getCategoryUserList" parameterType="java.util.List" resultType="com.vedeng.authorization.model.User">
		SELECT GROUP_CONCAT(B.USERNAME) AS USERNAME, A.CATEGORY_ID
		FROM T_R_CATEGORY_J_USER A INNER JOIN T_USER B ON A.USER_ID = B.USER_ID
		WHERE A.CATEGORY_ID IN
			<foreach collection="categoryIdList" item="categoryId" open="(" close=")" separator=",">
				#{categoryId,jdbcType=INTEGER}
			</foreach>
			AND B.COMPANY_ID = #{companyId,jdbcType=INTEGER}
		GROUP BY A.CATEGORY_ID
	</select>
	<select id="getUserOrgList" parameterType="java.util.List" resultMap="userOrgListMap">
		SELECT tu.USER_ID,tu.USERNAME,org.ORG_NAME,org.ORG_ID FROM T_USER tu
			LEFT JOIN T_R_USER_POSIT trp ON tu.USER_ID=trp.USER_ID
			LEFT JOIN T_POSITION tp ON tp.POSITION_ID=trp.POSITION_ID
			LEFT JOIN T_ORGANIZATION org ON tp.ORG_ID=org.ORG_ID
			<if test="groupUserVoList != null">
				WHERE tu.USER_ID IN
				<foreach collection="groupUserVoList" item="voList" open="(" close=")" separator=",">
					#{voList.userId,jdbcType=INTEGER}
				</foreach>
			</if>
			GROUP BY tu.USER_ID
			ORDER BY org.ORG_ID ASC
	</select>
	<select id="getUserlistpage" parameterType="Map" resultMap="userOrgListMap">
		SELECT
			DISTINCT tu.USER_ID,tu.USERNAME,org.ORG_NAME,org.ORG_ID
		FROM
			T_USER tu
		LEFT JOIN T_R_USER_POSIT up ON tu.USER_ID = up.USER_ID
		LEFT JOIN T_POSITION po ON up.POSITION_ID=po.POSITION_ID
		LEFT JOIN T_ORGANIZATION org ON org.ORG_ID = po.ORG_ID
		WHERE tu.IS_DISABLED=0
		<if test="userVo.username!=null and userVo.username!=''">
			AND tu.USERNAME like CONCAT('%',#{userVo.username,jdbcType=VARCHAR},'%')
		</if>
		<if test="userVo.orgId!=null">
			AND	org.ORG_ID=#{userVo.orgId,jdbcType=INTEGER}
		</if>
		GROUP BY tu.USER_ID
		ORDER BY org.ORG_ID ASC
	</select>

	<select id="getSaleUserOrgList" parameterType="java.lang.Integer" resultType="com.vedeng.authorization.model.User">
		SELECT
		a.USERNAME,
		a.USER_ID,
		a.PARENT_ID,

		IF (
		e.`LEVEL` = 4,
		e.ORG_ID,
		d.ORG_ID
		) orgId2,

		IF (d.`LEVEL` = 5, d.ORG_ID, 0) orgId3,

		IF (
		e.`LEVEL` = 4,
		e.ORG_NAME,
		d.ORG_NAME
		) orgName2,

		IF (d.`LEVEL` = 5, d.ORG_NAME, '') orgName3
		FROM
		T_USER a
		LEFT JOIN T_R_USER_POSIT b ON a.USER_ID = b.USER_ID
		LEFT JOIN T_POSITION c ON b.POSITION_ID = c.POSITION_ID
		LEFT JOIN T_ORGANIZATION d ON c.ORG_ID = d.ORG_ID
		LEFT JOIN T_ORGANIZATION e ON d.PARENT_ID = e.ORG_ID
		WHERE
		d.TYPE = 310 AND a.IS_DISABLED = 0 AND a.COMPANY_ID = #{companyId,jdbcType=INTEGER}
		GROUP BY a.USER_ID
	</select>

	<select id="getSaleUserOrgListAll" parameterType="java.lang.Integer" resultType="com.vedeng.authorization.model.User">
		SELECT
		a.USERNAME,
		a.USER_ID,
		a.PARENT_ID,

		IF (
		e.`LEVEL` = 4,
		e.ORG_ID,
		d.ORG_ID
		) orgId2,

		IF (d.`LEVEL` = 5, d.ORG_ID, 0) orgId3,

		IF (
		e.`LEVEL` = 4,
		e.ORG_NAME,
		d.ORG_NAME
		) orgName2,

		IF (d.`LEVEL` = 5, d.ORG_NAME, '') orgName3
		FROM
		T_USER a
		LEFT JOIN T_R_USER_POSIT b ON a.USER_ID = b.USER_ID
		LEFT JOIN T_POSITION c ON b.POSITION_ID = c.POSITION_ID
		LEFT JOIN T_ORGANIZATION d ON c.ORG_ID = d.ORG_ID
		LEFT JOIN T_ORGANIZATION e ON d.PARENT_ID = e.ORG_ID
		WHERE
		d.TYPE = 310 AND a.IS_DISABLED = 0 AND a.COMPANY_ID = #{companyId,jdbcType=INTEGER}
	</select>

	<select id="getOrgIdsByUserId" resultType="com.vedeng.authorization.model.User">
		SELECT
			a.USER_ID,
			e.ORG_NAME AS orgName2,
			d.ORG_NAME AS orgName3,
			IF(
				e.`LEVEL` = 4,
				e.ORG_ID,
				d.ORG_ID
			) AS ORGID2,
			IF(d.`LEVEL` = 5, d.ORG_ID, 0) AS ORGID3,
		 	a.USERNAME
		FROM
			T_USER a
			LEFT JOIN T_R_USER_POSIT b ON a.USER_ID = b.USER_ID
			LEFT JOIN T_POSITION c ON b.POSITION_ID = c.POSITION_ID
			LEFT JOIN T_ORGANIZATION d ON c.ORG_ID = d.ORG_ID
			LEFT JOIN T_ORGANIZATION e ON d.PARENT_ID = e.ORG_ID
		WHERE
			 1 = 1
	    		AND a.USER_ID = #{userId, jdbcType=INTEGER}
				AND a.COMPANY_ID = #{companyId, jdbcType=INTEGER}
	</select>
	<!-- 根据userId的list批量查询用户信息 -->
	<select id="getUserListByUserIdList" parameterType="java.util.List" resultMap="BaseResultMap">
		SELECT
			<include refid="Base_Column_List" />
		FROM T_USER
		WHERE
			USER_ID IN
			<foreach collection="userIdList" item="userId" open="(" close=")" separator=",">
				#{userId,jdbcType=INTEGER}
			</foreach>
	</select>


	<resultMap id="UserMap" type="com.vedeng.authorization.model.User" >
		<id column="USER_ID" property="userId" jdbcType="INTEGER" />
		<result column="USERNAME" property="username" jdbcType="VARCHAR" />
	</resultMap>
	<select id="selectAllAssignUser"   resultMap="UserMap">
	select distinct U.USER_ID,LOWER(U.USERNAME) USERNAME from T_USER U LEFT JOIN T_R_USER_ROLE RB ON U.USER_ID=RB.USER_ID
	LEFT JOIN T_ROLE R ON RB.ROLE_ID=R.ROLE_ID
	WHERE R.ROLE_NAME IN ('产品专员','产品主管','产品总监')
	AND U.IS_DISABLED='0'
	ORDER BY USERNAME
	</select>


	<select id="selectAllcheckPerson"   resultMap="UserMap">
	select distinct U.USER_ID,U.USERNAME from T_USER U LEFT JOIN T_R_USER_ROLE RB ON U.USER_ID=RB.USER_ID
	LEFT JOIN T_ROLE R ON RB.ROLE_ID=R.ROLE_ID
	WHERE R.ROLE_NAME IN ('供应主管','产品总监')
	AND U.IS_DISABLED='0'
	ORDER BY USERNAME
	</select>


	<select id="getSubUserList"  resultMap="BaseResultMap">
		SELECT
		<include refid="Base_Column_List" />
		FROM T_USER
		WHERE
		PARENT_ID = #{userId, jdbcType=INTEGER} AND a.COMPANY_ID = #{companyId, jdbcType=INTEGER}
	</select>

    <select id="getUserByTraderCustomerId" parameterType="java.lang.Integer" resultType="com.vedeng.authorization.model.User">
		SELECT b.USER_ID,
			   b.USERNAME,
			   d.ORG_ID,
			   e.ORG_NAME
		FROM T_R_TRADER_J_USER a
			 INNER JOIN T_TRADER_CUSTOMER f ON a.TRADER_ID = f.TRADER_ID
			 LEFT JOIN T_USER b ON a.USER_ID = b.USER_ID
			 LEFT JOIN T_R_USER_POSIT c ON b.USER_ID = c.USER_ID
			 LEFT JOIN T_POSITION d ON d.POSITION_ID = c.POSITION_ID
			 LEFT JOIN T_ORGANIZATION e ON d.ORG_ID = e.ORG_ID
		WHERE f.TRADER_CUSTOMER_ID = #{traderCustomerId,jdbcType=INTEGER} AND a.TRADER_TYPE = #{traderType, jdbcType=INTEGER}
		LIMIT 1
	</select>

    <!--查出相应角色的userid-->
	<select id="getRoleUserId" resultType="com.vedeng.authorization.model.User" >
		SELECT USER_ID FROM T_R_USER_ROLE trur
         INNER JOIN T_ROLE tr
         ON trur.`ROLE_ID`=tr.`ROLE_ID`
        WHERE tr.`ROLE_ID`=#{roleId}
	</select>
    <!--查出用户是否禁用-->
	<select id="getIsDisabled" resultType="com.vedeng.authorization.model.User">
	SELECT IS_DISABLED FROM T_USER
    WHERE USER_ID=#{userId}
	</select>


	<select id="getUserByName" parameterType="java.lang.String" resultType="com.vedeng.authorization.model.User">
		SELECT A.*, D.ORG_NAME, D.ORG_ID
		FROM T_USER A
		     LEFT JOIN T_R_USER_POSIT B ON A.USER_ID = B.USER_ID
		     LEFT JOIN T_POSITION C ON B.POSITION_ID = C.POSITION_ID
		     LEFT JOIN T_ORGANIZATION D ON C.ORG_ID = D.ORG_ID
		WHERE A.USERNAME = #{userName,jdbcType=VARCHAR}
		and A.IS_DISABLED=0 and A.company_id=1
		LIMIT 1
	</select>
	<!--根据归属人查找是否存在-->
	<select id="getUserId" parameterType="java.lang.Integer" resultType="java.lang.Integer">
		select spu.SPU_ID
		from
		V_CORE_SPU spu
		left join T_USER m ON spu.ASSIGNMENT_MANAGER_ID = m.USER_ID
		left join T_USER a ON spu.ASSIGNMENT_ASSISTANT_ID = a.USER_ID
		where spu.ASSIGNMENT_MANAGER_ID=#{proUserId} or spu.ASSIGNMENT_ASSISTANT_ID=#{proUserId}
	</select>

	<select id="getUserByName2" resultMap="BaseResultMap" parameterType="com.vedeng.authorization.model.User">
		select
		<include refid="Base_Column_List" />
		from T_USER a
		<where>
			1=1
				and a.USERNAME = #{username,jdbcType=VARCHAR}
			and a.IS_DISABLED=0
				and a.COMPANY_ID = #{companyId,jdbcType=INTEGER}
		</where>
	</select>
    <select id="getUserByNumber" resultMap="BaseResultMap" parameterType="com.vedeng.authorization.model.User">
        select
        <include refid="Base_Column_List" />
        from T_USER a
        <where>
            1=1
            and a.NUMBER = #{number,jdbcType=VARCHAR}
            and a.IS_DISABLED=0
            and a.COMPANY_ID = #{companyId,jdbcType=INTEGER}
        </where>
limit 1
    </select>

	<!--通过userid查出该职位部门-->
	<select id="userPositionOrganization" resultType="com.vedeng.authorization.model.Position" parameterType="java.lang.Integer">
		SELECT tp.`ORG_ID`,tp.`POSITION_ID`,tp.`POSITION_NAME` FROM
       T_USER tu LEFT JOIN T_R_USER_POSIT tr ON tu.`USER_ID`=tr.`USER_ID`
       LEFT JOIN T_POSITION tp ON tr.`POSITION_ID`=tp.`POSITION_ID`
       WHERE tu.`USER_ID`=#{userId}
       and  COMPANY_ID=#{companyId}
	</select>
	<select id="getNewUserByTraderId" resultMap="BaseResultMap" parameterType="java.lang.Integer">
  	select
  		 DISTINCT a.USER_ID, a.COMPANY_ID, a.USERNAME, a.NUMBER, a.PASSWORD, a.SALT, a.PARENT_ID, a.IS_DISABLED, a.DISABLED_REASON,
    	a.LAST_LOGIN_TIME, a.LAST_LOGIN_IP, a.ADD_TIME, a.CREATOR, a.MOD_TIME, a.UPDATER,UD.REAL_NAME,UD.MOBILE
  	from
  		T_USER a
  		left join T_R_TRADER_J_USER tr on tr.USER_ID =a.USER_ID
		left join T_USER_DETAIL UD ON a.USER_ID=UD.USER_ID
  		where 1=1
  			and tr.TRADER_ID = #{traderId,jdbcType=INTEGER}
  			and tr.TRADER_TYPE=1
  		LIMIT 1
  </select>
	<select id="getUserByUserId" resultType="com.vedeng.authorization.model.User">
		SELECT * FROM T_USER WHERE USER_ID = #{userId}
	</select>
    <select id="getUserNameByUserIdList" resultType="java.lang.String">
		SELECT USERNAME FROM T_USER WHERE USER_ID IN
		<foreach collection="list" index="index" item="item" open="(" close=")" separator=",">
			#{item}
		</foreach>
	</select>

	<select id="getAssistantBySkuId" resultType="java.lang.String">
		select USERNAME from T_USER T join V_CORE_SPU P on T.USER_ID=P.ASSIGNMENT_ASSISTANT_ID join V_CORE_SKU K on P.SPU_ID=K.SPU_ID where SKU_ID = #{skuId,jdbcType=INTEGER}
	</select>


	<select id="getManagerBySkuId" resultType="java.lang.String">
		select USERNAME from T_USER T join V_CORE_SPU P on T.USER_ID=P.ASSIGNMENT_MANAGER_ID join V_CORE_SKU K on P.SPU_ID=K.SPU_ID where SKU_ID = #{skuId,jdbcType=INTEGER}
	</select>
	<select id="getUserInfoByUserId" resultType="com.vedeng.authorization.model.User">
		SELECT A.*, D.ORG_NAME
		FROM T_USER A
		     LEFT JOIN T_R_USER_POSIT B ON A.USER_ID = B.USER_ID
		     LEFT JOIN T_POSITION C ON B.POSITION_ID = C.POSITION_ID
		     LEFT JOIN T_ORGANIZATION D ON C.ORG_ID = D.ORG_ID
		WHERE A.USER_ID = #{userId,jdbcType=INTEGER}
		LIMIT 1
	</select>

	<select id="getAllBuyorderUser" resultMap="UserMap">
		SELECT DISTINCT
			T1.USER_ID,
			T2.USERNAME
		FROM
			T_BUYORDER T1
			LEFT JOIN T_USER T2 ON T1.USER_ID = T2.USER_ID
		WHERE
			T1.USER_ID != 0
			AND T2.USERNAME IS NOT NULL
		order by T2.USERNAME
	</select>

	<select id="getAllEntryUser"  resultMap="UserMap">
		SELECT DISTINCT
			T1.CREATOR USER_ID,
			T2.USERNAME
		FROM
			T_INVOICE T1
			LEFT JOIN T_USER T2 ON T1.CREATOR = T2.USER_ID
		WHERE  T1.CREATOR != 0
order by T2.USERNAME
	</select>

    <select id="getRealNameByUserName" resultType="java.lang.String">
		SELECT
			T2.REAL_NAME
		FROM
			T_USER T1
			INNER JOIN T_USER_DETAIL T2 ON T1.USER_ID = T2.USER_ID
		WHERE     T1.COMPANY_ID=1 AND
			T1.USERNAME = #{userName,jdbcType=VARCHAR}
		   ORDER BY T1.IS_DISABLED ASC
		LIMIT 1
	</select>

	<select id="countUserRoles" parameterType="com.vedeng.authorization.model.vo.UserRoleQuery" resultType="java.lang.Integer">
		SELECT count(1) FROM T_USER U
		LEFT JOIN T_R_USER_ROLE UR ON U.USER_ID=UR.USER_ID
		LEFT JOIN T_ROLE R ON UR.ROLE_ID=R.ROLE_ID
		WHERE U.USER_ID=#{userId} and R.ROLE_NAME IN
		<foreach collection="roleNames" item="name" open="(" close=")" separator=",">
			#{name}
		</foreach>
	</select>

	<select id="selectAllAfterSaleDirector"   resultMap="UserMap">
		select distinct U.USER_ID,U.USERNAME from T_USER U LEFT JOIN T_R_USER_ROLE RB ON U.USER_ID=RB.USER_ID
		LEFT JOIN T_ROLE R ON RB.ROLE_ID=R.ROLE_ID
		WHERE R.ROLE_NAME IN ('售后总监')
		AND U.IS_DISABLED='0'
		ORDER BY USERNAME
	</select>
	<select id="getAllValidUserByParentId" resultType="com.vedeng.authorization.model.User">
		SELECT * FROM T_USER WHERE COMPANY_ID = #{companyId,jdbcType=INTEGER} AND IS_DISABLED = 0
	</select>


	<select id="getAllSubordinateByParentId" resultType="java.lang.Integer">
		<![CDATA[
		SELECT TORG.USER_ID
		FROM (
				 SELECT @ids AS ids,
						(SELECT @ids := GROUP_CONCAT(USER_ID)
				 FROM T_USER
				 WHERE FIND_IN_SET(PARENT_ID, @ids)) AS cids,
			@l := @l + 1 AS LEVEL
		FROM T_USER,
			(SELECT @ids := #{userId,jdbcType=INTEGER}, @l := 0) b
		WHERE @ids IS NOT NULL AND @L < 10
			) ID,
			T_USER TORG
		WHERE FIND_IN_SET(TORG.USER_ID, ID.ids) AND TORG.IS_DISABLED=0
		ORDER BY ID.LEVEL
		]]>
	</select>

	<select id="getRolesCountByUserIdAndOrgId" resultType="java.lang.Integer">
		SELECT
			COUNT(*)
		FROM
			T_R_USER_POSIT RP
		JOIN T_POSITION P ON RP.POSITION_ID = P.POSITION_ID
		WHERE
			RP.USER_ID = #{userId,jdbcType=INTEGER}
		AND P.ORG_ID IN
		<foreach collection="orgIdList" index="index" item="item" open="(" close=")" separator=",">
			#{item,jdbcType=INTEGER}
		</foreach>
	</select>
	<select id="getUserByUserIdSelective" resultType="com.vedeng.authorization.model.User">
		SELECT U.* FROM T_USER U
		LEFT JOIN T_R_USER_POSIT UP ON U.USER_ID = UP.USER_ID
		LEFT JOIN T_POSITION P ON UP.POSITION_ID = P.POSITION_ID
		WHERE
			1=1
		AND
			P.POSITION_NAME = "订单助理"
		AND
		  U.IS_DISABLED = 0
		<if test="orderAssitantUserId != null">
			AND U.USER_ID = #{orderAssitantUserId,jdbcType = INTEGER}
		</if>
	</select>

	<select id="getDepartAndPositionInfo" resultType="com.vedeng.todolist.model.UserInfo">
		SELECT
			(SELECT TITLE FROM T_SYS_OPTION_DEFINITION WHERE SYS_OPTION_DEFINITION_ID = P.LEVEL) AS positionTypeName,
			(SELECT TITLE FROM T_SYS_OPTION_DEFINITION WHERE SYS_OPTION_DEFINITION_ID = OA.TYPE) AS departTpyeName
		FROM T_R_USER_POSIT RP
		LEFT JOIN T_POSITION P ON RP.POSITION_ID = P.POSITION_ID
		LEFT JOIN T_ORGANIZATION OA ON OA.ORG_ID = P.ORG_ID
		WHERE RP.USER_ID = #{userId,jdbcType=INTEGER} AND P.TYPE = 311 LIMIT 1
	</select>
	<select id="getProductUserByRoleName" resultType="com.vedeng.authorization.model.User">
		select distinct U.USER_ID,LOWER(U.USERNAME) USERNAME from T_USER U LEFT JOIN T_R_USER_ROLE RB ON U.USER_ID=RB.USER_ID
		LEFT JOIN T_ROLE R ON RB.ROLE_ID=R.ROLE_ID
		WHERE R.ROLE_NAME IN ('产品专员','产品主管')
		AND U.IS_DISABLED='0'
		ORDER BY USERNAME
	</select>
	
	<select id="getUserByRoleName" resultType="com.vedeng.authorization.model.User">
		SELECT 
		    distinct(u.USER_ID) userId,
		    u.USERNAME username,
		    u.NUMBER number,
		    ud.REAL_NAME realName,
		    ud.MOBILE mobile
		FROM
		    T_USER u
		        LEFT JOIN
		    T_USER_DETAIL ud ON u.USER_ID = ud.USER_ID
		        INNER JOIN
		    T_R_USER_ROLE ur ON u.USER_ID = ur.USER_ID
		        INNER JOIN
		    T_ROLE r ON ur.ROLE_ID = r.ROLE_ID
		WHERE 
		        u.IS_DISABLED = 0
		        AND u.COMPANY_ID = 1
		        <if test="roleName != null and roleName!= ''">
					AND r.ROLE_NAME LIKE CONCAT('%',#{roleName,jdbcType=VARCHAR},'%' )
				</if>
		ORDER BY u.USERNAME
	</select>

	<select id="getPurchaseUserListByUserIdList" parameterType="java.util.List" resultType="com.vedeng.authorization.model.User">
		SELECT
			USER_ID,
			USERNAME,
			(
				SELECT OA.ORG_NAME
				FROM T_R_USER_POSIT RP
				LEFT JOIN T_POSITION P ON RP.POSITION_ID = P.POSITION_ID
				LEFT JOIN T_ORGANIZATION OA ON OA.ORG_ID = P.ORG_ID
				WHERE RP.USER_ID = T.USER_ID AND P.TYPE = 311 LIMIT 1
			) AS orgName
		FROM T_USER T
		WHERE
			T.USER_ID IN
			<foreach collection="userIdList" item="userId" open="(" close=")" separator=",">
				#{userId,jdbcType=INTEGER}
			</foreach>
	</select>


    <select id="getOrderAsisitIdByOrgId" resultType="java.lang.Integer">
        select
          a.USER_ID
        from T_USER a
        left join T_R_USER_POSIT b on a.USER_ID = b.USER_ID
        left join T_POSITION c on b.POSITION_ID = c.POSITION_ID
        where c.POSITION_NAME = '订单助理' and c.ORG_ID in
            <foreach collection="orgIdList" item="orgId" index="index" open="(" close=")" separator=",">
                #{orgId}
            </foreach>
    </select>


	<select id="getUserParentDetailInfoByUserId"  resultType="com.vedeng.authorization.model.User">
		SELECT
			b.USERNAME,
			b.USER_ID,
			c.EMAIL,
			e.LEVEL as positLevel,
			e.POSITION_NAME
		FROM
			T_USER a
		LEFT JOIN T_USER b ON a.PARENT_ID = b.USER_ID
		LEFT JOIN T_USER_DETAIL c ON c.USER_ID = b.USER_ID
		LEFT JOIN T_R_USER_POSIT d ON d.USER_ID = b.USER_ID
		LEFT JOIN T_POSITION e ON e.POSITION_ID = d.POSITION_ID
		WHERE
			a.USER_ID = #{userId,jdbcType=INTEGER}
	    AND b.USER_ID IS NOT NULL
	</select>
	<select id="getAllGoodsManagersAndAssistant" resultType="com.vedeng.authorization.model.User">
		SELECT
			aa.USER_ID,
			aa.USERNAME
		FROM
			(
			SELECT DISTINCT
				c.USER_ID,
				LOWER( c.USERNAME ) USERNAME
			FROM
				T_REGULAR_PREPARE_SKU T1
				LEFT JOIN V_CORE_SKU a ON T1.SKU_ID = a.SKU_ID
				LEFT JOIN V_CORE_SPU b ON a.SPU_ID = b.SPU_ID
				LEFT JOIN T_USER c ON b.ASSIGNMENT_MANAGER_ID = c.USER_ID
			WHERE
				c.USER_ID IS NOT NULL
				AND c.USERNAME IS NOT NULL UNION ALL
			SELECT DISTINCT
				c.USER_ID,
				LOWER( c.USERNAME ) USERNAME
			FROM
				T_REGULAR_PREPARE_SKU T1
				LEFT JOIN V_CORE_SKU a ON T1.SKU_ID = a.SKU_ID
				LEFT JOIN V_CORE_SPU b ON a.SPU_ID = b.SPU_ID
				LEFT JOIN T_USER c ON b.ASSIGNMENT_ASSISTANT_ID = c.USER_ID
			WHERE
				c.USER_ID IS NOT NULL
				AND c.USERNAME IS NOT NULL
			) aa
		GROUP BY
			LOWER( aa.USERNAME )
		ORDER BY
			LOWER( aa.USERNAME )
	</select>

	<select id="getSaleorderOwnerById" resultType="java.lang.Integer" parameterType="java.lang.Integer">
		SELECT coalesce(t.USER_ID,s.USER_ID)
		FROM T_SALEORDER s
		left join T_R_TRADER_J_USER t on s.TRADER_ID = t.TRADER_ID
		WHERE
		t.TRADER_TYPE = 1
		AND
		s.SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
	</select>


	<select id="getBHSaleOrderOwnerById" resultType="java.lang.Integer" parameterType="java.lang.Integer" >
		SELECT CREATOR
		FROM T_SALEORDER
		WHERE SALEORDER_ID = #{bhSaleorderId,jdbcType=INTEGER}
  </select>

	<select id="getBussinessChanceOwnerById" resultType="java.lang.Integer" parameterType="java.lang.Integer">
		select
		  USER_ID
		from
		  T_BUSSINESS_CHANCE
		where
		  BUSSINESS_CHANCE_ID = #{bussinessChanceId,jdbcType=INTEGER}
 </select>

	<select id="getQuoteorderOwnerById" resultType="java.lang.Integer" parameterType="java.lang.Integer">
		SELECT t.USER_ID
		FROM T_QUOTEORDER q
		left join T_R_TRADER_J_USER t on q.TRADER_ID = t.TRADER_ID
		WHERE
		t.TRADER_TYPE = 1
		AND
		q.QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER}
 </select>

	<select id="getUserParentById" resultMap="BaseResultMap" parameterType="java.lang.Integer">
		select
			b.USER_ID,b.PARENT_ID
		from
			T_USER a
		left join
			T_USER b
		on
			a.PARENT_ID = b.USER_ID
		where
			b.IS_DISABLED = 0 and
			a.USER_ID = #{userId,jdbcType=INTEGER}
	</select>
    <select id="getAuthorizationApplyOwnerById" resultType="java.lang.Integer">
		select
			CREATOR
		from
			T_AUTHORIZATION_APPLY
		where
			AUTHORIZATION_APPLY_ID = #{authorizationApplyId,jdbcType=INTEGER}
	</select>

	<select id="getAllSaleMange" resultType="com.vedeng.authorization.model.User">
        select U.USER_ID AS userId
        from T_USER U
        left join T_R_USER_ROLE UR ON UR.USER_ID = U.USER_ID
        left join T_ROLE  R ON R.ROLE_ID = UR.ROLE_ID
        where R.ROLE_NAME = #{roleName,jdbcType = VARCHAR}
    </select>
	<select id="getAllExpressCreatorUser" parameterType="com.vedeng.authorization.model.User" resultType="com.vedeng.authorization.model.User">
		SELECT DISTINCT U.USER_ID,U.USERNAME
		FROM T_EXPRESS T
		INNER JOIN T_USER U ON U.USER_ID = T.CREATOR
		AND T.IS_ENABLE = 1
		ORDER BY  U.USERNAME  asc
	</select>

	<select id="userInfo" resultType="com.vedeng.authorization.model.ImUserDto">
		SELECT
			a.USER_ID as userId,
			a.USERNAME as userName,
			a.PASSWORD as passWord,
			a.SALT as salt,
			a.SYSTEM as systems
		FROM
			T_USER a
			LEFT JOIN T_USER_DETAIL b ON a.USER_ID = b.USER_ID
		WHERE
			a.USERNAME =#{userName,jdbcType=VARCHAR}
			AND a.COMPANY_ID = 1
			AND a.IS_DISABLED=0
	</select>

	<select id="findImUserInfo" resultType="com.vedeng.authorization.model.ImUserInfoDto">
select
		b.USER_ID,
		d.USERNAME,
		f.REAL_NAME,
		e.COMPANY_NAME,
		d.NUMBER,
		tud.MOBILE,
		tud.EMAIL,
		d.STAFF,
	 GROUP_CONCAT( a.POSITION_NAME separator '/') as positionName	,
	 GROUP_CONCAT(c.ORG_NAME separator '/') as orgName
		from T_POSITION a
		left join T_R_USER_POSIT b on a.POSITION_ID=b.POSITION_ID
		left join T_ORGANIZATION c on a.ORG_ID = c.ORG_ID
		left join T_USER d on b.USER_ID = d.USER_ID
		left join T_USER_DETAIL f on d.USER_ID = f.USER_ID
		left join T_COMPANY e on c.COMPANY_ID=e.COMPANY_ID
		and e.IS_ENABLE=1
		left join T_USER_DETAIL tud on b.USER_ID = tud.USER_ID
		where
	 	b.USER_ID =#{userId}
	  	group by b.USER_ID
		order by b.USER_ID

	</select>

	<select id="findRoleName" parameterType="integer" resultType="java.lang.String">
		select
		GROUP_CONCAT( tr.ROLE_NAME separator '/') as roleName
	from T_ROLE tr left join  T_R_USER_ROLE trur
		on tr.ROLE_ID = trur.ROLE_ID
		where trur.USER_ID=#{userId}
		group by trur.USER_ID
	</select>
	<select id="queryOrgPositionName" resultType="com.vedeng.authorization.model.ImUserInfoDto">
		select
	 GROUP_CONCAT( a.POSITION_NAME separator '/') as positionName	,
	 GROUP_CONCAT(c.ORG_NAME separator '/') as orgName
		from T_POSITION a
		left join T_R_USER_POSIT b on a.POSITION_ID=b.POSITION_ID
		left join T_ORGANIZATION c on a.ORG_ID = c.ORG_ID
		left join T_USER d on b.USER_ID = d.USER_ID
		left join T_USER_DETAIL f on d.USER_ID = f.USER_ID
		left join T_COMPANY e on c.COMPANY_ID=e.COMPANY_ID
		and e.IS_ENABLE=1
		left join T_USER_DETAIL tud on b.USER_ID = tud.USER_ID
		where
	 	b.USER_ID =#{userId}
	  	group by a.POSITION_NAME,c.ORG_NAME
		order by b.USER_ID
	</select>


	<select id="getAllUserListInfo"  resultType="com.vedeng.authorization.model.User">
		SELECT
			A.*,E.ORG_NAME
		FROM T_USER A
		LEFT JOIN T_R_USER_POSIT C ON A.USER_ID = C.USER_ID
		LEFT JOIN T_POSITION D ON C.POSITION_ID = D.POSITION_ID
		LEFT JOIN T_ORGANIZATION E ON D.ORG_ID = E.ORG_ID
		WHERE A.COMPANY_ID = 1
	</select>
	<select id="getuserIdByTraderId" resultType="java.lang.Integer">
		 SELECT USER_ID FROM T_TRADER A LEFT JOIN T_R_TRADER_J_USER
     TRTJU on A.TRADER_ID = TRTJU.TRADER_ID AND TRTJU.TRADER_TYPE=1
	WHERE  A.IS_ENABLE=1
	and A.TRADER_ID=#{traderId}

	</select>

	<select id="getUserByParentId" resultType="com.vedeng.authorization.model.User">
		select
		DISTINCT a.USER_ID, a.COMPANY_ID, a.USERNAME, a.NUMBER, a.PARENT_ID,
		a.ADD_TIME, a.CREATOR, a.MOD_TIME, a.UPDATER
		from
		T_USER a
		left join
		T_R_USER_POSIT b on a.USER_ID=b.USER_ID
		left join
		T_POSITION d ON d.POSITION_ID = b.POSITION_ID
		left join
		T_USER_DETAIL c on a.USER_ID = c.USER_ID
		where
		a.PARENT_ID = #{parentId} AND a.IS_DISABLED = 0 AND a.COMPANY_ID = 1
		ORDER BY a.USERNAME
	</select>

	<select id="getUserInfo" resultMap="BaseResultMap" parameterType="com.vedeng.authorization.model.User">
		select
		DISTINCT a.USER_ID, a.COMPANY_ID, a.USERNAME, a.NUMBER, a.PARENT_ID,
		a.ADD_TIME, a.CREATOR, a.MOD_TIME, a.UPDATER
		from
		T_USER a
		left join
		T_R_USER_POSIT b on a.USER_ID=b.USER_ID
		left join
		T_POSITION d ON d.POSITION_ID = b.POSITION_ID
		left join
		T_USER_DETAIL c on a.USER_ID = c.USER_ID
		<where>
			1=1
			<if test="positType != null">
				and d.TYPE = #{positType}
			</if>
			<if test="companyId != null">
				and a.COMPANY_ID = #{companyId,jdbcType=INTEGER}
			</if>
			<if test="isDisabled != null">
				and a.IS_DISABLED = #{isDisabled,jdbcType=INTEGER}
			</if>
			<if test="parentId != null">
				and a.PARENT_ID = #{parentId,jdbcType=INTEGER}
			</if>
			ORDER BY a.USERNAME
		</where>
	</select>
	<select id="getPositionByUserId" resultType="com.vedeng.authorization.model.User">
		SELECT  UU.USER_ID,UU.USERNAME,PP.LEVEL AS positLevel,PP.TYPE AS positType,PP.ORG_ID
		FROM T_USER UU
		LEFT JOIN T_R_USER_POSIT RUP ON UU.USER_ID = RUP.USER_ID
		LEFT JOIN T_POSITION PP ON RUP.POSITION_ID = PP.POSITION_ID
		WHERE UU.IS_DISABLED = 0
		AND UU.COMPANY_ID=1
		AND PP.ORG_ID IS NOT NULL
		AND UU.USER_ID= #{userId,jdbcType=INTEGER}
 	</select>
	<select id="getUserByUserName" resultType="com.vedeng.authorization.model.User">
		SELECT A.*, D.ORG_NAME, D.ORG_ID
		FROM T_USER A
		     LEFT JOIN T_R_USER_POSIT B ON A.USER_ID = B.USER_ID
		     LEFT JOIN T_POSITION C ON B.POSITION_ID = C.POSITION_ID
		     LEFT JOIN T_ORGANIZATION D ON C.ORG_ID = D.ORG_ID
		WHERE A.USERNAME = #{userName,jdbcType=VARCHAR}
		and A.company_id=1
		LIMIT 1
	</select>

	<select id="getUserByUsersOrgIds" resultMap="BaseResultMap">
		select distinct A.USER_ID, LOWER(A.USERNAME) USERNAME
		from T_USER A
				 LEFT JOIN T_R_USER_POSIT TRUP on A.USER_ID = TRUP.USER_ID
				 LEFT JOIN T_POSITION C ON C.POSITION_ID = TRUP.POSITION_ID
		WHERE A.COMPANY_ID = 1
		  AND C.ORG_ID IN (
			select A.ORG_ID
			from T_ORGANIZATION A
					 LEFT JOIN T_ORGANIZATION B ON A.PARENT_ID = B.ORG_ID
					 LEFT JOIN T_ORGANIZATION C ON B.PARENT_ID = C.ORG_ID
					 LEFT JOIN T_ORGANIZATION D ON C.PARENT_ID = D.ORG_ID
			WHERE
				FIND_IN_SET(A.ORG_ID,(select ORG_IDS_LIST from T_USER WHERE USER_ID =  #{userId,jdbcType=INTEGER}))
			   OR FIND_IN_SET(B.ORG_ID,(select ORG_IDS_LIST from T_USER WHERE USER_ID =  #{userId,jdbcType=INTEGER}))
			   OR FIND_IN_SET(C.ORG_ID,(select ORG_IDS_LIST from T_USER WHERE USER_ID =  #{userId,jdbcType=INTEGER}))
			   OR FIND_IN_SET(D.ORG_ID,(select ORG_IDS_LIST from T_USER WHERE USER_ID =  #{userId,jdbcType=INTEGER}))
		) order by A.USERNAME
	</select>
	<select id="getOrgIdsByUsersOrgIds" resultType="java.lang.Integer">
		select A.ORG_ID
		from T_ORGANIZATION A
				 LEFT JOIN T_ORGANIZATION B ON A.PARENT_ID = B.ORG_ID
				 LEFT JOIN T_ORGANIZATION C ON B.PARENT_ID = C.ORG_ID
				 LEFT JOIN T_ORGANIZATION D ON C.PARENT_ID = D.ORG_ID
		WHERE FIND_IN_SET(A.ORG_ID,(select ORG_IDS_LIST from T_USER WHERE USER_ID =  #{userId,jdbcType=INTEGER}))
		   OR FIND_IN_SET(B.ORG_ID,(select ORG_IDS_LIST from T_USER WHERE USER_ID =  #{userId,jdbcType=INTEGER}))
		   OR FIND_IN_SET(C.ORG_ID,(select ORG_IDS_LIST from T_USER WHERE USER_ID =  #{userId,jdbcType=INTEGER}))
		   OR FIND_IN_SET(D.ORG_ID,(select ORG_IDS_LIST from T_USER WHERE USER_ID =  #{userId,jdbcType=INTEGER}))
	</select>

	<select id="getOrgIdsByOrgIds" resultType="java.lang.Integer">
		select A.ORG_ID
		from T_ORGANIZATION A
				 LEFT JOIN T_ORGANIZATION B ON A.PARENT_ID = B.ORG_ID
				 LEFT JOIN T_ORGANIZATION C ON B.PARENT_ID = C.ORG_ID
				 LEFT JOIN T_ORGANIZATION D ON C.PARENT_ID = D.ORG_ID
		WHERE A.ORG_ID IN
					<foreach item="orgId" index="index" collection="orgIdList" open="(" separator="," close=")">
						#{orgId,jdbcType=INTEGER}
					</foreach>
		   or B.ORG_ID IN
					<foreach item="orgId" index="index" collection="orgIdList" open="(" separator="," close=")">
						#{orgId,jdbcType=INTEGER}
					</foreach>
		   or C.ORG_ID IN
					<foreach item="orgId" index="index" collection="orgIdList" open="(" separator="," close=")">
						#{orgId,jdbcType=INTEGER}
					</foreach>
		   or D.ORG_ID IN
					<foreach item="orgId" index="index" collection="orgIdList" open="(" separator="," close=")">
						#{orgId,jdbcType=INTEGER}
					</foreach>
	</select>

	<select id="getUserBaseInfoByUserIds" resultType="com.vedeng.authorization.model.User">
		SELECT
			USER_ID,
			USERNAME
		FROM
			T_USER
		WHERE
			USER_ID IN
		<foreach item="userId" index="index" collection="userIdList" open="(" separator="," close=")">
			#{userId,jdbcType=INTEGER}
		</foreach>
		ORDER BY USERNAME
	</select>

	<select id="getUserOrgIdsByUserId" resultType="java.lang.Integer" parameterType="java.lang.Integer">
		select c.ORG_ID from
			T_USER a
				LEFT JOIN T_R_USER_POSIT b on a.USER_ID = b.USER_ID
				LEFT JOIN T_POSITION c on c.POSITION_ID = b.POSITION_ID
		where
			a.USER_ID = #{userId}
		GROUP BY c.ORG_ID
	</select>


	<select id="getPosTypeUserListByOrgId" parameterType="java.lang.Integer" resultType="com.vedeng.authorization.model.User">
		SELECT  A.USER_ID, A.USERNAME
		FROM T_USER A
		LEFT JOIN T_R_USER_POSIT B ON A.USER_ID = B.USER_ID
		LEFT JOIN T_POSITION C ON B.POSITION_ID = C.POSITION_ID
		WHERE C.ORG_ID = #{orgId,jdbcType=INTEGER} and C.TYPE = #{posType}
		order by A.USERNAME
	</select>

	<select id="getLandLineInfo" resultType="com.vedeng.authorization.model.User">
		SELECT
			TELECOM_LINE,
			MOBILE_LINE,
			UNICOM_LINE,
			CUSTOMER_LINE
		FROM
			T_USER
		WHERE
			TELECOM_LINE IS NOT NULL
		   OR MOBILE_LINE IS NOT NULL
		   OR UNICOM_LINE IS NOT NULL
		   OR CUSTOMER_LINE IS NOT NULL
	</select>

	<update id="updateCallLineInfo">
		UPDATE T_USER
		<set>
			<if test="telecomLine != null">
				TELECOM_LINE = #{telecomLine,jdbcType=VARCHAR},
			</if>
			<if test="mobileLine != null">
				MOBILE_LINE = #{mobileLine,jdbcType=VARCHAR},
			</if>
			<if test="unicomLine != null">
				UNICOM_LINE = #{unicomLine,jdbcType=VARCHAR},
			</if>
			<if test="customerLine != null">
				CUSTOMER_LINE = #{customerLine,jdbcType=VARCHAR},
			</if>
		</set>
		WHERE
			USER_ID IN
		<foreach item="userId" index="index" collection="userIds" open="(" separator="," close=")">
			#{userId,jdbcType=INTEGER}
		</foreach>
	</update>

	<select id="getUserListByPositionName" resultType="string" parameterType="string">
		select TU.USERNAME
		from T_POSITION TP
				  JOIN T_R_USER_POSIT TRP ON TP.POSITION_ID = TRP.POSITION_ID
				  JOIN T_USER TU
						   ON TU.USER_ID = TRP.USER_ID
		WHERE TP.POSITION_NAME = #{positionName,jdbcType=VARCHAR}
		AND TU.IS_DISABLED = 0
	</select>

	<select id="getUserIdListByPositionName" resultType="java.lang.Integer" parameterType="string">
		select TU.USER_ID
		from T_POSITION TP
				 JOIN T_R_USER_POSIT TRP ON TP.POSITION_ID = TRP.POSITION_ID
				 JOIN T_USER TU
					  ON TU.USER_ID = TRP.USER_ID
		WHERE TP.POSITION_NAME = #{positionName,jdbcType=VARCHAR}
		  AND TU.IS_DISABLED = 0
	</select>
	<select id="getAfterSalesDirectorList" resultType="com.vedeng.authorization.model.User">
		select
			a.USER_ID,
			a.USERNAME
		from
			T_USER a
				left join T_USER_DETAIL b on a.USER_ID=b.USER_ID
				left join T_R_USER_POSIT r_u_p on r_u_p.USER_ID = a.USER_ID
				left join T_POSITION c on c.POSITION_ID = r_u_p.POSITION_ID
				left join T_ORGANIZATION d on d.ORG_ID=c.ORG_ID
		where
			d.ORG_ID=10
		  and c.POSITION_ID=38
		  and a.IS_DISABLED = 0
		  and a.STAFF =1
		  and find_in_set(1,a.SYSTEM)
		group by
			a.USER_ID
		order by
			a.ADD_TIME desc
	</select>

  <select id="getUserListByOrgIdListAndPositionType" resultMap="BaseResultMap">
	  SELECT A.USER_ID, A.COMPANY_ID, A.USERNAME, A.NUMBER, A.PASSWORD, A.SALT, A.PARENT_ID, A.IS_DISABLED, A.DISABLED_REASON,
	  A.LAST_LOGIN_TIME, A.LAST_LOGIN_IP, A.ADD_TIME, A.CREATOR, A.MOD_TIME, A.UPDATER
	  FROM T_USER A
	  LEFT JOIN T_R_USER_POSIT B ON A.USER_ID = B.USER_ID
	  LEFT JOIN T_POSITION C ON B.POSITION_ID = C.POSITION_ID
	  WHERE C.ORG_ID IN
	  <foreach item="orgId" index="index" collection="orgIds" open="(" separator="," close=")">
		  #{orgId}
	  </foreach>
	  and A.COMPANY_ID = 1 order by A.USERNAME
	</select>

  <select id="findSalesDirectorByOrgId" resultType="java.lang.Integer">
	  select distinct TU.USER_ID
	  from T_USER TU
			   left join T_R_USER_POSIT TRUP on TRUP.USER_ID = TU.USER_ID
			   left join T_POSITION TP on TP.POSITION_ID = TRUP.POSITION_ID
	  where TP.ORG_ID = #{orgId,jdbcType=INTEGER}
		and TP.POSITION_NAME = '销售总监'
		and TU.IS_DISABLED = 0
		and TU.STAFF = 1
		and TU.COMPANY_ID = 1
		and TU.`SYSTEM` = 1
	</select>

  <select id="getUserListByPositionId" resultType="java.lang.String">
	  select TU.USERNAME
	  from T_POSITION TP
	  JOIN T_R_USER_POSIT TRP ON TP.POSITION_ID = TRP.POSITION_ID
	  JOIN T_USER TU
	  ON TU.USER_ID = TRP.USER_ID
	  WHERE TP.POSITION_ID = #{positionId,jdbcType=INTEGER}
	  AND TU.IS_DISABLED = 0
    </select>

	<select id="searchUserList" parameterType="java.lang.String" resultType="com.vedeng.authorization.model.User">
		select
		tu.USER_ID,
		tu.USERNAME,
		tud.REAL_NAME
		from
		T_USER tu
		left join T_USER_DETAIL tud ON
		tu.USER_ID = tud.USER_ID
		WHERE
		tu.IS_DISABLED = 0
		<if test="username != null and username != '' ">
			and (tu.USERNAME like concat('%', #{username,jdbcType=VARCHAR}, '%')
			or tud.REAL_NAME like concat('%', #{username,jdbcType=VARCHAR}, '%'))
		</if>
		GROUP BY tu.USER_ID
	</select>

	<select id="searchUserListHasDelete" parameterType="java.lang.String" resultType="com.vedeng.authorization.model.User">
		select
		tu.USER_ID,
		tu.USERNAME,
		tud.REAL_NAME
		from
		T_USER tu
		left join T_USER_DETAIL tud ON
		tu.USER_ID = tud.USER_ID
		WHERE
			  (tu.USERNAME like concat('%', #{username,jdbcType=VARCHAR}, '%')
			or tud.REAL_NAME like concat('%', #{username,jdbcType=VARCHAR}, '%'))
		GROUP BY tu.USER_ID
	</select>

	<select id="searchUserListForSelect" parameterType="java.lang.String" resultType="com.vedeng.authorization.model.User">
		select
		tu.USER_ID,
		case tu.IS_DISABLED when IS_DISABLED = 1 THEN concat(tu.USERNAME,'(离职)') ELSE tu.USERNAME AS USERNAME,
		tu.USERNAME,
		tu.IS_DISABLED,
		tud.REAL_NAME
		from
		T_USER tu
		left join T_USER_DETAIL tud ON
		tu.USER_ID = tud.USER_ID
		WHERE
 		1=1
		<if test="username != null and username != '' ">
			and (tu.USERNAME like concat('%', #{username,jdbcType=VARCHAR}, '%')
			or tud.REAL_NAME like concat('%', #{username,jdbcType=VARCHAR}, '%'))
		</if>
		GROUP BY tu.USER_ID
	</select>

	<select id="getAllUserIdList" resultType="com.vedeng.authorization.model.User">
		SELECT A.USER_ID, A.COMPANY_ID, A.USERNAME, A.NUMBER, A.PASSWORD, A.SALT, A.PARENT_ID, A.IS_DISABLED, A.DISABLED_REASON,
			   A.LAST_LOGIN_TIME, A.LAST_LOGIN_IP, A.ADD_TIME, A.CREATOR, A.MOD_TIME, A.UPDATER  FROM T_PUBLIC_CUSTOMER_REGION_RULES B LEFT JOIN T_USER A ON A.USER_ID = B.USER_ID
		GROUP BY B.USER_ID
	</select>
	
	<select id="getBDUserInfoByTraderId" resultType="com.vedeng.authorization.model.User">
		SELECT
		trju.`TRADER_ID`,
		D.`USER_ID`,
		D.`USERNAME`,
		F.`ORG_ID`,
		G.`ORG_NAME`
		FROM
		 T_USER D
	     left join T_R_TRADER_J_USER trju
		ON D.`USER_ID` = trju.`USER_ID`
		LEFT JOIN T_R_USER_POSIT E
		ON E.`USER_ID` = D.`USER_ID`
		LEFT JOIN T_POSITION F
		ON F.`POSITION_ID` = E.`POSITION_ID`
		LEFT JOIN T_ORGANIZATION G
		ON F.`ORG_ID` = G.`ORG_ID`
		WHERE trju.TRADER_ID = #{traderId}
		LIMIT 1
	</select>
	
</mapper>
