package com.vedeng.erp.kingdee.batch.dto;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.*;

@Getter
@Setter
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchTransactionVoucherDto extends BaseEntity {
    /**
     * 主键
     */
    private Long transactionVoucherId;

    /**
     * 单据编号
     */
    private String billNo;

    /**
     * 凭证号
     */
    private String voucherNo;

    /**
     * 交易类型 1-收款 2付款
     */
    private Integer transactionType;

    /**
     * 是否删除 0 否 1是
     */
    private Integer isDelete;
}
