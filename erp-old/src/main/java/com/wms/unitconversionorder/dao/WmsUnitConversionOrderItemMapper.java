package com.wms.unitconversionorder.dao;

import java.util.List;

import com.wms.unitconversionorder.model.entity.WmsUnitConversionOrderItem;
import org.apache.ibatis.annotations.Param;

/**
 * @description ${end}
 * <AUTHOR>
 * @date 2023/2/17 10:57
 **/
public interface WmsUnitConversionOrderItemMapper {
    /**
     * delete by primary key
     * @param wmsUnitConversionOrderItemId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer wmsUnitConversionOrderItemId);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(WmsUnitConversionOrderItem record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(WmsUnitConversionOrderItem record);

    /**
     * select by primary key
     * @param wmsUnitConversionOrderItemId primary key
     * @return object by primary key
     */
    WmsUnitConversionOrderItem selectByPrimaryKey(Integer wmsUnitConversionOrderItemId);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(WmsUnitConversionOrderItem record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(WmsUnitConversionOrderItem record);

    int batchInsert(List<WmsUnitConversionOrderItem> list);

    /**
     * 根据单位转换单查询 订单明细信息
     * @param wmsUnitConversionOrderId
     * @return
     */
    List<WmsUnitConversionOrderItem> selectByWmsUnitConversionOrderId(@Param("wmsUnitConversionOrderId")Integer wmsUnitConversionOrderId);


}