package com.vedeng.erp.buyorder.common.constant;

/**
 * <AUTHOR>
 * @Description com.vedeng.orderstream.buyorder.constant
 * @Date 2021/10/19 14:38
 */
public class CommonBuyOrder {

    public final static String BUY_ORDER_VERIFY = "buyorderVerify";
    public final static String PAYMENT_VERIFY = "paymentVerify";
    public final static String PAYMENT_STATUS = "付款状态";
    public final static String BUY_ORDER_STATUS = "buyorderStatus";
    public final static String BUY_ORDER_MODIFY_APPLY = "buyorderModifyAplly";

    public final static String WAIT_PAY = "未付款";
    public final static String PART_PAY = "部分付款";
    public final static String ALL_PAY = "全部付款";
    public final static String WAIT_DELIVERY = "未发货";
    public final static String PART_DELIVERY = "部分发货";
    public final static String ALL_DELIVERY = "全部发货";
    public final static String WAIT_ARRIVAL = "未收货";
    public final static String PART_ARRIVAL = "部分收货";
    public final static String ALL_ARRIVAL = "全部收货";
    public final static String WAIT_INVOICE = "未收票";
    public final static String PART_INVOICE = "部分收票";
    public final static String ALL_INVOICE = "全部收票";
}
