package com.vedeng.erp.finance.dto;

import lombok.Data;

import java.util.List;

/**
 * 承兑制单
 */
@Data
public class AcceptancePayDto {

    List<Integer> payApplyIds;

    /**
     * 承兑银行 1民生银行 2交通银行
     */
    private Integer bank;

    /**
     * 授信类型 1综合授信 2单笔授信
     */
    private String creditType;

    /**
     * 线下开票 1是 0否
     */
    private Integer offline;

    /**
     * 减免单号
     */
    private String taskNo;
}
