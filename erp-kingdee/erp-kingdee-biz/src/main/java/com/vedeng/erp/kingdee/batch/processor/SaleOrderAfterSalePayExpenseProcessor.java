package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.finance.enums.InvoiceTaxTypeEnum;
import com.vedeng.erp.finance.dto.InvoiceDto;
import com.vedeng.erp.finance.service.PayApplyApiService;
import com.vedeng.erp.kingdee.batch.common.processor.BaseProcessor;
import com.vedeng.erp.kingdee.batch.dto.*;
import com.vedeng.erp.kingdee.batch.repository.*;
import com.vedeng.erp.kingdee.dto.KingDeePayExpensesDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeePayExpensesDto;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @Author: ckt
 * @Date: 2023/6/5
 * @Description: 销售售后录票（蓝字有效）费用应付单，组装数据
 *
 */

@Service
@Slf4j
public class SaleOrderAfterSalePayExpenseProcessor extends BaseProcessor<BatchInvoiceDto, BatchPayExpensesDto> {
    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;
    @Autowired
    private BatchInvoiceDetailDtoMapper batchInvoiceDetailDtoMapper;
    @Autowired
    private BatchInvoiceDtoMapper batchInvoiceDtoMapper;
    @Autowired
    private BatchAfterSalesDtoMapper batchAfterSalesDtoMapper;
    @Autowired
    private BatchAfterSalesGoodsDtoMapper batchAfterSalesGoodsDtoMapper;
    @Autowired
    private BatchCapitalBillDtoMapper batchCapitalBillDtoMapper;
    @Autowired
    private PayApplyApiService payApplyApiService;

    @Override
    public BatchPayExpensesDto doProcess(BatchInvoiceDto batchInvoiceDto, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("销售售后录票(蓝字有效)费用应付单,组装数据,BatchInvoiceDto:{}", JSONUtil.toJsonStr(batchInvoiceDto));
        BatchPayExpensesDto batchPayExpensesDto = new BatchPayExpensesDto();
        //应付单对象 dto
        KingDeePayExpensesDto dto = new KingDeePayExpensesDto();
        dto.setFQzokBddjtId(Convert.toStr(batchInvoiceDto.getInvoiceId()));
        //判断该蓝字有效票是否有作废票，如果有不推送（不在sql中过滤是为了避免自连接时间过长）
        List<InvoiceDto> invoiceList = batchInvoiceDtoMapper.findDeprecatedBlueInvoice(batchInvoiceDto.getInvoiceId());
        if (invoiceList.size() > 0){
            log.info("(蓝票)售后费用应收单，该蓝字有效票有对应的蓝字作废票，不推送金蝶，发票信息：{}", JSON.toJSONString(batchInvoiceDto));
            return null;
        }
        // 判断是否数据已存在
        boolean old = kingDeeBaseApi.isExist(dto);
        if(old){
            log.info("销售售后录票(蓝字有效)费用应付单,数据已存在:{}", JSON.toJSONString(batchInvoiceDto));
            return null;
        }
        if (null == batchInvoiceDto.getRelatedId()){
            log.error("销售售后录票(蓝字有效)费用应付单,发票id为{},无法找到对应订单",batchInvoiceDto.getInvoiceId());
            return null;
        }
        if (null == batchInvoiceDto.getAfterSalesId()){
            log.error("销售售后录票(蓝字有效)费用应付单,发票id为{},无法找到对应售后",batchInvoiceDto.getInvoiceId());
            return null;
        }
        //获取售后单中特殊商品
        BatchAfterSalesGoodsDto specialGoods = batchAfterSalesGoodsDtoMapper.getSpecialGoods(batchInvoiceDto.getAfterSalesId());
        if (specialGoods == null) {
            log.error("销售售后录票(蓝字有效)费用应付单,发票id为{},无法找到对应特殊商品", batchInvoiceDto.getInvoiceId());
            return null;
        }
        log.info("销售售后录票(蓝字有效)费用应付单,售后单的特殊商品,specialGoods:{}",JSONUtil.toJsonStr(specialGoods));
        //组装对象
        if (this.packPayExpenseDto(batchInvoiceDto, dto,specialGoods)){
            batchPayExpensesDto.setKingDeePayExpensesDto(dto);
            return batchPayExpensesDto;
        }
        log.info("销售售后录票(蓝字有效)费用应付单,组装数据失败,batchInvoiceDto:{},specialGoods:{}",JSONUtil.toJsonStr(batchInvoiceDto),JSONUtil.toJsonStr(specialGoods));
        return null;
    }

    /**
     * @return 组装数据是否成功
     */
    private boolean packPayExpenseDto(BatchInvoiceDto batchInvoiceDto, KingDeePayExpensesDto dto, BatchAfterSalesGoodsDto specialGoods) {
        //发票详情
        List<BatchInvoiceDetailDto> invoiceDetailList = batchInvoiceDetailDtoMapper.findByInvoiceId(batchInvoiceDto.getInvoiceId());
        Assert.notEmpty(invoiceDetailList,"销售售后录票(蓝字有效)费用应付单,发票明细列表为空,invoiceID:{}",batchInvoiceDto.getInvoiceId());
        //是否为专票
        boolean isSpecialInvoice = InvoiceTaxTypeEnum.getIsSpecialInvoice(batchInvoiceDto.getInvoiceType());
        //税率
        DecimalFormat decimalFormat = new DecimalFormat("0.00#");
        //VDERP-15228 普票税率传0
        String taxRate = (Objects.isNull(batchInvoiceDto.getRatio()) || !isSpecialInvoice) ? "0.00" : decimalFormat.format(batchInvoiceDto.getRatio().multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP));
        dto.setFId(0);
        dto.setFQzokBddjtId(Convert.toStr(batchInvoiceDto.getInvoiceId()));
        //售后单的首笔付款流水的日期(付款交易记录-交易时间)
        Long firstTraderTime = batchCapitalBillDtoMapper.getFirstCapitalBillTraderTime(batchInvoiceDto.getAfterSalesId());
        Assert.notNull(firstTraderTime,"销售售后录票(蓝字有效)费用应付单,售后单的首笔付款流水的日期为空,afterSalesId:{}",batchInvoiceDto.getAfterSalesId());
        dto.setFDate(DateUtil.formatDate(new Date(batchInvoiceDto.getValidTime())));
        //付款申请记录-支付对象的供应商ID
        Integer supplierId = payApplyApiService.getPayApplyMaxRecord(batchInvoiceDto.getAfterSalesId());
        if (ObjectUtil.isNull(supplierId)){
            log.error("销售售后录票(蓝字有效)费用应付单,未查到供应商编码,BatchInvoiceDto:{}",JSONUtil.toJsonStr(batchInvoiceDto));
            return false;
        }
        dto.setFSupplierId(Convert.toStr(supplierId));
        //费用编码
        String kingDeeFCostID = batchAfterSalesGoodsDtoMapper.getKingDeeFCostID(specialGoods.getSku());
        if (StringUtils.isBlank(kingDeeFCostID)){
            log.error("销售售后录票(蓝字有效)应付单，未查到金蝶费用编码,skuNo:{}",specialGoods.getSku());
            return false;
        }
        invoiceDetailList.forEach(invoiceDetail -> {
            KingDeePayExpensesDetailDto detailDto = new KingDeePayExpensesDetailDto();
            detailDto.setFCOSTID(kingDeeFCostID);
            detailDto.setFPriceQty(Convert.toStr(invoiceDetail.getNum()));
            detailDto.setFTaxPrice(Convert.toStr(invoiceDetail.getTotalAmount().divide(invoiceDetail.getNum(),6,RoundingMode.HALF_UP)));
            detailDto.setFEntryTaxRate(taxRate);
            detailDto.setFINCLUDECOST(Boolean.FALSE);
            detailDto.setFCOSTDEPARTMENTID("BM9999");
            detailDto.setF_QZOK_BDDJHID(Convert.toStr(invoiceDetail.getInvoiceDetailId()));
            detailDto.setF_QZOK_WLBM(specialGoods.getSku());
            detailDto.setF_QZOK_GSXSDH(batchAfterSalesDtoMapper.findByAfterSalesId(batchInvoiceDto.getAfterSalesId()).getAfterSalesNo());
            dto.getFEntityDetail().add(detailDto);
        });
        return true;
    }
}
