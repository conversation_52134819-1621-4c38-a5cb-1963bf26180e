package com.vedeng.goods.mapper;

import com.vedeng.common.core.domain.UserComponent;
import com.vedeng.goods.domain.entity.SkuSceneEntity;
import com.vedeng.goods.dto.SkuSceneDto;
import com.vedeng.goods.dto.VHostWordDTOExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SkuSceneMapper {
    /**
     * delete by primary key
     *
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long id);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(SkuSceneEntity record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(SkuSceneEntity record);




    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    SkuSceneEntity selectByPrimaryKey(Long id);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(SkuSceneEntity record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(SkuSceneEntity record);

    int updateIsDeleteById(@Param("id") Long id);

    List<SkuSceneEntity> findAll();



    List<SkuSceneDto> findByAll(SkuSceneDto skuSceneDto);

    List<SkuSceneEntity> findBySortGreaterThan(@Param("minSort") Integer minSort);

    List<SkuSceneEntity> findBySortLessThan(@Param("maxSort") Integer maxSort);

    List<SkuSceneEntity> findByName(@Param("name") String name);

    SkuSceneDto findById(@Param("id")Long id);

    List<SkuSceneEntity> findBySortBetween(@Param("minSort")Integer minSort,@Param("maxSort")Integer maxSort);


    Integer findMaxSort();

    List<UserComponent> findCreator();

    List<UserComponent> findUpdater();




}