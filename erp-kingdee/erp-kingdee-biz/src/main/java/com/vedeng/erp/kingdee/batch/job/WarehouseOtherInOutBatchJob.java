package com.vedeng.erp.kingdee.batch.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.vedeng.erp.kingdee.batch.common.base.BaseJob;
import com.vedeng.erp.kingdee.batch.common.handle.CustomSkipPolicy;
import com.vedeng.erp.kingdee.batch.common.listener.BaseReadListener;
import com.vedeng.erp.kingdee.batch.common.listener.BaseWriteListener;
import com.vedeng.erp.kingdee.batch.common.listener.JobListener;
import com.vedeng.erp.kingdee.batch.common.listener.BaseProcessListener;
import com.vedeng.erp.kingdee.batch.common.reader.CommonMybatisItemReader;
import com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInDto;
import com.vedeng.erp.kingdee.batch.processor.*;
import com.vedeng.erp.kingdee.batch.writer.*;
import com.vedeng.erp.kingdee.dto.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.item.support.CompositeItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskExecutor;

import java.util.Arrays;


/**
 * <AUTHOR>
 * @version 1.0
 * @description: 其他出入库流程job
 * @date 2022/10/25 16:00
 * 9外借入库 10外借出库 12盘盈入库 13报废出库 14领用出库  16 盘亏出库 18 样品出库 19 单位转换出库 20 单位转换入库
 */
@Slf4j
@Configuration
@SuppressWarnings("all")
public class WarehouseOtherInOutBatchJob extends BaseJob {

    @Autowired
    private BatchAllocationInProcessor batchAllocationInProcessor;
    @Autowired
    private BatchAllocationInWriter batchAllocationInWriter;

    @Autowired
    private BatchAllocationOutProcessor batchAllocationOutProcessor;
    @Autowired
    private BatchAllocationOutWriter batchAllocationOutWriter;

    @Autowired
    private BatchScrapProcessor batchScrapProcessor;
    @Autowired
    private BatchScrapWriter batchScrapWriter;

    @Autowired
    private BatchReceiveProcessor batchReceiveProcessor;
    @Autowired
    private BatchReceiveWriter batchReceiveWriter;

    @Autowired
    private BatchProfitLossProcessor batchProfitLossProcessor;
    @Autowired
    private BatchProfitLossWriter batchProfitLossWriter;

    @Autowired
    private BatchInventoryProfitProcessor batchInventoryProfitProcessor;
    @Autowired
    private BatchInventoryProfitWriter batchInventoryProfitWriter;

    @Autowired
    private BatchUnitConversionOutProcessor batchUnitConversionOutProcessor;
    @Autowired
    private BatchUnitConversionOutWriter batchUnitConversionOutWriter;

    @Autowired
    private BatchUnitConversionInProcessor batchUnitConversionInProcessor;
    @Autowired
    private BatchUnitConversionInWriter batchUnitConversionInWriter;

    @Autowired
    private BatchOtherInAcceptanceFormProcessor batchOtherInAcceptanceFormProcessor;

    @Autowired
    private CommonFileDataWriter commonFileDataWriter;


    public Job otherWarehouseInFlowJob() {
        return jobBuilderFactory.get("otherWarehouseInFlowJob")
                .incrementer(new RunIdIncrementer())
                .listener(jobListener)
                .start(allocationIn())
                .next(inventoryProfit())
                .next(allocationOut())
                .next(scrap())
                .next(receive())
                .next(profitLoss())
                .next(unitConversionOut())
                .next(unitConversionIn())
                .next(sampleOut())
                .next(acceptanceForm())
                .build();
    }

    private Step acceptanceForm() {
        return stepBuilderFactory.get("其他出入库单附件推送")
                .<BatchWarehouseGoodsOutInDto, KingDeeFileDataDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(otherFileReader(null, null))
                .processor(batchOtherInAcceptanceFormProcessor)
                .writer(commonFileDataWriter)
                .listener(baseProcessListener)
                .listener(baseReadListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 样品出库
     */
    private Step sampleOut() {
        return stepBuilderFactory.get("样品出库")
                .<BatchWarehouseGoodsOutInDto, KingDeeStorageOutDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchSampleConversionOutDtoItemReader(null, null))
                .processor(batchScrapProcessor)
                .writer(batchScrapWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 单位转换出库
     */
    private Step unitConversionOut() {
        return stepBuilderFactory.get("单位转换出库")
                .<BatchWarehouseGoodsOutInDto, KingDeeStorageInDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchUnitConversionOutDtoItemReader(null, null))
                .processor(batchUnitConversionOutProcessor)
                .writer(batchUnitConversionOutWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 单位转换入库
     */
    private Step unitConversionIn() {
        return stepBuilderFactory.get("单位转换入库")
                .<BatchWarehouseGoodsOutInDto, KingDeeStorageInDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchUnitConversionInDtoItemReader(null, null))
                .processor(batchUnitConversionInProcessor)
                .writer(batchUnitConversionInWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 外借入库
     */
    private Step allocationIn() {
        return stepBuilderFactory.get("外借入库")
                .<BatchWarehouseGoodsOutInDto, KingDeeAllocationDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchAllocationInDtoItemReader(null, null))
                .processor(batchAllocationInProcessor)
                .writer(batchAllocationInWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 盘盈入库
     */
    private Step inventoryProfit() {
        return stepBuilderFactory.get("盘盈入库")
                .<BatchWarehouseGoodsOutInDto, KingDeeInventoryProfitDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchInventoryProfitDtoItemReader(null, null))
                .processor(batchInventoryProfitProcessor)
                .writer(batchInventoryProfitWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }


    /**
     * 外借出库
     */
    private Step allocationOut() {
        return stepBuilderFactory.get("外借出库")
                .<BatchWarehouseGoodsOutInDto, KingDeeAllocationDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchAllocationOutDtoItemReader(null, null))
                .processor(batchAllocationOutProcessor)
                .writer(batchAllocationOutWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 报废出库
     */
    private Step scrap() {
        return stepBuilderFactory.get("报废出库")
                .<BatchWarehouseGoodsOutInDto, KingDeeStorageOutDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchScrapDtoItemReader(null, null))
                .processor(batchScrapProcessor)
                .writer(batchScrapWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }


    /**
     * 领用出库
     */
    private Step receive() {
        return stepBuilderFactory.get("领用出库")
                .<BatchWarehouseGoodsOutInDto, KingDeeStorageOutDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchReceiveDtoItemReader(null, null))
                .processor(batchReceiveProcessor)
                .writer(batchReceiveWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 盘库出库
     */
    private Step profitLoss() {
        return stepBuilderFactory.get("盘库出库")
                .<BatchWarehouseGoodsOutInDto, KingDeeProfitLossDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchProfitLossDtoItemReader(null, null))
                .processor(batchProfitLossProcessor)
                .writer(batchProfitLossWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchWarehouseGoodsOutInDto> otherFileReader(
            @Value("#{jobParameters['beginTime']}") String beginTime,
            @Value("#{jobParameters['endTime']}") String endTime) {
        BatchWarehouseGoodsOutInDto warehouseGoodsOutInDto = BatchWarehouseGoodsOutInDto
                .builder()
                .isDelete(0)
                .outInTypeList(CollUtil.newArrayList(10,13,14,16,18,9,12,19,20))
                .beginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()) : DateUtil.parseDateTime(beginTime))
                .endTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()) : DateUtil.parseDateTime(endTime))
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchWarehouseGoodsOutInDto.class.getSimpleName(), warehouseGoodsOutInDto);
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchWarehouseGoodsOutInDto> batchSampleConversionOutDtoItemReader(
            @Value("#{jobParameters['beginTime']}") String beginTime,
            @Value("#{jobParameters['endTime']}") String endTime) {
        return getBatchWarehouseGoodsOutInDtoCommonMybatisItemReader(beginTime, endTime,18);
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchWarehouseGoodsOutInDto> batchUnitConversionOutDtoItemReader(
            @Value("#{jobParameters['beginTime']}") String beginTime,
            @Value("#{jobParameters['endTime']}") String endTime) {
        return getBatchWarehouseGoodsOutInDtoCommonMybatisItemReader(beginTime, endTime,19);
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchWarehouseGoodsOutInDto> batchUnitConversionInDtoItemReader(
            @Value("#{jobParameters['beginTime']}") String beginTime,
            @Value("#{jobParameters['endTime']}") String endTime) {
        return getBatchWarehouseGoodsOutInDtoCommonMybatisItemReader(beginTime, endTime,20);
    }


    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchWarehouseGoodsOutInDto> batchAllocationInDtoItemReader(
            @Value("#{jobParameters['beginTime']}") String beginTime,
            @Value("#{jobParameters['endTime']}") String endTime) {
        return getBatchWarehouseGoodsOutInDtoCommonMybatisItemReader(beginTime, endTime,9);
    }


    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchWarehouseGoodsOutInDto> batchInventoryProfitDtoItemReader(
            @Value("#{jobParameters['beginTime']}") String beginTime,
            @Value("#{jobParameters['endTime']}") String endTime) {
        return getBatchWarehouseGoodsOutInDtoCommonMybatisItemReader(beginTime, endTime,12);
    }



    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchWarehouseGoodsOutInDto> batchAllocationOutDtoItemReader(
            @Value("#{jobParameters['beginTime']}") String beginTime,
            @Value("#{jobParameters['endTime']}") String endTime) {
        return getBatchWarehouseGoodsOutInDtoCommonMybatisItemReader(beginTime, endTime,10);
    }



    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchWarehouseGoodsOutInDto> batchScrapDtoItemReader(
            @Value("#{jobParameters['beginTime']}") String beginTime,
            @Value("#{jobParameters['endTime']}") String endTime) {
        return getBatchWarehouseGoodsOutInDtoCommonMybatisItemReader(beginTime, endTime,13);
    }



    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchWarehouseGoodsOutInDto> batchReceiveDtoItemReader(
            @Value("#{jobParameters['beginTime']}") String beginTime,
            @Value("#{jobParameters['endTime']}") String endTime) {
        return getBatchWarehouseGoodsOutInDtoCommonMybatisItemReader(beginTime, endTime,14);
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchWarehouseGoodsOutInDto> batchProfitLossDtoItemReader(
            @Value("#{jobParameters['beginTime']}") String beginTime,
            @Value("#{jobParameters['endTime']}") String endTime) {
        return getBatchWarehouseGoodsOutInDtoCommonMybatisItemReader(beginTime, endTime,16);
    }


    private CommonMybatisItemReader<BatchWarehouseGoodsOutInDto> getBatchWarehouseGoodsOutInDtoCommonMybatisItemReader(
            String beginTime, String endTime, Integer outInType) {
        BatchWarehouseGoodsOutInDto warehouseGoodsOutInDto = BatchWarehouseGoodsOutInDto
                .builder()
                .outInType(outInType)
                .isDelete(0)
                .beginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()) : DateUtil.parseDateTime(beginTime))
                .endTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()) : DateUtil.parseDateTime(endTime))
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchWarehouseGoodsOutInDto.class.getSimpleName(), warehouseGoodsOutInDto);
    }


}

