package com.vedeng.kpi.dao;

import com.vedeng.kpi.model.DO.KpiDailyCountDo;
import com.vedeng.kpi.model.DTO.KpiDailyCountExtDto;
import com.vedeng.kpi.model.DTO.KpiDataQueryDto;

import java.util.List;

public interface KpiDailyCountMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(KpiDailyCountDo record);

    int insertSelective(KpiDailyCountDo record);

    KpiDailyCountDo selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(KpiDailyCountDo record);

    int updateByPrimaryKey(KpiDailyCountDo record);

    KpiDailyCountExtDto selectByUserDay(KpiDailyCountDo query);

    List<KpiDailyCountExtDto> selectByGroupDate(KpiDataQueryDto query);

    List<KpiDailyCountExtDto> selectByBatchUser(KpiDataQueryDto query);

    List<KpiDailyCountExtDto> selectAll(KpiDataQueryDto query);
}