<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.buyorder.mapper.GeActionLogMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.buyorder.domain.entity.GeActionLog">
    <id column="AUTHORIZATION_LOG_ID" jdbcType="INTEGER" property="authorizationLogId" />
    <result column="RELATED_ID" jdbcType="INTEGER" property="relatedId" />
    <result column="OPERATION" jdbcType="VARCHAR" property="operation" />
    <result column="CONTENT" jdbcType="VARCHAR" property="content" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="RELATED_TYPE" jdbcType="INTEGER" property="relatedType" />
    <result column="RELATED_BODY" jdbcType="VARCHAR" property="relatedBody" />
  </resultMap>
  <sql id="Base_Column_List">
    AUTHORIZATION_LOG_ID, RELATED_ID, `OPERATION`, CONTENT, CREATOR, CREATOR_NAME, ADD_TIME,
    RELATED_TYPE, RELATED_BODY
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_GE_ACTION_LOG
    where AUTHORIZATION_LOG_ID = #{authorizationLogId,jdbcType=INTEGER}
  </select>
  <select id="queryByRelatedTypeAndRelatedId" parameterType="com.vedeng.erp.buyorder.domain.entity.GeActionLog" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from T_GE_ACTION_LOG
    where RELATED_ID = #{relatedId}
    and RELATED_TYPE = #{relatedType}
    order by ADD_TIME DESC
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from T_GE_ACTION_LOG
    where AUTHORIZATION_LOG_ID = #{authorizationLogId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.erp.buyorder.domain.entity.GeActionLog">
    insert into T_GE_ACTION_LOG (AUTHORIZATION_LOG_ID, RELATED_ID, `OPERATION`,
      CONTENT, CREATOR, CREATOR_NAME,
      ADD_TIME, RELATED_TYPE, RELATED_BODY
      )
    values (#{authorizationLogId,jdbcType=INTEGER}, #{relatedId,jdbcType=INTEGER}, #{operation,jdbcType=VARCHAR},
      #{content,jdbcType=VARCHAR}, #{creator,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR},
      #{addTime,jdbcType=TIMESTAMP}, #{relatedType,jdbcType=INTEGER}, #{relatedBody,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="AUTHORIZATION_LOG_ID" keyProperty="authorizationLogId" parameterType="com.vedeng.erp.buyorder.domain.entity.GeActionLog" useGeneratedKeys="true">
    insert into T_GE_ACTION_LOG
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="authorizationLogId != null">
        AUTHORIZATION_LOG_ID,
      </if>
      <if test="relatedId != null">
        RELATED_ID,
      </if>
      <if test="operation != null">
        `OPERATION`,
      </if>
      <if test="content != null">
        CONTENT,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="relatedType != null">
        RELATED_TYPE,
      </if>
      <if test="relatedBody != null">
        RELATED_BODY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="authorizationLogId != null">
        #{authorizationLogId,jdbcType=INTEGER},
      </if>
      <if test="relatedId != null">
        #{relatedId,jdbcType=INTEGER},
      </if>
      <if test="operation != null">
        #{operation,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="relatedType != null">
        #{relatedType,jdbcType=INTEGER},
      </if>
      <if test="relatedBody != null">
        #{relatedBody,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.buyorder.domain.entity.GeActionLog">
    update T_GE_ACTION_LOG
    <set>
      <if test="relatedId != null">
        RELATED_ID = #{relatedId,jdbcType=INTEGER},
      </if>
      <if test="operation != null">
        `OPERATION` = #{operation,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        CONTENT = #{content,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="relatedType != null">
        RELATED_TYPE = #{relatedType,jdbcType=INTEGER},
      </if>
      <if test="relatedBody != null">
        RELATED_BODY = #{relatedBody,jdbcType=VARCHAR},
      </if>
    </set>
    where AUTHORIZATION_LOG_ID = #{authorizationLogId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.buyorder.domain.entity.GeActionLog">
    update T_GE_ACTION_LOG
    set RELATED_ID = #{relatedId,jdbcType=INTEGER},
      `OPERATION` = #{operation,jdbcType=VARCHAR},
      CONTENT = #{content,jdbcType=VARCHAR},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      RELATED_TYPE = #{relatedType,jdbcType=INTEGER},
      RELATED_BODY = #{relatedBody,jdbcType=VARCHAR}
    where AUTHORIZATION_LOG_ID = #{authorizationLogId,jdbcType=INTEGER}
  </update>
</mapper>