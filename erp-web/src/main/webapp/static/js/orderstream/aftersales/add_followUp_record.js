$(function() {
    
     $('#isOverCheckBox').on('change',function () {
        if (this.checked) {
            $('#nextTime').prop("disabled", true);
            $('#nextTime').css({
                "background-color": "#f0f0f0", // 置灰背景
                "color": "#999" // 置灰文字
            });
            $('#nextTime').val("")
        } else {
            $('#nextTime').prop("disabled", false);
            $('#nextTime').css({
                "background-color": "", // 恢复背景
                "color": "" // 恢复文字颜色
            });
        }
    })
    
     $('#isOverCheckBox').trigger('change');
    
    
    $("#submit").click(function(){
        checkLogin();
        $(".warning").remove();
        $("input").removeClass("errorbor");

        /*if($("input[name='operationalMatters']").length == 0 ){
            warnTips("operationalMattersError"," 待操作事项不允许为空");
            return false;
        }*/
        if ($("#operationalMatters").val().length > 200){
            warnTips("operationalMattersError"," 待操作事项最多输入200字符，请检查后提交");
            return false;
        }

        if($("textarea[name='content']").val().length > 200){
            warnTips("contentError"," 售后内容不允许超过200个字符");
            return false;
        }
        
        if ($("textarea[name='content']").val().trim().length == 0) {
            warnTips("contentError", "售后内容必填");
            return false;
        }
        if($('#nextTime').val() == '' ){
            if(!document.getElementById('isOverCheckBox').checked){
                 warnTips("nextTimeError"," 下次跟进时间必填");
                 return false;
            }
        }
        
        const isOverCheckbox = document.getElementById('isOverCheckBox');
        const isOver = document.getElementById('isOver');

        // 如果复选框被选中，则设置隐藏输入的值为 1，否则为 0
        isOver.value = isOverCheckbox.checked ? '1' : '0';
        debugger
        $.ajax({
            url:page_url+'/order/afterSalesCommon/saveFollowUpRecord.do',
            data:$('#addFollowUpRecord').serialize(),
            type:"POST",
            dataType : "json",
            async: false,
            success:function(data){
                if (null!=data && data.code != 0) {
                    layer.alert(data.message);
                }else{
                    $('#cancle').click();
                    parent.location.reload();
                }
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });
        return false;
    });
});
