package com.vedeng.goods.service.impl;

import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.goods.GoodsCheckStatusEnum;
import com.vedeng.goods.dao.*;
import com.vedeng.goods.enums.SkuSynchronizationStatusEnum;
import com.vedeng.goods.model.*;
import com.vedeng.goods.model.dto.CoreSkuBaseDTO;
import com.vedeng.goods.model.dto.CoreSpuBaseDTO;
import com.vedeng.goods.service.BaseGoodsService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Service
public class BaseGoodsServiceImpl implements BaseGoodsService {

	Logger logger = LoggerFactory.getLogger(BaseGoodsServiceImpl.class);
	@Autowired
	CoreSpuGenerateExtendMapper coreSpuGenerateExtendMapper;
	@Autowired
	CoreSpuSearchGenerateMapper spuSearchGenerateMapper;
	@Autowired
	CoreSkuSearchGenerateMapper skuSearchGenerateMapper;
	@Autowired
	CoreSpuGenerateMapper coreSpuGenerateMapper;
	@Autowired
	CoreSkuGenerateMapper coreSkuGenerateMapper;
	@Override
	public CoreSpuBaseDTO selectSpuBaseById(Integer spuId) {
		return coreSpuGenerateExtendMapper.selectSpuBaseBySpuId(spuId);
	}

	@Override
	public CoreSkuBaseDTO selectSkuBaseById(Integer skuId) {
		return coreSpuGenerateExtendMapper.selectSkuBaseBySkuId(skuId);
	}

	@Override
	public CoreSkuBaseDTO selectSkuBaseByNo(String skuNo) {
		return coreSpuGenerateExtendMapper.selectSkuBaseBySkuNo(skuNo);
	}

	@Override
	public List<CoreSkuBaseDTO> selectSkuBaseByIds(String[] skuIds) {
		return coreSpuGenerateExtendMapper.selectSkuBaseBySkuIds(skuIds);
	}


	@Override
	public List<CoreSkuBaseDTO> selectSkuBaseByIdsWithOutStatus(String[] skuIds) {
		return coreSpuGenerateExtendMapper.selectSkuBaseBySkuIdsWithOutStatus(skuIds);
	}

	@Override
	public void mergeSpu(CoreSpuGenerate spu) {
		if(spu==null){
			return;
		}

		if(spu.getSpuId()!=null){
			coreSpuGenerateMapper.updateByPrimaryKeySelective(spu);
			if (spu.getApparatusType()!=null && spu.getApparatusType().equals(2)){
				coreSpuGenerateMapper.updateApparatusByPrimaryKey(spu);
			}
			//审核通过才同步
			if(GoodsCheckStatusEnum.isApprove(spu.getCheckStatus())){
				CoreSpuGenerate dbPo=coreSpuGenerateMapper.selectByPrimaryKey(spu.getSpuId());

				if(spuSearchGenerateMapper.selectByPrimaryKey(spu.getSpuId())==null){
					CoreSpuSearchGenerate searchInsert=new CoreSpuSearchGenerate();
					BeanUtils.copyProperties(dbPo,searchInsert);
					coreSpuGenerateExtendMapper.insertSpuSearch(searchInsert);
				}else{
					CoreSpuSearchGenerate searchInsert=new CoreSpuSearchGenerate();
					BeanUtils.copyProperties(dbPo,searchInsert);
					spuSearchGenerateMapper.updateByPrimaryKeySelective(searchInsert);
				}
			}
		}else{
			coreSpuGenerateMapper.insert(spu);
		}
	}

	@Override
	public void mergeSku(CoreSkuGenerate sku) {
		if(sku==null){
			return;
		}

		if(sku.getSkuId()!=null){
		coreSkuGenerateMapper.updateByPrimaryKeySelective(sku);
		try {
			CoreSkuGenerate coreSkuGenerate = coreSkuGenerateMapper.selectByPrimaryKey(sku.getSkuId());
			if(StringUtils.isEmpty(coreSkuGenerate)){
				return;
			}
			Integer spuid=coreSkuGenerate.getSpuId();
			CoreSpuGenerate updateSpu=new CoreSpuGenerate();
			updateSpu.setSpuId(spuid);
			updateSpu.setModTime(new Date());
			updateSpu.setUpdater(sku.getUpdater());
			coreSpuGenerateMapper.updateByPrimaryKeySelective(updateSpu);
		}catch (Exception e){
			logger.error("保存sku信息时同步更新SPU信息时失败 - skuId:{}",sku.getSkuId(), e);
		}

		//审核通过才同步至前台用的Search后缀商品表中
		if(GoodsCheckStatusEnum.isApprove(sku.getCheckStatus())){
			CoreSkuGenerate dbPo=coreSkuGenerateMapper.selectByPrimaryKey(sku.getSkuId());

			if(skuSearchGenerateMapper.selectByPrimaryKey(sku.getSkuId())==null){
				CoreSkuSearchGenerate searchInsert=new CoreSkuSearchGenerate();
				BeanUtils.copyProperties(dbPo,searchInsert);
				searchInsert.setMinOrder(dbPo.getMinOrder());
				coreSpuGenerateExtendMapper.insertSkuSearch(searchInsert);
			}else{
				CoreSkuSearchGenerate searchInsert=new CoreSkuSearchGenerate();
				BeanUtils.copyProperties(dbPo,searchInsert);
				searchInsert.setMinOrder(dbPo.getMinOrder());
				skuSearchGenerateMapper.updateByPrimaryKeySelective(searchInsert);
			}
		}
		}else{
			coreSkuGenerateMapper.insertSelective(sku);

		}
	}

	@Override
	public void initHistoryName(CoreSkuGenerate sku){
		CoreSkuGenerate preSku=coreSkuGenerateMapper.selectByPrimaryKey(sku.getSkuId());
		if(!ErpConst.THREE.equals(preSku.getCheckStatus())||sku.getShowName().equals(preSku.getShowName())){
			return;
		}
		sku.setIsNameChange(ErpConst.ONE);
		sku.setHistoryName(preSku.getShowName());
	}

	@Override
	@Deprecated
	public void mergeSkuByIds(CoreSkuGenerate generate, List<Integer> list) {
		CoreSkuGenerateExample example = new CoreSkuGenerateExample();
		example.createCriteria().andSkuIdIn(list);
		//CoreSkuSearchGenerate search=new CoreSkuSearchGenerate();
		//BeanUtils.copyProperties(generate,search);
		List<CoreSkuGenerate> listDb=coreSkuGenerateMapper.selectByExample(example);

		if(CollectionUtils.isNotEmpty(listDb)){
			for(CoreSkuGenerate sku:listDb){
				generate.setSkuId(sku.getSkuId());
				generate.setCheckStatus(sku.getCheckStatus());
				mergeSku(generate);
			}
		}
		///CoreSkuSearchGenerateExample exampleSearch = new CoreSkuSearchGenerateExample();
		//exampleSearch.createCriteria().andSkuIdIn(list);
		//skuSearchGenerateMapper.updateByExampleSelective(search, exampleSearch);
	}

	@Override
	public int updatePushStatusBySpuId(Integer spuId, int status) {
		return coreSkuGenerateMapper.updatePushStatusBySpuId(spuId,status);
	}


	@Override
	public int getPushStatusBySkuId(Integer skuId) {
		return coreSkuGenerateMapper.getPushStatusBySkuId(skuId);
	}

	@Override
	public Integer getOnsaleBySkuId(Integer skuId) {
		return coreSkuGenerateMapper.getSkuOnSaleBySkuId(skuId);
	}

	@Override
	public int updatePushStatusBySkuId(Integer skuId, int status) {
		return coreSkuGenerateMapper.updatePushStatusBySkuId(skuId,status);
	}

	@Override
	public int getSynchronizationStatusBySkuId(Integer skuId) {
		if (skuId == null || skuId <= 0){
			logger.error("获取同步状态时 error skuId:{}", skuId);
			return 0;
		}
		return coreSkuGenerateMapper.getSynchronizationStatusBySkuId(skuId);
	}

    @Override
    public void initSynchronizationStatus(CoreSkuGenerate sku) {
		/**
		 * 编辑SKU时如果SKU为非D级商品并且推送状态为已推送时需要将推送状态修改为需重推
		 */
        CoreSkuGenerate preSku=coreSkuGenerateMapper.selectByPrimaryKey(sku.getSkuId());
        if (preSku != null && preSku.getSynchronizationStatus() != null &&
                SkuSynchronizationStatusEnum.PUSHED.getStatus()
                        .equals(preSku.getSynchronizationStatus()) &&
				preSku.getGoodsLevelNo() != null && preSku.getGoodsLevelNo() != 4){
            sku.setSynchronizationStatus(SkuSynchronizationStatusEnum.RE_PUSH.getStatus());
        }
    }
}
