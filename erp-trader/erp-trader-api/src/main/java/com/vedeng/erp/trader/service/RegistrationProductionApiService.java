package com.vedeng.erp.trader.service;

import com.vedeng.erp.trader.dto.RegistrationProductionModeDto;

import java.util.List;

public interface RegistrationProductionApiService {

    List<RegistrationProductionModeDto> queryRegistrationProductionMode(Integer registrationNumberId);

    void saveRegistrationProductionMode(Integer registrationNumberId,List<RegistrationProductionModeDto> registrationProductionModeDtoList);

    /**
     *
     * @param registrationNumberId
     */
    void deleteRegistrationProductionMode(Integer registrationNumberId);
}
