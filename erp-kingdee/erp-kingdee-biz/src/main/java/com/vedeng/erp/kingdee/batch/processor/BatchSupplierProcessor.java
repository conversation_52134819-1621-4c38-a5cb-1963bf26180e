package com.vedeng.erp.kingdee.batch.processor;

import com.vedeng.erp.kingdee.batch.dto.BatchSupplierFinanceDto;
import com.vedeng.erp.kingdee.domain.entity.KingDeeSupplierEntity;
import com.vedeng.erp.kingdee.dto.KingDeeSupplierDto;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeSupplierMapper;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/5 11:05
 */
@Service
@Slf4j
public class BatchSupplierProcessor implements ItemProcessor<BatchSupplierFinanceDto, KingDeeSupplierDto> {

    @Autowired
    private KingDeeSupplierMapper kingDeeSupplierMapper;

    @Override
    public KingDeeSupplierDto process(BatchSupplierFinanceDto batchSupplierFinanceDto) throws Exception {
        KingDeeSupplierEntity kingDeeSupplier = kingDeeSupplierMapper.selectByFNumber(batchSupplierFinanceDto.getTraderSupplierId());
        if (kingDeeSupplier == null) {
            log.error("审计供应商信息未推送过金蝶，无法更新，supplier信息：{}", batchSupplierFinanceDto.getTraderSupplierId());
            return null;
        }

        KingDeeSupplierDto kingDeeSupplierDto = new KingDeeSupplierDto(KingDeeBizEnums.updateSupplier, batchSupplierFinanceDto.getTraderSupplierId(), batchSupplierFinanceDto.getTraderName());
        kingDeeSupplierDto.setTraderSupplierFinanceId(batchSupplierFinanceDto.getTraderSupplierFinanceId());
        kingDeeSupplierDto.setId(kingDeeSupplier.getId());
        kingDeeSupplierDto.setFSupplierId(kingDeeSupplier.getFSupplierId());
        kingDeeSupplierDto.setFSupplierClassify(batchSupplierFinanceDto.getTraderTypeName());
        return kingDeeSupplierDto;
    }
}