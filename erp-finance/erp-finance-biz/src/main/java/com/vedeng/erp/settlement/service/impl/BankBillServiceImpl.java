package com.vedeng.erp.settlement.service.impl;

import com.vedeng.erp.finance.domain.dto.BankBillCustomerAccountDto;
import com.vedeng.erp.finance.domain.entity.BankBillSettlementEntity;
import com.vedeng.erp.finance.dto.BankBillDto;
import com.vedeng.erp.finance.mapper.BankBillSettlementMapper;
import com.vedeng.erp.settlement.domain.entity.BankBillEntity;
import com.vedeng.erp.settlement.mapper.BankBillMapper;
import com.vedeng.erp.settlement.mapstruct.BankBillConvertor;
import org.springframework.stereotype.Service;

import org.springframework.beans.factory.annotation.Autowired;

import com.vedeng.erp.settlement.service.BankBillService;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class BankBillServiceImpl implements BankBillService {

    @Autowired
    private BankBillMapper bankBillMapper;
    @Autowired
    private BankBillSettlementMapper bankBillSettlementMapper;
    @Autowired
    BankBillConvertor bankBillConvertor;

    @Override
    public List<BankBillCustomerAccountDto> queryBankBillBySaleOrderId(Integer saleOrderId) {
        List<BankBillCustomerAccountDto> bankBillDtoList = bankBillMapper.queryBankBillBySaleOrderId(saleOrderId);
        return bankBillDtoList;
    }

    @Override
    public int deleteByPrimaryKey(Integer bankBillId) {
        return bankBillMapper.deleteByPrimaryKey(bankBillId);
    }

    @Override
    public int insert(BankBillEntity record) {
        return bankBillMapper.insert(record);
    }

    @Override
    public int insertSelective(BankBillEntity record) {
        return bankBillMapper.insertSelective(record);
    }

    @Override
    public BankBillEntity selectByPrimaryKey(Integer bankBillId) {
        return bankBillMapper.selectByPrimaryKey(bankBillId);
    }

    @Override
    public int updateByPrimaryKeySelective(BankBillEntity record) {
        return bankBillMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(BankBillEntity record) {
        return bankBillMapper.updateByPrimaryKey(record);
    }

    @Override
    public void create(BankBillDto bankBillDto) {
        BankBillEntity bankBillEntity = bankBillConvertor.toEntity(bankBillDto);
        insertSelective(bankBillEntity);
        bankBillDto.setBankBillId(bankBillEntity.getBankBillId());

        if (bankBillDto.getSettlementMethod() != null) {
            BankBillSettlementEntity bankBillSettlementEntity = new BankBillSettlementEntity();
            bankBillSettlementEntity.setBankBillId(bankBillEntity.getBankBillId());
            bankBillSettlementEntity.setSettlementMethod(bankBillDto.getSettlementMethod());
            bankBillSettlementMapper.insertSelective(bankBillSettlementEntity);
        }
    }

    @Override
    public List<BankBillDto> findAcceptanceBill() {
        return bankBillMapper.findAcceptanceBill();
    }


}
