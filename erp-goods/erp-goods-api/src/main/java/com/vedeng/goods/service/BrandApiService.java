package com.vedeng.goods.service;

import com.vedeng.goods.dto.BrandFrontDto;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface BrandApiService {
    /**
     * 影响范围：TraderSupplierBizServiceImpl授权书列表页查询品牌名称展示
     * <AUTHOR>
     * @desc 根据品牌id查询品牌名
     * @param brandId
     * @return
     */
    String queryBrandNameById(Integer brandId);

    /**
     * 查询品牌
     * @param brandName  品牌名 模糊查询
     * @param limit 限制条数
     * @return List<BrandFrontDto>
     */
    List<BrandFrontDto> queryBrand(String brandName,Integer limit);

    /**
     * 根据品牌id集合查询品牌信息
     *
     * @param brandIdList 品牌id集合
     * @return List<BrandFrontDto>
     */
    List<BrandFrontDto> getByBrandIdList(List<Integer> brandIdList);

    /**
     * 根据id查询
     * @param brandIds
     * @return
     */
    List<BrandFrontDto> getBrand(List<Integer> brandIds);

    /**
     * 查询交易品牌下拉框
     * @param
     * @return
     */
    List<BrandFrontDto> queryTransactionBrandDropdown();
}
