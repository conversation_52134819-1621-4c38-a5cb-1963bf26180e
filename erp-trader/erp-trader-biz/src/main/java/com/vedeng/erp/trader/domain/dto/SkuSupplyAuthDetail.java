package com.vedeng.erp.trader.domain.dto;

import java.util.Date;

public class SkuSupplyAuthDetail {
    /** 主键id  SKU_SUPPLY_AUTH_DETAIL_ID **/
    private Integer skuSupplyAuthDetailId;

    /** sku商品供应商授权书信息主表ID  SKU_SUPPLY_AUTH_ID **/
    private Integer skuSupplyAuthId;

    /** skuID  SKU_ID **/
    private Integer skuId;

    /** 产品名称  SKU_NAME **/
    private String skuName;

    /** skuNo  SKU_NO **/
    private String skuNo;

    /** 品牌  BRAND_NAME **/
    private String brandName;

    /**
     * 规格
     */
    private String spec;

    /** 型号  MODEL **/
    private String model;

    /** 是否删除0否1是  IS_DELETE **/
    private Integer isDelete;

    /** 创建时间  ADD_TIME **/
    private Date addTime;

    /** 创建者  CREATOR **/
    private Integer creator;

    /** 更新时间  MOD_TIME **/
    private Date modTime;

    /** 更新者  UPDATER **/
    private Integer updater;

    private Integer spuType;

    public Integer getSpuType() {
        return spuType;
    }

    public void setSpuType(Integer spuType) {
        this.spuType = spuType;
    }

    /**   主键id  SKU_SUPPLY_AUTH_DETAIL_ID   **/
    public Integer getSkuSupplyAuthDetailId() {
        return skuSupplyAuthDetailId;
    }

    /**   主键id  SKU_SUPPLY_AUTH_DETAIL_ID   **/
    public void setSkuSupplyAuthDetailId(Integer skuSupplyAuthDetailId) {
        this.skuSupplyAuthDetailId = skuSupplyAuthDetailId;
    }

    /**   sku商品供应商授权书信息主表ID  SKU_SUPPLY_AUTH_ID   **/
    public Integer getSkuSupplyAuthId() {
        return skuSupplyAuthId;
    }

    /**   sku商品供应商授权书信息主表ID  SKU_SUPPLY_AUTH_ID   **/
    public void setSkuSupplyAuthId(Integer skuSupplyAuthId) {
        this.skuSupplyAuthId = skuSupplyAuthId;
    }

    /**   skuID  SKU_ID   **/
    public Integer getSkuId() {
        return skuId;
    }

    /**   skuID  SKU_ID   **/
    public void setSkuId(Integer skuId) {
        this.skuId = skuId;
    }

    /**   产品名称  SKU_NAME   **/
    public String getSkuName() {
        return skuName;
    }

    /**   产品名称  SKU_NAME   **/
    public void setSkuName(String skuName) {
        this.skuName = skuName == null ? null : skuName.trim();
    }

    /**   skuNo  SKU_NO   **/
    public String getSkuNo() {
        return skuNo;
    }

    /**   skuNo  SKU_NO   **/
    public void setSkuNo(String skuNo) {
        this.skuNo = skuNo == null ? null : skuNo.trim();
    }

    /**   品牌  BRAND_NAME   **/
    public String getBrandName() {
        return brandName;
    }

    /**   品牌  BRAND_NAME   **/
    public void setBrandName(String brandName) {
        this.brandName = brandName == null ? null : brandName.trim();
    }

    public String getSpec() {
        return spec;
    }

    public void setSpec(String spec) {
        this.spec = spec;
    }

    /**   规格型号  MODEL   **/
    public String getModel() {
        return model;
    }

    /**   规格型号  MODEL   **/
    public void setModel(String model) {
        this.model = model == null ? null : model.trim();
    }

    /**   是否删除0否1是  IS_DELETE   **/
    public Integer getIsDelete() {
        return isDelete;
    }

    /**   是否删除0否1是  IS_DELETE   **/
    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    /**   创建时间  ADD_TIME   **/
    public Date getAddTime() {
        return addTime;
    }

    /**   创建时间  ADD_TIME   **/
    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    /**   创建者  CREATOR   **/
    public Integer getCreator() {
        return creator;
    }

    /**   创建者  CREATOR   **/
    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    /**   更新时间  MOD_TIME   **/
    public Date getModTime() {
        return modTime;
    }

    /**   更新时间  MOD_TIME   **/
    public void setModTime(Date modTime) {
        this.modTime = modTime;
    }

    /**   更新者  UPDATER   **/
    public Integer getUpdater() {
        return updater;
    }

    /**   更新者  UPDATER   **/
    public void setUpdater(Integer updater) {
        this.updater = updater;
    }
}