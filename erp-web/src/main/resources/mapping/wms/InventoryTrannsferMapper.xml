<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wms.inventorytransfer.dao.InventoryTransferMapper">
    <resultMap id="InventoryTransferDtoResultMap" type="com.wms.inventorytransfer.model.dto.InventoryTransferDto">
        <result column="INVENTORY_TRANSFER_ID" property="inventoryTransferId" jdbcType="INTEGER"/>
        <result column="TYPE" property="type" jdbcType="INTEGER"/>
        <result column="STATUS" property="status" jdbcType="TINYINT"/>
        <collection property="inventoryTransferDetailDtos"
                    ofType="com.wms.inventorytransfer.model.dto.InventoryTransferDetailDto"
                    javaType="java.util.ArrayList">
            <result column="INVENTORY_TRANSFER_DETAIL_ID" property="inventoryTransferDetailId" jdbcType="INTEGER"/>
            <result column="SKU_NO" property="skuNo" jdbcType="VARCHAR"/>
            <result column="NUM" property="num" jdbcType="INTEGER"/>
            <result column="TRANSFER_NUM" property="transferNum" jdbcType="INTEGER"/>
            <result column="FROM_WAREHOUSE_ID" property="fromWarehouseId" jdbcType="INTEGER"/>
            <result column="TO_WAREHOUSE_ID" property="toWarehouseId" jdbcType="INTEGER"/>
        </collection>
    </resultMap>

    <select id="getInventoryTransferVosListPage" resultType="com.wms.inventorytransfer.model.vo.InventoryTransferVO">
        SELECT
        INVENTORY_TRANSFER_ID,
        INVENTORY_TRANSFER_NO,
        TYPE,
        STATUS,
        TO_WAREHOUSE_ID,
        REASONS,
        COMMENTS,
        ORDER_ID,
        ORDER_NO,
        ADD_TIME
        FROM
        `T_WMS_INVENTORY_TRANSFER`
        WHERE 1 = 1
        <if test="inventoryTransferDto != null">
            <if test="inventoryTransferDto.inventoryTransferNo != null and inventoryTransferDto.inventoryTransferNo != ''">
                AND INVENTORY_TRANSFER_NO LIKE
                CONCAT('%',#{inventoryTransferDto.inventoryTransferNo,jdbcType=VARCHAR},'%' )
            </if>
            <if test="inventoryTransferDto.type != null and inventoryTransferDto.type != 0">
                AND TYPE = #{inventoryTransferDto.type,jdbcType=INTEGER}
            </if>
            <if test="inventoryTransferDto.status != null and inventoryTransferDto.status != 0">
                AND STATUS = #{inventoryTransferDto.status,jdbcType=INTEGER}
            </if>
            <if test="inventoryTransferDto.orderNo != null and inventoryTransferDto.orderNo != ''">
                AND ORDER_NO LIKE
                CONCAT('%',#{inventoryTransferDto.orderNo,jdbcType=VARCHAR},'%' )
            </if>
            <if test="inventoryTransferDto.startTime != null">
                AND ADD_TIME <![CDATA[ >= ]]> #{inventoryTransferDto.startTime,jdbcType=BIGINT}
            </if>
            <if test="inventoryTransferDto.endTime != null">
                AND ADD_TIME <![CDATA[ <= ]]> #{inventoryTransferDto.endTime,jdbcType=BIGINT}
            </if>
        </if>
        ORDER BY
            ADD_TIME DESC
    </select>

    <select id="getInventoryTransferDetailById"
            resultType="com.wms.inventorytransfer.model.vo.InventoryTransferDetailVO">
        SELECT
            INVENTORY_TRANSFER_DETAIL_ID,
            INVENTORY_TRANSFER_ID,
            SKU_NO,
            GOODS_NAME,
            NUM,
            FROM_WAREHOUSE_ID,
            TO_WAREHOUSE_ID,
            TRANSFER_NUM
        FROM
            `T_WMS_INVENTORY_TRANSFER_DETAIL`
        WHERE
            INVENTORY_TRANSFER_ID = #{inventoryTransferId,jdbcType=INTEGER}
    </select>

    <select id="getInventoryTransferById" resultType="com.wms.inventorytransfer.model.vo.InventoryTransferVO">
        SELECT
            INVENTORY_TRANSFER_ID,
            INVENTORY_TRANSFER_NO,
            TYPE,
            STATUS,
            TO_WAREHOUSE_ID,
            REASONS,
            COMMENTS,
            ADD_TIME
        FROM
            `T_WMS_INVENTORY_TRANSFER`
        WHERE
            INVENTORY_TRANSFER_ID =  #{inventoryTransferId,jdbcType=INTEGER}
    </select>

    <insert id="insertInventoryTransfer" parameterType="com.wms.inventorytransfer.model.po.InventoryTransferPO"
            useGeneratedKeys="true" keyProperty="inventoryTransferId">
        INSERT INTO
        `T_WMS_INVENTORY_TRANSFER`
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="inventoryTransferId != null">
                INVENTORY_TRANSFER_ID,
            </if>
            <if test="inventoryTransferNo != null">
                INVENTORY_TRANSFER_NO,
            </if>
            <if test="actionId != null">
                ACTION_ID,
            </if>
            <if test="type != null">
                TYPE,
            </if>
            <if test="status != null">
                STATUS,
            </if>
            <if test="toWarehouseId != null">
                TO_WAREHOUSE_ID,
            </if>
            <if test="reasons != null">
                REASONS,
            </if>
            <if test="comments != null">
                COMMENTS,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="creator != null">
                CREATOR
            </if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="inventoryTransferId != null">
                #{inventoryTransferId,jdbcType=INTEGER},
            </if>
            <if test="inventoryTransferNo != null">
                #{inventoryTransferNo,jdbcType=VARCHAR},
            </if>
            <if test="actionId != null">
                #{actionId,jdbcType=INTEGER},
            </if>
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="toWarehouseId != null">
                #{toWarehouseId,jdbcType=INTEGER},
            </if>
            <if test="reasons != null">
                #{reasons,jdbcType=VARCHAR},
            </if>
            <if test="comments != null">
                #{comments,jdbcType=VARCHAR},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER}
            </if>
        </trim>
    </insert>

    <insert id="insertInventoryTransferDetail" parameterType="com.wms.inventorytransfer.model.po.InventoryTransferDetailPO"
            useGeneratedKeys="true" keyProperty="inventoryTransferDetailId">
        INSERT INTO
        `T_WMS_INVENTORY_TRANSFER_DETAIL`
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="inventoryTransferDetailId != null">
                INVENTORY_TRANSFER_DETAIL_ID,
            </if>
            <if test="inventoryTransferId != null">
                INVENTORY_TRANSFER_ID,
            </if>
            <if test="skuNo != null">
                SKU_NO,
            </if>
            <if test="goodsName != null">
                GOODS_NAME,
            </if>
            <if test="num != null">
                NUM,
            </if>
            <if test="transferNum != null">
                TRANSFER_NUM,
            </if>
            <if test="fromWarehouseId != null">
                FROM_WAREHOUSE_ID,
            </if>
            <if test="toWarehouseId != null">
                TO_WAREHOUSE_ID,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="creator != null">
                CREATOR
            </if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="inventoryTransferDetailId != null">
                #{inventoryTransferDetailId,jdbcType=INTEGER},
            </if>
            <if test="inventoryTransferId != null">
                #{inventoryTransferId,jdbcType=INTEGER},
            </if>
            <if test="skuNo != null">
                #{skuNo,jdbcType=VARCHAR},
            </if>
            <if test="goodsName != null">
                #{goodsName,jdbcType=VARCHAR},
            </if>
            <if test="num != null">
                #{num,jdbcType=INTEGER},
            </if>
            <if test="transferNum != null">
                #{transferNum,jdbcType=INTEGER},
            </if>
            <if test="fromWarehouseId != null">
                #{fromWarehouseId,jdbcType=INTEGER},
            </if>
            <if test="toWarehouseId != null">
                #{toWarehouseId,jdbcType=INTEGER},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER}
            </if>
        </trim>
    </insert>

    <select id="getInventoryTransferInfoByActionId" resultMap="InventoryTransferDtoResultMap">
        SELECT
            T1.INVENTORY_TRANSFER_ID,
            T1.TYPE,
            T1.STATUS,
            T2.INVENTORY_TRANSFER_DETAIL_ID,
            T2.INVENTORY_TRANSFER_ID,
            T2.SKU_NO,
            T2.NUM,
            T2.TRANSFER_NUM,
            T2.FROM_WAREHOUSE_ID,
            T2.TO_WAREHOUSE_ID
        FROM
            T_WMS_INVENTORY_TRANSFER T1
            LEFT JOIN T_WMS_INVENTORY_TRANSFER_DETAIL T2 ON T1.INVENTORY_TRANSFER_ID = T2.INVENTORY_TRANSFER_ID
        WHERE
            T1.ACTION_ID = #{actionId，jdbcType=INTEGER}
    </select>

    <update id="updateInventoryTransfer" parameterType="com.wms.inventorytransfer.model.po.InventoryTransferPO">
        UPDATE T_WMS_INVENTORY_TRANSFER
        <set >
            <if test="inventoryTransferNo != null" >
                INVENTORY_TRANSFER_NO = #{inventoryTransferNo,jdbcType=VARCHAR},
            </if>
            <if test="actionId != null" >
                ACTION_ID = #{actionId,jdbcType=INTEGER},
            </if>
            <if test="type != null" >
                TYPE = #{type,jdbcType=INTEGER},
            </if>
            <if test="status != null" >
                STATUS = #{status,jdbcType=INTEGER},
            </if>
            <if test="toWarehouseId != null" >
                TO_WAREHOUSE_ID = #{toWarehouseId,jdbcType=INTEGER},
            </if>
            <if test="reasons != null" >
                REASONS = #{reasons,jdbcType=VARCHAR},
            </if>
            <if test="comments != null" >
                COMMENTS = #{comments,jdbcType=VARCHAR},
            </if>
            <if test="orderId != null" >
                ORDER_ID = #{orderId,jdbcType=INTEGER},
            </if>
            <if test="orderNo != null" >
                ORDER_NO = #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null" >
                UPDATE_TIME = #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null" >
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
        </set>
        WHERE INVENTORY_TRANSFER_ID = #{inventoryTransferId,jdbcType=INTEGER}
    </update>

    <update id="updateInventoryTransferDetail" parameterType="com.wms.inventorytransfer.model.po.InventoryTransferDetailPO">
        UPDATE T_WMS_INVENTORY_TRANSFER_DETAIL
        <set >
            <if test="skuNo != null" >
                SKU_NO = #{skuNo,jdbcType=VARCHAR},
            </if>
            <if test="goodsName != null" >
                GOODS_NAME = #{goodsName,jdbcType=VARCHAR},
            </if>
            <if test="num != null" >
                NUM = NUM + #{num,jdbcType=INTEGER},
            </if>
            <if test="transferNum != null" >
                TRANSFER_NUM = TRANSFER_NUM + #{transferNum,jdbcType=INTEGER},
            </if>
            <if test="fromWarehouseId != null" >
                FROM_WAREHOUSE_ID = #{fromWarehouseId,jdbcType=INTEGER},
            </if>
            <if test="toWarehouseId != null" >
                TO_WAREHOUSE_ID = #{toWarehouseId,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null" >
                UPDATE_TIME = #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null" >
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
        </set>
        WHERE INVENTORY_TRANSFER_DETAIL_ID = #{inventoryTransferDetailId,jdbcType=INTEGER}
    </update>

    <select id="getInventoryTransferByNo" resultType="com.wms.inventorytransfer.model.po.InventoryTransferPO">
        SELECT
            INVENTORY_TRANSFER_ID,
            INVENTORY_TRANSFER_NO,
            ACTION_ID,
            TYPE,
            STATUS,
            TO_WAREHOUSE_ID,
            REASONS,
            COMMENTS
        FROM
            `T_WMS_INVENTORY_TRANSFER`
        WHERE
            INVENTORY_TRANSFER_NO = #{inventoryTransferDetailNo,jdbcType=VARCHAR}
    </select>

</mapper>