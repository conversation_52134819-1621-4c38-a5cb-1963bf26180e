<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ include file="../../common/common.jsp" %>
<div class="tcenter" style="background-color: #f3f3f3;padding:10px">
    <div style="font-size: 18px;font-weight: 700;display: inline-block;">关联订单</div>
</div>
<div class="searchfunc">
    <form method="post"
          action="${pageContext.request.contextPath}/order/saleorder/linkQuoteOrderChoosePage.do"
          id="search">
        <input type="hidden" name="quoteId" id="quoteId" value="${quoteId}">
        <ul>
            <li>
                <label class="infor_name">订单号</label>
                <input type="text" class="input-middle" name="saleorderNo" id="saleorderNo"
                       value="${saleorder.saleorderNo}"/>
            </li>
            <li>
                <label class="infor_name">客户ID</label>
                <input type="text" class="input-middle" name="traderId" id="traderId" value="${saleorder.traderId}"/>
            </li>
            <li>
                <label class="infor_name">客户名称</label>
                <input type="text" class="input-middle" name="traderName" id="traderName"
                       value="${saleorder.traderName}"/>
            </li>
            <li>
                <label class="infor_name">联系人</label>
                <input type="text" class="input-middle" name="traderContactName"
                       value="${saleorder.traderContactName}"/>
            </li>
            <li>
                <label class="infor_name">产品名称</label>
                <input type="text" class="input-middle" name="productName" value="${saleorder.productName}"/>
            </li>
            <li>
                <label class="infor_name">订货号</label>
                <input type="text" class="input-middle" name="skuNo" value="${saleorder.skuNo}"/>
            </li>
            <li>
                <label class="infor_name">订单状态</label>
                <select class="input-middle" name="status" id="status">
                    <option value="-1">全部</option>
                    <option
                            <c:if test="${saleorder.status eq 0}">selected</c:if> value="0">待确认
                    </option>
                    <option
                            <c:if test="${saleorder.status eq 1}">selected</c:if> value="1">进行中
                    </option>

                </select>
            </li>
            <li>
                <label class="infor_name">订单创建时间</label>
                <input class="Wdate f_left input-smaller96 mr5" type="text" placeholder="请选择日期" autocomplete="off"
                       onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'searchEndTimeStr\')}'})"
                       name="searchBeginTimeStr" id="searchBeginTimeStr"
                       value='<date:date value ="${saleorder.beginTime}" format="yyyy-MM-dd"/>'>
                <div class="gang">-</div>
                <input class="Wdate f_left input-smaller96" type="text" placeholder="请选择日期" autocomplete="off"
                       onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'searchBeginTimeStr\')}'})"
                       name="searchEndTimeStr" id="searchEndTimeStr"
                       value='<date:date value ="${saleorder.endTime}" format="yyyy-MM-dd"/>'>
            </li>
        </ul>
        <div class="tcenter">
			<span class="bt-small bg-light-blue bt-bg-style mr20 "
                  onclick="search();" id="searchSpan">搜索</span> <span
                class="bt-small bg-light-blue bt-bg-style mr20" onclick="reset();">重置</span>

            <!-- <span class="bg-light-blue bt-bg-style bt-small" onclick="exportList()">导出列表</span> -->
        </div>
    </form>
</div>
<div class="content">
    <div>
        <div style='width:100%;'>
            <table class="table table-bordered table-striped table-condensed table-centered">
                <thead>
                <tr>
                    <th class="wid1">选择</th>
                    <th class="wid3">订单号</th>
                    <th class="wid2">客户ID</th>
                    <th class="wid10">客户名称</th>
                    <th class="wid2">联系人</th>
                    <th class="wid4">手机号</th>
                    <th class="wid10">产品名称</th>
                    <th class="wid2">订货号</th>
                    <th class="wid3">订单原金额</th>
                    <th class="wid3">优惠金额</th>
                    <th class="wid3">订单实际金额</th>
                    <th class="wid2">订单状态</th>
                    <th class="wid4">订单创建时间</th>
                    <th class="wid2">归属销售</th>
                </tr>
                </thead>
                <tbody>
                <c:forEach var="s" items="${saleorderList}" varStatus="num">
                    <tr>
                        <td>
                            <input type="radio" name="orderId" value="${s.saleorderId}">
                        </td>
                        <td>
                            <a class="addtitle" href="javascript:void(0);"
                               tabTitle='{"num":"viewsaleorder${list.saleorderId}","link":"./order/saleorder/view.do?saleorderId=${s.saleorderId}","title":"订单信息"}'>
                                    ${s.saleorderNo}
                            </a>
                        </td>
                        <td>
                                ${s.traderId}
                        </td>
                        <td>
                            <a class="addtitle" href="javascript:void(0);"
                               tabTitle='{"num":"viewcustomer<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											"link":"./trader/customer/baseinfo.do?traderId=${s.traderId}",
											"title":"客户信息"}'>${s.traderName}</a>
                        </td>
                        <td>
                                ${s.traderContactName}
                        </td>
                        <td>
                                ${s.traderContactMobile}
                        </td>
                        <td>
                            <c:if test="${not empty s.goodsList}">
                                <c:forEach items="${s.goodsList}" var="g" varStatus="i" end="1">
                                    <span style="display: inline-block;float: left;text-align: left;height: auto;line-height: 18px"
                                          class="title-click addtitle"
                                          tabTitle='{"num":"view${g.sku}","link":"./goods/vgoods/viewSkuBySkuId.do?skuNo=${g.sku}","title":"sku详情"}'>${g.goodsName}
                                        <c:if test="${!i.last}">,</c:if>
                                    </span>
                                </c:forEach>
                                <c:if test="${fn:length(s.goodsList) > 2}">
                                    <span style="display: inline-block;float: left;text-align: left;height: auto;line-height: 18px"
                                          class="title-click">
                                          等
                                    </span>
                                </c:if>
                            </c:if>
                        </td>
                        <td>
                            <c:if test="${not empty s.goodsList}">
                                <c:forEach items="${s.goodsList}" var="g" varStatus="i" end="1">
                                    ${g.sku}
                                    <c:if test="${!i.last}">,</c:if>
                                </c:forEach>
                                <c:if test="${fn:length(s.goodsList) > 2}">
                                    等
                                </c:if>
                            </c:if>
                        </td>
                        <td>${s.totalAmount}</td>
                        <td>${s.couponInfo == null ? "0" : s.couponInfo.denomination}</td>
                        <td>${s.realAmount}</td>
                        <td>
                            <c:choose>
                                <c:when test="${s.status eq 0}">待确认</c:when>
                                <c:when test="${s.status eq 1}">进行中</c:when>
                                <c:when test="${s.status eq 2}">已完结</c:when>
                                <c:when test="${s.status eq 3}">已关闭</c:when>
                                <c:when test="${s.status eq 4}">待用户确认</c:when>
                                <c:otherwise></c:otherwise>
                            </c:choose>
                        </td>
                        <td><date:date value="${s.addTime}"/></td>
                        <td>${s.optUserName}</td>
                    </tr>
                </c:forEach>
                <c:if test="${empty saleorderList}">
                    <!-- 查询无结果弹出 -->
                    <tr>
                        <td colspan="14">查询无结果！请尝试使用其他搜索条件。</td>
                    </tr>
                </c:if>
                </tbody>
            </table>
        </div>
    </div>
    <div>
        <tags:page page="${page}"/>
    </div>
    <div class="clear"></div>
    <div class="add-tijiao tcenter" style="background-color: #f3f3f3;padding:10px">
        <input type="hidden" id="formToken" value="${formToken}"/>
        <span class="bt-small bg-light-blue bt-bg-style mr20" onclick="chooseCallBack()">提交</span>
        <button class="dele" id="close-layer" type="button">取消</button>
    </div>
</div>
<script>
    function chooseCallBack() {
        var url = page_url + "/order/quote/quoteLinkOrder.do"
        var quoteId = $("#quoteId").val();
        if (!quoteId) {
            layer.alert("请确认报价单。");
        }

        var orderId = $("input[name='orderId']:checked").val();
        var formToken = $('#formToken').val();
        console.log('orderId:' + orderId);
        if (orderId == undefined || orderId == '') {
            layer.alert('请选择需要关联的订单');
            return;
        }
        $.ajax({
            type: "POST",
            url: url,
            data: {'quoteId': quoteId, 'bdOrderId': orderId,'formToken': formToken},
            dataType: 'json',
            success: function (data) {
                if (data.code == -1) {
                    layer.alert(data.message);
                } else if (data.code == 0) {
                    window.parent.linkBd(1);
                    layer.closeAll();
                }
            },
            error: function (data) {
                if (data.status == 1001) {
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                    return;
                }
                console.log(data);
            }
        });

    }
</script>