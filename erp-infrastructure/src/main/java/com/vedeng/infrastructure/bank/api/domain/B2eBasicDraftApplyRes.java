package com.vedeng.infrastructure.bank.api.domain;

import com.vedeng.infrastructure.bank.api.domain.base.BankResponse;
import lombok.*;

import javax.xml.bind.annotation.*;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 融资预申请返回体
 * @date 2024/10/12 11:26
 */
@Getter
@Setter
@XmlRootElement(name = "CMBC")
@XmlAccessorType(XmlAccessType.FIELD)
public class B2eBasicDraftApplyRes extends BankResponse {

    /**
     * 响应体
     */
    @XmlElement(name = "xDataBody")
    private B2eBasicDraftApplyResBody xDataBody;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @XmlAccessorType(XmlAccessType.FIELD)
    public static class B2eBasicDraftApplyResBody {

        /**
         * 银行渠道交易流水号
         */
        private String svrId;

        /**
         * 客户技术请求流水号，同一客户请勿重复
         */
        private String trnId;

        /**
         * 外部融资申请编号
         */
        private String outApplicationNo;

        /**
         * 借据号
         */
        private String creditCode;

        /**
         * 收款人信息列表校验结果
         */
        @XmlElementWrapper(name = "checkResultList")
        @XmlElement(name = "Map")
        private List<CheckResult> checkResultList;
        /**
         * 待签章文件信息列表
         */
        @XmlElementWrapper(name = "fileInfoList")
        @XmlElement(name = "Map")
        private List<FileInfo> fileInfoList;

    }


    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    public static class CheckResult {

        /**
         * 电票帐号
         */
        private String elecAcctNo;

        /**
         * 电票账号名称
         */
        private String elecAcctName;

        /**
         * 账号开户行行号
         */
        private String elecBankNo;

        /**
         * 校验结果
         */
        private String result;

        /**
         * 校验未通过原因
         */
        private String failReason;

    }


    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    public static class FileInfo {

        /**
         * 账号类型
         * 01 活期 02 定期
         */
        private String fileSealHash;


        /**
         * 保证金账号
         */
        private String cntrNum;


        /**
         * 开票保证金金额
         */
        private String id;

        /**
         * 开票保证金金额
         */
        private String imageId;


        /**
         * 开票保证金金额
         */
        private String fileName;


        /**
         * 文件类型
         * 119 授信申请书
         * 18 承兑协议
         * 48 信用信息查询使用授权书
         */
        private String fileType;


    }

}
