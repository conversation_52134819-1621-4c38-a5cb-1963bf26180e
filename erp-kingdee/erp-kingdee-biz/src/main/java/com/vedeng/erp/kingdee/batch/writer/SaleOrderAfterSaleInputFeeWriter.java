package com.vedeng.erp.kingdee.batch.writer;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.batch.dto.BatchPayExpensesDto;
import com.vedeng.erp.kingdee.dto.InPutFeePlainInvoiceDto;
import com.vedeng.erp.kingdee.dto.InPutFeeSpecialInvoiceDto;
import com.vedeng.erp.kingdee.service.KingDeeInPutFeePlainInvoiceApiService;
import com.vedeng.erp.kingdee.service.KingDeeInPutFeeSpecialInvoiceApiService;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SaleOrderAfterSaleInputFeeWriter extends BaseWriter<BatchPayExpensesDto> {
    @Autowired
    KingDeeInPutFeePlainInvoiceApiService kingDeeInPutFeePlainInvoiceApiService;
    @Autowired
    KingDeeInPutFeeSpecialInvoiceApiService kingDeeInPutFeeSpecialInvoiceApiService;
    @Override
    public void doWrite(BatchPayExpensesDto dto, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("销售售后录票(蓝字有效),进项票推送金蝶,BatchPayExpensesDto:{}", JSONUtil.toJsonStr(dto));

        //进项费用普票
        InPutFeePlainInvoiceDto plainDto = dto.getInPutFeePlainInvoiceDto();
        if (ObjectUtil.isNotNull(plainDto)){
        plainDto.setKingDeeBizEnums(KingDeeBizEnums.saveInPutFeePlainInvoice);
        kingDeeInPutFeePlainInvoiceApiService.register(plainDto,true);
        }

        //进项费用专票
        InPutFeeSpecialInvoiceDto specialDto = dto.getInPutFeeSpecialInvoiceDto();
        if (ObjectUtil.isNotNull(specialDto)){
        specialDto.setKingDeeBizEnums(KingDeeBizEnums.saveInPutFeeSpecialInvoice);
        kingDeeInPutFeeSpecialInvoiceApiService.register(specialDto,true);
        }
    }
}
