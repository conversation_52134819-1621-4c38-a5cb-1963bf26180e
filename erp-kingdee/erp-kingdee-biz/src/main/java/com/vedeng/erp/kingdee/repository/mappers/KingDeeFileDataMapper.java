package com.vedeng.erp.kingdee.repository.mappers;

import com.vedeng.erp.kingdee.domain.entity.KingDeeFileDataEntity;

import java.util.List;

public interface KingDeeFileDataMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(KingDeeFileDataEntity record);

    int insertSelective(KingDeeFileDataEntity record);

    KingDeeFileDataEntity selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(KingDeeFileDataEntity record);

    int updateByPrimaryKey(KingDeeFileDataEntity record);

    /**
     * 根据formId、erpId和附件的uri查询该附件是否已推送
     *
     * @param query 查询入参
     * @return List<KingDeeFileDataEntity>
     */
    List<KingDeeFileDataEntity> getByBusinessIdAndUri(KingDeeFileDataEntity query);
}