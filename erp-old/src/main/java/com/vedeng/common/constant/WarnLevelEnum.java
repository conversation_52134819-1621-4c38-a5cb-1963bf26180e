package com.vedeng.common.constant;

import com.vedeng.common.util.StringUtil;

/**
 * @Description:
 * @Author:       davis
 * @Date:         2021/4/16 下午4:43
 * @Version:      1.0
 */
public enum WarnLevelEnum {
    ONE_L(1 , "一级"),
    TWO_L(2, "二级"),
    THREE_L(3, "三级");

    WarnLevelEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    private Integer code;

    private String msg;

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    /**
     * 根据编码获取编码信息
     * @param code
     * @return
     */
    public static String codeToMessage(Integer code){
        if(code == null){
            return "";
        }
        for(WarnLevelEnum warnLevelEnum : WarnLevelEnum.values()){
            if(code.equals(warnLevelEnum.getCode())){
                return warnLevelEnum.getMsg();
            }
        }
        return "";
    }

}
