package com.vedeng.api.standard.adapter.peerlist.dto;

import cn.hutool.core.util.StrUtil;
import com.vedeng.api.standard.core.exception.ApiStandardException;
import lombok.Data;

/**
 * 查询采购单出入库记录请求DTO
 */
@Data
public class PeerListStockQueryRequest {

    /**
     * 采购单
     */
    private String buyOrderNo;
    
    /**
     * 是否是第一个节点
     */
    private Integer isFirst;

    /**
     * 验证请求参数
     */
    public void validate() {
        if (StrUtil.isBlank(buyOrderNo)) {
            throw ApiStandardException.dataConversionError("采购单号不能为空");
        }
    }
} 
