package com.vedeng.erp.kingdee.repository.mappers;

import com.vedeng.erp.kingdee.domain.entity.KingDeePayBillEntity;

import java.util.List;

import com.vedeng.erp.kingdee.dto.KingDeePayBillDto;
import org.apache.ibatis.annotations.Param;

public interface KingDeePayBillMapper {
    /**
     * delete by primary key
     * @param kingDeePayBillId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer kingDeePayBillId);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(KingDeePayBillEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(KingDeePayBillEntity record);

    /**
     * select by primary key
     * @param kingDeePayBillId primary key
     * @return object by primary key
     */
    KingDeePayBillEntity selectByPrimaryKey(Integer kingDeePayBillId);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(KingDeePayBillEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(KingDeePayBillEntity record);

    List<KingDeePayBillEntity> findByAll(KingDeePayBillEntity kingDeePayBillEntity);

    int updateBatchSelective(List<KingDeePayBillEntity> list);

    int batchInsert(@Param("list") List<KingDeePayBillEntity> list);

    /**
     * 查询没有回单的付款申请单
     * @return List<KingDeePayBillDto>
     */
    List<KingDeePayBillDto> queryKingDeePayBillNoReturnUrl();

    /**
     * 查询没有回单的付款申请单 给定的集合里
     * @param kingDeePayBillIds
     * @return List<KingDeePayBillDto>
     */
    List<KingDeePayBillDto> queryKingDeePayBillNoReturnUrlAndIds(@Param("list") List<Integer> kingDeePayBillIds);

    /**
     * <AUTHOR>
     * @desc 根据付款申请id作废金蝶付款单信息记录
     * @return
     */
    int deleteKingDeeInfoByFBillNo(String fBillNo);

    int updatePushStatus(Integer kingDeeReceiveBillId);

    /**
     * 根据银行流水id查询付款单
     *
     * @param billId
     * @param bankBillId
     * @return
     */
    List<KingDeePayBillDto> getKingDeePayByBankBillId(@Param("bankBillId") Integer bankBillId, @Param("fileIsPush") Integer fileIsPush);

    List<KingDeePayBillDto> queryKingDeeReceiveRefundBillNoReturnUrl();

    int deleteReceiveRefundInfoByFBillNo(String fBillNo);

    KingDeePayBillDto selectByFBillNo(String fBillNo);
}
