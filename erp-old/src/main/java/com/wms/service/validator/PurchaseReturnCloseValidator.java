package com.wms.service.validator;

import com.alibaba.fastjson.JSON;
import com.vedeng.aftersales.dao.AfterSalesGoodsMapper;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSalesGoods;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.OrderConstant;
import com.vedeng.common.constant.SpecialDeliveryEnum;
import com.vedeng.common.constant.stock.StockOperateTypeConst;
import com.vedeng.common.util.DateUtil;
import com.vedeng.order.dao.BuyorderGoodsMapper;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.model.Buyorder;
import com.vedeng.system.service.UserService;
import com.wms.constant.WMSContant;
import com.wms.constant.WmsInterfaceOrderType;
import com.wms.constant.WmsLogicalOperateTypeEnum;
import com.wms.dao.WmsLogicalOrdergoodsMapper;
import com.wms.dto.PutPurchaseOrderDto;
import com.wms.dto.PutPurchaseOrderGoodsDto;
import com.wms.model.dto.ValidatorResult;
import com.wms.model.po.WmsLogicalOrdergoods;
import com.wms.service.*;
import com.wms.service.chain.Build.StepBuildFactory;
import com.wms.service.chain.HandlerStep;
import com.wms.service.chain.HandlerStepContext;
import com.wms.service.listenner.PutSaleAfterReturnFinshLister;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 采购退货关闭校验器
 */
@Service
public class PurchaseReturnCloseValidator extends AbstractValidator{

    public static Logger logger = LoggerFactory.getLogger(PutSaleAfterReturnFinshLister.class);

    public static final Integer UN_EFFECT = 0;

    @Autowired
    private WmsLogicalOrdergoodsMapper wmsLogicalOrdergoodsMapper;

    @Autowired
    private WMSInterfaceFactory wmsInterfaceFactory;

    @Autowired
    private BuyorderMapper buyorderMapper;

    @Resource
    private BuyorderGoodsMapper buyorderGoodsMapper;

    @Resource
    private AfterSalesGoodsMapper afterSalesGoodsMapper;

    @Resource
    private UserService userService;

    @Autowired
    private StepBuildFactory stepBuildFactory;

    @Autowired
    private CancelTypeService cancelTypeService;

    @Resource
    private AfterSalesMapper afterSalesMapper;

    @Autowired
    private DoPutService doPutService;

    @Override
    protected boolean needToValidate(Object[] requestData) {

        AfterSalesVo afterSalesVo = (AfterSalesVo) requestData[0];

        return afterSalesVo.getSubjectType() == 536 && afterSalesVo.getType() == 546;
    }

    @Override
    protected ValidatorResult doValidator(Object[] requestData) throws Exception {

        AfterSalesVo afterSaleDB = (AfterSalesVo) requestData[0];

        if(afterSaleDB.getAfterSalesId() == null){
            throw new Exception("PurchaseReturnCloseValidator error 售后单ID为空");
        }

        afterSaleDB = afterSalesMapper.getAfterSaleVoInfoById(afterSaleDB.getAfterSalesId());

        logger.info("采购单退货关闭入口:" + JSON.toJSONString(afterSaleDB));

        Buyorder buyorder = buyorderMapper.selectByPrimaryKey(afterSaleDB.getOrderId());

        //如果是直发 直接校验通过
        if(OrderConstant.DELEVIRY_STATUS_1.equals(buyorder.getDeliveryDirect())){
            return ValidatorResult.newBuild().setResult(true);
        }

        //未生效关闭 重新下发原来的单据
        if(UN_EFFECT.equals(afterSaleDB.getValidStatus()) ){

            logger.info("采购退货单未生效关闭:" + JSON.toJSONString(afterSaleDB));
            doPutService.doPutPurchaseOrderMethod(buyorder.getBuyorderId(),buyorder.getLogisticsComments());

//            sendOriginalPurchaseOrder(afterSaleDB.getOrderId(),buyorder);

            return ValidatorResult.newBuild().setResult(true);
        }

        logger.info("采购退货单已生效关闭:" + JSON.toJSONString(afterSaleDB));

        logger.info("采购退货单已生效关闭,调用取消退货单接口:" + JSON.toJSONString(afterSaleDB));

        //看下退货单能否关闭 TODO HOLLIS ok
        /*CancelPoDto cancelReturnDto = new CancelPoDto();
        cancelReturnDto.setDocNo(afterSaleDB.getAfterSalesNo());
        cancelReturnDto.setOrderType(WmsInterfaceOrderType.OUT_PURCHASE_RETURN);
        cancelReturnDto.setErpCancelReason(afterSaleDB.getReasonName());

        WmsInterface wmsInterface = wmsInterfaceFactory.getWmsInterface(WMSContant.CANCEL_ORGGINCAL_SALESORDER);
        WmsResponse wmsResponse = wmsInterface.request(cancelReturnDto);*/

        //如果不能取消,返回提示消息
        if(!cancelTypeService.cancelOutPurchaseReturnMethod(afterSaleDB.getAfterSalesNo(),"采购退货单已生效关闭,调用取消退货单接口")){
            return ValidatorResult.newBuild().setMessage("物流正在进行货物操作，请等待作业完成后操作，如紧急请直接联系物流部。");
        }

        //处理入库单的部分
        dealWithInputOrder(buyorder);

        //处理出库单的部分
        dealWithOutputOrder(afterSaleDB,buyorder);

        return ValidatorResult.newBuild().setResult(true);
    }

    /**
     * 处理出库单的部分
     */
    private void dealWithOutputOrder(AfterSalesVo afterSalesVo,Buyorder buyorder) throws Exception {

        Map queryMap = new HashMap();
        queryMap.put("afterSaleId",afterSalesVo.getAfterSalesId());
        queryMap.put("operateType", WmsLogicalOperateTypeEnum.PURCHASE_RETURN.getOperateTypeCode());

        List<WmsLogicalOrdergoods> logicalOrdergoodList = wmsLogicalOrdergoodsMapper.getAfterSaleRelateLogicOrderGood(queryMap);

        //没有出库单 皆大欢喜
        if(CollectionUtils.isEmpty(logicalOrdergoodList)){
            return;
        }

        //有出库单 需要做逆向操作
        //1.取消出库单
        //2.删除逻辑订单表数据
        //3.取消库存服务的占用库存
        HandlerStepContext handlerStepContext = new HandlerStepContext();
        handlerStepContext.put("afterSalesInfo",afterSalesVo);
        handlerStepContext.put("logicalOrdergoodList",logicalOrdergoodList);
        handlerStepContext.put("buyOrder",buyorder);

        //采购退货出库单的反向步骤
        HandlerStep handlerStep = stepBuildFactory.buildPurchaseReturnOutReverseStep();
        handlerStep.dealWith(handlerStepContext);

    }

    /**
     * 处理入库单的部分
     * @throws Exception
     */
    private void dealWithInputOrder(Buyorder buyorder) throws Exception{
        //重新下发原始的采购单
//        sendOriginalPurchaseOrder(buyorder.getBuyorderId(),buyorder);
        doPutService.doPutPurchaseOrderMethod(buyorder.getBuyorderId(),buyorder.getLogisticsComments());
    }


    /**
     * 下发原来的采购入库单
     * @param buyOrderId
     * @returnVP207350453
     */
    /*private void sendOriginalPurchaseOrder(Integer buyOrderId,Buyorder buyorder) throws Exception{

        List<PutPurchaseOrderGoodsDto> details = new ArrayList<>();

        buyorderGoodsMapper.getBuyorderGoodsVoListByBuyorderIdNoSpecial(buyorder.getBuyorderId())
                .stream()
                .forEach(buyOrderGood -> {

                    PutPurchaseOrderGoodsDto detailItem = new PutPurchaseOrderGoodsDto();
                    detailItem.setSku(buyOrderGood.getSku());

                    //采购单数量 - 已到货数量 - 已完结售后数量
                    detailItem.setOrderedQty(buyOrderGood.getNum()
                            - buyOrderGood.getArrivalNum()
                            - getFinishAfterSaleNum(buyOrderGood.getBuyorderGoodsId()));

                    detailItem.setDedi04(buyOrderGood.getBuyorderGoodsId() + StringUtils.EMPTY);

                    if (SpecialDeliveryEnum.NEED_SPECIAL_DELIVERY.getCode().equals(
                            buyorderGoodsMapper.getSpecialDeliveryByRelatedId(buyOrderGood.getBuyorderGoodsId()))){
                        detailItem.setLotAtt07(buyorder.getBuyorderNo());
                    }

                    if(detailItem.getOrderedQty() > 0){
                        details.add(detailItem);
                    }
                });


        PutPurchaseOrderDto putPurchaseOrderDto = new PutPurchaseOrderDto();

        putPurchaseOrderDto.setPoType(WmsInterfaceOrderType.INPUT_PURCHASE);

        //采购单重新下发
        putPurchaseOrderDto.setDocNo(WmsCommonUtil.addTimestampForOrderNo(buyorder.getBuyorderNo()));

        putPurchaseOrderDto.setPoCreationTime(DateUtil.convertString(buyorder.getAddTime(),DateUtil.TIME_FORMAT));

        putPurchaseOrderDto.setExpectedArriveTime1(DateUtil.convertString(System.currentTimeMillis(),DateUtil.TIME_FORMAT));
        putPurchaseOrderDto.setExpectedArriveTime2(DateUtil.convertString(System.currentTimeMillis(),DateUtil.TIME_FORMAT));

        putPurchaseOrderDto.setSupplierId(buyorder.getTraderId());
        putPurchaseOrderDto.setSupplierName(buyorder.getTraderName());

        User user = userService.getUserById(buyorder.getCreator());

        putPurchaseOrderDto.setPoReferenceA(StringUtils.isEmpty(user.getPositionName()) ? "/" : user.getPositionName());
        putPurchaseOrderDto.setPoReferenceB(user.getUsername());
        putPurchaseOrderDto.setNotes(buyorder.getLogisticsComments());

        //入库单的详情
        putPurchaseOrderDto.setDetails(details);

        //入库详情为空 不下发
        if(CollectionUtils.isEmpty(details)){
            return;
        }

        //wms采购入库单
        WmsInterface wmsInterface = wmsInterfaceFactory.getWmsInterface(WMSContant.PUT_PURCHASE_ORDER);
        wmsInterface.request(putPurchaseOrderDto);
    }*/

    /**
     * 获取采购单某个商品已完结售后数量
     * @return
     */
    /*public int getFinishAfterSaleNum(Integer buyorderGoodsId){
        //已完结售后数量
        AfterSalesGoods searchGoods = new AfterSalesGoods();
        searchGoods.setOrderDetailId(buyorderGoodsId);
        searchGoods.setOperateType(StockOperateTypeConst.AFTERBUYORDER_BACK_FINSH);

        return afterSalesGoodsMapper.getAfterbuyorderNumByBuyorderGoodsId(searchGoods);
    }*/
}
