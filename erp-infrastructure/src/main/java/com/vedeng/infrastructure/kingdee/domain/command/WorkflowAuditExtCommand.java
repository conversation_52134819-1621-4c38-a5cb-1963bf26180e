package com.vedeng.infrastructure.kingdee.domain.command;

import java.util.ArrayList;
import java.util.List;

public class WorkflowAuditExtCommand extends BaseCommand{

    private final String FormId;
    private final List<Integer> Ids;

    private final List<String> Numbers;

    private final String UserName;
    private final Integer ApprovalType;
    private final String Disposition;

    public WorkflowAuditExtCommand(String formId, List<String> numbers, Integer approvalType, String disposition, String userName) {
        super(null);
        FormId = formId;
        Ids = new ArrayList<>();
        Numbers = numbers;
        UserName = userName;
        ApprovalType = approvalType;
        Disposition = disposition;
    }

    public List<Integer> getIds() {
        return Ids;
    }

    public List<String> getNumbers() {
        return Numbers;
    }

    public String getUserName() {
        return UserName;
    }

    public Integer getApprovalType() {
        return ApprovalType;
    }

    public String getDisposition() {
        return Disposition;
    }

    public String getFormId() {
        return FormId;
    }
}
