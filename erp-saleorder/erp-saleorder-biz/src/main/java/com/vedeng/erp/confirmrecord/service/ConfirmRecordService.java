package com.vedeng.erp.confirmrecord.service;

import com.vedeng.authorization.model.User;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.erp.confirmrecord.dto.ButtonIsShow;
import com.vedeng.erp.confirmrecord.dto.SignInRecordDto;
import com.vedeng.erp.confirmrecord.model.ConfirmRecord;
import com.vedeng.erp.confirmrecord.model.ConfirmRecordRes;
import com.vedeng.order.model.vo.SaleorderVo;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/11 9:28
 * @describe
 */
public interface ConfirmRecordService {
    /**
     * 异步加载订单确认模块
     * @param saleorderId 订单Id
     */
    ConfirmRecordRes getConfirmRecord(Integer saleorderId);

    /**
     * 复制链接
     * @param saleorderId 订单Id
     */
    String copyUrlLink(Integer saleorderId);

    /**
     * 发送短信
     * @param saleorderId 订单Id
     */
    void sendMessage(Integer saleorderId, User user);

    /**
     * 复制按钮是否展示
     */
    ButtonIsShow  copyAndSendMessageButtonIsShow(Integer saleorderId, User user);

    /**
     * 客户签收记录模块
     */
    List<SignInRecordDto> queryCustomerSignature(Integer saleorderId);

    /**
     *更新确认记录
     */
    void updateConfirmTimeAndStatus(ConfirmRecord confirmRecord);

    /**
     * 更新确认记录表
     * @param customerId 客户id
     * @param traderContactMobile 联系人手机号
     * @param signDate 确认时间
     */
    void updateConfirmInfo(Integer customerId, String traderContactMobile, Date signDate, List<SaleorderVo> saleorderVos);
}
