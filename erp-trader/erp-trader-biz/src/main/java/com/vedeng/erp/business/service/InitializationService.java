package com.vedeng.erp.business.service;

import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @description 初始化服务接口
 * @date 2022/7/28 9:02
 **/
public interface InitializationService {

    /**
     * 导入 商机
     * @param file 文件
     * @return 导入成功数量
     * @throws IOException
     */
    List<Integer> importBusinessChance(MultipartFile file) throws IOException;

    /**
     * 导入 线索
     * @param file 文件
     * @return 导入数量
     * @throws IOException
     */
    List<Integer> importBusinessLeads(MultipartFile file) throws IOException;

}
