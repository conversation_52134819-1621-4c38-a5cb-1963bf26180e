package com.vedeng.system.model;

/**
 * @description: 区间字典对象.
 * @notes: VDERP-2336 预计可发货时间接口.
 * @version: 1.0.
 * @date: 2020/5/9 4:26 下午.
 * @author: Tomcat.Hui.
 */
public class RangeDictionary {

    private Integer id;

    private Integer type;

    private String name;

    private String value;

    private Integer min;

    private Integer max;

    private Integer leftClosure;

    private Integer rightClosure;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public Integer getMin() {
        return min;
    }

    public void setMin(Integer min) {
        this.min = min;
    }

    public Integer getMax() {
        return max;
    }

    public void setMax(Integer max) {
        this.max = max;
    }

    public Integer getLeftClosure() {
        return leftClosure;
    }

    public void setLeftClosure(Integer leftClosure) {
        this.leftClosure = leftClosure;
    }

    public Integer getRightClosure() {
        return rightClosure;
    }

    public void setRightClosure(Integer rightClosure) {
        this.rightClosure = rightClosure;
    }

    @Override
    public String toString() {
        return null != this.getMax() ? (this.getMin() + "-" + this.getMax()) : this.getMin().toString() + "+";
    }
}
