package com.vedeng.flash.service.oreding;

import com.vedeng.authorization.model.User;
import com.vedeng.common.model.FileInfo;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.flash.dto.po.NewAddOrderingQueryPO;
import com.vedeng.flash.dto.po.OrderingPoolQueryPO;
import com.vedeng.flash.dto.po.RecommendOrderingQueryPO;
import com.vedeng.flash.dto.temp.JoinOrderingPoolOperateTemp;
import com.vedeng.flash.dto.temp.OneThreeMonthLastYearSaleNumTemp;
import com.vedeng.flash.dto.temp.SumaryRepeatCountTemp;
import com.vedeng.flash.dto.vo.NewAddOrderingQueryVO;
import com.vedeng.flash.dto.vo.OrderingPoolQueryVO;
import com.vedeng.flash.dto.vo.RecommendOrderingQueryVO;
import com.vedeng.flash.dto.vo.RemoveOrderingPoolOperateVO;
import com.vedeng.flash.model.RegularOperateLog;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

public interface OrderingPoolService {

    /**
     * 查询定品池
     * @param httpServletRequest
     * @param page
     * @param orderingPoolQueryVO
     * @return
     */
    List<OrderingPoolQueryPO> getOrderingPoolListPage(HttpServletRequest httpServletRequest, Page page, OrderingPoolQueryVO orderingPoolQueryVO);

    /**
     * 查询推荐定品
     * @param httpServletRequest
     * @param page
     * @param recommendOrderingQueryVO
     * @return
     */
    List<RecommendOrderingQueryPO> getRecommendOrderingListPage(HttpServletRequest httpServletRequest, Page page, RecommendOrderingQueryVO recommendOrderingQueryVO);

    /**
     * 获取对应销售量
     * @param skuIdList
     * @return
     */
    List<OneThreeMonthLastYearSaleNumTemp> getOneThreeMonthLastYearSaleNum(List<Integer> skuIdList);

    /**
     * 加入定品池 SkuId
     * @param joinOrderingPoolOperateTemps
     * @param sumaryRepeatCountTemp
     */
    void joinOrderingPoolBySkuId(List<JoinOrderingPoolOperateTemp> joinOrderingPoolOperateTemps, SumaryRepeatCountTemp sumaryRepeatCountTemp);

    /**
     * 加入定品池 SkuNo
     * @param joinOrderingPoolOperateTemps
     * @param sumaryRepeatCountTemp
     */
    ResultInfo joinOrderingPoolBySkuNo(List<JoinOrderingPoolOperateTemp> joinOrderingPoolOperateTemps, SumaryRepeatCountTemp sumaryRepeatCountTemp);

    /**
     * 批量导入定品保存
     * @param fileInfo
     * @param user
     * @return
     */
    ResultInfo<Object> batchSaveOrdering(FileInfo fileInfo, User user) throws IOException, InvalidFormatException;

    /**
     * 查询新增定品
     * @param httpServletRequest
     * @param page
     * @param newAddOrderingQueryVO
     * @return
     */
    List<NewAddOrderingQueryPO> getNewAddOrderingListPage(HttpServletRequest httpServletRequest, Page page, NewAddOrderingQueryVO newAddOrderingQueryVO);

    /**
     * 移除定品池里定品操作
     * @param removeOrderingPoolOperateVO
     * @param httpServletRequest
     */
    void deleteOrderingPool(RemoveOrderingPoolOperateVO removeOrderingPoolOperateVO, HttpServletRequest httpServletRequest);

    /**
     * 查询删除原因
     * @param skuId
     * @return
     */
    List<RegularOperateLog> removePoolCause(Integer skuId);

    /**
     * 导出定品列表
     * @param exportMonth
     */
    Map<String, Object> exportOrderingList(String exportMonth);
}
