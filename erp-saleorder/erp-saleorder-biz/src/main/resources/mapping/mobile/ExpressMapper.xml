<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.mobile.mapper.ExpressMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.mobile.domain.Express">
    <!--@mbg.generated-->
    <!--@Table T_EXPRESS-->
    <id column="EXPRESS_ID" jdbcType="INTEGER" property="expressId" />
    <result column="LOGISTICS_ID" jdbcType="INTEGER" property="logisticsId" />
    <result column="LOGISTICS_NO" jdbcType="VARCHAR" property="logisticsNo" />
    <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId" />
    <result column="DELIVERY_TIME" jdbcType="BIGINT" property="deliveryTime" />
    <result column="ARRIVAL_STATUS" jdbcType="BOOLEAN" property="arrivalStatus" />
    <result column="ARRIVAL_TIME" jdbcType="BIGINT" property="arrivalTime" />
    <result column="DELIVERY_FROM" jdbcType="INTEGER" property="deliveryFrom" />
    <result column="LOGISTICS_COMMENTS" jdbcType="VARCHAR" property="logisticsComments" />
    <result column="IS_ENABLE" jdbcType="BOOLEAN" property="isEnable" />
    <result column="PAYMENT_TYPE" jdbcType="BOOLEAN" property="paymentType" />
    <result column="CARD_NUMBER" jdbcType="VARCHAR" property="cardNumber" />
    <result column="BUSINESS_TYPE" jdbcType="BOOLEAN" property="businessType" />
    <result column="REAL_WEIGHT" jdbcType="DECIMAL" property="realWeight" />
    <result column="NUM" jdbcType="INTEGER" property="num" />
    <result column="AMOUNT_WEIGHT" jdbcType="DECIMAL" property="amountWeight" />
    <result column="MAIL_GOODS" jdbcType="VARCHAR" property="mailGoods" />
    <result column="MAIL_GOODS_NUM" jdbcType="INTEGER" property="mailGoodsNum" />
    <result column="IS_PROTECT_PRICE" jdbcType="BOOLEAN" property="isProtectPrice" />
    <result column="PROTECT_PRICE" jdbcType="DECIMAL" property="protectPrice" />
    <result column="IS_RECEIPT" jdbcType="BOOLEAN" property="isReceipt" />
    <result column="MAIL_COMMTENTS" jdbcType="VARCHAR" property="mailCommtents" />
    <result column="SENT_SMS" jdbcType="BOOLEAN" property="sentSms" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="TRAVELING_BY_TICKET" jdbcType="BOOLEAN" property="travelingByTicket" />
    <result column="IS_INVOICING" jdbcType="BOOLEAN" property="isInvoicing" />
    <result column="WMS_ORDER_NO" jdbcType="VARCHAR" property="wmsOrderNo" />
    <result column="TRANSPORT_DATE" jdbcType="DATE" property="transportDate" />
    <result column="RECEIVED_COMPANY" jdbcType="VARCHAR" property="receivedCompany" />
    <result column="RECEIVED_ADDRESS" jdbcType="VARCHAR" property="receivedAddress" />
    <result column="RECEIVED_USERNAME" jdbcType="VARCHAR" property="receivedUsername" />
    <result column="SIGNED_DATE" jdbcType="DATE" property="signedDate" />
    <result column="WITHOUT_TAX_AMOUNT" jdbcType="DECIMAL" property="withoutTaxAmount" />
    <result column="EXPRESS_NAME" jdbcType="VARCHAR" property="expressName" />
    <result column="ONLINE_RECEIPT_ID" jdbcType="INTEGER" property="onlineReceiptId" />
    <result column="BATCH_NO" jdbcType="VARCHAR" property="batchNo" />
    <result column="SYSTEM_ADD_TIME" jdbcType="TIMESTAMP" property="systemAddTime" />
    <result column="ENABLE_RECEIVE" jdbcType="BOOLEAN" property="enableReceive" />
    <result column="OLD_LOGISTICS_NO" jdbcType="VARCHAR" property="oldLogisticsNo" />
    <result column="IS_INTERCEPTED" jdbcType="BOOLEAN" property="isIntercepted" />
    <result column="INTERCEPT_TIME" jdbcType="BIGINT" property="interceptTime" />
  </resultMap>

  <resultMap id="ExpressResultMap" type="com.vedeng.erp.mobile.dto.SaleOrderExpressInfoDto">
    <id column="EXPRESS_ID" jdbcType="INTEGER" property="expressId" />
    <result column="LOGISTICS_ID" jdbcType="INTEGER" property="logisticsId" />
    <result column="LOGISTICS_NO" jdbcType="VARCHAR" property="logisticsNo" />
    <result column="LOGISTICS_NAME" property="logisticsName" jdbcType="VARCHAR" />
    <result column="EXPRESS_STATUS" property="expressStatus" jdbcType="VARCHAR" />
    <collection property="expressDetailInfoList" ofType="com.vedeng.erp.mobile.dto.SaleOrderExpressDetailInfoDto">
      <id column="EXPRESS_DETAIL_ID" property="expressDetailId" jdbcType="INTEGER" />
      <result column="EXPRESS_ID" property="expressId" jdbcType="INTEGER" />
      <result column="BUSINESS_TYPE" property="businessType" jdbcType="INTEGER" />
      <result column="RELATED_ID" property="relatedId" jdbcType="INTEGER" />
      <result column="DETAIL_NUM" property="expressDetailNum" jdbcType="INTEGER" />
      <result column="PRICE" property="price" jdbcType="DECIMAL" />
      <result column="GOODS_NAME" property="goodsName" jdbcType="VARCHAR" />
      <result column="SKU" property="sku" jdbcType="VARCHAR" />
    </collection>
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    EXPRESS_ID, LOGISTICS_ID, LOGISTICS_NO, COMPANY_ID, DELIVERY_TIME, ARRIVAL_STATUS, 
    ARRIVAL_TIME, DELIVERY_FROM, LOGISTICS_COMMENTS, IS_ENABLE, PAYMENT_TYPE, CARD_NUMBER, 
    BUSINESS_TYPE, REAL_WEIGHT, NUM, AMOUNT_WEIGHT, MAIL_GOODS, MAIL_GOODS_NUM, IS_PROTECT_PRICE, 
    PROTECT_PRICE, IS_RECEIPT, MAIL_COMMTENTS, SENT_SMS, ADD_TIME, CREATOR, MOD_TIME, 
    UPDATER, TRAVELING_BY_TICKET, IS_INVOICING, WMS_ORDER_NO, TRANSPORT_DATE, RECEIVED_COMPANY, 
    RECEIVED_ADDRESS, RECEIVED_USERNAME, SIGNED_DATE, WITHOUT_TAX_AMOUNT, EXPRESS_NAME, 
    ONLINE_RECEIPT_ID, BATCH_NO, SYSTEM_ADD_TIME, ENABLE_RECEIVE, OLD_LOGISTICS_NO, IS_INTERCEPTED, 
    INTERCEPT_TIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_EXPRESS
    where EXPRESS_ID = #{expressId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_EXPRESS
    where EXPRESS_ID = #{expressId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="EXPRESS_ID" keyProperty="expressId" parameterType="com.vedeng.erp.mobile.domain.Express" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_EXPRESS (LOGISTICS_ID, LOGISTICS_NO, COMPANY_ID, 
      DELIVERY_TIME, ARRIVAL_STATUS, ARRIVAL_TIME, 
      DELIVERY_FROM, LOGISTICS_COMMENTS, IS_ENABLE, 
      PAYMENT_TYPE, CARD_NUMBER, BUSINESS_TYPE, 
      REAL_WEIGHT, NUM, AMOUNT_WEIGHT, 
      MAIL_GOODS, MAIL_GOODS_NUM, IS_PROTECT_PRICE, 
      PROTECT_PRICE, IS_RECEIPT, MAIL_COMMTENTS, 
      SENT_SMS, ADD_TIME, CREATOR, 
      MOD_TIME, UPDATER, TRAVELING_BY_TICKET, 
      IS_INVOICING, WMS_ORDER_NO, TRANSPORT_DATE, 
      RECEIVED_COMPANY, RECEIVED_ADDRESS, RECEIVED_USERNAME, 
      SIGNED_DATE, WITHOUT_TAX_AMOUNT, EXPRESS_NAME, 
      ONLINE_RECEIPT_ID, BATCH_NO, SYSTEM_ADD_TIME, 
      ENABLE_RECEIVE, OLD_LOGISTICS_NO, IS_INTERCEPTED, 
      INTERCEPT_TIME)
    values (#{logisticsId,jdbcType=INTEGER}, #{logisticsNo,jdbcType=VARCHAR}, #{companyId,jdbcType=INTEGER}, 
      #{deliveryTime,jdbcType=BIGINT}, #{arrivalStatus,jdbcType=BOOLEAN}, #{arrivalTime,jdbcType=BIGINT}, 
      #{deliveryFrom,jdbcType=INTEGER}, #{logisticsComments,jdbcType=VARCHAR}, #{isEnable,jdbcType=BOOLEAN}, 
      #{paymentType,jdbcType=BOOLEAN}, #{cardNumber,jdbcType=VARCHAR}, #{businessType,jdbcType=BOOLEAN}, 
      #{realWeight,jdbcType=DECIMAL}, #{num,jdbcType=INTEGER}, #{amountWeight,jdbcType=DECIMAL}, 
      #{mailGoods,jdbcType=VARCHAR}, #{mailGoodsNum,jdbcType=INTEGER}, #{isProtectPrice,jdbcType=BOOLEAN}, 
      #{protectPrice,jdbcType=DECIMAL}, #{isReceipt,jdbcType=BOOLEAN}, #{mailCommtents,jdbcType=VARCHAR}, 
      #{sentSms,jdbcType=BOOLEAN}, #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}, 
      #{modTime,jdbcType=BIGINT}, #{updater,jdbcType=INTEGER}, #{travelingByTicket,jdbcType=BOOLEAN}, 
      #{isInvoicing,jdbcType=BOOLEAN}, #{wmsOrderNo,jdbcType=VARCHAR}, #{transportDate,jdbcType=DATE}, 
      #{receivedCompany,jdbcType=VARCHAR}, #{receivedAddress,jdbcType=VARCHAR}, #{receivedUsername,jdbcType=VARCHAR}, 
      #{signedDate,jdbcType=DATE}, #{withoutTaxAmount,jdbcType=DECIMAL}, #{expressName,jdbcType=VARCHAR}, 
      #{onlineReceiptId,jdbcType=INTEGER}, #{batchNo,jdbcType=VARCHAR}, #{systemAddTime,jdbcType=TIMESTAMP}, 
      #{enableReceive,jdbcType=BOOLEAN}, #{oldLogisticsNo,jdbcType=VARCHAR}, #{isIntercepted,jdbcType=BOOLEAN}, 
      #{interceptTime,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" keyColumn="EXPRESS_ID" keyProperty="expressId" parameterType="com.vedeng.erp.mobile.domain.Express" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_EXPRESS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="logisticsId != null">
        LOGISTICS_ID,
      </if>
      <if test="logisticsNo != null and logisticsNo != ''">
        LOGISTICS_NO,
      </if>
      <if test="companyId != null">
        COMPANY_ID,
      </if>
      <if test="deliveryTime != null">
        DELIVERY_TIME,
      </if>
      <if test="arrivalStatus != null">
        ARRIVAL_STATUS,
      </if>
      <if test="arrivalTime != null">
        ARRIVAL_TIME,
      </if>
      <if test="deliveryFrom != null">
        DELIVERY_FROM,
      </if>
      <if test="logisticsComments != null and logisticsComments != ''">
        LOGISTICS_COMMENTS,
      </if>
      <if test="isEnable != null">
        IS_ENABLE,
      </if>
      <if test="paymentType != null">
        PAYMENT_TYPE,
      </if>
      <if test="cardNumber != null and cardNumber != ''">
        CARD_NUMBER,
      </if>
      <if test="businessType != null">
        BUSINESS_TYPE,
      </if>
      <if test="realWeight != null">
        REAL_WEIGHT,
      </if>
      <if test="num != null">
        NUM,
      </if>
      <if test="amountWeight != null">
        AMOUNT_WEIGHT,
      </if>
      <if test="mailGoods != null and mailGoods != ''">
        MAIL_GOODS,
      </if>
      <if test="mailGoodsNum != null">
        MAIL_GOODS_NUM,
      </if>
      <if test="isProtectPrice != null">
        IS_PROTECT_PRICE,
      </if>
      <if test="protectPrice != null">
        PROTECT_PRICE,
      </if>
      <if test="isReceipt != null">
        IS_RECEIPT,
      </if>
      <if test="mailCommtents != null and mailCommtents != ''">
        MAIL_COMMTENTS,
      </if>
      <if test="sentSms != null">
        SENT_SMS,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="travelingByTicket != null">
        TRAVELING_BY_TICKET,
      </if>
      <if test="isInvoicing != null">
        IS_INVOICING,
      </if>
      <if test="wmsOrderNo != null and wmsOrderNo != ''">
        WMS_ORDER_NO,
      </if>
      <if test="transportDate != null">
        TRANSPORT_DATE,
      </if>
      <if test="receivedCompany != null and receivedCompany != ''">
        RECEIVED_COMPANY,
      </if>
      <if test="receivedAddress != null and receivedAddress != ''">
        RECEIVED_ADDRESS,
      </if>
      <if test="receivedUsername != null and receivedUsername != ''">
        RECEIVED_USERNAME,
      </if>
      <if test="signedDate != null">
        SIGNED_DATE,
      </if>
      <if test="withoutTaxAmount != null">
        WITHOUT_TAX_AMOUNT,
      </if>
      <if test="expressName != null and expressName != ''">
        EXPRESS_NAME,
      </if>
      <if test="onlineReceiptId != null">
        ONLINE_RECEIPT_ID,
      </if>
      <if test="batchNo != null and batchNo != ''">
        BATCH_NO,
      </if>
      <if test="systemAddTime != null">
        SYSTEM_ADD_TIME,
      </if>
      <if test="enableReceive != null">
        ENABLE_RECEIVE,
      </if>
      <if test="oldLogisticsNo != null and oldLogisticsNo != ''">
        OLD_LOGISTICS_NO,
      </if>
      <if test="isIntercepted != null">
        IS_INTERCEPTED,
      </if>
      <if test="interceptTime != null">
        INTERCEPT_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="logisticsId != null">
        #{logisticsId,jdbcType=INTEGER},
      </if>
      <if test="logisticsNo != null and logisticsNo != ''">
        #{logisticsNo,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="deliveryTime != null">
        #{deliveryTime,jdbcType=BIGINT},
      </if>
      <if test="arrivalStatus != null">
        #{arrivalStatus,jdbcType=BOOLEAN},
      </if>
      <if test="arrivalTime != null">
        #{arrivalTime,jdbcType=BIGINT},
      </if>
      <if test="deliveryFrom != null">
        #{deliveryFrom,jdbcType=INTEGER},
      </if>
      <if test="logisticsComments != null and logisticsComments != ''">
        #{logisticsComments,jdbcType=VARCHAR},
      </if>
      <if test="isEnable != null">
        #{isEnable,jdbcType=BOOLEAN},
      </if>
      <if test="paymentType != null">
        #{paymentType,jdbcType=BOOLEAN},
      </if>
      <if test="cardNumber != null and cardNumber != ''">
        #{cardNumber,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=BOOLEAN},
      </if>
      <if test="realWeight != null">
        #{realWeight,jdbcType=DECIMAL},
      </if>
      <if test="num != null">
        #{num,jdbcType=INTEGER},
      </if>
      <if test="amountWeight != null">
        #{amountWeight,jdbcType=DECIMAL},
      </if>
      <if test="mailGoods != null and mailGoods != ''">
        #{mailGoods,jdbcType=VARCHAR},
      </if>
      <if test="mailGoodsNum != null">
        #{mailGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="isProtectPrice != null">
        #{isProtectPrice,jdbcType=BOOLEAN},
      </if>
      <if test="protectPrice != null">
        #{protectPrice,jdbcType=DECIMAL},
      </if>
      <if test="isReceipt != null">
        #{isReceipt,jdbcType=BOOLEAN},
      </if>
      <if test="mailCommtents != null and mailCommtents != ''">
        #{mailCommtents,jdbcType=VARCHAR},
      </if>
      <if test="sentSms != null">
        #{sentSms,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="travelingByTicket != null">
        #{travelingByTicket,jdbcType=BOOLEAN},
      </if>
      <if test="isInvoicing != null">
        #{isInvoicing,jdbcType=BOOLEAN},
      </if>
      <if test="wmsOrderNo != null and wmsOrderNo != ''">
        #{wmsOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="transportDate != null">
        #{transportDate,jdbcType=DATE},
      </if>
      <if test="receivedCompany != null and receivedCompany != ''">
        #{receivedCompany,jdbcType=VARCHAR},
      </if>
      <if test="receivedAddress != null and receivedAddress != ''">
        #{receivedAddress,jdbcType=VARCHAR},
      </if>
      <if test="receivedUsername != null and receivedUsername != ''">
        #{receivedUsername,jdbcType=VARCHAR},
      </if>
      <if test="signedDate != null">
        #{signedDate,jdbcType=DATE},
      </if>
      <if test="withoutTaxAmount != null">
        #{withoutTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="expressName != null and expressName != ''">
        #{expressName,jdbcType=VARCHAR},
      </if>
      <if test="onlineReceiptId != null">
        #{onlineReceiptId,jdbcType=INTEGER},
      </if>
      <if test="batchNo != null and batchNo != ''">
        #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="systemAddTime != null">
        #{systemAddTime,jdbcType=TIMESTAMP},
      </if>
      <if test="enableReceive != null">
        #{enableReceive,jdbcType=BOOLEAN},
      </if>
      <if test="oldLogisticsNo != null and oldLogisticsNo != ''">
        #{oldLogisticsNo,jdbcType=VARCHAR},
      </if>
      <if test="isIntercepted != null">
        #{isIntercepted,jdbcType=BOOLEAN},
      </if>
      <if test="interceptTime != null">
        #{interceptTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.mobile.domain.Express">
    <!--@mbg.generated-->
    update T_EXPRESS
    <set>
      <if test="logisticsId != null">
        LOGISTICS_ID = #{logisticsId,jdbcType=INTEGER},
      </if>
      <if test="logisticsNo != null and logisticsNo != ''">
        LOGISTICS_NO = #{logisticsNo,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="deliveryTime != null">
        DELIVERY_TIME = #{deliveryTime,jdbcType=BIGINT},
      </if>
      <if test="arrivalStatus != null">
        ARRIVAL_STATUS = #{arrivalStatus,jdbcType=BOOLEAN},
      </if>
      <if test="arrivalTime != null">
        ARRIVAL_TIME = #{arrivalTime,jdbcType=BIGINT},
      </if>
      <if test="deliveryFrom != null">
        DELIVERY_FROM = #{deliveryFrom,jdbcType=INTEGER},
      </if>
      <if test="logisticsComments != null and logisticsComments != ''">
        LOGISTICS_COMMENTS = #{logisticsComments,jdbcType=VARCHAR},
      </if>
      <if test="isEnable != null">
        IS_ENABLE = #{isEnable,jdbcType=BOOLEAN},
      </if>
      <if test="paymentType != null">
        PAYMENT_TYPE = #{paymentType,jdbcType=BOOLEAN},
      </if>
      <if test="cardNumber != null and cardNumber != ''">
        CARD_NUMBER = #{cardNumber,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        BUSINESS_TYPE = #{businessType,jdbcType=BOOLEAN},
      </if>
      <if test="realWeight != null">
        REAL_WEIGHT = #{realWeight,jdbcType=DECIMAL},
      </if>
      <if test="num != null">
        NUM = #{num,jdbcType=INTEGER},
      </if>
      <if test="amountWeight != null">
        AMOUNT_WEIGHT = #{amountWeight,jdbcType=DECIMAL},
      </if>
      <if test="mailGoods != null and mailGoods != ''">
        MAIL_GOODS = #{mailGoods,jdbcType=VARCHAR},
      </if>
      <if test="mailGoodsNum != null">
        MAIL_GOODS_NUM = #{mailGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="isProtectPrice != null">
        IS_PROTECT_PRICE = #{isProtectPrice,jdbcType=BOOLEAN},
      </if>
      <if test="protectPrice != null">
        PROTECT_PRICE = #{protectPrice,jdbcType=DECIMAL},
      </if>
      <if test="isReceipt != null">
        IS_RECEIPT = #{isReceipt,jdbcType=BOOLEAN},
      </if>
      <if test="mailCommtents != null and mailCommtents != ''">
        MAIL_COMMTENTS = #{mailCommtents,jdbcType=VARCHAR},
      </if>
      <if test="sentSms != null">
        SENT_SMS = #{sentSms,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="travelingByTicket != null">
        TRAVELING_BY_TICKET = #{travelingByTicket,jdbcType=BOOLEAN},
      </if>
      <if test="isInvoicing != null">
        IS_INVOICING = #{isInvoicing,jdbcType=BOOLEAN},
      </if>
      <if test="wmsOrderNo != null and wmsOrderNo != ''">
        WMS_ORDER_NO = #{wmsOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="transportDate != null">
        TRANSPORT_DATE = #{transportDate,jdbcType=DATE},
      </if>
      <if test="receivedCompany != null and receivedCompany != ''">
        RECEIVED_COMPANY = #{receivedCompany,jdbcType=VARCHAR},
      </if>
      <if test="receivedAddress != null and receivedAddress != ''">
        RECEIVED_ADDRESS = #{receivedAddress,jdbcType=VARCHAR},
      </if>
      <if test="receivedUsername != null and receivedUsername != ''">
        RECEIVED_USERNAME = #{receivedUsername,jdbcType=VARCHAR},
      </if>
      <if test="signedDate != null">
        SIGNED_DATE = #{signedDate,jdbcType=DATE},
      </if>
      <if test="withoutTaxAmount != null">
        WITHOUT_TAX_AMOUNT = #{withoutTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="expressName != null and expressName != ''">
        EXPRESS_NAME = #{expressName,jdbcType=VARCHAR},
      </if>
      <if test="onlineReceiptId != null">
        ONLINE_RECEIPT_ID = #{onlineReceiptId,jdbcType=INTEGER},
      </if>
      <if test="batchNo != null and batchNo != ''">
        BATCH_NO = #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="systemAddTime != null">
        SYSTEM_ADD_TIME = #{systemAddTime,jdbcType=TIMESTAMP},
      </if>
      <if test="enableReceive != null">
        ENABLE_RECEIVE = #{enableReceive,jdbcType=BOOLEAN},
      </if>
      <if test="oldLogisticsNo != null and oldLogisticsNo != ''">
        OLD_LOGISTICS_NO = #{oldLogisticsNo,jdbcType=VARCHAR},
      </if>
      <if test="isIntercepted != null">
        IS_INTERCEPTED = #{isIntercepted,jdbcType=BOOLEAN},
      </if>
      <if test="interceptTime != null">
        INTERCEPT_TIME = #{interceptTime,jdbcType=BIGINT},
      </if>
    </set>
    where EXPRESS_ID = #{expressId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.mobile.domain.Express">
    <!--@mbg.generated-->
    update T_EXPRESS
    set LOGISTICS_ID = #{logisticsId,jdbcType=INTEGER},
      LOGISTICS_NO = #{logisticsNo,jdbcType=VARCHAR},
      COMPANY_ID = #{companyId,jdbcType=INTEGER},
      DELIVERY_TIME = #{deliveryTime,jdbcType=BIGINT},
      ARRIVAL_STATUS = #{arrivalStatus,jdbcType=BOOLEAN},
      ARRIVAL_TIME = #{arrivalTime,jdbcType=BIGINT},
      DELIVERY_FROM = #{deliveryFrom,jdbcType=INTEGER},
      LOGISTICS_COMMENTS = #{logisticsComments,jdbcType=VARCHAR},
      IS_ENABLE = #{isEnable,jdbcType=BOOLEAN},
      PAYMENT_TYPE = #{paymentType,jdbcType=BOOLEAN},
      CARD_NUMBER = #{cardNumber,jdbcType=VARCHAR},
      BUSINESS_TYPE = #{businessType,jdbcType=BOOLEAN},
      REAL_WEIGHT = #{realWeight,jdbcType=DECIMAL},
      NUM = #{num,jdbcType=INTEGER},
      AMOUNT_WEIGHT = #{amountWeight,jdbcType=DECIMAL},
      MAIL_GOODS = #{mailGoods,jdbcType=VARCHAR},
      MAIL_GOODS_NUM = #{mailGoodsNum,jdbcType=INTEGER},
      IS_PROTECT_PRICE = #{isProtectPrice,jdbcType=BOOLEAN},
      PROTECT_PRICE = #{protectPrice,jdbcType=DECIMAL},
      IS_RECEIPT = #{isReceipt,jdbcType=BOOLEAN},
      MAIL_COMMTENTS = #{mailCommtents,jdbcType=VARCHAR},
      SENT_SMS = #{sentSms,jdbcType=BOOLEAN},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      TRAVELING_BY_TICKET = #{travelingByTicket,jdbcType=BOOLEAN},
      IS_INVOICING = #{isInvoicing,jdbcType=BOOLEAN},
      WMS_ORDER_NO = #{wmsOrderNo,jdbcType=VARCHAR},
      TRANSPORT_DATE = #{transportDate,jdbcType=DATE},
      RECEIVED_COMPANY = #{receivedCompany,jdbcType=VARCHAR},
      RECEIVED_ADDRESS = #{receivedAddress,jdbcType=VARCHAR},
      RECEIVED_USERNAME = #{receivedUsername,jdbcType=VARCHAR},
      SIGNED_DATE = #{signedDate,jdbcType=DATE},
      WITHOUT_TAX_AMOUNT = #{withoutTaxAmount,jdbcType=DECIMAL},
      EXPRESS_NAME = #{expressName,jdbcType=VARCHAR},
      ONLINE_RECEIPT_ID = #{onlineReceiptId,jdbcType=INTEGER},
      BATCH_NO = #{batchNo,jdbcType=VARCHAR},
      SYSTEM_ADD_TIME = #{systemAddTime,jdbcType=TIMESTAMP},
      ENABLE_RECEIVE = #{enableReceive,jdbcType=BOOLEAN},
      OLD_LOGISTICS_NO = #{oldLogisticsNo,jdbcType=VARCHAR},
      IS_INTERCEPTED = #{isIntercepted,jdbcType=BOOLEAN},
      INTERCEPT_TIME = #{interceptTime,jdbcType=BIGINT}
    where EXPRESS_ID = #{expressId,jdbcType=INTEGER}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_EXPRESS
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="LOGISTICS_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.logisticsId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="LOGISTICS_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.logisticsNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="COMPANY_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.companyId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="DELIVERY_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.deliveryTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="ARRIVAL_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.arrivalStatus,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="ARRIVAL_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.arrivalTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="DELIVERY_FROM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.deliveryFrom,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="LOGISTICS_COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.logisticsComments,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="IS_ENABLE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.isEnable,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="PAYMENT_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.paymentType,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="CARD_NUMBER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.cardNumber,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="BUSINESS_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.businessType,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="REAL_WEIGHT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.realWeight,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.num,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="AMOUNT_WEIGHT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.amountWeight,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="MAIL_GOODS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.mailGoods,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="MAIL_GOODS_NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.mailGoodsNum,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="IS_PROTECT_PRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.isProtectPrice,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="PROTECT_PRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.protectPrice,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="IS_RECEIPT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.isReceipt,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="MAIL_COMMTENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.mailCommtents,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="SENT_SMS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.sentSms,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.addTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.modTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="TRAVELING_BY_TICKET = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.travelingByTicket,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="IS_INVOICING = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.isInvoicing,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="WMS_ORDER_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.wmsOrderNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="TRANSPORT_DATE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.transportDate,jdbcType=DATE}
        </foreach>
      </trim>
      <trim prefix="RECEIVED_COMPANY = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.receivedCompany,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="RECEIVED_ADDRESS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.receivedAddress,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="RECEIVED_USERNAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.receivedUsername,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="SIGNED_DATE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.signedDate,jdbcType=DATE}
        </foreach>
      </trim>
      <trim prefix="WITHOUT_TAX_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.withoutTaxAmount,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="EXPRESS_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.expressName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="ONLINE_RECEIPT_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.onlineReceiptId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="BATCH_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.batchNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="SYSTEM_ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.systemAddTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="ENABLE_RECEIVE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.enableReceive,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="OLD_LOGISTICS_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.oldLogisticsNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="IS_INTERCEPTED = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.isIntercepted,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="INTERCEPT_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.interceptTime,jdbcType=BIGINT}
        </foreach>
      </trim>
    </trim>
    where EXPRESS_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.expressId,jdbcType=INTEGER}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_EXPRESS
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="LOGISTICS_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.logisticsId != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.logisticsId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="LOGISTICS_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.logisticsNo != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.logisticsNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="COMPANY_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.companyId != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.companyId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="DELIVERY_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.deliveryTime != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.deliveryTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="ARRIVAL_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.arrivalStatus != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.arrivalStatus,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="ARRIVAL_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.arrivalTime != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.arrivalTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="DELIVERY_FROM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.deliveryFrom != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.deliveryFrom,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="LOGISTICS_COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.logisticsComments != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.logisticsComments,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_ENABLE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isEnable != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.isEnable,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="PAYMENT_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.paymentType != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.paymentType,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="CARD_NUMBER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.cardNumber != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.cardNumber,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="BUSINESS_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.businessType != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.businessType,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="REAL_WEIGHT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.realWeight != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.realWeight,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.num != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.num,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="AMOUNT_WEIGHT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.amountWeight != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.amountWeight,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="MAIL_GOODS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.mailGoods != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.mailGoods,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="MAIL_GOODS_NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.mailGoodsNum != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.mailGoodsNum,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_PROTECT_PRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isProtectPrice != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.isProtectPrice,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="PROTECT_PRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.protectPrice != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.protectPrice,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_RECEIPT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isReceipt != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.isReceipt,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="MAIL_COMMTENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.mailCommtents != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.mailCommtents,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="SENT_SMS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.sentSms != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.sentSms,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.addTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.modTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="TRAVELING_BY_TICKET = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.travelingByTicket != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.travelingByTicket,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_INVOICING = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isInvoicing != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.isInvoicing,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="WMS_ORDER_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.wmsOrderNo != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.wmsOrderNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="TRANSPORT_DATE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.transportDate != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.transportDate,jdbcType=DATE}
          </if>
        </foreach>
      </trim>
      <trim prefix="RECEIVED_COMPANY = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.receivedCompany != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.receivedCompany,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="RECEIVED_ADDRESS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.receivedAddress != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.receivedAddress,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="RECEIVED_USERNAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.receivedUsername != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.receivedUsername,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="SIGNED_DATE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.signedDate != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.signedDate,jdbcType=DATE}
          </if>
        </foreach>
      </trim>
      <trim prefix="WITHOUT_TAX_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.withoutTaxAmount != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.withoutTaxAmount,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="EXPRESS_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.expressName != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.expressName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="ONLINE_RECEIPT_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.onlineReceiptId != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.onlineReceiptId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="BATCH_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.batchNo != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.batchNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="SYSTEM_ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.systemAddTime != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.systemAddTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="ENABLE_RECEIVE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.enableReceive != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.enableReceive,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="OLD_LOGISTICS_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.oldLogisticsNo != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.oldLogisticsNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_INTERCEPTED = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isIntercepted != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.isIntercepted,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="INTERCEPT_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.interceptTime != null">
            when EXPRESS_ID = #{item.expressId,jdbcType=INTEGER} then #{item.interceptTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
    </trim>
    where EXPRESS_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.expressId,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="EXPRESS_ID" keyProperty="expressId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_EXPRESS
    (LOGISTICS_ID, LOGISTICS_NO, COMPANY_ID, DELIVERY_TIME, ARRIVAL_STATUS, ARRIVAL_TIME, 
      DELIVERY_FROM, LOGISTICS_COMMENTS, IS_ENABLE, PAYMENT_TYPE, CARD_NUMBER, BUSINESS_TYPE, 
      REAL_WEIGHT, NUM, AMOUNT_WEIGHT, MAIL_GOODS, MAIL_GOODS_NUM, IS_PROTECT_PRICE, 
      PROTECT_PRICE, IS_RECEIPT, MAIL_COMMTENTS, SENT_SMS, ADD_TIME, CREATOR, MOD_TIME, 
      UPDATER, TRAVELING_BY_TICKET, IS_INVOICING, WMS_ORDER_NO, TRANSPORT_DATE, RECEIVED_COMPANY, 
      RECEIVED_ADDRESS, RECEIVED_USERNAME, SIGNED_DATE, WITHOUT_TAX_AMOUNT, EXPRESS_NAME, 
      ONLINE_RECEIPT_ID, BATCH_NO, SYSTEM_ADD_TIME, ENABLE_RECEIVE, OLD_LOGISTICS_NO, 
      IS_INTERCEPTED, INTERCEPT_TIME)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.logisticsId,jdbcType=INTEGER}, #{item.logisticsNo,jdbcType=VARCHAR}, #{item.companyId,jdbcType=INTEGER}, 
        #{item.deliveryTime,jdbcType=BIGINT}, #{item.arrivalStatus,jdbcType=BOOLEAN}, #{item.arrivalTime,jdbcType=BIGINT}, 
        #{item.deliveryFrom,jdbcType=INTEGER}, #{item.logisticsComments,jdbcType=VARCHAR}, 
        #{item.isEnable,jdbcType=BOOLEAN}, #{item.paymentType,jdbcType=BOOLEAN}, #{item.cardNumber,jdbcType=VARCHAR}, 
        #{item.businessType,jdbcType=BOOLEAN}, #{item.realWeight,jdbcType=DECIMAL}, #{item.num,jdbcType=INTEGER}, 
        #{item.amountWeight,jdbcType=DECIMAL}, #{item.mailGoods,jdbcType=VARCHAR}, #{item.mailGoodsNum,jdbcType=INTEGER}, 
        #{item.isProtectPrice,jdbcType=BOOLEAN}, #{item.protectPrice,jdbcType=DECIMAL}, 
        #{item.isReceipt,jdbcType=BOOLEAN}, #{item.mailCommtents,jdbcType=VARCHAR}, #{item.sentSms,jdbcType=BOOLEAN}, 
        #{item.addTime,jdbcType=BIGINT}, #{item.creator,jdbcType=INTEGER}, #{item.modTime,jdbcType=BIGINT}, 
        #{item.updater,jdbcType=INTEGER}, #{item.travelingByTicket,jdbcType=BOOLEAN}, #{item.isInvoicing,jdbcType=BOOLEAN}, 
        #{item.wmsOrderNo,jdbcType=VARCHAR}, #{item.transportDate,jdbcType=DATE}, #{item.receivedCompany,jdbcType=VARCHAR}, 
        #{item.receivedAddress,jdbcType=VARCHAR}, #{item.receivedUsername,jdbcType=VARCHAR}, 
        #{item.signedDate,jdbcType=DATE}, #{item.withoutTaxAmount,jdbcType=DECIMAL}, #{item.expressName,jdbcType=VARCHAR}, 
        #{item.onlineReceiptId,jdbcType=INTEGER}, #{item.batchNo,jdbcType=VARCHAR}, #{item.systemAddTime,jdbcType=TIMESTAMP}, 
        #{item.enableReceive,jdbcType=BOOLEAN}, #{item.oldLogisticsNo,jdbcType=VARCHAR}, 
        #{item.isIntercepted,jdbcType=BOOLEAN}, #{item.interceptTime,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="EXPRESS_ID" keyProperty="expressId" parameterType="com.vedeng.erp.mobile.domain.Express" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_EXPRESS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="expressId != null">
        EXPRESS_ID,
      </if>
      LOGISTICS_ID,
      LOGISTICS_NO,
      COMPANY_ID,
      DELIVERY_TIME,
      ARRIVAL_STATUS,
      ARRIVAL_TIME,
      DELIVERY_FROM,
      LOGISTICS_COMMENTS,
      IS_ENABLE,
      PAYMENT_TYPE,
      CARD_NUMBER,
      BUSINESS_TYPE,
      REAL_WEIGHT,
      NUM,
      AMOUNT_WEIGHT,
      MAIL_GOODS,
      MAIL_GOODS_NUM,
      IS_PROTECT_PRICE,
      PROTECT_PRICE,
      IS_RECEIPT,
      MAIL_COMMTENTS,
      SENT_SMS,
      ADD_TIME,
      CREATOR,
      MOD_TIME,
      UPDATER,
      TRAVELING_BY_TICKET,
      IS_INVOICING,
      WMS_ORDER_NO,
      TRANSPORT_DATE,
      RECEIVED_COMPANY,
      RECEIVED_ADDRESS,
      RECEIVED_USERNAME,
      SIGNED_DATE,
      WITHOUT_TAX_AMOUNT,
      EXPRESS_NAME,
      ONLINE_RECEIPT_ID,
      BATCH_NO,
      SYSTEM_ADD_TIME,
      ENABLE_RECEIVE,
      OLD_LOGISTICS_NO,
      IS_INTERCEPTED,
      INTERCEPT_TIME,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="expressId != null">
        #{expressId,jdbcType=INTEGER},
      </if>
      #{logisticsId,jdbcType=INTEGER},
      #{logisticsNo,jdbcType=VARCHAR},
      #{companyId,jdbcType=INTEGER},
      #{deliveryTime,jdbcType=BIGINT},
      #{arrivalStatus,jdbcType=BOOLEAN},
      #{arrivalTime,jdbcType=BIGINT},
      #{deliveryFrom,jdbcType=INTEGER},
      #{logisticsComments,jdbcType=VARCHAR},
      #{isEnable,jdbcType=BOOLEAN},
      #{paymentType,jdbcType=BOOLEAN},
      #{cardNumber,jdbcType=VARCHAR},
      #{businessType,jdbcType=BOOLEAN},
      #{realWeight,jdbcType=DECIMAL},
      #{num,jdbcType=INTEGER},
      #{amountWeight,jdbcType=DECIMAL},
      #{mailGoods,jdbcType=VARCHAR},
      #{mailGoodsNum,jdbcType=INTEGER},
      #{isProtectPrice,jdbcType=BOOLEAN},
      #{protectPrice,jdbcType=DECIMAL},
      #{isReceipt,jdbcType=BOOLEAN},
      #{mailCommtents,jdbcType=VARCHAR},
      #{sentSms,jdbcType=BOOLEAN},
      #{addTime,jdbcType=BIGINT},
      #{creator,jdbcType=INTEGER},
      #{modTime,jdbcType=BIGINT},
      #{updater,jdbcType=INTEGER},
      #{travelingByTicket,jdbcType=BOOLEAN},
      #{isInvoicing,jdbcType=BOOLEAN},
      #{wmsOrderNo,jdbcType=VARCHAR},
      #{transportDate,jdbcType=DATE},
      #{receivedCompany,jdbcType=VARCHAR},
      #{receivedAddress,jdbcType=VARCHAR},
      #{receivedUsername,jdbcType=VARCHAR},
      #{signedDate,jdbcType=DATE},
      #{withoutTaxAmount,jdbcType=DECIMAL},
      #{expressName,jdbcType=VARCHAR},
      #{onlineReceiptId,jdbcType=INTEGER},
      #{batchNo,jdbcType=VARCHAR},
      #{systemAddTime,jdbcType=TIMESTAMP},
      #{enableReceive,jdbcType=BOOLEAN},
      #{oldLogisticsNo,jdbcType=VARCHAR},
      #{isIntercepted,jdbcType=BOOLEAN},
      #{interceptTime,jdbcType=BIGINT},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="expressId != null">
        EXPRESS_ID = #{expressId,jdbcType=INTEGER},
      </if>
      LOGISTICS_ID = #{logisticsId,jdbcType=INTEGER},
      LOGISTICS_NO = #{logisticsNo,jdbcType=VARCHAR},
      COMPANY_ID = #{companyId,jdbcType=INTEGER},
      DELIVERY_TIME = #{deliveryTime,jdbcType=BIGINT},
      ARRIVAL_STATUS = #{arrivalStatus,jdbcType=BOOLEAN},
      ARRIVAL_TIME = #{arrivalTime,jdbcType=BIGINT},
      DELIVERY_FROM = #{deliveryFrom,jdbcType=INTEGER},
      LOGISTICS_COMMENTS = #{logisticsComments,jdbcType=VARCHAR},
      IS_ENABLE = #{isEnable,jdbcType=BOOLEAN},
      PAYMENT_TYPE = #{paymentType,jdbcType=BOOLEAN},
      CARD_NUMBER = #{cardNumber,jdbcType=VARCHAR},
      BUSINESS_TYPE = #{businessType,jdbcType=BOOLEAN},
      REAL_WEIGHT = #{realWeight,jdbcType=DECIMAL},
      NUM = #{num,jdbcType=INTEGER},
      AMOUNT_WEIGHT = #{amountWeight,jdbcType=DECIMAL},
      MAIL_GOODS = #{mailGoods,jdbcType=VARCHAR},
      MAIL_GOODS_NUM = #{mailGoodsNum,jdbcType=INTEGER},
      IS_PROTECT_PRICE = #{isProtectPrice,jdbcType=BOOLEAN},
      PROTECT_PRICE = #{protectPrice,jdbcType=DECIMAL},
      IS_RECEIPT = #{isReceipt,jdbcType=BOOLEAN},
      MAIL_COMMTENTS = #{mailCommtents,jdbcType=VARCHAR},
      SENT_SMS = #{sentSms,jdbcType=BOOLEAN},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      TRAVELING_BY_TICKET = #{travelingByTicket,jdbcType=BOOLEAN},
      IS_INVOICING = #{isInvoicing,jdbcType=BOOLEAN},
      WMS_ORDER_NO = #{wmsOrderNo,jdbcType=VARCHAR},
      TRANSPORT_DATE = #{transportDate,jdbcType=DATE},
      RECEIVED_COMPANY = #{receivedCompany,jdbcType=VARCHAR},
      RECEIVED_ADDRESS = #{receivedAddress,jdbcType=VARCHAR},
      RECEIVED_USERNAME = #{receivedUsername,jdbcType=VARCHAR},
      SIGNED_DATE = #{signedDate,jdbcType=DATE},
      WITHOUT_TAX_AMOUNT = #{withoutTaxAmount,jdbcType=DECIMAL},
      EXPRESS_NAME = #{expressName,jdbcType=VARCHAR},
      ONLINE_RECEIPT_ID = #{onlineReceiptId,jdbcType=INTEGER},
      BATCH_NO = #{batchNo,jdbcType=VARCHAR},
      SYSTEM_ADD_TIME = #{systemAddTime,jdbcType=TIMESTAMP},
      ENABLE_RECEIVE = #{enableReceive,jdbcType=BOOLEAN},
      OLD_LOGISTICS_NO = #{oldLogisticsNo,jdbcType=VARCHAR},
      IS_INTERCEPTED = #{isIntercepted,jdbcType=BOOLEAN},
      INTERCEPT_TIME = #{interceptTime,jdbcType=BIGINT},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="EXPRESS_ID" keyProperty="expressId" parameterType="com.vedeng.erp.mobile.domain.Express" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_EXPRESS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="expressId != null">
        EXPRESS_ID,
      </if>
      <if test="logisticsId != null">
        LOGISTICS_ID,
      </if>
      <if test="logisticsNo != null and logisticsNo != ''">
        LOGISTICS_NO,
      </if>
      <if test="companyId != null">
        COMPANY_ID,
      </if>
      <if test="deliveryTime != null">
        DELIVERY_TIME,
      </if>
      <if test="arrivalStatus != null">
        ARRIVAL_STATUS,
      </if>
      <if test="arrivalTime != null">
        ARRIVAL_TIME,
      </if>
      <if test="deliveryFrom != null">
        DELIVERY_FROM,
      </if>
      <if test="logisticsComments != null and logisticsComments != ''">
        LOGISTICS_COMMENTS,
      </if>
      <if test="isEnable != null">
        IS_ENABLE,
      </if>
      <if test="paymentType != null">
        PAYMENT_TYPE,
      </if>
      <if test="cardNumber != null and cardNumber != ''">
        CARD_NUMBER,
      </if>
      <if test="businessType != null">
        BUSINESS_TYPE,
      </if>
      <if test="realWeight != null">
        REAL_WEIGHT,
      </if>
      <if test="num != null">
        NUM,
      </if>
      <if test="amountWeight != null">
        AMOUNT_WEIGHT,
      </if>
      <if test="mailGoods != null and mailGoods != ''">
        MAIL_GOODS,
      </if>
      <if test="mailGoodsNum != null">
        MAIL_GOODS_NUM,
      </if>
      <if test="isProtectPrice != null">
        IS_PROTECT_PRICE,
      </if>
      <if test="protectPrice != null">
        PROTECT_PRICE,
      </if>
      <if test="isReceipt != null">
        IS_RECEIPT,
      </if>
      <if test="mailCommtents != null and mailCommtents != ''">
        MAIL_COMMTENTS,
      </if>
      <if test="sentSms != null">
        SENT_SMS,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="travelingByTicket != null">
        TRAVELING_BY_TICKET,
      </if>
      <if test="isInvoicing != null">
        IS_INVOICING,
      </if>
      <if test="wmsOrderNo != null and wmsOrderNo != ''">
        WMS_ORDER_NO,
      </if>
      <if test="transportDate != null">
        TRANSPORT_DATE,
      </if>
      <if test="receivedCompany != null and receivedCompany != ''">
        RECEIVED_COMPANY,
      </if>
      <if test="receivedAddress != null and receivedAddress != ''">
        RECEIVED_ADDRESS,
      </if>
      <if test="receivedUsername != null and receivedUsername != ''">
        RECEIVED_USERNAME,
      </if>
      <if test="signedDate != null">
        SIGNED_DATE,
      </if>
      <if test="withoutTaxAmount != null">
        WITHOUT_TAX_AMOUNT,
      </if>
      <if test="expressName != null and expressName != ''">
        EXPRESS_NAME,
      </if>
      <if test="onlineReceiptId != null">
        ONLINE_RECEIPT_ID,
      </if>
      <if test="batchNo != null and batchNo != ''">
        BATCH_NO,
      </if>
      <if test="systemAddTime != null">
        SYSTEM_ADD_TIME,
      </if>
      <if test="enableReceive != null">
        ENABLE_RECEIVE,
      </if>
      <if test="oldLogisticsNo != null and oldLogisticsNo != ''">
        OLD_LOGISTICS_NO,
      </if>
      <if test="isIntercepted != null">
        IS_INTERCEPTED,
      </if>
      <if test="interceptTime != null">
        INTERCEPT_TIME,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="expressId != null">
        #{expressId,jdbcType=INTEGER},
      </if>
      <if test="logisticsId != null">
        #{logisticsId,jdbcType=INTEGER},
      </if>
      <if test="logisticsNo != null and logisticsNo != ''">
        #{logisticsNo,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="deliveryTime != null">
        #{deliveryTime,jdbcType=BIGINT},
      </if>
      <if test="arrivalStatus != null">
        #{arrivalStatus,jdbcType=BOOLEAN},
      </if>
      <if test="arrivalTime != null">
        #{arrivalTime,jdbcType=BIGINT},
      </if>
      <if test="deliveryFrom != null">
        #{deliveryFrom,jdbcType=INTEGER},
      </if>
      <if test="logisticsComments != null and logisticsComments != ''">
        #{logisticsComments,jdbcType=VARCHAR},
      </if>
      <if test="isEnable != null">
        #{isEnable,jdbcType=BOOLEAN},
      </if>
      <if test="paymentType != null">
        #{paymentType,jdbcType=BOOLEAN},
      </if>
      <if test="cardNumber != null and cardNumber != ''">
        #{cardNumber,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=BOOLEAN},
      </if>
      <if test="realWeight != null">
        #{realWeight,jdbcType=DECIMAL},
      </if>
      <if test="num != null">
        #{num,jdbcType=INTEGER},
      </if>
      <if test="amountWeight != null">
        #{amountWeight,jdbcType=DECIMAL},
      </if>
      <if test="mailGoods != null and mailGoods != ''">
        #{mailGoods,jdbcType=VARCHAR},
      </if>
      <if test="mailGoodsNum != null">
        #{mailGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="isProtectPrice != null">
        #{isProtectPrice,jdbcType=BOOLEAN},
      </if>
      <if test="protectPrice != null">
        #{protectPrice,jdbcType=DECIMAL},
      </if>
      <if test="isReceipt != null">
        #{isReceipt,jdbcType=BOOLEAN},
      </if>
      <if test="mailCommtents != null and mailCommtents != ''">
        #{mailCommtents,jdbcType=VARCHAR},
      </if>
      <if test="sentSms != null">
        #{sentSms,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="travelingByTicket != null">
        #{travelingByTicket,jdbcType=BOOLEAN},
      </if>
      <if test="isInvoicing != null">
        #{isInvoicing,jdbcType=BOOLEAN},
      </if>
      <if test="wmsOrderNo != null and wmsOrderNo != ''">
        #{wmsOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="transportDate != null">
        #{transportDate,jdbcType=DATE},
      </if>
      <if test="receivedCompany != null and receivedCompany != ''">
        #{receivedCompany,jdbcType=VARCHAR},
      </if>
      <if test="receivedAddress != null and receivedAddress != ''">
        #{receivedAddress,jdbcType=VARCHAR},
      </if>
      <if test="receivedUsername != null and receivedUsername != ''">
        #{receivedUsername,jdbcType=VARCHAR},
      </if>
      <if test="signedDate != null">
        #{signedDate,jdbcType=DATE},
      </if>
      <if test="withoutTaxAmount != null">
        #{withoutTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="expressName != null and expressName != ''">
        #{expressName,jdbcType=VARCHAR},
      </if>
      <if test="onlineReceiptId != null">
        #{onlineReceiptId,jdbcType=INTEGER},
      </if>
      <if test="batchNo != null and batchNo != ''">
        #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="systemAddTime != null">
        #{systemAddTime,jdbcType=TIMESTAMP},
      </if>
      <if test="enableReceive != null">
        #{enableReceive,jdbcType=BOOLEAN},
      </if>
      <if test="oldLogisticsNo != null and oldLogisticsNo != ''">
        #{oldLogisticsNo,jdbcType=VARCHAR},
      </if>
      <if test="isIntercepted != null">
        #{isIntercepted,jdbcType=BOOLEAN},
      </if>
      <if test="interceptTime != null">
        #{interceptTime,jdbcType=BIGINT},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="expressId != null">
        EXPRESS_ID = #{expressId,jdbcType=INTEGER},
      </if>
      <if test="logisticsId != null">
        LOGISTICS_ID = #{logisticsId,jdbcType=INTEGER},
      </if>
      <if test="logisticsNo != null and logisticsNo != ''">
        LOGISTICS_NO = #{logisticsNo,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="deliveryTime != null">
        DELIVERY_TIME = #{deliveryTime,jdbcType=BIGINT},
      </if>
      <if test="arrivalStatus != null">
        ARRIVAL_STATUS = #{arrivalStatus,jdbcType=BOOLEAN},
      </if>
      <if test="arrivalTime != null">
        ARRIVAL_TIME = #{arrivalTime,jdbcType=BIGINT},
      </if>
      <if test="deliveryFrom != null">
        DELIVERY_FROM = #{deliveryFrom,jdbcType=INTEGER},
      </if>
      <if test="logisticsComments != null and logisticsComments != ''">
        LOGISTICS_COMMENTS = #{logisticsComments,jdbcType=VARCHAR},
      </if>
      <if test="isEnable != null">
        IS_ENABLE = #{isEnable,jdbcType=BOOLEAN},
      </if>
      <if test="paymentType != null">
        PAYMENT_TYPE = #{paymentType,jdbcType=BOOLEAN},
      </if>
      <if test="cardNumber != null and cardNumber != ''">
        CARD_NUMBER = #{cardNumber,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        BUSINESS_TYPE = #{businessType,jdbcType=BOOLEAN},
      </if>
      <if test="realWeight != null">
        REAL_WEIGHT = #{realWeight,jdbcType=DECIMAL},
      </if>
      <if test="num != null">
        NUM = #{num,jdbcType=INTEGER},
      </if>
      <if test="amountWeight != null">
        AMOUNT_WEIGHT = #{amountWeight,jdbcType=DECIMAL},
      </if>
      <if test="mailGoods != null and mailGoods != ''">
        MAIL_GOODS = #{mailGoods,jdbcType=VARCHAR},
      </if>
      <if test="mailGoodsNum != null">
        MAIL_GOODS_NUM = #{mailGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="isProtectPrice != null">
        IS_PROTECT_PRICE = #{isProtectPrice,jdbcType=BOOLEAN},
      </if>
      <if test="protectPrice != null">
        PROTECT_PRICE = #{protectPrice,jdbcType=DECIMAL},
      </if>
      <if test="isReceipt != null">
        IS_RECEIPT = #{isReceipt,jdbcType=BOOLEAN},
      </if>
      <if test="mailCommtents != null and mailCommtents != ''">
        MAIL_COMMTENTS = #{mailCommtents,jdbcType=VARCHAR},
      </if>
      <if test="sentSms != null">
        SENT_SMS = #{sentSms,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="travelingByTicket != null">
        TRAVELING_BY_TICKET = #{travelingByTicket,jdbcType=BOOLEAN},
      </if>
      <if test="isInvoicing != null">
        IS_INVOICING = #{isInvoicing,jdbcType=BOOLEAN},
      </if>
      <if test="wmsOrderNo != null and wmsOrderNo != ''">
        WMS_ORDER_NO = #{wmsOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="transportDate != null">
        TRANSPORT_DATE = #{transportDate,jdbcType=DATE},
      </if>
      <if test="receivedCompany != null and receivedCompany != ''">
        RECEIVED_COMPANY = #{receivedCompany,jdbcType=VARCHAR},
      </if>
      <if test="receivedAddress != null and receivedAddress != ''">
        RECEIVED_ADDRESS = #{receivedAddress,jdbcType=VARCHAR},
      </if>
      <if test="receivedUsername != null and receivedUsername != ''">
        RECEIVED_USERNAME = #{receivedUsername,jdbcType=VARCHAR},
      </if>
      <if test="signedDate != null">
        SIGNED_DATE = #{signedDate,jdbcType=DATE},
      </if>
      <if test="withoutTaxAmount != null">
        WITHOUT_TAX_AMOUNT = #{withoutTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="expressName != null and expressName != ''">
        EXPRESS_NAME = #{expressName,jdbcType=VARCHAR},
      </if>
      <if test="onlineReceiptId != null">
        ONLINE_RECEIPT_ID = #{onlineReceiptId,jdbcType=INTEGER},
      </if>
      <if test="batchNo != null and batchNo != ''">
        BATCH_NO = #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="systemAddTime != null">
        SYSTEM_ADD_TIME = #{systemAddTime,jdbcType=TIMESTAMP},
      </if>
      <if test="enableReceive != null">
        ENABLE_RECEIVE = #{enableReceive,jdbcType=BOOLEAN},
      </if>
      <if test="oldLogisticsNo != null and oldLogisticsNo != ''">
        OLD_LOGISTICS_NO = #{oldLogisticsNo,jdbcType=VARCHAR},
      </if>
      <if test="isIntercepted != null">
        IS_INTERCEPTED = #{isIntercepted,jdbcType=BOOLEAN},
      </if>
      <if test="interceptTime != null">
        INTERCEPT_TIME = #{interceptTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>

  <select id="getExpressInfoBySaleOrder" resultMap="ExpressResultMap">
    select TE.EXPRESS_ID,
    TE.LOGISTICS_NO,
    case
    TE.ARRIVAL_STATUS
    when 0 then '未签收'
    when 1 then '部分签收'
    when 2 then '已签收'
    else '-'
    end as EXPRESS_STATUS,
    TL.NAME as LOGISTICS_NAME,
    TED.EXPRESS_DETAIL_ID,
    TED.BUSINESS_TYPE,
    TED.RELATED_ID,
    TED.NUM as DETAIL_NUM,
    TSG.PRICE,
    TSG.SKU,
    TSG.GOODS_NAME
    from T_EXPRESS TE
    left join T_EXPRESS_DETAIL TED on TE.EXPRESS_ID = TED.EXPRESS_ID
    left join T_LOGISTICS TL on TE.LOGISTICS_ID = TL.LOGISTICS_ID and TL.IS_ENABLE = 1
    left join T_SALEORDER_GOODS TSG on TSG.SALEORDER_GOODS_ID = TED.RELATED_ID and TSG.IS_DELETE = 0
    where TE.IS_ENABLE = 1
      and TE.COMPANY_ID = 1
      and TED.BUSINESS_TYPE = 496
      and TED.RELATED_ID in
      <foreach item="item" collection="saleOrderGoodsIds" open="(" separator="," close=")">
        #{item,jdbcType=INTEGER}
      </foreach>
    order by TE.ADD_TIME desc
  </select>

  <select id="getDirectExpressInfoBySaleOrder" resultMap="ExpressResultMap">
    SELECT TE.EXPRESS_ID,
           TE.LOGISTICS_NO,
           case
             TE.ARRIVAL_STATUS
             when 0 then '未签收'
             when 1 then '部分签收'
             when 2 then '已签收'
             else '-'
             end as EXPRESS_STATUS,
           TL.NAME as LOGISTICS_NAME,
           TED.EXPRESS_DETAIL_ID,
           TED.BUSINESS_TYPE,
           TED.RELATED_ID,
           TED.NUM as DETAIL_NUM,
           TSG.PRICE,
           TSG.SKU,
           TSG.GOODS_NAME
    FROM T_EXPRESS TE
           LEFT JOIN T_EXPRESS_DETAIL TED ON TE.EXPRESS_ID = TED.EXPRESS_ID
           LEFT JOIN T_LOGISTICS TL ON TE.LOGISTICS_ID = TL.LOGISTICS_ID AND TL.IS_ENABLE = 1
           left join T_R_BUYORDER_J_SALEORDER TRBJS on TRBJS.BUYORDER_GOODS_ID = TED.RELATED_ID
           left join T_SALEORDER_GOODS TSG on TSG.SALEORDER_GOODS_ID = TRBJS.SALEORDER_GOODS_ID
    WHERE TE.IS_ENABLE = 1
      and TE.COMPANY_ID = 1
      and TED.BUSINESS_TYPE = 515
      and TSG.SALEORDER_GOODS_ID in
      <foreach item="item" collection="saleOrderGoodsIds" open="(" separator="," close=")">
        #{item,jdbcType=INTEGER}
      </foreach>
    order by TE.ADD_TIME desc;
  </select>

  <select id="getExpressByExpressIdAndLogisticsNo" resultMap="ExpressResultMap">
    SELECT a.EXPRESS_ID,
           a.LOGISTICS_ID,
           a.LOGISTICS_NO
    FROM T_EXPRESS a
           LEFT JOIN T_LOGISTICS b ON a.LOGISTICS_ID = b.LOGISTICS_ID
    WHERE a.LOGISTICS_NO = #{logisticsNo,jdbcType=VARCHAR}
      and a.EXPRESS_ID = #{expressId,jdbcType=INTEGER}
      and b.SYNC_KUAIDI = 1
      and a.IS_ENABLE = 1
      and a.COMPANY_ID = 1
      and (
          a.LOGISTICS_NO NOT LIKE 'XN%'
        and a.LOGISTICS_NO NOT LIKE 'SL%'
        and a.LOGISTICS_NO NOT LIKE 'BD%'
      )
      and b.NAME != '虚拟出入库专用承运商'
      and a.LOGISTICS_COMMENTS != '虚拟快递单'
  </select>

  <select id="getSaleOrderExpressPhoneByExpressId" resultType="java.lang.String">
    SELECT DISTINCT
    s.TAKE_TRADER_CONTACT_MOBILE
    FROM
    T_EXPRESS e
    LEFT JOIN T_EXPRESS_DETAIL ed ON e.EXPRESS_ID = ed.EXPRESS_ID
    LEFT JOIN T_SALEORDER_GOODS sg ON ed.RELATED_ID = sg.SALEORDER_GOODS_ID
    LEFT JOIN T_SALEORDER s ON sg.SALEORDER_ID = s.SALEORDER_ID
    WHERE
    e.EXPRESS_ID = #{expressId,jdbcType = INTEGER}
  </select>

  <select id="getBuyOrderExpressPhoneByExpressId" resultType="java.lang.String">
    SELECT DISTINCT
    s.TAKE_TRADER_CONTACT_MOBILE
    FROM
    T_EXPRESS e
    LEFT JOIN T_EXPRESS_DETAIL ed ON e.EXPRESS_ID = ed.EXPRESS_ID
    LEFT JOIN T_BUYORDER_GOODS sg ON ed.RELATED_ID = sg.BUYORDER_GOODS_ID
    LEFT JOIN T_BUYORDER s ON sg.BUYORDER_ID = s.BUYORDER_ID
    WHERE
    e.EXPRESS_ID = #{expressId,jdbcType = INTEGER}
  </select>

  <select id="getInvoiceExpressPhoneByExpressId" resultType="java.lang.String">
    SELECT DISTINCT
    s.INVOICE_TRADER_CONTACT_MOBILE
    FROM
    T_EXPRESS e
    LEFT JOIN T_EXPRESS_DETAIL ed ON e.EXPRESS_ID = ed.EXPRESS_ID
    LEFT JOIN T_INVOICE_DETAIL id ON ed.RELATED_ID = id.INVOICE_ID
    LEFT JOIN T_SALEORDER_GOODS sg ON id.DETAILGOODS_ID = sg.SALEORDER_GOODS_ID
    LEFT JOIN T_SALEORDER s ON sg.SALEORDER_ID = s.SALEORDER_ID
    WHERE
    e.EXPRESS_ID = #{expressId,jdbcType = INTEGER}
  </select>

  <select id="getLogisticsCode" resultType="java.lang.String">
    SELECT TLC.CODE
    FROM T_LOGISTICS TL
           LEFT JOIN T_LOGISTICS_CODE TLC ON TL.NAME = TLC.NAME
    WHERE TL.NAME = #{logisticsName,jdbcType=VARCHAR}
      and TL.IS_ENABLE = 1
  </select>
</mapper>