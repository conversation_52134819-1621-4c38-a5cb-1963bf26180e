package com.vedeng.erp.saleorder.model.dto;

import lombok.Data;

import java.util.Objects;

/**
 * 订单发票信息运输对象
 *
 * <AUTHOR>
 */
@Data
public class OrderInvoiceInfo {

    /**
     * 发票类型
     */
    private Integer invoiceType;

    /**
     * 开票类型
     */
    private Integer invoiceMethod;

    /**
     * 收票联系人名称
     */
    private String invoiceTraderContactName;

    /**
     * 收票联系人手机号
     */
    private String invoiceTraderContactMobile;

    /**
     * 收票联系人地区最小ID
     */
    private Integer invoiceTraderAddressId;

    /**
     * 收票地址
     */
    private String invoiceTraderAddress;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        OrderInvoiceInfo that = (OrderInvoiceInfo) o;
        return Objects.equals(invoiceType, that.invoiceType) && Objects.equals(invoiceMethod, that.invoiceMethod) && Objects.equals(invoiceTraderContactName, that.invoiceTraderContactName) && Objects.equals(invoiceTraderContactMobile, that.invoiceTraderContactMobile) && Objects.equals(invoiceTraderAddressId, that.invoiceTraderAddressId) && Objects.equals(invoiceTraderAddress, that.invoiceTraderAddress);
    }

    @Override
    public int hashCode() {
        return Objects.hash(invoiceType, invoiceMethod, invoiceTraderContactName, invoiceTraderContactMobile, invoiceTraderAddressId, invoiceTraderAddress);
    }
}
