package com.vedeng.erp.buyorderexpense.common.util;

/**
 * <AUTHOR>
 * @description 计算 子状态工具类
 * @date 2022/8/29 15:03
 **/
public class BuyorderExpenseUtils {

    /**
     * 根据采购费用单主、子状态计算新的采购单状态，因为存在多个节点同时高亮的情况，因此用逗号隔开
     * 由于要兼容列表页筛选字段的定时任务，所以此处没有返回具体的相加后的数值，而是用逗号隔开
     *
     * @param data 费用单
     * @return 计算后的采购单状态
     */
//    public static String calcStatusAndSubStatusOfBuyOrderExpense(BuyorderExpenseDto data) {
//        // 节点对应规则: 1待确认、2审核中、3待付款、5待收票、6已完结、7已关闭
//        if (data.getStatus() == 0) {
//            if (data.getVerifyStatus() != null && data.getVerifyStatus() == 0) {
//                return "2"; // 审核中
//            } else {
//                return "1"; // 待确认
//            }
//        } else if (data.getStatus() == 1) {
//            // 付款状态 1:全部付款 0: 部分/未付款
//            int paymentStatus = data.getPaymentStatus() == 2 ? 1 : 0;
//            // 收票状态 8:全部收票 0:部分/未收票
//            int invoiceStatus = data.getInvoiceStatus() == 2 ? 8 : 0;
//
//            // 新订单流中对票货款状态做了联动，因此正常情况下，下列一些情况不会出现，但为了不遗漏还是罗列了所有16种情况
//            switch (paymentStatus + invoiceStatus) {
//                case 0:
//                    return "3,5"; // 待付款+待收票
//                case 8:
//                    return "3"; // 待付款
//                case 1:
//                    return "5"; // 待收票
//                case 9:
//                    return "6"; // 票货款全部完成，理论上应该不会出现，会变为已完结
//                default:
//                    return "0";
//            }
//        } else if (data.getStatus() == 2) {
//            return "6"; // 已完结
//        } else {
//            return "7"; // 已关闭
//        }
//    }

    /**
     * 根据采购费用单主、子状态计算新的采购单状态，因为存在多个节点同时高亮的情况，因此用逗号隔开
     * 由于要兼容列表页筛选字段的定时任务，所以此处没有返回具体的相加后的数值，而是用逗号隔开
     *
     * @param data 费用单
     * @return 计算后的采购单状态
     */
//    public static void BuyOrderExpenseStepsNode(BuyorderExpenseDto data) {
//
//        List<StepsNodeDto> stepsNodes = new ArrayList<>();
//
//        StepsNodeDto valid = StepsNodeDto.builder().title("待确认").type(StepsTypeEnum.wait.getType()).build();
//        StepsNodeDto verify = StepsNodeDto.builder().title("待审核").type(StepsTypeEnum.wait.getType()).build();
//        StepsNodeDto payment = StepsNodeDto.builder().title("待付款").type(StepsTypeEnum.wait.getType()).build();
//        StepsNodeDto invoice = StepsNodeDto.builder().title("待收票").type(StepsTypeEnum.wait.getType()).build();
//        StepsNodeDto end = StepsNodeDto.builder().title("已完结").type(StepsTypeEnum.wait.getType()).build();
//        StepsNodeDto close = StepsNodeDto.builder().title("审核中").type(StepsTypeEnum.wait.getType()).build();
//        // 节点对应规则: 1待确认、2审核中、3待付款、5待收票、6已完结、7已关闭
//        if (data.getStatus() == 0) {
//            if (data.getVerifyStatus() != null && data.getVerifyStatus() == 0) {// 审核中
//                valid.setTitle("已确认");
//                valid.setType(StepsTypeEnum.success.getType());
//                stepsNodes.add(valid);
//                stepsNodes.add(verify);
//                stepsNodes.add(payment);
//                stepsNodes.add(invoice);
//                stepsNodes.add(end);
//                data.setStepsNodes(stepsNodes);
//                return;
//            }
//            if (data.getVerifyStatus() != null && data.getVerifyStatus() == 0) {
//                stepsNodes.add(StepsNodeDto.builder().title("待确认").type(StepsTypeEnum.success.getType()).build());
//                data.setStepsNodes(stepsNodes);
//                return;
//            }
//        } else if (data.getStatus() == 1) {
//            // 付款状态 1:全部付款 0: 部分/未付款
//            int paymentStatus = data.getPaymentStatus() == 2 ? 1 : 0;
//            // 收票状态 8:全部收票 0:部分/未收票
//            int invoiceStatus = data.getInvoiceStatus() == 2 ? 8 : 0;
//
//            // 新订单流中对票货款状态做了联动，因此正常情况下，下列一些情况不会出现，但为了不遗漏还是罗列了所有16种情况
//            switch (paymentStatus + invoiceStatus) {
//                case 0:
//                    return "3,5"; // 待付款+待收票
//                case 8:
//                    return "3"; // 待付款
//                case 1:
//                    return "5"; // 待收票
//                case 9:
//                    return "6"; // 票货款全部完成，理论上应该不会出现，会变为已完结
//                default:
//                    return "0";
//            }
//        } else if (data.getStatus() == 2) {
//            return "6"; // 已完结
//        } else {
//            return "7"; // 已关闭
//        }
//    }
}
