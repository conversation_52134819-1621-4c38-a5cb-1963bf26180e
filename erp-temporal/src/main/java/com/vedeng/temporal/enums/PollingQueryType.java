package com.vedeng.temporal.enums;

import cn.hutool.core.util.StrUtil;
import com.vedeng.common.core.utils.ErpSpringBeanUtil;
import com.vedeng.temporal.domain.dto.CustomPollingRequest;
import com.vedeng.temporal.domain.entity.BaseCompanyInfoEntity;
import com.vedeng.temporal.mapper.TemporalBaseCompanyInfoMapper;
import com.vedeng.temporal.mapper.TemporalFlowOrderMapper;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 轮询查询类型枚举
 * 
 * 定义支持的通用化轮询查询类型，每个枚举值包含自己的查询执行逻辑
 * 采用枚举方法模式，实现高内聚、易扩展的架构设计
 * 
 * <AUTHOR> 4.0 sonnet
 * @version 2.0 (枚举方法模式版本)
 * @since 2025-01-22
 */
@Slf4j
public enum PollingQueryType {
    
    /**
     * Temporal流程订单查询
     * 
     * 支持的查询类型（通过queryParameters.queryTypes配置）：
     * - "SALE_ORDER": 销售订单查询
     * - "BUY_ORDER": 采购订单查询
     * - 可组合使用，如：["SALE_ORDER", "BUY_ORDER"]
     * 
     * 查询参数示例：
     * {
     *   "queryTypes": ["SALE_ORDER", "BUY_ORDER"],
     *   "currentCompany": "COMPANY_A",
     *   "previousCompany": "COMPANY_B"
     * }
     * 
     * 返回结果示例：
     * {
     *   "saleOrderNo": "SO123456",
     *   "buyOrderNo": "PO789012"
     * }
     */
    TEMPORAL_FLOW_ORDER_QUERY("temporal.flow.order.query", "Temporal流程订单查询") {
        @Override
        @SuppressWarnings("unchecked")
        public Map<String, Object> executeQuery(CustomPollingRequest request) {
            Map<String, Object> queryParams = request.getQueryParameters();
            List<String> queryTypes = (List<String>) queryParams.get("queryTypes");
            String currentCompany = (String) queryParams.get("currentCompany");
            String previousCompany = (String) queryParams.get("previousCompany");
            String businessId = request.getBusinessId();
            
            Map<String, Object> result = new HashMap<>();
            
            try {
                TemporalFlowOrderMapper temporalFlowOrderMapper = ErpSpringBeanUtil.getBean(TemporalFlowOrderMapper.class);
                
                for (String queryType : queryTypes) {
                    switch (queryType) {
                        case "SALE_ORDER":
                            // 查询销售单号（flowOrderInfoType = 1 表示销售类型）
                            String saleOrderNo = temporalFlowOrderMapper.selectSaleOrderIdByCompanyAndFlowOrder(
                                    currentCompany, Long.valueOf(businessId), 1);
                            result.put("saleOrderNo", saleOrderNo);
                            break;
                            
                        case "BUY_ORDER":
                            // 查询采购单号（flowOrderInfoType = 0 表示采购类型）
                            String buyOrderNo;
                            if (previousCompany != null) {
                                buyOrderNo = temporalFlowOrderMapper.selectSaleOrderIdByCompanyAndFlowOrder(
                                        previousCompany, Long.valueOf(businessId), 0);
                                
                                // 如果上一家公司是第一家公司，则需要从业务单中获取采购单号
                                if (StrUtil.isBlank(buyOrderNo)) {
                                    buyOrderNo = temporalFlowOrderMapper.selectBusinessNoByCompanyAndFlowOrder(
                                            previousCompany, Long.valueOf(businessId));
                                }
                            } else {
                                buyOrderNo = temporalFlowOrderMapper.selectSaleOrderIdByCompanyAndFlowOrder(
                                        currentCompany, Long.valueOf(businessId), 0);
                                
                                // 如果当前公司是第一家公司，则需要从业务单中获取采购单号
                                if (StrUtil.isBlank(buyOrderNo)) {
                                    buyOrderNo = temporalFlowOrderMapper.selectBusinessNoByCompanyAndFlowOrder(
                                            currentCompany, Long.valueOf(businessId));
                                }
                            }
                            result.put("buyOrderNo", buyOrderNo);
                            break;
                            
                        default:
                            log.warn("不支持的流程订单查询类型: {}", queryType);
                    }
                }
                
            } catch (Exception e) {
                log.warn("执行流程订单查询异常，请求: {}, 错误: {}", request.getRequestKey(), e.getMessage());
                // 异常时返回空结果，让轮询继续
                return new HashMap<>();
            }
            
            return result;
        }
    },
    
    /**
     * 基础公司信息查询
     * 
     * 用于查询公司基础信息，如判断是否为子公司等
     * 
     * 查询参数示例：
     * {
     *   "companyCode": "COMPANY_A"
     * }
     * 
     * 返回结果示例：
     * {
     *   "companyInfo": BaseCompanyInfoEntity对象,
     *   "companyCode": "COMPANY_A",
     *   "companyName": "公司A名称"
     * }
     */
    BASE_COMPANY_INFO_QUERY("base.company.info.query", "基础公司信息查询") {
        @Override
        public Map<String, Object> executeQuery(CustomPollingRequest request) {
            Map<String, Object> queryParams = request.getQueryParameters();
            String companyCode = (String) queryParams.get("companyCode");
            
            Map<String, Object> result = new HashMap<>();
            
            try {
                TemporalBaseCompanyInfoMapper temporalBaseCompanyInfoMapper = ErpSpringBeanUtil.getBean(TemporalBaseCompanyInfoMapper.class);
                
                // 查询公司信息，如果存在则表示是子公司
                BaseCompanyInfoEntity companyInfo = temporalBaseCompanyInfoMapper.selectByShortName(companyCode);
                
                result.put("companyInfo", companyInfo);
                result.put("companyCode", companyCode);
                result.put("companyName", companyInfo != null ? companyInfo.getCompanyName() : null);
                
            } catch (Exception e) {
                log.warn("执行公司信息查询异常，请求: {}, 错误: {}", request.getRequestKey(), e.getMessage());
                // 异常时返回空结果，让轮询继续
                return new HashMap<>();
            }
            
            return result;
        }
    };
    
    /**
     * 查询类型代码
     */
    private final String code;
    
    /**
     * 查询类型描述
     */
    private final String description;
    
    /**
     * 构造函数
     */
    PollingQueryType(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    /**
     * 执行具体的查询逻辑
     * 
     * @param request 自定义轮询请求对象
     * @return 查询结果Map
     */
    public abstract Map<String, Object> executeQuery(CustomPollingRequest request);
    
    /**
     * 获取查询类型代码
     */
    public String getCode() {
        return code;
    }
    
    /**
     * 获取查询类型描述
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取枚举值
     */
    public static PollingQueryType fromCode(String code) {
        for (PollingQueryType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的轮询查询类型代码: " + code);
    }
}