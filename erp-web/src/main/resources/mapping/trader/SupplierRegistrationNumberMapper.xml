<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.trader.dao.SupplierRegistrationNumberMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.trader.model.SupplierRegistrationNumber" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 30 18:50:07 CST 2020.
    -->
    <id column="SUPPLIER_REGISTRATION_NUMBER_ID" property="supplierRegistrationNumberId" jdbcType="INTEGER" />
    <result column="TRADER_ID" property="traderId" jdbcType="INTEGER" />
    <result column="REGISTRATION_NUMBER_ID" property="registrationNumberId" jdbcType="INTEGER" />
    <result column="CHECK_STATUS" property="checkStatus" jdbcType="TINYINT" />
    <result column="CHECK_USER_ID" property="checkUserId" jdbcType="INTEGER" />
    <result column="CHECK_TIME" property="checkTime" jdbcType="BIGINT" />
    <result column="IS_DELETE" property="isDelete" jdbcType="BIT" />
    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="BIGINT" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="UPDATOR" property="updator" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 30 18:50:07 CST 2020.
    -->
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 30 18:50:07 CST 2020.
    -->
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 30 18:50:07 CST 2020.
    -->
    SUPPLIER_REGISTRATION_NUMBER_ID, TRADER_ID, REGISTRATION_NUMBER_ID, CHECK_STATUS, 
    CHECK_USER_ID, CHECK_TIME, IS_DELETE, ADD_TIME, UPDATE_TIME, CREATOR, UPDATOR
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.vedeng.trader.model.SupplierRegistrationNumberExample" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 30 18:50:07 CST 2020.
    -->
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from T_SUPPLIER_REGISTRATION_NUMBER
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 30 18:50:07 CST 2020.
    -->
    select 
    <include refid="Base_Column_List" />
    from T_SUPPLIER_REGISTRATION_NUMBER
    where SUPPLIER_REGISTRATION_NUMBER_ID = #{supplierRegistrationNumberId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 30 18:50:07 CST 2020.
    -->
    delete from T_SUPPLIER_REGISTRATION_NUMBER
    where SUPPLIER_REGISTRATION_NUMBER_ID = #{supplierRegistrationNumberId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.vedeng.trader.model.SupplierRegistrationNumberExample" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 30 18:50:07 CST 2020.
    -->
    delete from T_SUPPLIER_REGISTRATION_NUMBER
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.vedeng.trader.model.SupplierRegistrationNumber" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 30 18:50:07 CST 2020.
    -->
    <selectKey resultType="java.lang.Integer" keyProperty="supplierRegistrationNumberId" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_SUPPLIER_REGISTRATION_NUMBER (TRADER_ID, REGISTRATION_NUMBER_ID, CHECK_STATUS, 
      CHECK_USER_ID, CHECK_TIME, IS_DELETE, 
      ADD_TIME, UPDATE_TIME, CREATOR, 
      UPDATOR)
    values (#{traderId,jdbcType=INTEGER}, #{registrationNumberId,jdbcType=INTEGER}, #{checkStatus,jdbcType=TINYINT}, 
      #{checkUserId,jdbcType=INTEGER}, #{checkTime,jdbcType=BIGINT}, #{isDelete,jdbcType=BIT}, 
      #{addTime,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}, 
      #{updator,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.trader.model.SupplierRegistrationNumber" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 30 18:50:07 CST 2020.
    -->
    <selectKey resultType="java.lang.Integer" keyProperty="supplierRegistrationNumberId" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_SUPPLIER_REGISTRATION_NUMBER
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="traderId != null" >
        TRADER_ID,
      </if>
      <if test="registrationNumberId != null" >
        REGISTRATION_NUMBER_ID,
      </if>
      <if test="checkStatus != null" >
        CHECK_STATUS,
      </if>
      <if test="checkUserId != null" >
        CHECK_USER_ID,
      </if>
      <if test="checkTime != null" >
        CHECK_TIME,
      </if>
      <if test="isDelete != null" >
        IS_DELETE,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="updator != null" >
        UPDATOR,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="traderId != null" >
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="registrationNumberId != null" >
        #{registrationNumberId,jdbcType=INTEGER},
      </if>
      <if test="checkStatus != null" >
        #{checkStatus,jdbcType=TINYINT},
      </if>
      <if test="checkUserId != null" >
        #{checkUserId,jdbcType=INTEGER},
      </if>
      <if test="checkTime != null" >
        #{checkTime,jdbcType=BIGINT},
      </if>
      <if test="isDelete != null" >
        #{isDelete,jdbcType=BIT},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updator != null" >
        #{updator,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vedeng.trader.model.SupplierRegistrationNumberExample" resultType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 30 18:50:07 CST 2020.
    -->
    select count(*) from T_SUPPLIER_REGISTRATION_NUMBER
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 30 18:50:07 CST 2020.
    -->
    update T_SUPPLIER_REGISTRATION_NUMBER
    <set >
      <if test="record.supplierRegistrationNumberId != null" >
        SUPPLIER_REGISTRATION_NUMBER_ID = #{record.supplierRegistrationNumberId,jdbcType=INTEGER},
      </if>
      <if test="record.traderId != null" >
        TRADER_ID = #{record.traderId,jdbcType=INTEGER},
      </if>
      <if test="record.registrationNumberId != null" >
        REGISTRATION_NUMBER_ID = #{record.registrationNumberId,jdbcType=INTEGER},
      </if>
      <if test="record.checkStatus != null" >
        CHECK_STATUS = #{record.checkStatus,jdbcType=TINYINT},
      </if>
      <if test="record.checkUserId != null" >
        CHECK_USER_ID = #{record.checkUserId,jdbcType=INTEGER},
      </if>
      <if test="record.checkTime != null" >
        CHECK_TIME = #{record.checkTime,jdbcType=BIGINT},
      </if>
      <if test="record.isDelete != null" >
        IS_DELETE = #{record.isDelete,jdbcType=BIT},
      </if>
      <if test="record.addTime != null" >
        ADD_TIME = #{record.addTime,jdbcType=BIGINT},
      </if>
      <if test="record.updateTime != null" >
        UPDATE_TIME = #{record.updateTime,jdbcType=BIGINT},
      </if>
      <if test="record.creator != null" >
        CREATOR = #{record.creator,jdbcType=INTEGER},
      </if>
      <if test="record.updator != null" >
        UPDATOR = #{record.updator,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 30 18:50:07 CST 2020.
    -->
    update T_SUPPLIER_REGISTRATION_NUMBER
    set SUPPLIER_REGISTRATION_NUMBER_ID = #{record.supplierRegistrationNumberId,jdbcType=INTEGER},
      TRADER_ID = #{record.traderId,jdbcType=INTEGER},
      REGISTRATION_NUMBER_ID = #{record.registrationNumberId,jdbcType=INTEGER},
      CHECK_STATUS = #{record.checkStatus,jdbcType=TINYINT},
      CHECK_USER_ID = #{record.checkUserId,jdbcType=INTEGER},
      CHECK_TIME = #{record.checkTime,jdbcType=BIGINT},
      IS_DELETE = #{record.isDelete,jdbcType=BIT},
      ADD_TIME = #{record.addTime,jdbcType=BIGINT},
      UPDATE_TIME = #{record.updateTime,jdbcType=BIGINT},
      CREATOR = #{record.creator,jdbcType=INTEGER},
      UPDATOR = #{record.updator,jdbcType=INTEGER}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.trader.model.SupplierRegistrationNumber" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 30 18:50:07 CST 2020.
    -->
    update T_SUPPLIER_REGISTRATION_NUMBER
    <set >
      <if test="traderId != null" >
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="registrationNumberId != null" >
        REGISTRATION_NUMBER_ID = #{registrationNumberId,jdbcType=INTEGER},
      </if>
      <if test="checkStatus != null" >
        CHECK_STATUS = #{checkStatus,jdbcType=TINYINT},
      </if>
      <if test="checkUserId != null" >
        CHECK_USER_ID = #{checkUserId,jdbcType=INTEGER},
      </if>
      <if test="checkTime != null" >
        CHECK_TIME = #{checkTime,jdbcType=BIGINT},
      </if>
      <if test="isDelete != null" >
        IS_DELETE = #{isDelete,jdbcType=BIT},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updator != null" >
        UPDATOR = #{updator,jdbcType=INTEGER},
      </if>
    </set>
    where SUPPLIER_REGISTRATION_NUMBER_ID = #{supplierRegistrationNumberId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.trader.model.SupplierRegistrationNumber" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 30 18:50:07 CST 2020.
    -->
    update T_SUPPLIER_REGISTRATION_NUMBER
    set TRADER_ID = #{traderId,jdbcType=INTEGER},
      REGISTRATION_NUMBER_ID = #{registrationNumberId,jdbcType=INTEGER},
      CHECK_STATUS = #{checkStatus,jdbcType=TINYINT},
      CHECK_USER_ID = #{checkUserId,jdbcType=INTEGER},
      CHECK_TIME = #{checkTime,jdbcType=BIGINT},
      IS_DELETE = #{isDelete,jdbcType=BIT},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      UPDATE_TIME = #{updateTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATOR = #{updator,jdbcType=INTEGER}
    where SUPPLIER_REGISTRATION_NUMBER_ID = #{supplierRegistrationNumberId,jdbcType=INTEGER}
  </update>

  <select id="getInfoByTraderIdAndNumberId" resultType="com.vedeng.trader.model.SupplierRegistrationNumber" parameterType="com.vedeng.trader.model.SupplierRegistrationNumber">
    SELECT SUPPLIER_REGISTRATION_NUMBER_ID,CHECK_TIME,CHECK_STATUS,CHECK_USER_ID FROM T_SUPPLIER_REGISTRATION_NUMBER WHERE TRADER_ID=#{traderId} AND REGISTRATION_NUMBER_ID=#{registrationNumberId}
  </select>
</mapper>