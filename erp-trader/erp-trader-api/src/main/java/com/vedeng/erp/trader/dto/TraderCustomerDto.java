package com.vedeng.erp.trader.dto;

import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 供应商dto
 * @date 2022/5/24 9:52
 */
@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TraderCustomerDto implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer traderCustomerId;

    private Integer traderId;

    private String traderName;

    /**
     * 资质状态 状态0审核中1审核通过2审核不通过
     */
    private Integer aptitudeStatus;

    private TraderFinanceDto traderFinanceDto;

    /**
     * 归属总公司名称
     */
    private String parentTraderName;

    private Integer customerNature;

    /**
     * 地区
     */
    private String address;

    /**
     * 归属销售
     */
    private Integer userId;

    /**
     * 地区id
     */
    private Integer areaId;

    /**
     * 归属销售id集合
     */
    private List<Integer> userIdList;

    /**
     * 归属销售名称
     */
    private String userName;

    /**
     * 创建时间
     */
    private Long addTime;

    /**
     * 关联的客户分组ID
     */
    private Long associatedCustomerGroup;

    /**
     * 余额
     */
    private BigDecimal amount;

    /**
     * 无效原因
     */
    private String invalidReason;

    /**
     * 无效原因-具体原因
     */
    private String otherReason;
}
