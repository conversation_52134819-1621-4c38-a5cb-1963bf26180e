html,
body {
  background: #fff;
}
html::-webkit-scrollbar,
body::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
html::-webkit-scrollbar-track,
body::-webkit-scrollbar-track {
  background: transparent;
  width: 6px;
  height: 6px;
}
html::-webkit-scrollbar-thumb,
body::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 6px;
  height: 6px;
  border-radius: 3px;
}
html::-webkit-scrollbar-thumb:hover,
body::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
html::-webkit-scrollbar-thumb:active,
body::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}
.page-wrap {
  width: 800px;
  margin: 20px auto 0 auto;
  padding-bottom: 65px;
}
.page-block {
  margin-bottom: 20px;
}
.page-block.custom-block {
  padding: 0 0 15px 10px;
  border: 1px solid transparent;
}
.page-block.active {
  background: #E0F3FF;
  border: solid 1px #E1E5E8;
}
.page-block:last-child {
  margin-bottom: 0;
}
.page-block .page-block-title-wrap {
  display: flex;
  align-items: center;
  position: relative;
}
.page-block .page-block-title-wrap .vd-ui-checkbox-item {
  margin-right: 5px;
  margin-top: -3px;
}
.page-block .page-block-title-wrap .page-block-title-tip {
  display: flex;
  align-items: center;
  color: #f60;
  font-size: 12px;
  position: absolute;
  right: 10px;
}
.page-block .page-block-title-wrap .page-block-title-tip .icon-caution1 {
  font-size: 16px;
  line-height: 1;
  margin-right: 5px;
}
.page-block .page-block-title {
  padding: 10px 0;
  font-size: 16px;
  font-weight: 700;
}
.page-block .page-block-cnt {
  padding: 10px;
  border: solid 1px transparent;
  border-bottom: solid 1px #E1E5E8;
  margin-bottom: -1px;
}
.page-block .page-block-cnt.active {
  background: #E0F3FF;
  border: solid 1px #E1E5E8;
}
.page-block .page-block-detail {
  display: flex;
}
.page-block .page-block-detail .vd-ui-checkbox-item {
  margin-right: 5px;
}
.page-block .page-block-detail .page-block-txt {
  flex: 1;
  line-height: 24px;
}
.page-block .page-block-detail .page-block-txt .strong {
  color: #f60;
}
.page-block .page-block-form {
  margin: 15px 0 5px 36px;
  font-size: 12px;
}
.page-block .page-block-form .page-block-form-item {
  margin-bottom: 10px;
  display: flex;
}
.page-block .page-block-form .page-block-form-item:last-child {
  margin-bottom: 0;
}
.page-block .page-block-form .page-block-form-item .page-form-label {
  width: 120px;
  color: #999;
  text-align: right;
  padding-top: 6px;
}
.page-block .page-block-form .page-block-form-item .page-form-label .must {
  color: #e64545;
}
.page-block .page-block-form .page-block-form-item .vd-ui-input .vd-ui-input__inner {
  height: 30px;
  font-size: 12px;
}
.page-block .page-block-form .page-block-form-item .vd-ui-input .vd-ui-input__inner::placeholder {
  font-size: 12px;
}
.page-block .page-block-form .page-block-form-item .vd-ui-cascader .vd-ui-cascader-wrapper .icon {
  line-height: 28px;
}
.page-block .page-block-form .page-block-form-item .vd-ui-input-error {
  color: #e64545;
  display: flex;
  margin-top: 5px;
  align-items: center;
}
.page-block .page-block-form .page-block-form-item .vd-ui-input-error .vd-ui-input-error--icon {
  font-size: 16px;
  margin-right: 5px;
  line-height: 1;
  margin-top: 1px;
}
.page-block .page-block-form .page-block-form-item .address-wrap {
  width: 300px;
}
.page-block .page-block-form .page-block-form-item .price-wrap {
  display: flex;
}
.page-block .page-block-form .page-block-form-item .price-wrap .vd-ui-input-error {
  white-space: nowrap;
}
.page-block .page-block-form .page-block-form-item .price-wrap .unit {
  margin-left: 5px;
  line-height: 30px;
}
.page-block .page-block-form .page-block-form-item .page-form-field {
  flex: 1;
}
.page-block .page-custom-form {
  padding-left: 26px;
}
.page-block .page-custom-form .vd-ui-input-error {
  color: #e64545;
  display: flex;
  margin-top: 5px;
  align-items: center;
}
.page-block .page-custom-form .vd-ui-input-error .vd-ui-input-error--icon {
  font-size: 16px;
  margin-right: 5px;
  line-height: 1;
  margin-top: 1px;
}
.page-block .page-custom-form .vd-ui-textarea .vd-ui-textarea__inner {
  height: 117px;
  max-height: 222px;
}
.page-block-button {
  display: flex;
  position: fixed;
  bottom: 0;
  padding: 20px 0;
  background: #fff;
  width: 100%;
  max-width: 800px;
  align-items: center;
}
.page-block-button .vd-ui-button {
  margin-right: 10px;
}
.page-block-button .vd-ui-button:last-child {
  margin-right: 0;
}
.page-block-button .select-num {
  color: #999;
  font-size: 12px;
}
