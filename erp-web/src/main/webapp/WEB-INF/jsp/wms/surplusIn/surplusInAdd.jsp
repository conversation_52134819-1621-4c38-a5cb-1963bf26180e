<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="新增盘盈入库单" scope="application" />
<%@ taglib uri="/WEB-INF/tld/myfn.tld" prefix="myfn" %>
<%@ include file="../../common/common.jsp"%>

<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/js/wms/surplusIn/surplusInAdd.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/suggestSelect.js?rnd=${resourceVersionKey}"></script>
<div class="content">
    <div class="formtitle"></div>
    <div  id="desc_div">
        <form method="post" id="addForm" action="${pageContext.request.contextPath}/wms/surplusIn/saveSurplusInOrder.do">
            <ul class="payplan">
                <li>
                    <div class="infor_name">
                        <span class="font-red">*</span><label>申请部门:</label>
                    </div>
                    <div class="f_left">
                        <select class="input-middle f_left" id="applyerDepartmentId" name="applyerDepartmentId" onchange="selectOrgUser();">
                            <option value="0">请选择申请部门</option>
                            <c:forEach items="${orgList}" var="org">
                                <option value="${org.orgId }" name="applyerDepartmentId"
                                        <c:if test="${user.orgId != null and user.orgId == org.orgId}">selected="selected"</c:if>>
                                        ${org.orgName }
                                </option>
                            </c:forEach>
                        </select>
                    </div>
                </li>
                <li>
                    <div class="infor_name">
                        <span class="font-red">*</span><label>申请人:</label>
                    </div>
                    <div class="f_left">
                        <select class="input-middle f_left" id="applyerUserid" name="applyerUserid" >
                            <option value="0">请选择申请人</option>
                            <c:forEach items="${getUserListByOrgId}" var="singleUser">
                                <option value="${singleUser.userId }" name="applyerUserid"
                                        <c:if test="${singleUser.userId != null and user.userId == singleUser.userId}">selected="selected"</c:if>>
                                        ${singleUser.username}
                                </option>
                            </c:forEach>
                        </select>
                    </div>
                </li>
                <li>
                <li>
                    <div class="infor_name">
                        <span class="font-red">*</span><label>入库产品:</label>
                    </div>
                    <div class="f_left">
                        <span class="bt-small bg-light-blue bt-bg-style pop-new-data"
                              layerParams='{"width":"800px","height":"480px","title":"选择入库产品","link":"<%=path%>/order/saleorder/addSaleorderGoods.do?callbackFuntion=setGoodData&lendOut=2"}'>
						      请选择产品
					</span>
                    </div>
                </li>
                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">已选入库产品</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered" >
                            <thead>
                            <tr>
                                <th style="width:80px">订货号</th>
                                <th style="width:80px">产品名称</th>
                                <th style="width:80px">品牌</th>
                                <th style="width:80px">型号</th>
                                <th style="width:80px">单位</th>
                                <th style="width:120px">入库数量</th>
                                <th style="width:80px">操作</th>
                            </tr>
                            </thead>
                            <tbody class="J-prod-list">
                            </tbody>
                        </table>
                    </div>
                </li>
                <li>
                    <div class="infor_name">
                        <span class="font-red">*</span><label>申请入库日期:</label>
                    </div>
                    <div class="f_left">
                        <input type="text"
                               onClick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'${today}'})" autocomplete="off"
                               name="applyIntime" id="applyIntime" value="${today}">
                    </div>
                </li>
                <li>
                    <div class="infor_name">
                        <label>备注:</label>
                    </div>
                    <div class="f_left">
                        <input type="text" style="width: 400px;height:80px"  name="remark" id="remark" value=""/>
                    </div>
                </li>
            </ul>
            <div class="add-tijiao tcenter mt10">
                <button  name="submit" type="submit" data-type="save">申请入库</button>
                <button type="button" class="bt-bg-style bg-deep-green" onclick="window.location.href='./index.do'">取消</button>
            </div>
        </form>
    </div>
    <script type="text/tmpl" class="J-prod-tmpl">
        <tr class="J-prod-item">
             <td> {{=sku}} <input type="hidden" name="wmsInputOrderGoods[i].skuNo" class="J-sku" value="{{=sku}}"> </td>
            <td > {{=goodsName}} </td>
            <td > {{=brandName}} </td>
            <td > {{=model}} </td>
            <td>{{=unitName}} </td>
             <td><input type="text" name="wmsInputOrderGoods[i].inputNum" class="J-item-num" value=""></td>
              <td> <a href="#" onclick="deleteRow(this)">删除</a> </td>
        </tr>
    </script>
</div>
<%@ include file="../../common/footer.jsp"%>