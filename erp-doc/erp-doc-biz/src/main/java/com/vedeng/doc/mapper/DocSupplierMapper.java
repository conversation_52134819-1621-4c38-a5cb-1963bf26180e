package com.vedeng.doc.mapper;


import com.vedeng.doc.domain.entity.DocSupplier;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Entity com.vedeng.doc.model.DocSupplier
 */
public interface DocSupplierMapper {

    /**
     * 通过品牌搜索厂商资质
     *
     * @param brandId 品牌id
     * @return
     */
    List<DocSupplier> selectListByBrandId(@Param("brandId") Integer brandId);

    /**
     * 查询厂商资质
     *
     * @param docSupplierId
     * @return
     */
    DocSupplier selectByPrimaryKey(@Param("docSupplierId") Integer docSupplierId);
}




