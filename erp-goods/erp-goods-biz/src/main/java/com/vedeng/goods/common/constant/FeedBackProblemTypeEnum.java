package com.vedeng.goods.common.constant;

public enum FeedBackProblemTypeEnum {
    NO_STAMP(1,"印章缺失/不清晰"),PRINT_ERROR(2,"打印内容模糊"),
    WATER_PRINT_ERROR(3,"水印内容不当"),
    EXPIRED(4,"已过期"),OTHER(5,"其他");

    private FeedBackProblemTypeEnum(){}
    private Integer problemType;
    private String desc;
    FeedBackProblemTypeEnum(Integer problemType,String desc){
        this.problemType=problemType;
        this.desc=desc;
    }

    public   Integer problemType(){
        return problemType;
    }
    public static FeedBackProblemTypeEnum get(Integer problemType){
        for(FeedBackProblemTypeEnum e:values()){
            if(e.problemType.equals(problemType)){
                return e;
            }
        }
        return OTHER;
    }

    public String desc() {
        return desc;
    }
}
