package com.vedeng.erp.kingdee.repository.mappers;

import com.vedeng.erp.kingdee.domain.entity.KingDeeInPutFeePlainInvoiceEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface KingDeeInPutFeePlainInvoiceMapper {
    /**
     * delete by primary key
     * @param inPutFeePlainInvoiceId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer inPutFeePlainInvoiceId);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(KingDeeInPutFeePlainInvoiceEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(KingDeeInPutFeePlainInvoiceEntity record);

    /**
     * select by primary key
     * @param inPutFeePlainInvoiceId primary key
     * @return object by primary key
     */
    KingDeeInPutFeePlainInvoiceEntity selectByPrimaryKey(Integer inPutFeePlainInvoiceId);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(KingDeeInPutFeePlainInvoiceEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(KingDeeInPutFeePlainInvoiceEntity record);

    int updateBatchSelective(List<KingDeeInPutFeePlainInvoiceEntity> list);

    int batchInsert(@Param("list") List<KingDeeInPutFeePlainInvoiceEntity> list);

    KingDeeInPutFeePlainInvoiceEntity selectByFQzokBddjtid(String FQzokBddjtid);
}