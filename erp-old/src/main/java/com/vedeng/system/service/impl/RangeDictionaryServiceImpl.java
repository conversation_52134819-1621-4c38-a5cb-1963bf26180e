package com.vedeng.system.service.impl;

import com.vedeng.common.util.StringUtil;
import com.vedeng.goods.model.CoreSkuGenerate;
import com.vedeng.system.dao.RangeDictionaryMapper;
import com.vedeng.system.model.RangeDictionary;
import com.vedeng.system.service.RangeDictionaryService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description: RangeDictionaryServiceImpl.
 * @notes: VDERP-2336 预计可发货时间接口.
 * @version: 1.0.
 * @date: 2020/5/9 4:24 下午.
 * @author: Tomcat.Hui.
 */
@Service
public class RangeDictionaryServiceImpl implements RangeDictionaryService {

    @Resource
    RangeDictionaryMapper rangeDictionaryMapper;

    @Override
    public List<RangeDictionary> getAllDicts() {
        return rangeDictionaryMapper.getAllDict();
    }
}
