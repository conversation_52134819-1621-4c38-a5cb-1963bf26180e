<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>表单-sql级联下拉</title>
</head>
<body id="cascadersql-depart"  type="form"  >
<style>.el-input__inner{height:26px ;line-height:26px}</style>
   <input  th:value="${#strings.isEmpty(value)?'':value}" th:name="${item_name}" th:id="${'search-item_name-' + item_name}"
                class="ez-laycascader-2"
           th:itemsJson="${itemsJson}"
           ez_value="ID" ez_label="LABEL"
           th:multi="${multi}"
           th:collapseTags="${collapsetags}"
           th:showAllLevels="${showalllevels}"
                th:itemPlaceholder="${itemPlaceholder}" >
</body>
</html>