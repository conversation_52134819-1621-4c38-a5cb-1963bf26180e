$(function()
{
    var userId = $("#others_userId_id").val();
    init_page(userId);
    
});

/**
 * @description: 初始化页面.
 * @jira: VDERP-2376【五行剑法】规则修改.
 * @notes: .
 * @version: 1.0.
 * @date: 2020/6/17 5:48 下午.
 * @author: Tomcat.Hui.
 */
function init_page(userId) {

    var month = $("#hisMonth").val();

    // 本月概况
    $.ajax(
    {
        type : "POST",
        url : page_url + "/kpi/query/queryKpiDetailBdkh.do",
        data :
            {
                'userId' : userId,
                'month' : month
            },
        dataType : 'json',
        success : function(result)
        {
            if (result.code == 0)
            {
                //用户
                $("#kpi_bdcustomer_co_user").text(result.data.personalKpi.coBdCustomerNum);
                $("#kpi_bdcustomer_lost_user").text(result.data.personalKpi.lostBdCustomerNum);
                $("#kpi_bdcustomer_score_user").text((result.data.personalKpi.score).toFixed(2));
                $("#kpi_bdcustomer_sort_user").text(result.data.personalKpi.sort);

                //团队
                $("#kpi_bdcustomer_co_group").text(result.data.groupKpi.coBdCustomerNum);
                $("#kpi_bdcustomer_lost_group").text(result.data.groupKpi.lostBdCustomerNum);
                $("#kpi_bdcustomer_score_group").text((result.data.groupKpi.score).toFixed(2));
                // $("#kpi_bdcustomer_sort_group").text(result.data.groupKpi.sort);

                //榜首
                $("#first_one_span_name").text(result.data.firstKpi.userName)
                $("#kpi_bdcustomer_co_first").text(result.data.firstKpi.coBdCustomerNum);
                $("#kpi_bdcustomer_lost_first").text(result.data.firstKpi.lostBdCustomerNum);
                $("#kpi_bdcustomer_score_first").text((result.data.firstKpi.score).toFixed(2));
                $("#kpi_bdcustomer_sort_first").text(result.data.firstKpi.sort);

                //昨天
                $("#kpi_bdcustomer_co_yesterday").text(result.data.yesterdayKpi.coBdCustomerNum);
                $("#kpi_bdcustomer_lost_yesterday").text(result.data.yesterdayKpi.lostBdCustomerNum);
                $("#kpi_bdcustomer_score_yesterday").text((result.data.yesterdayKpi.score).toFixed(2));
                $("#kpi_bdcustomer_sort_yesterday").text(result.data.yesterdayKpi.sort);

                //上月
                $("#kpi_bdcustomer_co_lastmonth").text(result.data.lastMonthKpi.coBdCustomerNum);
                $("#kpi_bdcustomer_lost_lastmonth").text(result.data.lastMonthKpi.lostBdCustomerNum);
                $("#kpi_bdcustomer_score_lastmonth").text((result.data.lastMonthKpi.score).toFixed(2));
                $("#kpi_bdcustomer_sort_lastmonth").text(result.data.lastMonthKpi.sort);

                //新增详情
                show_detail(result.data.monthCoLog,'detail_bdcustomer_new');

                //失效详情
                show_detail(result.data.monthLostLog,'detail_bdcustomer_lost');

                //历史记录
                show_his(result.data.hisMonthList,'his_bdcustomer');

                loadMoreAddTitle();
            }
            else {
                layer.alert("返回码错误");
            }

        },
        error : function(data)
        {

            if (data.status == 1001)
            {
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
            } else {
                layer.alert("请求出现异常");
            }

        }

    });

    function show_detail(logList,table_id){
        var emptyFlag = false;
        var ht = '';
        if(null == logList || logList.length == 0)
        {
            emptyFlag = true;
        }

        if(!emptyFlag)
        {
            for(var i = 0; i < logList.length; i++)
            {
                var mod = logList[i];

                if(null == mod)
                    continue;
                ht += '<tr>';
                ht += '<td>' + (i+1) +'</td>';
                ht += '<td><a class="loadMoreAddtitle" href="javascript:void(0);" tabTitle=\'{"num":"viewcustomer' + mod.traderId + '","link":"./trader/customer/baseinfo.do?traderId=' +  mod.traderId + '","title":"客户信息"}\'>' + mod.traderName + '</a></td>';
                ht += '<td><a class="loadMoreAddtitle" href="javascript:void(0);" tabTitle=\'{"num":"viewsaleorder' + mod.orderId + '","link":"./order/saleorder/view.do?saleorderId=' +  mod.orderId + '","title":"订单信息"}\'>' + mod.orderNo + '</a></td>';
                ht += '<td>'+ mod.orderRealAmount +'</td>';
                ht += '<td>'+new Date(mod.validDate).format('yyyy-MM-dd hh:mm:ss')+'</td>';
                ht += '<td>'+new Date(mod.kpiDate).format('yyyy-MM-dd hh:mm:ss')+'</td>';
                ht += '</tr>';
                emptyFlag = false;
            }
        }

        if(emptyFlag)
        {
            ht += '<tr><td colspan="6">暂无数据</td></tr>';
        }



        $("#" + table_id).html(ht);
    }

    function show_his(logList,table_id){
        var emptyFlag = false;
        var ht = '';
        if(null == logList || logList.length == 0)
        {
            emptyFlag = true;
        }

        if(!emptyFlag)
        {
            for(var i = 0; i < logList.length; i++)
            {
                var mod = logList[i];

                if(null == mod)
                    continue;
                ht += '<tr>';
                ht += '<td>' + mod.monthStr +'</td>';
                ht += '<td>' + mod.coBdCustomerNum +'</td>';
                ht += '<td>' + mod.sort +'</td>';
                ht += '</tr>';
                emptyFlag = false;
            }
        }

        if(emptyFlag)
        {
            ht += '<tr><td colspan="3">暂无数据</td></tr>';
        }

        $("#" + table_id).html(ht);
    }
}