.table {
    max-width: 100%;
    background-color: transparent;
    border-collapse: collapse;
    border-spacing: 0;
    table-layout: fixed;
    font-size: 12px;
    text-align: left;
}

.table th {
    font-weight: normal;
    text-align: left;
    color: #999;
}

.table th,
.table td {
    padding: 10px 20px 10px 0;
    word-wrap: break-word;
    vertical-align: top;
}

.table th:first-child,
.table td:first-child {
    padding-left: 20px;
}

.table td {
    color: #666;
}

.table td.dir-l {
    text-align: left;
}

.table-base {
    width: 100%;
}

.table-base td {
    border-bottom: 1px solid #EDF0F2;
}

.table-base th {
    border-bottom: 1px solid #EDF0F2;
}

.table-normal,
.table-vertical {
    width: 100%;
}

.table-normal td,
.table-vertical td {
    border: 1px solid #EDF0F2;
}

.table-normal th,
.table-vertical th {
    border: 1px solid #EDF0F2;
    background: #f5f7fa;
}

.table-stripe.table-normal tr:nth-child(2n - 1) td {
    background: #f5f7fa;
}

.table-stripe.table-base tr:nth-child(2n) td {
    background: #f5f7fa;
}

.table-stripe.table-vertical td:nth-child(2n -1) {
    background: #f5f7fa;
}

.table-hover tr:hover td {
    background: #edf4fa !important;
}