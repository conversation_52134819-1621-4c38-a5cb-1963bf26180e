<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.aftersale.mapper.AfterSaleAuditInfoMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.aftersale.domain.entity.AfterSaleAuditInfoEntity">
    <!--@mbg.generated-->
    <!--@Table T_AFTER_SALE_AUDIT_INFO-->
    <id column="AFTER_SALE_AUDIT_INFO_ID" jdbcType="INTEGER" property="afterSaleAuditInfoId" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="AFTER_SALE_ID" jdbcType="INTEGER" property="afterSaleId" />
    <result column="AFTER_SALE_GOODS_ID" jdbcType="INTEGER" property="afterSaleGoodsId" />
    <result column="IS_RETURN" jdbcType="INTEGER" property="isReturn" />
    <result column="AUDIT_STATUS" jdbcType="INTEGER" property="auditStatus" />
    <result column="AUDIT_REMARK" jdbcType="VARCHAR" property="auditRemark" />
    <result column="USER_NAME" jdbcType="VARCHAR" property="userName" />
    <result column="USER_ID" jdbcType="VARCHAR" property="userId" />
    <result column="SKU_ID" jdbcType="INTEGER" property="skuId" />
    <result column="SKU_NO" jdbcType="VARCHAR" property="skuNo" />
    <result column="SKU_NAME" jdbcType="VARCHAR" property="skuName" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    AFTER_SALE_AUDIT_INFO_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, 
    AFTER_SALE_ID, AFTER_SALE_GOODS_ID, IS_RETURN, AUDIT_STATUS, AUDIT_REMARK, USER_NAME, 
    USER_ID, SKU_ID, SKU_NO, SKU_NAME, IS_DELETE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_AFTER_SALE_AUDIT_INFO
    where AFTER_SALE_AUDIT_INFO_ID = #{afterSaleAuditInfoId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_AFTER_SALE_AUDIT_INFO
    where AFTER_SALE_AUDIT_INFO_ID = #{afterSaleAuditInfoId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="AFTER_SALE_AUDIT_INFO_ID" keyProperty="afterSaleAuditInfoId" parameterType="com.vedeng.erp.aftersale.domain.entity.AfterSaleAuditInfoEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_AFTER_SALE_AUDIT_INFO (ADD_TIME, MOD_TIME, CREATOR, 
      UPDATER, CREATOR_NAME, UPDATER_NAME, 
      AFTER_SALE_ID, AFTER_SALE_GOODS_ID, IS_RETURN, 
      AUDIT_STATUS, AUDIT_REMARK, USER_NAME, 
      USER_ID, SKU_ID, SKU_NO, 
      SKU_NAME, IS_DELETE)
    values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR}, 
      #{afterSaleId,jdbcType=INTEGER}, #{afterSaleGoodsId,jdbcType=INTEGER}, #{isReturn,jdbcType=INTEGER}, 
      #{auditStatus,jdbcType=INTEGER}, #{auditRemark,jdbcType=VARCHAR}, #{userName,jdbcType=VARCHAR}, 
      #{userId,jdbcType=VARCHAR}, #{skuId,jdbcType=INTEGER}, #{skuNo,jdbcType=VARCHAR}, 
      #{skuName,jdbcType=VARCHAR}, #{isDelete,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="AFTER_SALE_AUDIT_INFO_ID" keyProperty="afterSaleAuditInfoId" parameterType="com.vedeng.erp.aftersale.domain.entity.AfterSaleAuditInfoEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_AFTER_SALE_AUDIT_INFO
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME,
      </if>
      <if test="afterSaleId != null">
        AFTER_SALE_ID,
      </if>
      <if test="afterSaleGoodsId != null">
        AFTER_SALE_GOODS_ID,
      </if>
      <if test="isReturn != null">
        IS_RETURN,
      </if>
      <if test="auditStatus != null">
        AUDIT_STATUS,
      </if>
      <if test="auditRemark != null and auditRemark != ''">
        AUDIT_REMARK,
      </if>
      <if test="userName != null and userName != ''">
        USER_NAME,
      </if>
      <if test="userId != null and userId != ''">
        USER_ID,
      </if>
      <if test="skuId != null">
        SKU_ID,
      </if>
      <if test="skuNo != null and skuNo != ''">
        SKU_NO,
      </if>
      <if test="skuName != null and skuName != ''">
        SKU_NAME,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="afterSaleId != null">
        #{afterSaleId,jdbcType=INTEGER},
      </if>
      <if test="afterSaleGoodsId != null">
        #{afterSaleGoodsId,jdbcType=INTEGER},
      </if>
      <if test="isReturn != null">
        #{isReturn,jdbcType=INTEGER},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="auditRemark != null and auditRemark != ''">
        #{auditRemark,jdbcType=VARCHAR},
      </if>
      <if test="userName != null and userName != ''">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="userId != null and userId != ''">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="skuId != null">
        #{skuId,jdbcType=INTEGER},
      </if>
      <if test="skuNo != null and skuNo != ''">
        #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null and skuName != ''">
        #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.aftersale.domain.entity.AfterSaleAuditInfoEntity">
    <!--@mbg.generated-->
    update T_AFTER_SALE_AUDIT_INFO
    <set>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="afterSaleId != null">
        AFTER_SALE_ID = #{afterSaleId,jdbcType=INTEGER},
      </if>
      <if test="afterSaleGoodsId != null">
        AFTER_SALE_GOODS_ID = #{afterSaleGoodsId,jdbcType=INTEGER},
      </if>
      <if test="isReturn != null">
        IS_RETURN = #{isReturn,jdbcType=INTEGER},
      </if>
      <if test="auditStatus != null">
        AUDIT_STATUS = #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="auditRemark != null and auditRemark != ''">
        AUDIT_REMARK = #{auditRemark,jdbcType=VARCHAR},
      </if>
      <if test="userName != null and userName != ''">
        USER_NAME = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="userId != null and userId != ''">
        USER_ID = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="skuId != null">
        SKU_ID = #{skuId,jdbcType=INTEGER},
      </if>
      <if test="skuNo != null and skuNo != ''">
        SKU_NO = #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null and skuName != ''">
        SKU_NAME = #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
    </set>
    where AFTER_SALE_AUDIT_INFO_ID = #{afterSaleAuditInfoId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.aftersale.domain.entity.AfterSaleAuditInfoEntity">
    <!--@mbg.generated-->
    update T_AFTER_SALE_AUDIT_INFO
    set ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      AFTER_SALE_ID = #{afterSaleId,jdbcType=INTEGER},
      AFTER_SALE_GOODS_ID = #{afterSaleGoodsId,jdbcType=INTEGER},
      IS_RETURN = #{isReturn,jdbcType=INTEGER},
      AUDIT_STATUS = #{auditStatus,jdbcType=INTEGER},
      AUDIT_REMARK = #{auditRemark,jdbcType=VARCHAR},
      USER_NAME = #{userName,jdbcType=VARCHAR},
      USER_ID = #{userId,jdbcType=VARCHAR},
      SKU_ID = #{skuId,jdbcType=INTEGER},
      SKU_NO = #{skuNo,jdbcType=VARCHAR},
      SKU_NAME = #{skuName,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=INTEGER}
    where AFTER_SALE_AUDIT_INFO_ID = #{afterSaleAuditInfoId,jdbcType=INTEGER}
  </update>

  <select id="findAuditListByAfterSaleorderId" parameterType="com.vedeng.erp.aftersale.domain.entity.AfterSaleAuditInfoEntity" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_AFTER_SALE_AUDIT_INFO
    where AFTER_SALE_ID = #{afterSaleId,jdbcType=INTEGER}
    and
    <if test="auditStatus != null">
      AUDIT_STATUS = #{auditStatus,jdbcType=INTEGER}
    </if>
  </select>

<!--auto generated by MybatisCodeHelper on 2023-01-11-->
  <select id="selectByAfterSaleId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_AFTER_SALE_AUDIT_INFO
    where AFTER_SALE_ID=#{afterSaleId,jdbcType=INTEGER}
    and IS_DELETE = 0
  </select>

  <select id="getCannotReturnSkuByAfterSalesId" parameterType="java.lang.Integer" resultType="string">
    select
    SKU_NO
    from T_AFTER_SALE_AUDIT_INFO
    where AFTER_SALE_ID = #{afterSaleId,jdbcType=INTEGER}
    and IS_RETURN=0
  </select>

  <delete id="clearAuditInfoByAfterSaleId" parameterType="java.lang.Integer">
    delete from T_AFTER_SALE_AUDIT_INFO
    where AFTER_SALE_ID = #{afterSaleId,jdbcType=INTEGER}
  </delete>
</mapper>