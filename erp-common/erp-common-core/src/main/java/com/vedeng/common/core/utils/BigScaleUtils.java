package com.vedeng.common.core.utils;


import com.vedeng.common.core.annotation.BigScale;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @ClassName BigScaleUtils.java
 * @Description TODO 大数字截取工具
 * @createTime 2022年08月26日 16:46:00
 */
@Slf4j
public class BigScaleUtils {

    private static final String GET = "get";

    private static final String SET = "set";

    public static <T>T coverBigDecimalScale(T t){
        try {
            Class<?> aClass = t.getClass();
            Field[] fields = aClass.getDeclaredFields();
            for (Field field : fields) {
                BigScale annotation = field.getAnnotation(BigScale.class);
                if(annotation == null){
                    continue;
                }
                if (!field.getType().equals(BigDecimal.class)){
                    continue;
                }
                String getMethodName = captureName(field.getName(), GET);
                Method declaredMethod = aClass.getDeclaredMethod(getMethodName);
                BigDecimal invoke = (BigDecimal)declaredMethod.invoke(t, null);
                if(invoke == null){
                    continue;
                }
                int scale = annotation.scale();
                RoundingMode rounding = annotation.rounding();
                BigDecimal bigDecimal = invoke.setScale(scale, rounding);
                String setMethodName = captureName(field.getName(), SET);
                aClass.getDeclaredMethod(setMethodName,BigDecimal.class).invoke(t,new Object[]{bigDecimal});
            }
        } catch (InstantiationException e) {
            log.error("【coverBigDecimalScale】处理异常",e);
        } catch (Exception e) {
            log.error("【coverBigDecimalScale】处理异常",e);
        }

        return t;
    }

    /**
     * 将字符串的首字母转大写
     * @param str 需要转换的字符串
     * @param method 修饰方案
     * @return
     */
    private static String captureName(String str,String method) throws Exception {
        // 进行字母的ascii编码前移，效率要高于截取字符串进行转换的操作
        char[] cs=str.toCharArray();
        cs[0]-=32;
        return method+String.valueOf(cs);
    }

}
