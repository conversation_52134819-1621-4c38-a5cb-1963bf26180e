package com.vedeng.erp.kingdee.common.base.service.impl;

import cn.hutool.core.annotation.AnnotationUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.domain.FileInfoDto;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.core.utils.FileInfoUtils;
import com.vedeng.common.mybatis.domain.BaseEntity;
import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.common.mybatis.jbatis.BaseDao;
import com.vedeng.common.mybatis.jbatis.annotation.Id;
import com.vedeng.erp.kingdee.common.base.BaseCommandMapStruct;
import com.vedeng.erp.kingdee.common.utils.KingDeeBaseUtil;
import com.vedeng.infrastructure.kingdee.annotation.BusinessID;
import com.vedeng.infrastructure.kingdee.annotation.KingDeeID;
import com.vedeng.infrastructure.kingdee.annotation.WriteBackField;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.common.exception.KingDeeException;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.RepoRet;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.RepoStatus;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.SuccessEntity;
import com.vedeng.infrastructure.kingdee.domain.command.OperateExtCommand;
import com.vedeng.infrastructure.kingdee.domain.command.SaveExtCommand;
import com.vedeng.infrastructure.kingdee.domain.command.UpdateExtCommand;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeMqBaseDto;
import com.vedeng.infrastructure.kingdee.service.KingDeeBaseService;
import com.vedeng.infrastructure.kingdee.service.impl.KingDeeMqBaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.ParameterizedType;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 金蝶基础能力实现
 * @date 2022/8/27 12:40
 */
@Service
@Slf4j
@SuppressWarnings("all")
public abstract class KingDeeBaseServiceImpl<
        T extends BaseEntity,
        D extends KingDeeMqBaseDto,
        C,
        Repository extends BaseDao<T, ?>,
        DtoConvertor extends BaseMapStruct<T, D>,
        CommandConvertor extends BaseCommandMapStruct<C, D>>
        extends KingDeeMqBaseServiceImpl<D> implements KingDeeBaseService<D> {


    @Autowired
    protected DtoConvertor dtoConvertor;
    @Autowired
    protected CommandConvertor commandConvertor;
    @Autowired
    protected KingDeeBaseApi kingDeeBaseApi;
    @Autowired
    protected Repository repository;


    protected Class<T> getTClass() {
        Class<T> tClass =
                (Class<T>) ((ParameterizedType) this.getClass().getGenericSuperclass()).getActualTypeArguments()[0];
        return tClass;
    }

    protected Class<D> getDClass() {
        Class<D> dc = (Class<D>) ((ParameterizedType) this.getClass().getGenericSuperclass()).getActualTypeArguments()[1];
        return dc;
    }

    protected Class<C> getCClass() {
        Class<C> cc = (Class<C>) ((ParameterizedType) this.getClass().getGenericSuperclass()).getActualTypeArguments()[2];
        return cc;
    }


    @Override
    public void query(D d) {
        log.info("查询金蝶数据开始{}", JSON.toJSONString(d));
        T t = getlocalData(d);
        if (ObjectUtil.isNotEmpty(t)) {

            // 获取对本地的主键字段
            Map<String, Object> idAnnotation = KingDeeBaseUtil.getAnnotationParameter(t, Id.class, null);
            idAnnotation.forEach((k, v) -> BeanUtil.setProperty(d, k, v));

            WriteBackField annotation = AnnotationUtil.getAnnotation(d.getClass(), WriteBackField.class);
            if (annotation == null) {
                throw new KingDeeException(StrUtil.format("金蝶业务主回写字段注解不存在,请添加回写注解及字段,参数{}", JSON.toJSONString(d)));
            }
            List<String> needReturnFields = CollUtil.newArrayList(annotation.needBackField());
            List<String> queryFields = KingDeeBaseUtil.assembleQueryFields(needReturnFields, null);

            List<Map<String, Object>> exist = kingDeeBaseApi.isExist(d, queryFields);
            if (CollUtil.isNotEmpty(exist)) {
                AtomicReference<String> kingDeeIdField = new AtomicReference<>("");
                // 获取对应金蝶的主键字段
                Map<String, Object> annotationParameter = KingDeeBaseUtil.getAnnotationParameter(d, KingDeeID.class, null);
                annotationParameter.forEach((k, v) -> kingDeeIdField.set(k));
                Map<String, Object> map = CollUtil.getFirst(exist);
                // 获取返回值
                Object o = map.get(kingDeeIdField.get().toUpperCase());
                if (ObjectUtil.isEmpty(o)) {
                    throw new KingDeeException(StrUtil.format("金蝶返回的主键字段值为空，请检查字段,参数{},字段{}", JSON.toJSONString(d), kingDeeIdField));
                }
                BeanUtil.setProperty(d, kingDeeIdField.get(), o);
            }
        }
        log.info("查询金蝶数据结束{}", JSON.toJSONString(d));
    }

    /**
     * 提交
     *
     * @param d
     * @param isAutoSubmitAndAudit 是否自动提交审核
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, propagation = Propagation.REQUIRES_NEW)
    public void save(D d) {

        log.info("金蝶通用baseSave方法开始,{}", JSON.toJSONString(d));

        savePre(d);

        Assert.notNull(d, "金蝶异常：金蝶dto不能为空:{}", JSON.toJSONString(d));

        C command = commandConvertor.toCommand(d);
        Assert.notNull(command, "金蝶异常：金蝶command不能为空:{}", JSON.toJSONString(d));

        ArrayList<String> needReturnFields = kingDeeSavePre(d, command);
        if (CollUtil.isEmpty(needReturnFields)) {
            WriteBackField annotation = AnnotationUtil.getAnnotation(d.getClass(), WriteBackField.class);
            if (annotation == null) {
                throw new KingDeeException(StrUtil.format("金蝶业务主回写字段注解不存在,请添加回写注解及字段,参数{}", JSON.toJSONString(d)));
            }
            needReturnFields = CollUtil.newArrayList(annotation.needBackField());
        }

        boolean localIsExist = localIsExist(d);

        boolean kingDeeIsExist = kingDeeIsExist(d);

        if (localIsExist && kingDeeIsExist) {
            log.info("数据已存在,参数{}", JSON.toJSONString(d));
            return;
        }

        //本地不存在，金蝶存在时根据金蝶返回数据进行新增本地数据
        if (!localIsExist && kingDeeIsExist) {
            this.byBusinessIdGetKingDeeId(d);
        }

        if (!localIsExist && !kingDeeIsExist) {

            RepoStatus save = kingDeeBaseApi.save(new SaveExtCommand<>(command, d.getFormId(), needReturnFields, getIsAutoSubmitAndAudit(d)));
            ArrayList<SuccessEntity> successEntities = save.getSuccessEntitys();

            Assert.isTrue(CollUtil.isNotEmpty(successEntities), "金蝶异常：调用金蝶数据保存失败,errors:{}", JSON.toJSONString(save.getErrors()));

            SuccessEntity successEntity = CollUtil.getFirst(successEntities);

            kingDeeSavePost(d, command, successEntity);

            List<Map<String, Object>> mapList = KingDeeBaseUtil.getMaps(successEntity.getNeedReturnData());
            KingDeeBaseUtil.writeBack(d, needReturnFields, mapList);
        }

        if (localIsExist && !kingDeeIsExist) {
            throw new KingDeeException(StrUtil.format("业务逻辑错误！本地数据已存在，金蝶数据不存在,参数{}", JSON.toJSONString(d)));
        }

        T entity = dtoConvertor.toEntity(d);

        repository.create(entity);

        savePost(d, command, entity);

        log.info("金蝶通用baseSave方法结束,{},{},{},{}", JSON.toJSONString(d), JSON.toJSONString(command), JSON.toJSONString(entity));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, propagation = Propagation.REQUIRES_NEW)
    public void update(D d) {
        doUpdate(d, new UpdateExtCommand(d.getFormId()));
    }

    protected void doUpdate(D d, UpdateExtCommand updateExtCommand) {
        log.info("金蝶通用update方法开始,{}", JSON.toJSONString(d));

        updatePre(d);

        if (!isExist(d)) {
            throw new KingDeeException(StrUtil.format("数据不存在,参数{}", JSON.toJSONString(d)));
        }

        Assert.notNull(d, "金蝶异常：金蝶dto不能为空:{}", JSON.toJSONString(d));

        C command = commandConvertor.toCommand(d);
        Assert.notNull(command, "金蝶异常：金蝶command不能为空:{}", JSON.toJSONString(d));


        ArrayList<String> needReturnFields = kingDeeUpdatePre(d, command);
        if (CollUtil.isEmpty(needReturnFields)) {
            WriteBackField annotation = AnnotationUtil.getAnnotation(d.getClass(), WriteBackField.class);
            needReturnFields = CollUtil.newArrayList(annotation.needBackField());
        }
        updateExtCommand.buildBaseUpdateExtCommand(updateExtCommand, command, needReturnFields);
        RepoStatus update = kingDeeBaseApi.update(updateExtCommand);
        ArrayList<SuccessEntity> successEntities = update.getSuccessEntitys();
        SuccessEntity successEntity = CollUtil.getFirst(successEntities);

        Assert.isTrue(CollUtil.isNotEmpty(successEntities), "金蝶异常：调用金蝶数据更新失败,errors:{}", JSON.toJSONString(update.getErrors()));

        kingDeeUpdatePost(d, command, successEntity);

        List<Map<String, Object>> maps = KingDeeBaseUtil.getMaps(successEntity.getNeedReturnData());

        KingDeeBaseUtil.writeBack(d, needReturnFields, maps);

        // update 传递只需要修改的字段
        T t = getlocalData(d);

        T entity = dtoConvertor.update(d, t);

        repository.update(entity);

        updatePost(d, command, entity);

        log.info("金蝶通用update方法结束,{},{},{},{}", JSON.toJSONString(d), JSON.toJSONString(command), JSON.toJSONString(entity));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, propagation = Propagation.REQUIRES_NEW)
    public void updateAndUnAudit(D d) {
        if (!unAudit(d)) return;
        this.update(d);
    }

    protected boolean unAudit(D d) {
        AtomicReference<Object> kingDeeValue = new AtomicReference<>("");
        AtomicReference<String> kingDeeIdField = new AtomicReference<>("");
        Map<String, Object> annotationParameter = KingDeeBaseUtil.getAnnotationParameter(d, KingDeeID.class, "value");
        annotationParameter.forEach((k, v) -> {
            kingDeeIdField.set(k);
            kingDeeValue.set(v);
        });
        if (StrUtil.isBlank((String) kingDeeValue.get())) {
            throw new KingDeeException(StrUtil.format("更新金蝶主键为空,参数{}", JSON.toJSONString(d)));
        }
        OperateExtCommand operateExtCommand = new OperateExtCommand(d.getFormId(), (String) kingDeeValue.get(), KingDeeConstant.ORG_ID.toString(), null);
        ArrayList<SuccessEntity> successUnAudit = kingDeeBaseApi.unAudit(operateExtCommand);
        if (CollUtil.isEmpty(successUnAudit)) {
            log.error("更新金蝶信息，反审核失败，参数：{} ,金蝶主键id：{}", d, kingDeeValue.get());
            return false;
        }
        log.info("反审核成功，参数：{} ,金蝶主键id：{}", d, kingDeeValue.get());
        return true;
    }

    /**
     * 提交并审核
     *
     * @param d
     */
    @Override
    public void submitAndAudit(D d) {
        AtomicReference<Object> kingDeeValue = new AtomicReference<>("");
        AtomicReference<String> kingDeeIdField = new AtomicReference<>("");
        Map<String, Object> annotationParameter = KingDeeBaseUtil.getAnnotationParameter(d, KingDeeID.class, "value");
        annotationParameter.forEach((k, v) -> {
            kingDeeIdField.set(k);
            kingDeeValue.set(v);
        });
        if (StrUtil.isBlank((String) kingDeeValue.get())) {
            throw new KingDeeException(StrUtil.format("提交并审核金蝶主键为空,参数{}", JSON.toJSONString(d)));
        }
        OperateExtCommand operateExtCommand = new OperateExtCommand(d.getFormId(), (String) kingDeeValue.get(), KingDeeConstant.ORG_ID.toString(), null);
        ArrayList<SuccessEntity> successSubmit = kingDeeBaseApi.submit(operateExtCommand);
        if (CollUtil.isEmpty(successSubmit)) {
            log.error("提交并审核金蝶信息，提交失败，参数：{} ,金蝶主键id：{}", d, kingDeeValue.get());
            return;
        }
        ArrayList<SuccessEntity> successAudit = kingDeeBaseApi.audit(operateExtCommand);
        if (CollUtil.isEmpty(successAudit)) {
            log.error("提交并审核金蝶信息，审核失败，参数：{} ,金蝶主键id：{}", d, kingDeeValue.get());
            return;
        }
        log.info("提交并审核成功，参数：{} ,金蝶主键id：{}", d, kingDeeValue.get());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class, propagation = Propagation.REQUIRES_NEW)
    public void updateAndUnAuditAndSubmit(D d) {
        // 反审核并更新
        updateAndUnAudit(d);
        // 提交并审核
        submitAndAudit(d);

    }

    @Override
    public void upload(D d) {
        log.info("附件上传金蝶：{}", JSON.toJSONString(d));
        C command = commandConvertor.toCommand(d);
        String url = ReflectUtil.getFieldValue(d, "url").toString();
        List<String> base64ListFromUrl = null;
        try {
            base64ListFromUrl = FileInfoUtils.getBase64ListFromUrl(url);
        } catch (Exception e) {
            log.error("文件分片失败", e);
            throw new ServiceException("文件分片失败");
        }
        if (CollUtil.isEmpty(base64ListFromUrl)) {
            log.error("文件分片返回结果为空");
            throw new ServiceException("文件分片返回结果为空");
        }
        String fileId = "";
        for (int i = 0; i < base64ListFromUrl.size(); i++) {

            ReflectUtil.setFieldValue(command, "sendByte", base64ListFromUrl.get(i));
            ReflectUtil.setFieldValue(command, "fileId", fileId);
            ReflectUtil.setFieldValue(command, "isLast", false);
            if (i == base64ListFromUrl.size() - 1) {
                ReflectUtil.setFieldValue(command, "isLast", true);
            }
            RepoRet fileResult = kingDeeBaseApi.attachmentUpload(command);
            if (fileResult == null) {
                log.error("附件上传金蝶失败：{}", JSON.toJSONString(d));
                throw new ServiceException("附件上传金蝶失败");
            } else {
                fileId = fileResult.getResult().getFileId();
                if (fileResult.getResult().getResponseStatus().isIsSuccess()) {
                    //收到成功保存附件信息
                    log.info("附件上传分片,item:{},total:{},金蝶成功：{}", i + 1, base64ListFromUrl.size(), JSON.toJSONString(d));
                    if (i == base64ListFromUrl.size() - 1) {
                        T entity = dtoConvertor.toEntity(d);
                        repository.create(entity);
                    }
                } else {
                    log.error("附件上传金蝶失败,金蝶反馈{},", JSON.toJSONString(fileResult));
                    throw new ServiceException("附件上传金蝶失败");
                }
            }

        }

    }

    @Override
    public void delete(D d) {

    }

    @Override
    public Boolean isExist(D d) {
        return localIsExist(d) && kingDeeBaseApi.isExist(d);
    }


    @Override
    public Boolean localIsExist(D d) {
        return ObjectUtil.isNotEmpty(getlocalData(d));
    }

    @Override
    public Boolean kingDeeIsExist(D d) {
        return kingDeeBaseApi.isExist(d);
    }

    public void byBusinessIdGetKingDeeId(D d) {
        Map<String, Object> annotationParameter = KingDeeBaseUtil.getAnnotationParameter(d, KingDeeID.class, null);
        AtomicReference<String> feild = new AtomicReference<>("");
        annotationParameter.forEach((k, v) -> feild.set(k));
        List<Map<String, Object>> exist = kingDeeBaseApi.isExist(d, CollUtil.newArrayList(feild.get()));
        log.info("根据业务id获取金蝶id,返回值{}", JSON.toJSONString(exist));
        BeanUtil.setProperty(d, feild.get(), CollUtil.getFirst(exist).get(feild.get().toUpperCase()).toString());
    }

    /**
     * 获取本地数据
     */
    private T getlocalData(D d) {
        AtomicReference<Object> kingDeeValue = new AtomicReference<>("");
        AtomicReference<String> kingDeeIdField = new AtomicReference<>("");
        Map<String, Object> annotationParameter = KingDeeBaseUtil.getAnnotationParameter(d, BusinessID.class, null);
        annotationParameter.forEach((k, v) -> {
            kingDeeIdField.set(k);
            kingDeeValue.set(v);
        });

        D newD = null;
        try {
            newD = getDClass().newInstance();
        } catch (Exception e) {
            log.error("金蝶异常实例化错误", e);
            throw new KingDeeException("查看本地数据是否存在：金蝶异常实例化错误", e);
        }
        BeanUtil.setProperty(newD, String.valueOf(kingDeeIdField), kingDeeValue);
        T entity = dtoConvertor.toEntity(newD);
        List<T> list = repository.queryByObject(entity);
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        log.info("获取本地数据{}", CollUtil.getFirst(list));
        return CollUtil.getFirst(list);
    }

    public D getD(Object... objects) {
        for (Object object : Arrays.asList(objects)) {
            Class<D> clazz = getDClass();
            if (clazz.isInstance(object)) {
                return Convert.convert(clazz, object);
            }
        }
        return null;
    }


    public C getC(Object... objects) {
        for (Object object : Arrays.asList(objects)) {
            Class<C> clazz = getCClass();
            if (getCClass().isInstance(object)) {
                return Convert.convert(clazz, object);
            }
        }
        return null;
    }


    public T getT(Object... objects) {
        for (Object object : Arrays.asList(objects)) {
            Class<T> clazz = getTClass();
            if (getTClass().isInstance(object)) {
                return Convert.convert(clazz, object);
            }
        }
        return null;
    }


}
