<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="容错配置" scope="application"/>
<%@ include file="../../../common/common.jsp" %>
<link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css" media="all">
<style type="text/css">
    .layui-tab-brief > .layui-tab-title .layui-this {
        color: #3384ef;
    }

    .layui-tab-brief > .layui-tab-title .layui-this:after {
        border-bottom: 2px solid #3384ef;
    }

    .layui-layer-page .layui-layer-content {
        overflow: visible !important;
    }
</style>

    <div class="layui-tab layui-tab-brief" lay-filter="test" style="height: 100%;margin: 0;padding: 10px 0;">
        <ul class="layui-tab-title">
            <li class="layui-this">
                <div class="customername pos_rel">
                    <span class="mr4">
                        字符剔除
                    </span>
                    <i class="iconbluequestion ml4 contorlIcon"></i>
                    <div class="pos_abs customernameshow mouthControlPos" style="width: 300px;line-height: normal;color: black">
                        设置剔除的字符后，异常票匹配时将优先剔除<br>
                        所设置的字符。剔除后如果匹配正确，将直接<br>
                        流转到待录票！
                    </div>
                </div>
            </li>
            <li>
                <div class="customername pos_rel">
                    <span class="mr4">
                        容错匹配
                    </span>
                    <i class="iconbluequestion ml4 contorlIcon"></i>
                    <div class="pos_abs customernameshow mouthControlPos" style="width: 300px;line-height: normal;color: black">
                        设置容错内容后，异常票匹配时将对应字段与<br>
                        所设置的内容进行匹配。如果匹配成功，将直<br>
                        接流转到待录票
                    </div>
                </div>
            </li>
        </ul>
<%--        <div class="layui-layer-content" style="width: 100%;height: 100%">--%>
                <form class="layui-form" action="" style="width: 100%;height: 100%">
                    <div class="layui-tab-content" style="">
                        <div class="layui-tab-item layui-show" style="height: 100%;width: 100%" id="configForm">
                            <div class="" style="width: 400px;margin:0 auto;">
        <%--                        <c:choose>--%>
        <%--                            <c:when test="${fn:length(characterList) == 0}">--%>
        <%--                                <div class="layui-form-item character"  style="text-align: center;">--%>
        <%--                                    <div class="layui-input-inline" style="width: 300px;">--%>
        <%--                                        <input class="layui-input"  value=""  >--%>
        <%--                                    </div>--%>
        <%--                                    <div class="layui-form-mid layui-word-aux delete" style="display: none">--%>
        <%--                                        <a onclick="deleteCharacter(this,'character')"--%>
        <%--                                           style="width: 20px;height: 20px;margin-left: 10px;display: inline;color: #3384ef">--%>
        <%--                                            删除--%>
        <%--                                        </a>--%>
        <%--                                    </div>--%>
        <%--                                </div>--%>
        <%--                            </c:when>--%>
        <%--                            <c:when test="${fn:length(characterList) == 1}">--%>
        <%--                                <div class="layui-form-item character"  style="text-align: center;">--%>
        <%--                                    <div class="layui-input-inline" style="width: 300px;">--%>
        <%--                                        <input class="layui-input"  value="${characterList[0].content}"  >--%>
        <%--                                    </div>--%>
        <%--                                    <div class="layui-form-mid layui-word-aux delete" style="display: none">--%>
        <%--                                        <a onclick="deleteCharacter(this,'character')"--%>
        <%--                                           style="width: 20px;height: 20px;margin-left: 10px;display: inline;color: #3384ef">--%>
        <%--                                            删除--%>
        <%--                                        </a>--%>
        <%--                                    </div>--%>
        <%--                                </div>--%>
        <%--                            </c:when>--%>
        <%--                            <c:otherwise>--%>
                                        <c:forEach items="${characterList}" var="character" varStatus="index">
                                            <div class="layui-form-item character"  style="text-align: center;">
                                                <div class="layui-input-inline" style="width: 300px;">
                                                    <input class="layui-input content"  value="${character.content}"  >
                                                    <input class="layui-input uniqueId"  value="${character.uniqueId}" style="display: none" >
                                                </div>
                                                <div class="layui-form-mid layui-word-aux delete" style="">
                                                    <a onclick="deleteCharacter(this,'character')"
                                                       style="width: 20px;height: 20px;margin-left: 10px;display: inline;color: #3384ef">
                                                        删除
                                                    </a>
                                                </div>
                                            </div>
                                        </c:forEach>

                                <div class="" style="margin-left: 130px">
                                    <a onclick="addCharacter(this,'character')"
                                       style="width: 20px;height: 20px;margin-left: 10px;display: inline;color: #3384ef">
                                        添加
                                    </a>
                                </div>
                            </div>

                        </div>
                        <div class="layui-tab-item" style="height: 100%;width: 100%">
                            <div class="layui-form-item">
                                <label class="layui-form-label">字段： </label>
                                <div class="layui-input-block">
                                    <select name="source" lay-filter="source">
                                        <option value="0">请选择</option>
                                        <option value="1">开户行</option>
                                        <option value="2">账户</option>
                                        <option value="3">注册地址</option>
                                        <option value="4">注册电话</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-form-item"  id="content" style="display: none">
                                <label class="layui-form-label" style="width: 100px">标准内容： </label>
                                <div class="layui-input-block" >
                                    <input id="standardContent" disabled="disabled" class="layui-input" contenteditable="true" style="background-color:transparent;border:0;" value="">
                                </div>
                            </div>

                            <div class="content" style="width: 400px;margin:0px auto;">
                                <div class="tab-item" id="bankContent" style="display: none;">
                                            <c:forEach items="${bankList}" var="bank" varStatus="index">
                                                <div class="layui-form-item bank"  style="text-align: center;">
                                                    <div class="layui-input-inline" style="width: 300px;">
                                                        <input class="layui-input content"  value="${bank.content}"  >
                                                        <input class="layui-input uniqueId"  value="${bank.uniqueId}"  style="display: none">
                                                    </div>
                                                    <div class="layui-form-mid layui-word-aux delete" style="">
                                                        <a onclick="deleteCharacter(this,'bank')"
                                                           style="width: 20px;height: 20px;margin-left: 10px;display: inline;color: #3384ef">
                                                            删除
                                                        </a>
                                                    </div>
                                                </div>
                                            </c:forEach>

                                    <div class="" style="margin-left: 130px">
                                        <a onclick="addCharacter(this,'bank')"
                                           style="width: 20px;height: 20px;margin-left: 10px;display: inline;color: #3384ef">
                                            添加
                                        </a>
                                    </div>
                                </div>
                                <div class="tab-item" id="accountContent" style="display: none">

                                            <c:forEach items="${accountList}" var="account" varStatus="index">
                                                <div class="layui-form-item account"  style="text-align: center;">
                                                    <div class="layui-input-inline" style="width: 300px;">
                                                        <input class="layui-input content"  value="${account.content}"  >
                                                        <input class="layui-input uniqueId"  value="${account.uniqueId}"  style="display: none">
                                                    </div>
                                                    <div class="layui-form-mid layui-word-aux delete" style="">
                                                        <a onclick="deleteCharacter(this,'account')"
                                                           style="width: 20px;height: 20px;margin-left: 10px;display: inline;color: #3384ef">
                                                            删除
                                                        </a>
                                                    </div>
                                                </div>
                                            </c:forEach>

                                    <div class="" style="margin-left: 130px">
                                        <a onclick="addCharacter(this,'account')"
                                           style="width: 20px;height: 20px;margin-left: 10px;display: inline;color: #3384ef">
                                            添加
                                        </a>
                                    </div>
                                </div>
                                <div class="tab-item" id="addressContent" style="display: none">
                                            <c:forEach items="${addressList}" var="address" varStatus="index">
                                                <div class="layui-form-item address"  style="text-align: center;">
                                                    <div class="layui-input-inline" style="width: 300px;">
                                                        <input class="layui-input content"  value="${address.content}"  >
                                                        <input class="layui-input uniqueId"  value="${address.uniqueId}"  style="display: none">
                                                    </div>
                                                    <div class="layui-form-mid layui-word-aux delete" style="">
                                                        <a onclick="deleteCharacter(this,'address')"
                                                           style="width: 20px;height: 20px;margin-left: 10px;display: inline;color: #3384ef">
                                                            删除
                                                        </a>
                                                    </div>
                                                </div>
                                            </c:forEach>
                                    <div class="" style="margin-left: 130px">
                                        <a onclick="addCharacter(this,'address')"
                                           style="width: 20px;height: 20px;margin-left: 10px;display: inline;color: #3384ef">
                                            添加
                                        </a>
                                    </div>
                                </div>
                                <div class="tab-item" id="phoneContent" style="display: none">
                                            <c:forEach items="${phoneList}" var="phone" varStatus="index">
                                                <div class="layui-form-item phone"  style="text-align: center;">
                                                    <div class="layui-input-inline" style="width: 300px;">
                                                        <input class="layui-input content"  value="${phone.content}"  >
                                                        <input class="layui-input uniqueId"  value="${phone.uniqueId}" style="display: none" >
                                                    </div>
                                                    <div class="layui-form-mid layui-word-aux delete" style="">
                                                        <a onclick="deleteCharacter(this,'phone')"
                                                           style="width: 20px;height: 20px;margin-left: 10px;display: inline;color: #3384ef">
                                                            删除
                                                        </a>
                                                    </div>
                                                </div>
                                            </c:forEach>
                                    <div class="" style="margin-left: 130px">
                                        <a onclick="addCharacter(this,'phone')"
                                           style="width: 20px;height: 20px;margin-left: 10px;display: inline;color: #3384ef">
                                            添加
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>

                </form>


    </div>

<script src="/webjars/ezadmin/plugins/layui/layui.js" charset="utf-8"></script>
<script>
    layui.use(['element','form'], function () {
        var element = layui.element, //Tab的切换功能，切换事件监听等，需要依赖element模块
            form = layui.form;

        //触发事件
        var active = {
            tabChange: function () {
                //切换到指定Tab项
                element.tabChange('demo', '22'); //切换到：用户管理
            }
        };
        //
        // element.on('tab(test)', function (data) {
        //     $(".layui-tab-content").find("iframe").attr("src", './hx_invoice.do?id=' + data.index);
        // });
        element.on('tab(test)', function(data){
            $(".layui-tab-item").each(function (item) {
                $(this).removeClass('layui-show');
                $(this).css("display","none");
                if(item === data.index){
                    $(this).addClass('layui-show');
                    $(this).css("display","");
                }
            });
        })

        form.on('select(source)', function(data){
            if(data.value == 1){
                $("#content").css("display","");
                $("#bankContent").css("display","");
                $("#accountContent").css("display","none");
                $("#addressContent").css("display","none");
                $("#phoneContent").css("display","none");
                $("#standardContent").val("建设银行南京市中山南路支行")
            }else if (data.value == 2){
                $("#content").css("display","");
                $("#accountContent").css("display","");
                $("#bankContent").css("display","none");
                $("#addressContent").css("display","none");
                $("#phoneContent").css("display","none");
                $("#standardContent").val("32001881236052503686")
            }else if (data.value == 3){
                $("#content").css("display","");
                $("#addressContent").css("display","");
                $("#bankContent").css("display","none");
                $("#accountContent").css("display","none");
                $("#phoneContent").css("display","none");
                $("#standardContent").val("南京市秦淮区永顺路2号2幢三楼301室")
            }else if (data.value == 4){
                $("#content").css("display","");
                $("#phoneContent").css("display","");
                $("#bankContent").css("display","none");
                $("#addressContent").css("display","none");
                $("#accountContent").css("display","none");
                $("#standardContent").val("025-********")
            }else if (data.value == 0){
                $("#content").css("display","none");
                $("#bankContent").css("display","none");
                $("#addressContent").css("display","none");
                $("#phoneContent").css("display","none");
                $("#accountContent").css("display","none");
                $("#standardContent").val("")
            }
        });

        //监听提交
        form.on('submit(layuiadmin-app-form-submit)', function(data){
            debugger
            var field = data.field; //获取提交的字段
            var index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引

            //提交 Ajax 成功后，关闭当前弹层并重载表格
            //$.ajax({});
            parent.layui.table.reload('LAY-app-content-list'); //重载表格
            parent.layer.close(index); //再执行关闭
        });




    });

    // $(function () {
    //     resetConfigNames("character");
    //     resetConfigNames("bank");
    //     resetConfigNames("account");
    //     resetConfigNames("address");
    //     resetConfigNames("phone");
    // });

    function addCharacter(e,content){
        $('.'+ content + " .delete").css("display","");
        var a =content
        var expectHtml = ' <div class="layui-form-item '+ content + '"'+ 'style="text-align: center;">\n' +
            '                            <div class="layui-input-inline" style="width: 300px">\n' +
            '                                <input class="layui-input content"  value="" >\n' +
            '                                <input class="layui-input uniqueId"  value="" style="display: none">\n' +
            '                            </div>\n' +
            '                            <div class="layui-form-mid layui-word-aux delete">\n' +
            '                                <a onclick="deleteCharacter(this,' +'\''+ content +'\''+ ')"\n' +
            '                                   style="width: 20px;height: 20px;margin-left: 10px;display: inline;color: #3384ef">\n' +
            '                                    删除\n' +
            '                                </a>\n' +
            '                            </div>\n' +
            '                        </div>'
        $(e).parent().before(expectHtml);
        // resetConfigNames(content)
    }

    function deleteCharacter(e,content){
        $(e).parent().parent().remove();
        // resetConfigNames(content);
    }


    var resetConfigNames = function (content) {

        if($('.'+ content).length > 1){
            $('.'+ content + ' .delete').show();
        }else{
            $('.'+ content + ' .add').show();
            $('.'+ content + ' .content').val("");
            $('.'+ content + ' .uniqueId').val("");
            $('.'+ content + ' .delete').hide();
        }
    };

    function check(configList,obj){
        debugger
        for(let i=0;i<configList.length;i++){
            if (configList[i].configType == obj.configType && configList[i].content == obj.content){
                return true
            }
        }
        return false;
    }
    function isRepeat(){
        var configList = getConfigList();
        // let  hash = {};
        // for(let i in configList) {
        //     var obj = new Object();
        //     obj.content = configList[i].content
        //     obj.configType = configList[i].configType
        //     if(hash[obj]) {
        //         return true;
        //     }
        //     hash[obj] = true;
        // }
        // return false;

        var find = false;
        var obj = null;

        for (var i = 0; i < configList.length; i++) {
            for (var j = i + 1; j < configList.length; j++) {
                if (configList[i].content == configList[j].content && configList[i].configType==configList[j].configType ) {
                    // obj.content = configList[i].content
                    // obj.configType = configList[i].configType
                    return configList[i].configType
                }
            }

        }
        return obj;

    }
    var getConfigList = function () {
        var configList = [];
        var config = [];
        var flag = true

        //字符
        $(".character").each(function () {

            if ($(this).find(".content").val() !='' && $(this).find(".content").val() != undefined){
                var obj = new Object();
                obj.content = $(this).find(".content").val()
                obj.uniqueId = $(this).find(".uniqueId").val()
                obj.configType = 1
                configList.push(obj);
            }

        });

        $(".bank").each(function () {
            if ($(this).find(".content").val() !='' && $(this).find(".content").val() != undefined) {
                var obj = new Object();
                obj.content = $(this).find(".content").val()
                obj.uniqueId = $(this).find(".uniqueId").val()
                obj.configType = 2
                configList.push(obj);
            }
        });

        $(".account").each(function () {
            if ($(this).find(".content").val() !='' && $(this).find(".content").val() != undefined) {
                var obj = new Object();
                obj.content = $(this).find(".content").val()
                obj.uniqueId = $(this).find(".uniqueId").val()
                obj.configType = 3
                configList.push(obj);
            }
        });

        $(".address").each(function () {
            if ($(this).find(".content").val() !='' && $(this).find(".content").val() != undefined) {
                var obj = new Object();
                obj.content = $(this).find(".content").val()
                obj.uniqueId = $(this).find(".uniqueId").val()
                obj.configType = 4
                configList.push(obj);
            }
        });

        $(".phone").each(function () {
            if ($(this).find(".content").val() !='' && $(this).find(".content").val() != undefined) {
                var obj = new Object();
                obj.content = $(this).find(".content").val()
                obj.uniqueId = $(this).find(".uniqueId").val()
                obj.configType = 5
                configList.push(obj);
            }
        });
        return configList;
    }





    /**
     * 查看客户详情信息
     * @param traderId
     */
    function viewTraderInfo(traderId) {
        $('#viewTraderSpan').attr('tabTitle', '{"num":"viewtrader", "link":"/trader/supplier/baseinfo.do?traderId=' + traderId + '","title":"供应商信息"}');
        $('#viewTraderSpan').click();
    }

    /**
     * 导出费用票信息
     */
    function exportCostListInfo() {
        //$('#exportCostListSpan').attr('tabTitle', '{"num":"exportCostList", "link":"http://ezadmin.ivedeng.com/ezlist/list/list.html?pageId=180","title":"导出费用票信息"}');
        //$('#exportCostListSpan').click();
    }

    /**
     * 查看采购订单信息
     */
    function viewBuyOrderInfo(buyOrderId) {
        $('#viewBuyOrderInfoSpan').attr('tabTitle', '{"num":"viewbuyorder<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>", "link":"/order/buyorder/viewBuyorder.do?buyorderId=' + buyOrderId + '","title":"订单信息"}');
        $('#viewBuyOrderInfoSpan').click();
    }
</script>
<%@ include file="../../../common/footer.jsp" %>
