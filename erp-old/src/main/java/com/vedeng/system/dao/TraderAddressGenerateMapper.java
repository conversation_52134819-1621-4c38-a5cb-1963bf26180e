package com.vedeng.system.dao;

import com.vedeng.system.model.TraderAddressGenerate;
import com.vedeng.system.model.TraderAddressGenerateExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TraderAddressGenerateMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_TRADER_ADDRESS
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    long countByExample(TraderAddressGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_TRADER_ADDRESS
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    int deleteByExample(TraderAddressGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_TRADER_ADDRESS
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    int deleteByPrimaryKey(Integer traderAddressId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_TRADER_ADDRESS
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    int insert(TraderAddressGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_TRADER_ADDRESS
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    int insertSelective(TraderAddressGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_TRADER_ADDRESS
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    List<TraderAddressGenerate> selectByExample(TraderAddressGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_TRADER_ADDRESS
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    TraderAddressGenerate selectByPrimaryKey(Integer traderAddressId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_TRADER_ADDRESS
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    int updateByExampleSelective(@Param("record") TraderAddressGenerate record, @Param("example") TraderAddressGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_TRADER_ADDRESS
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    int updateByExample(@Param("record") TraderAddressGenerate record, @Param("example") TraderAddressGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_TRADER_ADDRESS
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    int updateByPrimaryKeySelective(TraderAddressGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_TRADER_ADDRESS
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    int updateByPrimaryKey(TraderAddressGenerate record);
}