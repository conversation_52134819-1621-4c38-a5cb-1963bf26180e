
//新增ajax补充录音字段的coidURI
function checkcoidURI(recordId){

	$.ajax({
		type: "POST",
		url: page_url + "/order/aftersalesUpgrade/getRecordCoidURI.do",
		data:{"recordId":recordId},
		dataType:'json',
		success: function(data){
			if(data.code==0){
				playrecord(data.data);
			}else{
				layer.alert(data.message);
			}
		},
		error:function(data){
		}
	});
}
function lendout() {
	layer.alert("存在未完成的外借单，无法 操作");
}
function colse(){//关闭
	checkLogin();
	index = layer.confirm("您是否确认该订单关闭？确认后无法操作、无法更改状态！", {
		  btn: ['确定','取消'] //按钮
		}, function(){
			$.ajax({
				url:page_url+'/order/aftersalesUpgrade/saveCloseAfterSales.do',
				data:$('#myform').serialize(),
				type:"POST",
				dataType : "json",
				async: false,
				success:function(data)
				{
					if(data.code==0){
						layer.alert(data.message);
						self.location.reload();
					}else{
						layer.alert(data.message);
					}

				},
				error:function(data){
					if(data.status ==1001){
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
					}
				}
			});
			return false;
	layer.close(index);
		}, function(){
		});
}
//直发确认收货
function updateArrival(afterSalesGoodsId,arrivalNum){
	checkLogin();
	index = layer.confirm("请确认当前产品是否已全部收货？", {
		  btn: ['确定','取消'] //按钮
		}, function(){
			$.ajax({
				url:page_url+'/order/aftersalesUpgrade/updateAfterSalesGoodsArrival.do',
				data:{'afterSalesGoodsId':afterSalesGoodsId,'arrivalNum':arrivalNum},
				type:"POST",
				dataType : "json",
				async: false,
				success:function(data)
				{
					if(data.code==0){
						self.location.reload();
					}else{
						layer.alert(data.message);
					}

				},
				error:function(data){
					if(data.status ==1001){
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
					}
				}
			});
			return false;
	layer.close(index);
		}, function(){
		});

}

function editAfterSales(num){//编辑
	checkLogin();
	if(num == 1){
		$("#myform").attr("action", page_url + "/order/aftersalesUpgrade/editAfterSalesPage.do");
	}else if(num==2){
		$("#myform").attr("action", page_url + "/order/aftersalesUpgrade/editAfterSalesPage.do");
	}else{
		$("#myform").attr("action", page_url + "/order/aftersalesUpgrade/editAfterSalesPage.do?traderType=3");
	}
	$("#myform").submit();
}

function applyAudit(flag){//申请审核
	checkLogin();
    if (flag == 1){
        alert("请先完善退货信息！");
        return;
    }
	index = layer.confirm("您是否确认该操作？", {
		  btn: ['确定','取消'] //按钮
		}, function(){

			$.ajax({
				url:page_url+'/order/aftersalesUpgrade/editApplyAudit.do',
				data:$('#myform').serialize(),
				type:"POST",
				dataType : "json",
				async: false,
				success:function(data)
				{
					if(data.code==0){
						self.location.reload();
					}else{
						layer.alert(data.message);
					}
					
				},
				error:function(data){
					if(data.status ==1001){
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
					}
				}
			});
			return false;
	layer.close(index);
		}, function(){
		});
}

function contractReturnDel(attachmentId){
	checkLogin();
	layer.confirm("确认删除该条合同回传吗？", {
		  btn: ['确定','取消'] //按钮
		}, function(){
			$.ajax({
				type: "POST",
				url: page_url+"/order/aftersalesUpgrade/contractReturnDel.do",
				data: {'attachmentId':attachmentId},
				dataType:'json',
				success: function(data){
					self.location.reload();
				},
				error:function(data){
					if(data.status ==1001){
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
					}
				}
			});
		}, function(){
	});
}

function contractReturnDel2(attachmentId){
	checkLogin();
	layer.confirm("确认删除该条退票材料吗？", {
		  btn: ['确定','取消'] //按钮
		}, function(){
			$.ajax({
				type: "POST",
				url: page_url+"/order/aftersalesUpgrade/contractReturnDel.do",
				data: {'attachmentId':attachmentId},
				dataType:'json',
				success: function(data){
					self.location.reload();
				},
				error:function(data){
					if(data.status ==1001){
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
					}
				}
			});
		}, function(){
	});
}

function updateClientStatus(afterSalesId, oldClientStatus, newClientStatus) {
	checkLogin();
	layer.confirm("确认进行推送吗？", {
		btn: ['确定', '取消'] //按钮
	}, function () {
		$.ajax({
			type: "POST",
			url: page_url + "/order/aftersalesUpgrade/updateClientStatus.do",
			data: {
				'afterSalesId': afterSalesId,
				'oldClientStatus': oldClientStatus,
				'newClientStatus': newClientStatus
			},
			dataType: 'json',
			success: function (data) {
				if (data.code == 0) {
					self.location.reload();
				} else {
					layer.confirm("当前状态请求错误，请刷新后重试", {
						btn: ['确定', '取消'] //按钮
					}, function () {
						self.location.reload();
					}, function () {
						self.location.reload();
					});
				}
			},
			error: function (data) {
				if (data.status == 1001) {
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi")
				}
			}
		});
	}, function () {
	});
}


function delAfterTrader(afterSalesTraderId){
	checkLogin();
	layer.confirm("确认删除该条记录吗？", {
		  btn: ['确定','取消'] //按钮
		}, function(){
			$.ajax({
				type: "POST",
				url: "./delAfterTrader.do",
				data: {'afterSalesTraderId':afterSalesTraderId},
				dataType:'json',
				success: function(data){
					window.location.reload();
				},
				error:function(data){
					if(data.status ==1001){
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
					}
				}
			});
		}, function(){
	});
}

function passAudit(){//审核通过
	checkLogin();
	var type = $("input[name='type']").val();
	var msg ="";
	if(type ==539){
		msg ="是否确认审核通过？您需要确认退货手续费及发票是否需寄回。";
	}else if(type ==540){
		msg ="是否确认审核通过？您需要确认换货手续费及发票是否需寄回。";
	}else if(type ==541 || type ==4090 || type ==4091){
		msg ="是否确认审核通过？您需要确认安调手续费及发票是否需寄回。";
	}else if(type ==584){
		msg ="是否确认审核通过？您需要确认维修手续费及发票是否需寄回。";
	}else{
		msg ="是否确认审核通过？";
	}
	index = layer.confirm(msg, {
		  btn: ['确定','取消'] //按钮
		}, function(){
			$.ajax({
				url:page_url+'/order/aftersalesUpgrade/editPassAudit.do',
				data:$('#myform').serialize(),
				type:"POST",
				dataType : "json",
				async: false,
				success:function(data)
				{
					if(data.code==0){
						self.location.reload();
					}else{
						layer.alert(data.message);
					}
					
				},
				error:function(data){
					if(data.status ==1001){
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
					}
				}
			});
			return false;
	layer.close(index);
		}, function(){
		});

}

function confirmComplete(){//确认完成
	checkLogin();
	index = layer.confirm("您是否确认该订单已完成？确认后无法操作、无法更改状态！", {
		  btn: ['确定','取消'] //按钮
		}, function(){
			$.ajax({
				url:page_url+'/order/aftersalesUpgrade/editConfirmComplete.do',
				data:$('#myform').serialize(),
				type:"POST",
				dataType : "json",
				async: false,
				success:function(data)
				{
					if(data.code==0){
						self.location.reload();
					}else{
						layer.alert(data.message);
					}
					
				},
				error:function(data){
					if(data.status ==1001){
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
					}
				}
			});
			return false;
	layer.close(index);
		}, function(){
		});

}

//售后实退金额大于0，申请退款
function applyPay(){
	checkLogin();
	index = layer.confirm("是否确认当前操作？", {
		  btn: ['确定','取消'] //按钮
		}, function(){
			$.ajax({
				url:page_url+'/order/aftersalesUpgrade/saveRealRefundAmountApplyPay.do',
				data:$('#myform').serialize(),
				type:"POST",
				dataType : "json",
				async: false,
				success:function(data)
				{
					if(data.code==0){
						self.location.reload();
					}else{
						layer.alert(data.message);
					}
					
				},
				error:function(data){
					if(data.status ==1001){
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
					}
				}
			});
			return false;
	layer.close(index);
		}, function(){
		});
}

//执行退款运算
function executeRefundOperation(){
	checkLogin();
	// const confirmMessage = "请选择退款方式";
	// const options = `
	// 	<div style="display: flex; align-items: center;">
	// 		<div style="margin-right: 10px;">
	// 			<input type="radio" id="toBalance" name="refundMethod" value="1" checked>
	// 			<label for="toBalance">退到余额</label>
	// 		</div>
	// 		<div>
	// 			<input type="radio" id="toCustomer" name="refundMethod" value="2">
	// 			<label for="toCustomer">退回账户</label>
	// 		</div>
	// 	</div>
    // `;
	// var type = $("input[name='type']").val();
	// console.log('执行退款运算 type' + type);
	// layer.confirm(confirmMessage, {
	// 	title: '退款方式选择',
	// 	content: options,
	// 	btn: ['确定', '取消'] //按钮
	// }, function () {
	// 	const selectedRefundMethod = $("input[name='refundMethod']:checked").val();
	var afterSalesId = $("input[name='afterSalesId']").val();
	index = layer.confirm("是否确认当前操作？", {
		btn: ['确定','取消'] //按钮
	}, function(){
		// changed by Tomcat.Hui 2020/3/10 5:11 下午 .Desc: . .

		$.ajax({
			url:page_url+'/order/aftersalesUpgrade/calRefund.do',
			data:$('#myform').serialize(),
			type:"POST",
			dataType : "json",
			async: false,
			success:function(data)
			{
				if(data.code==0){
					self.location.reload();
				}else{
					layer.alert(data.message);
				}

			},
			error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
				}
			}
		});
		return false;
		layer.close(index);
	}, function(){
	});
}
//	删除售后费用类型
	function delectCostTypeList(afterSalesCostId){
		checkLogin();
		layer.confirm("确认删除该售后费用记录吗？", {
			  btn: ['确定','取消'] //按钮
			}, function(){
				$.ajax({
					type: "POST",
					url: page_url+"/order/aftersalesUpgrade/delectAfterSalesCost.do",
					data: {'afterSalesCostId':afterSalesCostId},
					dataType:'json',
					success: function(data){
						self.location.reload();
					},
					error:function(data){
						if(data.status ==1001){
							layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
						}
					}
				});
			}, function(){
		});
	}

	function editError(){
		layer.alert('实退金额为0，无法修改退款信息');
	}
//编辑售后信息
	function editAfterSaleOrder() {
	var url = '';
	url =  '/order/afterSalesCommon/editAfterSalesInfo.do'
		$.ajax({
			type: 'POST',
			url: url,
			data:$('#editAfterSaleOrderForm').serialize(),
			dataType : "html",
			success: function(result) {
				var htmlCont = result;
				var open = layer.open({
					type: 1,
					title: '编辑',
					shadeClose: false,
					area : ['750px', '500px'],
					content: htmlCont,
					success: function(layero, index){

					}
				});
				$('#layerIndex').val(open);
			}
		});
	}
function cancelDispatch() {
	var afterSalesId = $("#afterSalesId").val();
	layui.use("layer", function () {
		var layer = layui.layer;
		layer.prompt(
			{
				title: "该工单已经下派,确认需要取消?",
				formType: 2,
				btn: ["确认取消", "关闭"],
				success: function (layero, index) {
					// 手动设置 placeholder
					var textarea = layero.find(".layui-layer-input");
					textarea.attr("placeholder", "*请填写取消原因");

					// 手动设置 maxlength
					textarea.attr("maxlength", 499);

					// 监听输入事件，防止粘贴文本超过 maxlength
					textarea.on("input", function () {
						if (textarea.val().length > 499) {
							textarea.val(textarea.val().substring(0, 499));
						}
					});
				},
			},
			function (value, index, elem) {
				layer.close(index); // 关闭提示框
				var url = "";
				url = page_url + "/order/afterSalesCommon/cancelDispatch.do";

				var jsonData = {
					afterSalesId: afterSalesId,
					cancelReason: value,
				};

				$.ajax({
					type: "POST",
					url: url,
					data: JSON.stringify(jsonData),
					contentType: "application/json",
					dataType: "json",
					success: function (result) {
						location.reload(); // 刷新页面
					},
				});
			}
		);
	});
}


//警告无法下派医修帮
function warnDispatch() {
	checkLogin();
	var index = layer.confirm("仅安调服务公司是医修帮可以下派", {
		btn: ['确定', '取消'] //按钮
	}, function () {
		// 执行确定按钮的操作
		layer.close(index);
	}, function () {
		// 执行取消按钮的操作
		layer.close(index);
	});
}

//下派医修帮
function productSelectionDispatch() {
	var afterSalesId = $("#afterSalesId").val();
	var afterSalesVoJSon = $("#afterSalesVoJSon").val();
	var afterSalesVoJSonObj = JSON.parse(afterSalesVoJSon);

	var url = '';
	url = page_url + '/order/afterSalesCommon/productSelectionDispatch.do'

	// 显示加载动画
	var loadingIndex = layer.load(1);

	$.ajax({
		type: 'POST',
		data: {
			afterSalesId: afterSalesId
		},
		url: url,
		success: function(result) {
			// 关闭加载动画
			layer.close(loadingIndex);

			var htmlCont = result;
			var open = layer.open({
				type: 0,
				title: '下派商品选择',
				shadeClose: false,
				area: ['660px', '620px'],
				content: htmlCont,
				btn: ['确认下派', '关闭'],
				yes: function(index, layero) {
					var url = '';
					url = page_url + '/order/afterSalesCommon/confirmationDispatch.do'
					var table = layui.table;
					// 根据 ID 获取 input 元素
					var inputElement = document.getElementById("afterSalesQuantity");
					var checkStatus = table.checkStatus('userTable');
					var selectedData = checkStatus.data;
					var inputValue = inputElement.value;
					afterSalesVoJSonObj.productSelectionDispatchDtos = selectedData;
					afterSalesVoJSonObj.comments = inputValue;
					// 显示加载动画
					var loadingIndex = layer.load(1);

					$.ajax({
						type: 'POST',
						url: url,
						data: JSON.stringify(afterSalesVoJSonObj),
						contentType: 'application/json',
						dataType: 'json',
						success: function(result) {
							// 关闭加载动画
							layer.close(loadingIndex);

							layer.confirm(result.message,{
								btn: ['确定','取消'] //按钮
							}, function() {
								location.reload(); // 刷新页面
							},function () {
								// 点击取消按钮
								location.reload();
							});
						},
						error: function() {
							// 关闭加载动画
							layer.close(loadingIndex);

							layer.confirm(result.message,{
								btn: ['确定','取消'] //按钮
							}, function() {
								location.reload(); // 刷新页面
							},function () {
								// 点击取消按钮
								location.reload();
							});
						}
					});
					layer.close(index);
				}
			});
			$('#layerIndex').val(open);
		},
		error: function() {
			// 关闭加载动画
			layer.close(loadingIndex);
		}
	});
}

//编辑款项退还信息
function editAfterSalesBackFundInfo(afterSalesId,type,refund,finalRefundableAmount) {
	var url =  '/afterSalesOrder/refundInfo/edit.do'
	$.ajax({
		type: 'POST',
		url: url,
		data: {"afterSalesId": afterSalesId,"type":type,"refund":refund, "finalRefundableAmount": finalRefundableAmount},
		success: function(result) {
			var htmlCont = result;
			var open = layer.open({
				type: 1,
				title: '编辑款项退款信息',
				shadeClose: false,
				area : ['75%', '600px'],
				content: htmlCont,
				success: function(layero, index){

				},
				cancel: function(){
					window.location.reload();
				}
			});
			$('#layerIndex').val(open);
		}
	});
}
//编辑售后信息
function editAfterSaleFollow() {
	var url = '';
	url =  '/order/afterSalesCommon/addFollowUpRecord.do'
	$.ajax({
		type: 'POST',
		url: url,
		data:$('#editAfterSaleOrderForm').serialize(),
		dataType : "html",
		success: function(result) {
			var htmlCont = result;
			var open = layer.open({
				type: 1,
				title: '编辑',
				shadeClose: false,
				area : ['750px', '350px'],
				content: htmlCont,
				success: function(layero, index){

				}
			});
			$('#layerIndex').val(open);
		}
	});
}

//编辑直发入库信息
function editAfterSaleDirectInfo(num,type) {
	var haveAfterSalesDirectInfo = $("#haveAfterSalesDirectInfo").val();
	if(haveAfterSalesDirectInfo == 1){
		layer.alert("直发退货记录为空，无需录入");
	}else {
		var data = "";
		if(type == 1){
			data = $('#editDirectOut' + num).serialize();
		}else {
			data = $('#editDirectIn' + num).serialize();
		}
		var url = '';
		url =  '/order/afterSalesCommon/addDirectStockInfo.do'
		$.ajax({
			type: 'POST',
			url: url,
			data: data,
			dataType : "html",
			success: function(result) {
				var htmlCont = result;
				var open = layer.open({
					type: 1,
					title: '编辑',
					shadeClose: false,
					area : ['850px', '500px'],
					content: htmlCont,
					success: function(layero, index){
					},
					cancel: function(){
						window.location.reload();
					}
				});
				$('#layerIndex').val(open);
			}
		});
	}
}

/**
 * 关闭子页面
 */
function closeChildrenView() {
	layer.closeAll();
}

/**
 * 初始化操作信息保存
 *
 * @param afterSalesId
 */
function initHandleInfoSave(afterSalesInvoiceId) {
	$.ajax({
		type: 'POST',
		url: '/order/afterSalesCommon/initInvoiceHandleInfo.do',
		data:{
			"afterSalesId": $('#afterSalesId').val(),
			"afterSalesInvoiceId": afterSalesInvoiceId
		},
		dataType : "html",
		success: function(result) {
			var htmlCont = result;
			var open = layer.open({
				type: 1,
				title: '是否确认处理完毕',
				shadeClose: false,
				area : ['480px', '200px'],
				content: htmlCont,
				success: function(layero, index){

				}
			});
			$('#layerIndex').val(open);
		}
	});
}

function auditRecordsCommonClose(num,the){

	$(".athchoice").removeClass("a-th-noChoice a-th-choice");
	$(".athchoicebu").removeClass("a-bu-noChoice a-bu-choice");
	$(the).addClass("a-bu-choice");
	$(the).parent().addClass("a-th-choice");

	if (num == 1) {
		$("#auditRecordsCommon").show();
		$("#auditRecordsOverCommon").hide();
		$("#auditRecordsPayCommon").hide();
	}else if (num == 2){
		$("#auditRecordsCommon").hide();
		$("#auditRecordsOverCommon").show();
		$("#auditRecordsPayCommon").hide();
	}else if (num == 3){
		$("#auditRecordsCommon").hide();
		$("#auditRecordsOverCommon").hide();
		$("#auditRecordsPayCommon").show();
	}
}

function confirmBackInvoiceStatus(){
	$.ajax({
		type: 'POST',
		url: '/order/aftersalesUpgrade/confirmBackInvoiceStatus.do',
		data:{
			"afterSalesId": $('#afterSalesId').val()
		},
		dataType:'json',
		success: function(data){
			window.location.reload();
		},
		error:function(data){
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}
		}
	});
}

function isTraderAllowInvoiceAfter(afterSalesId,invoiceType) {
	checkLogin();
	var url=page_url+"/trader/customer/isTraderAllowInvoiceAfter.do"
	$.ajax({
		type: "POST",
		url: url,
		data: {'afterSalesId':afterSalesId},
		dataType:'json',
		success: function(data){
			console.log(data.data);
			if(data.code != 0){
				layer.alert(data.message);
				return;
			}
			if(data.data.passCredential){
				console.log("全部通过");
				$("#invoiceApplyAfter").click();
				return;
			}else if (!data.data.passCredential){
				layer.open({
					title: '财务资质信息',
					type: 2,
					area: ['600px', '350px'],
					shadeClose: true,
					content: '/invoice/invoiceApply/invoiceApplyCheckCustomer.do?traderId='+data.data.traderFinanceDto.traderId+'&invoiceType='+invoiceType,
					success: function(layer, index){

					}
				});
			}
		},
		error:function(data){
			if(data.status ==1001){
				layer.alert("当前操作无权限")
				return;
			}
			layer.alert("验证客户能否开票失败")
		}
	});
}