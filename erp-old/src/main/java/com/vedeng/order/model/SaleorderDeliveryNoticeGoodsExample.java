package com.vedeng.order.model;
import java.util.ArrayList;
import java.util.List;

public class SaleorderDeliveryNoticeGoodsExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public SaleorderDeliveryNoticeGoodsExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andDeliveryNoticeGoodsIdIsNull() {
            addCriterion("DELIVERY_NOTICE_GOODS_ID is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoticeGoodsIdIsNotNull() {
            addCriterion("DELIVERY_NOTICE_GOODS_ID is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoticeGoodsIdEqualTo(Integer value) {
            addCriterion("DELIVERY_NOTICE_GOODS_ID =", value, "deliveryNoticeGoodsId");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoticeGoodsIdNotEqualTo(Integer value) {
            addCriterion("DELIVERY_NOTICE_GOODS_ID <>", value, "deliveryNoticeGoodsId");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoticeGoodsIdGreaterThan(Integer value) {
            addCriterion("DELIVERY_NOTICE_GOODS_ID >", value, "deliveryNoticeGoodsId");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoticeGoodsIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("DELIVERY_NOTICE_GOODS_ID >=", value, "deliveryNoticeGoodsId");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoticeGoodsIdLessThan(Integer value) {
            addCriterion("DELIVERY_NOTICE_GOODS_ID <", value, "deliveryNoticeGoodsId");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoticeGoodsIdLessThanOrEqualTo(Integer value) {
            addCriterion("DELIVERY_NOTICE_GOODS_ID <=", value, "deliveryNoticeGoodsId");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoticeGoodsIdIn(List<Integer> values) {
            addCriterion("DELIVERY_NOTICE_GOODS_ID in", values, "deliveryNoticeGoodsId");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoticeGoodsIdNotIn(List<Integer> values) {
            addCriterion("DELIVERY_NOTICE_GOODS_ID not in", values, "deliveryNoticeGoodsId");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoticeGoodsIdBetween(Integer value1, Integer value2) {
            addCriterion("DELIVERY_NOTICE_GOODS_ID between", value1, value2, "deliveryNoticeGoodsId");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoticeGoodsIdNotBetween(Integer value1, Integer value2) {
            addCriterion("DELIVERY_NOTICE_GOODS_ID not between", value1, value2, "deliveryNoticeGoodsId");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoticeIdIsNull() {
            addCriterion("DELIVERY_NOTICE_ID is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoticeIdIsNotNull() {
            addCriterion("DELIVERY_NOTICE_ID is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoticeIdEqualTo(Integer value) {
            addCriterion("DELIVERY_NOTICE_ID =", value, "deliveryNoticeId");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoticeIdNotEqualTo(Integer value) {
            addCriterion("DELIVERY_NOTICE_ID <>", value, "deliveryNoticeId");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoticeIdGreaterThan(Integer value) {
            addCriterion("DELIVERY_NOTICE_ID >", value, "deliveryNoticeId");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoticeIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("DELIVERY_NOTICE_ID >=", value, "deliveryNoticeId");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoticeIdLessThan(Integer value) {
            addCriterion("DELIVERY_NOTICE_ID <", value, "deliveryNoticeId");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoticeIdLessThanOrEqualTo(Integer value) {
            addCriterion("DELIVERY_NOTICE_ID <=", value, "deliveryNoticeId");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoticeIdIn(List<Integer> values) {
            addCriterion("DELIVERY_NOTICE_ID in", values, "deliveryNoticeId");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoticeIdNotIn(List<Integer> values) {
            addCriterion("DELIVERY_NOTICE_ID not in", values, "deliveryNoticeId");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoticeIdBetween(Integer value1, Integer value2) {
            addCriterion("DELIVERY_NOTICE_ID between", value1, value2, "deliveryNoticeId");
            return (Criteria) this;
        }

        public Criteria andDeliveryNoticeIdNotBetween(Integer value1, Integer value2) {
            addCriterion("DELIVERY_NOTICE_ID not between", value1, value2, "deliveryNoticeId");
            return (Criteria) this;
        }

        public Criteria andSaleorderGoodsIdIsNull() {
            addCriterion("SALEORDER_GOODS_ID is null");
            return (Criteria) this;
        }

        public Criteria andSaleorderGoodsIdIsNotNull() {
            addCriterion("SALEORDER_GOODS_ID is not null");
            return (Criteria) this;
        }

        public Criteria andSaleorderGoodsIdEqualTo(Integer value) {
            addCriterion("SALEORDER_GOODS_ID =", value, "saleorderGoodsId");
            return (Criteria) this;
        }

        public Criteria andSaleorderGoodsIdNotEqualTo(Integer value) {
            addCriterion("SALEORDER_GOODS_ID <>", value, "saleorderGoodsId");
            return (Criteria) this;
        }

        public Criteria andSaleorderGoodsIdGreaterThan(Integer value) {
            addCriterion("SALEORDER_GOODS_ID >", value, "saleorderGoodsId");
            return (Criteria) this;
        }

        public Criteria andSaleorderGoodsIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("SALEORDER_GOODS_ID >=", value, "saleorderGoodsId");
            return (Criteria) this;
        }

        public Criteria andSaleorderGoodsIdLessThan(Integer value) {
            addCriterion("SALEORDER_GOODS_ID <", value, "saleorderGoodsId");
            return (Criteria) this;
        }

        public Criteria andSaleorderGoodsIdLessThanOrEqualTo(Integer value) {
            addCriterion("SALEORDER_GOODS_ID <=", value, "saleorderGoodsId");
            return (Criteria) this;
        }

        public Criteria andSaleorderGoodsIdIn(List<Integer> values) {
            addCriterion("SALEORDER_GOODS_ID in", values, "saleorderGoodsId");
            return (Criteria) this;
        }

        public Criteria andSaleorderGoodsIdNotIn(List<Integer> values) {
            addCriterion("SALEORDER_GOODS_ID not in", values, "saleorderGoodsId");
            return (Criteria) this;
        }

        public Criteria andSaleorderGoodsIdBetween(Integer value1, Integer value2) {
            addCriterion("SALEORDER_GOODS_ID between", value1, value2, "saleorderGoodsId");
            return (Criteria) this;
        }

        public Criteria andSaleorderGoodsIdNotBetween(Integer value1, Integer value2) {
            addCriterion("SALEORDER_GOODS_ID not between", value1, value2, "saleorderGoodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdIsNull() {
            addCriterion("GOODS_ID is null");
            return (Criteria) this;
        }

        public Criteria andGoodsIdIsNotNull() {
            addCriterion("GOODS_ID is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsIdEqualTo(Integer value) {
            addCriterion("GOODS_ID =", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdNotEqualTo(Integer value) {
            addCriterion("GOODS_ID <>", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdGreaterThan(Integer value) {
            addCriterion("GOODS_ID >", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("GOODS_ID >=", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdLessThan(Integer value) {
            addCriterion("GOODS_ID <", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdLessThanOrEqualTo(Integer value) {
            addCriterion("GOODS_ID <=", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdIn(List<Integer> values) {
            addCriterion("GOODS_ID in", values, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdNotIn(List<Integer> values) {
            addCriterion("GOODS_ID not in", values, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdBetween(Integer value1, Integer value2) {
            addCriterion("GOODS_ID between", value1, value2, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdNotBetween(Integer value1, Integer value2) {
            addCriterion("GOODS_ID not between", value1, value2, "goodsId");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNull() {
            addCriterion("ORDER_ID is null");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNotNull() {
            addCriterion("ORDER_ID is not null");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualTo(Integer value) {
            addCriterion("ORDER_ID =", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualTo(Integer value) {
            addCriterion("ORDER_ID <>", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThan(Integer value) {
            addCriterion("ORDER_ID >", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("ORDER_ID >=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThan(Integer value) {
            addCriterion("ORDER_ID <", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualTo(Integer value) {
            addCriterion("ORDER_ID <=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdIn(List<Integer> values) {
            addCriterion("ORDER_ID in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotIn(List<Integer> values) {
            addCriterion("ORDER_ID not in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdBetween(Integer value1, Integer value2) {
            addCriterion("ORDER_ID between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotBetween(Integer value1, Integer value2) {
            addCriterion("ORDER_ID not between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andNumIsNull() {
            addCriterion("NUM is null");
            return (Criteria) this;
        }

        public Criteria andNumIsNotNull() {
            addCriterion("NUM is not null");
            return (Criteria) this;
        }

        public Criteria andNumEqualTo(Integer value) {
            addCriterion("NUM =", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumNotEqualTo(Integer value) {
            addCriterion("NUM <>", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumGreaterThan(Integer value) {
            addCriterion("NUM >", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("NUM >=", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumLessThan(Integer value) {
            addCriterion("NUM <", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumLessThanOrEqualTo(Integer value) {
            addCriterion("NUM <=", value, "num");
            return (Criteria) this;
        }

        public Criteria andNumIn(List<Integer> values) {
            addCriterion("NUM in", values, "num");
            return (Criteria) this;
        }

        public Criteria andNumNotIn(List<Integer> values) {
            addCriterion("NUM not in", values, "num");
            return (Criteria) this;
        }

        public Criteria andNumBetween(Integer value1, Integer value2) {
            addCriterion("NUM between", value1, value2, "num");
            return (Criteria) this;
        }

        public Criteria andNumNotBetween(Integer value1, Integer value2) {
            addCriterion("NUM not between", value1, value2, "num");
            return (Criteria) this;
        }

        public Criteria andOccupyNumIsNull() {
            addCriterion("OCCUPY_NUM is null");
            return (Criteria) this;
        }

        public Criteria andOccupyNumIsNotNull() {
            addCriterion("OCCUPY_NUM is not null");
            return (Criteria) this;
        }

        public Criteria andOccupyNumEqualTo(Integer value) {
            addCriterion("OCCUPY_NUM =", value, "occupyNum");
            return (Criteria) this;
        }

        public Criteria andOccupyNumNotEqualTo(Integer value) {
            addCriterion("OCCUPY_NUM <>", value, "occupyNum");
            return (Criteria) this;
        }

        public Criteria andOccupyNumGreaterThan(Integer value) {
            addCriterion("OCCUPY_NUM >", value, "occupyNum");
            return (Criteria) this;
        }

        public Criteria andOccupyNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("OCCUPY_NUM >=", value, "occupyNum");
            return (Criteria) this;
        }

        public Criteria andOccupyNumLessThan(Integer value) {
            addCriterion("OCCUPY_NUM <", value, "occupyNum");
            return (Criteria) this;
        }

        public Criteria andOccupyNumLessThanOrEqualTo(Integer value) {
            addCriterion("OCCUPY_NUM <=", value, "occupyNum");
            return (Criteria) this;
        }

        public Criteria andOccupyNumIn(List<Integer> values) {
            addCriterion("OCCUPY_NUM in", values, "occupyNum");
            return (Criteria) this;
        }

        public Criteria andOccupyNumNotIn(List<Integer> values) {
            addCriterion("OCCUPY_NUM not in", values, "occupyNum");
            return (Criteria) this;
        }

        public Criteria andOccupyNumBetween(Integer value1, Integer value2) {
            addCriterion("OCCUPY_NUM between", value1, value2, "occupyNum");
            return (Criteria) this;
        }

        public Criteria andOccupyNumNotBetween(Integer value1, Integer value2) {
            addCriterion("OCCUPY_NUM not between", value1, value2, "occupyNum");
            return (Criteria) this;
        }

        public Criteria andArrivalNumIsNull() {
            addCriterion("ARRIVAL_NUM is null");
            return (Criteria) this;
        }

        public Criteria andArrivalNumIsNotNull() {
            addCriterion("ARRIVAL_NUM is not null");
            return (Criteria) this;
        }

        public Criteria andArrivalNumEqualTo(Integer value) {
            addCriterion("ARRIVAL_NUM =", value, "arrivalNum");
            return (Criteria) this;
        }

        public Criteria andArrivalNumNotEqualTo(Integer value) {
            addCriterion("ARRIVAL_NUM <>", value, "arrivalNum");
            return (Criteria) this;
        }

        public Criteria andArrivalNumGreaterThan(Integer value) {
            addCriterion("ARRIVAL_NUM >", value, "arrivalNum");
            return (Criteria) this;
        }

        public Criteria andArrivalNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("ARRIVAL_NUM >=", value, "arrivalNum");
            return (Criteria) this;
        }

        public Criteria andArrivalNumLessThan(Integer value) {
            addCriterion("ARRIVAL_NUM <", value, "arrivalNum");
            return (Criteria) this;
        }

        public Criteria andArrivalNumLessThanOrEqualTo(Integer value) {
            addCriterion("ARRIVAL_NUM <=", value, "arrivalNum");
            return (Criteria) this;
        }

        public Criteria andArrivalNumIn(List<Integer> values) {
            addCriterion("ARRIVAL_NUM in", values, "arrivalNum");
            return (Criteria) this;
        }

        public Criteria andArrivalNumNotIn(List<Integer> values) {
            addCriterion("ARRIVAL_NUM not in", values, "arrivalNum");
            return (Criteria) this;
        }

        public Criteria andArrivalNumBetween(Integer value1, Integer value2) {
            addCriterion("ARRIVAL_NUM between", value1, value2, "arrivalNum");
            return (Criteria) this;
        }

        public Criteria andArrivalNumNotBetween(Integer value1, Integer value2) {
            addCriterion("ARRIVAL_NUM not between", value1, value2, "arrivalNum");
            return (Criteria) this;
        }

        public Criteria andDeleteStateIsNull() {
            addCriterion("DELETE_STATE is null");
            return (Criteria) this;
        }

        public Criteria andDeleteStateIsNotNull() {
            addCriterion("DELETE_STATE is not null");
            return (Criteria) this;
        }

        public Criteria andDeleteStateEqualTo(Integer value) {
            addCriterion("DELETE_STATE =", value, "deleteState");
            return (Criteria) this;
        }

        public Criteria andDeleteStateNotEqualTo(Integer value) {
            addCriterion("DELETE_STATE <>", value, "deleteState");
            return (Criteria) this;
        }

        public Criteria andDeleteStateGreaterThan(Integer value) {
            addCriterion("DELETE_STATE >", value, "deleteState");
            return (Criteria) this;
        }

        public Criteria andDeleteStateGreaterThanOrEqualTo(Integer value) {
            addCriterion("DELETE_STATE >=", value, "deleteState");
            return (Criteria) this;
        }

        public Criteria andDeleteStateLessThan(Integer value) {
            addCriterion("DELETE_STATE <", value, "deleteState");
            return (Criteria) this;
        }

        public Criteria andDeleteStateLessThanOrEqualTo(Integer value) {
            addCriterion("DELETE_STATE <=", value, "deleteState");
            return (Criteria) this;
        }

        public Criteria andDeleteStateIn(List<Integer> values) {
            addCriterion("DELETE_STATE in", values, "deleteState");
            return (Criteria) this;
        }

        public Criteria andDeleteStateNotIn(List<Integer> values) {
            addCriterion("DELETE_STATE not in", values, "deleteState");
            return (Criteria) this;
        }

        public Criteria andDeleteStateBetween(Integer value1, Integer value2) {
            addCriterion("DELETE_STATE between", value1, value2, "deleteState");
            return (Criteria) this;
        }

        public Criteria andDeleteStateNotBetween(Integer value1, Integer value2) {
            addCriterion("DELETE_STATE not between", value1, value2, "deleteState");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}