package com.vedeng.erp.buyorder.service.impl;

import com.common.dto.CanalMqBodyDTO;
import com.vedeng.erp.buyorder.common.constant.BuyorderSystemDefinitionConstant;
import com.vedeng.erp.buyorder.service.BuyorderDataSyncService;
import com.vedeng.erp.buyorder.task.buyorder.statusSync.BuyorderIsContractReturnStatusSync;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;

/**
 * @Author: daniel
 * @Date: 2022/4/6 19 52
 * @Description:
 */
@Service
public class BuyorderDataSyncServiceImpl implements BuyorderDataSyncService {

    @Autowired
    private BuyorderIsContractReturnStatusSync buyorderIsContractReturnStatusSync;


    @Override
    public void syncDataByAttachmentTable(CanalMqBodyDTO mqBodyDTO) {
        Arrays.stream(mqBodyDTO.getData()).forEach(data -> {
            JSONObject item = JSONObject.fromObject(data);
            Integer relatedId = item.getInt("RELATED_ID");
            int attachmentFunction = item.getInt("ATTACHMENT_FUNCTION");
            if (BuyorderSystemDefinitionConstant.BUYORDER_CONTRACT_RETURN_ATTACHMENT_FUNCTION == attachmentFunction){
                buyorderIsContractReturnStatusSync.process(relatedId);
            }
        });
    }
}
