<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchBankBillDtoMapper">
    <resultMap id="baseResultMap" type="com.vedeng.erp.kingdee.batch.dto.BatchBankBillDto">
        <id column="BANK_BILL_ID" property="bankBillId" jdbcType="INTEGER"/>
        <result column="BANK_TAG" property="bankTag" jdbcType="INTEGER"/>
        <result column="TRAN_FLOW" property="tranFlow" jdbcType="VARCHAR"/>
        <result column="TRANDATE" property="tranDate" jdbcType="DATE"/>
        <result column="TRANTIME" property="tranTime" jdbcType="TIME"/>
        <result column="REAL_TRANDATETIME" jdbcType="TIMESTAMP" property="realTrandatetime" />
        <result column="AMT" property="amt" jdbcType="DECIMAL"/>
        <result column="FLAG1" property="flag1" jdbcType="INTEGER"/>
        <result column="STATUS" property="status" jdbcType="INTEGER"/>
        <result column="MATCHED_OBJECT" property="matchedObject" jdbcType="INTEGER"/>
        <result column="RECEIPT_URL" property="receiptUrl" jdbcType="VARCHAR"/>
        <result column="HAVE_PUSHED_KINGDEE" property="havePushedKingdee" jdbcType="INTEGER"/>
        <result column="ACC_NAME1" property="accName1" jdbcType="VARCHAR"/>
        <result column="RECEIPT_NAME" property="receiptName" jdbcType="VARCHAR"/>
        <result column="CAPITAL_BILL_ID" property="capitalBillId" jdbcType="INTEGER"/>
        <result column="AMOUNT" property="amount" jdbcType="DECIMAL"/>
        <result column="TRADER_ID" property="traderId" jdbcType="INTEGER"/>
        <result column="CAPITAL_SEARCH_FLOW" property="capitalSearchFlow" jdbcType="VARCHAR"/>
        <result column="ACCNO2" property="accno2" jdbcType="VARCHAR"/>
        <result column="CADBANK_NM" property="cadBankNm" jdbcType="VARCHAR"/>
        <result column="ACC_BANKNO" property="accBankNo" jdbcType="VARCHAR"/>
        <result column="SETTLEMENT_METHOD" property="settlementMethod" jdbcType="INTEGER"/>
    </resultMap>


    <update id="updateKingdeePushedStatus">
        UPDATE T_BANK_BILL SET HAVE_PUSHED_KINGDEE = 1 WHERE BANK_BILL_ID = #{bankBillId,jdbcType=INTEGER}
    </update>

    <select id="queryNeedPullBankBill" resultMap="baseResultMap">
        SELECT
            A.BANK_BILL_ID, A.BANK_TAG, A.TRAN_FLOW, A.TRANDATE, A.TRANTIME ,A.AMT, A.FLAG1, A.STATUS,A.MATCHED_OBJECT,A.RECEIPT_URL,A.RECEIPT_NAME,
            A.HAVE_PUSHED_KINGDEE,A.ACC_NAME1,A.MATCHED_AMOUNT,
            B.TITLE AS ignoreReason
            FROM T_BANK_BILL A
            LEFT JOIN T_SYS_OPTION_DEFINITION B ON A.MATCHED_OBJECT = B.SYS_OPTION_DEFINITION_ID
        <if test="beginTime == null and endTime == null">
            LEFT JOIN KING_DEE_RECEIVE_BILL C on A.TRAN_FLOW = C.F_BILL_NO
        </if>
        WHERE
            A.FLAG1 = #{flag1,jdbcType=INTEGER}
            AND (A.STATUS = 1 OR (A.STATUS = 0 AND A.MATCHED_AMOUNT != 0))
            <if test="beginTime != null">
                and A.TRANDATE <![CDATA[>=]]> #{beginTime}
            </if>
            <if test="endTime != null">
                and A.TRANDATE <![CDATA[<=]]> #{endTime}
            </if>
            <if test="beginTime == null and endTime == null">
                and C.F_BILL_NO is null
                and A.TRANDATE >= '2023-01-01'
            </if>
        and (A.IS_FEE = 0 or A.IS_FEE is null)
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>

    <select id="queryReceiveBillFill" resultMap="baseResultMap">
        SELECT
        A.BANK_BILL_ID, A.BANK_TAG, A.TRAN_FLOW, A.TRANDATE, A.TRANTIME ,A.AMT, A.FLAG1, A.STATUS,A.MATCHED_OBJECT,A.RECEIPT_URL,A.RECEIPT_NAME,
        A.HAVE_PUSHED_KINGDEE,A.ACC_NAME1,A.MATCHED_AMOUNT,
        B.TITLE AS ignoreReason,D.CAPITAL_BILL_ID
        FROM T_BANK_BILL A
        LEFT JOIN T_SYS_OPTION_DEFINITION B ON A.MATCHED_OBJECT = B.SYS_OPTION_DEFINITION_ID
        left join T_CAPITAL_BILL D on A.BANK_BILL_ID = D.BANK_BILL_ID
        LEFT JOIN KING_DEE_RECEIVE_BILL C on CONCAT('C_',A.BANK_BILL_ID) = C.F_BILL_NO
        WHERE
        A.FLAG1 = #{flag1,jdbcType=INTEGER}
        AND A.STATUS = 0
        AND A.MATCHED_AMOUNT != 0
        <if test="beginTime != null">
            and A.TRANDATE <![CDATA[>=]]> #{beginTime}
        </if>
        <if test="endTime != null">
            and A.TRANDATE <![CDATA[<=]]> #{endTime}
        </if>
        and C.F_BILL_NO is not null
        and A.TRANDATE >= '2023-01-01'
        and (A.IS_FEE = 0 or A.IS_FEE is null)
        and A.RECEIPT_URL is not null
        group by A.BANK_BILL_ID
        union
        SELECT
        A.BANK_BILL_ID, A.BANK_TAG, A.TRAN_FLOW, A.TRANDATE, A.TRANTIME ,A.AMT, A.FLAG1, A.STATUS,A.MATCHED_OBJECT,A.RECEIPT_URL,A.RECEIPT_NAME,
        A.HAVE_PUSHED_KINGDEE,A.ACC_NAME1,A.MATCHED_AMOUNT,
        B.TITLE AS ignoreReason,D.CAPITAL_BILL_ID
        FROM T_BANK_BILL A
        LEFT JOIN T_SYS_OPTION_DEFINITION B ON A.MATCHED_OBJECT = B.SYS_OPTION_DEFINITION_ID
        left join T_CAPITAL_BILL D on A.BANK_BILL_ID = D.BANK_BILL_ID
        LEFT JOIN KING_DEE_RECEIVE_BILL C on CONCAT('B_',A.BANK_BILL_ID) = C.F_BILL_NO
        WHERE
        A.FLAG1 = #{flag1,jdbcType=INTEGER}
        <if test="beginTime != null">
            and A.TRANDATE <![CDATA[>=]]> #{beginTime}
        </if>
        <if test="endTime != null">
            and A.TRANDATE <![CDATA[<=]]> #{endTime}
        </if>
        and C.F_BILL_NO is not null
        and A.TRANDATE >= '2023-01-01'
        and (A.IS_FEE = 0 or A.IS_FEE is null)
        and A.RECEIPT_URL is not null
        group by A.BANK_BILL_ID
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>

    <select id="queryNeedPullIgnoreBankBill" resultMap="baseResultMap">
        SELECT
        A.BANK_BILL_ID, A.BANK_TAG, A.TRAN_FLOW, A.TRANDATE, A.TRANTIME ,A.REAL_TRANDATETIME,A.AMT, A.FLAG1, A.STATUS,A.MATCHED_OBJECT,A.RECEIPT_URL,A.RECEIPT_NAME,
        A.HAVE_PUSHED_KINGDEE,A.ACC_NAME1,A.MATCHED_AMOUNT,A.CAPITAL_SEARCH_FLOW,A.ACCNO2,A.CADBANK_NM,A.ACC_BANKNO,
        D.IGNORE_REASON AS ignoreReason,S.SETTLEMENT_METHOD
        FROM T_BANK_BILL A
        LEFT JOIN T_BANK_BILL_SETTLEMENT S ON A.BANK_BILL_ID = S.BANK_BILL_ID
        LEFT JOIN T_BANK_BILL_IGNORE_RECORD D on A.BANK_BILL_ID = D.BANK_BILL_ID
        <if test="beginTime == null and endTime == null">
            LEFT JOIN KING_DEE_RECEIVE_BILL C on CONCAT('B_',A.BANK_BILL_ID) = C.F_BILL_NO
        </if>
        WHERE
        A.FLAG1 = #{flag1,jdbcType=INTEGER}
        AND (A.IS_FEE = 0 OR A.IS_FEE IS NULL)
        AND A.STATUS = 1
        and A.TRANDATE >= '2023-01-01'
        and D.IS_DELETE = 0
        <if test="beginTime != null">
            and D.ADD_TIME <![CDATA[>=]]> #{beginTime}
        </if>
        <if test="endTime != null">
            and D.ADD_TIME <![CDATA[<=]]> #{endTime}
        </if>
        <if test="beginTime == null and endTime == null">
            and C.F_BILL_NO is null
        </if>
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>

    <select id="queryhistoryNeedPullBankBill" resultMap="baseResultMap">
        SELECT
        A.BANK_BILL_ID, A.BANK_TAG, A.TRAN_FLOW,A.TRANDATE,
        A.TRANTIME ,A.AMT, A.FLAG1, A.STATUS,A.MATCHED_OBJECT,A.RECEIPT_URL,
        A.HAVE_PUSHED_KINGDEE,A.ACC_NAME1,A.MATCHED_AMOUNT,
        B.TITLE AS ignoreReason,D.BUSSINESS_TYPE,D.ORDER_TYPE,A.ACC_BANKNO,A.ACCNO2,A.CADBANK_NM,A.ACC_NAME1,
        D.ORDER_NO,C.TRADER_SUBJECT
        FROM T_BANK_BILL A
        LEFT JOIN T_SYS_OPTION_DEFINITION B ON A.MATCHED_OBJECT = B.SYS_OPTION_DEFINITION_ID
        LEFT JOIN T_CAPITAL_BILL C ON A.BANK_BILL_ID = C.BANK_BILL_ID
        LEFT JOIN T_CAPITAL_BILL_DETAIL D ON C.CAPITAL_BILL_ID = D.CAPITAL_BILL_ID
        WHERE
        A.FLAG1 = #{flag1,jdbcType=INTEGER}
        AND A.HAVE_PUSHED_KINGDEE = #{havePushedKingdee}
        AND C.TRADER_TYPE = 2
        AND C.TRADER_MODE = 521
        AND D.BUSSINESS_TYPE IN ( 531, 525, 533 )
        AND D.ORDER_TYPE IN (2,3)
        <if test="receiptUrl != null">
            AND A.RECEIPT_URL IS NOT NULL
        </if>
        <if test="beginTime != null">
            and A.TRANDATE <![CDATA[>=]]> #{beginTime}
        </if>
        <if test="endTime != null">
            and A.TRANDATE <![CDATA[<=]]> #{endTime}
        </if>
        GROUP BY A.BANK_BILL_ID
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>
    <select id="getAliReceiptBillNotPushedToKingDee" resultType="com.vedeng.erp.kingdee.batch.dto.BatchBankBillDto">
        SELECT BB.BANK_BILL_ID,
               BB.BANK_TAG,
               BB.FLAG1,
               BB.STATUS,
               BB.TRAN_FLOW,
               BB.MATCHED_OBJECT,
               BB.RECEIPT_URL,
               BB.TRANTIME,
               BB.TRANDATE,
               BB.AMT,
               BB.ACCNO2,
               BB.ACC_NAME1,
               TC.TRADER_CUSTOMER_ID,
               CB.TRADER_SUBJECT,
               CB.TRADER_TYPE,
               CBD.ORDER_NO,
               BB.RECEIPT_NAME
        FROM T_BANK_BILL BB
                 LEFT JOIN T_CAPITAL_BILL CB ON CB.BANK_BILL_ID = BB.BANK_BILL_ID
                 LEFT JOIN T_CAPITAL_BILL_DETAIL CBD ON CBD.CAPITAL_BILL_ID = CB.CAPITAL_BILL_ID
                 LEFT JOIN T_TRADER_CUSTOMER TC ON TC.TRADER_ID = CBD.TRADER_ID
                 <if test="beginTime == null and endTime == null">
                     LEFT JOIN KING_DEE_RECEIVE_BILL C on BB.TRAN_FLOW = C.F_BILL_NO
                 </if>
        WHERE BB.BANK_TAG = 4
          AND CB.CAPITAL_BILL_ID IS NOT NULL
          AND CBD.ORDER_TYPE = 1
          and (BB.IS_FEE is null or BB.IS_FEE = 0)
        <if test="beginTime == null and endTime == null">
            and C.F_BILL_NO is null
            and BB.TRANDATE >= '2023-01-01'
        </if>
        <if test="beginTime != null">
            and BB.TRANDATE <![CDATA[>=]]> #{beginTime}
        </if>
        <if test="endTime != null">
            and BB.TRANDATE <![CDATA[<=]]> #{endTime}
        </if>
        GROUP BY BB.BANK_BILL_ID
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>

    <select id="queryInfoById" resultMap="baseResultMap">
        SELECT
            BANK_BILL_ID,BANK_TAG,RECEIPT_URL,RECEIPT_NAME
            FROM T_BANK_BILL
        WHERE BANK_BILL_ID = #{bankBillId,jdbcType=INTEGER}
    </select>
    <select id="querypaymentwechatNeedPullBankBill"
            resultType="com.vedeng.erp.kingdee.batch.dto.BatchBankBillDto">
        SELECT
        A.BANK_BILL_ID,
        A.BANK_TAG,
        A.TRAN_FLOW,
        A.TRANDATE,
        A.TRANTIME,
        A.AMT,
        A.FLAG1,
        A.STATUS,
        A.MATCHED_OBJECT,
        A.RECEIPT_URL,
        A.HAVE_PUSHED_KINGDEE,
        A.ACC_NAME1,
        A.MATCHED_AMOUNT,
        B.TITLE AS ignoreReason,
        D.BUSSINESS_TYPE,
        D.ORDER_TYPE,
        A.ACC_BANKNO,
        A.ACCNO2,
        A.CADBANK_NM,
        A.ACC_NAME1,
        D.ORDER_NO,
        C.TRADER_SUBJECT,
        D.TRADER_ID
        FROM
        T_BANK_BILL A
        LEFT JOIN T_SYS_OPTION_DEFINITION B ON A.MATCHED_OBJECT = B.SYS_OPTION_DEFINITION_ID
        LEFT JOIN T_CAPITAL_BILL C ON A.TRAN_FLOW = C.TRAN_FLOW
        LEFT JOIN T_CAPITAL_BILL_DETAIL D ON C.CAPITAL_BILL_ID = D.CAPITAL_BILL_ID
        <if test="beginTime == null and endTime == null">
            LEFT JOIN KING_DEE_PAY_BILL PB on A.TRAN_FLOW = PB.F_BILL_NO
        </if>
        WHERE
        1 = 1
        AND C.TRADER_TYPE='2'
        <if test="beginTime != null">
            and A.TRANDATE <![CDATA[>=]]> #{beginTime}
        </if>
        <if test="endTime != null">
            and A.TRANDATE <![CDATA[<=]]> #{endTime}
        </if>
        AND A.BANK_TAG IN ( 5 )
        AND A.MATCHED_AMOUNT != 0
        AND A.`STATUS` = 0
        and (A.IS_FEE = 0 or A.IS_FEE is null)
        <if test="beginTime == null and endTime == null">
            and PB.F_BILL_NO is null
            and A.TRANDATE >= '2023-01-01'
        </if>
        GROUP BY A.BANK_BILL_ID
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>

    <select id="getAliFeeBankBillByOrderNo" resultType="com.vedeng.erp.kingdee.batch.dto.BatchBankBillDto">
        SELECT A.AMT
        FROM
            T_BANK_BILL A
        WHERE A.BANK_TAG = 4 AND A.ORDER_NO = #{orderNo,jdbcType=VARCHAR}
          and A.IS_FEE = 1
        LIMIT 1
    </select>

    <select id="getAliFeeBankBillByCapitalSearchFlow" resultType="com.vedeng.erp.kingdee.batch.dto.BatchBankBillDto">
        SELECT A.AMT
        FROM
        T_BANK_BILL A
        WHERE A.BANK_TAG = 4 AND A.CAPITAL_SEARCH_FLOW = #{capitalSearchFlow,jdbcType=VARCHAR}
        and A.IS_FEE = 1
        LIMIT 1
    </select>

    <select id="getAliFeeByCapitalSearchFlow" resultType="com.vedeng.erp.kingdee.batch.dto.BatchBankBillDto">
        SELECT A.AMT
        FROM
        T_BANK_BILL A
        WHERE A.BANK_TAG = 4 AND A.CAPITAL_SEARCH_FLOW  = #{capitalSearchFlow,jdbcType=VARCHAR}
        and A.IS_FEE = 1
        LIMIT 1
    </select>

    <select id="getWeChatFeeBankBillByTranFlow" resultType="com.vedeng.erp.kingdee.batch.dto.BatchBankBillDto">
        SELECT A.AMT
        FROM
            T_BANK_BILL A
        WHERE A.BANK_TAG = 5 AND A.TRAN_FLOW = #{tranFlow,jdbcType=VARCHAR}
        LIMIT 1
    </select>

    <select id="queryNeedPushPaymentBankBillNew" resultMap="baseResultMap">
        select A.BANK_BILL_ID,
        A.BANK_TAG,
        A.TRAN_FLOW,
        A.TRANDATE,
        A.TRANTIME,
        A.REAL_TRANDATETIME,
        A.AMT,
        A.FLAG1,
        A.STATUS,
        A.MATCHED_OBJECT,
        A.RECEIPT_URL,
        A.HAVE_PUSHED_KINGDEE,
        A.ACC_NAME1,
        A.MATCHED_AMOUNT,
        D.BUSSINESS_TYPE,
        D.ORDER_TYPE,
        A.ACC_BANKNO,
        A.ACCNO2,
        A.CADBANK_NM,
        A.ACC_NAME1,
        D.ORDER_NO,
        C.TRADER_SUBJECT,
        C.CAPITAL_BILL_ID,
        C.AMOUNT,
        S.SETTLEMENT_METHOD
        from T_BANK_BILL A
        LEFT JOIN T_BANK_BILL_SETTLEMENT S ON A.BANK_BILL_ID = S.BANK_BILL_ID
        LEFT JOIN T_CAPITAL_BILL C ON A.BANK_BILL_ID = C.BANK_BILL_ID
        LEFT JOIN T_CAPITAL_BILL_DETAIL D ON C.CAPITAL_BILL_ID = D.CAPITAL_BILL_ID
        LEFT JOIN T_TRADER E ON D.TRADER_ID = E.TRADER_ID
        LEFT JOIN KING_DEE_PAY_BILL B ON CONCAT('C_', CAST(A.BANK_BILL_ID AS CHAR)) = B.F_BILL_NO
        where A.FLAG1 = 0
        AND A.BANK_TAG not in (4, 5)
        AND (A.IS_FEE = 0 OR A.IS_FEE IS NULL)
        AND A.AMT = A.MATCHED_AMOUNT
        AND A.STATUS = 0
        AND A.MATCHED_AMOUNT != 0
        AND A.TRANDATE >= '2023-01-01'
        AND B.F_BILL_NO is null
        AND C.TRADER_TYPE = 2
        AND C.CAPITAL_BILL_ID IS NOT NULL
        AND B.F_BILL_NO is null
        AND (
        (
        C.TRADER_MODE = 521
        <if test="needPushTraderNameList != null and needPushTraderNameList.size() != 0">
            AND E.TRADER_NAME IN
            <foreach collection="needPushTraderNameList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        )
        or
        C.TRADER_MODE = 10001
        )
        <if test="needPushTraderNameList == null or needPushTraderNameList.size() == 0">
            AND E.TRADER_NAME = ''
        </if>
        <if test="capitalBillBeginTime != null">
            AND C.TRADER_TIME <![CDATA[>=]]> #{capitalBillBeginTime}
        </if>
        <if test="capitalBillEndTime != null">
            AND C.TRADER_TIME <![CDATA[<=]]> #{capitalBillEndTime}
        </if>
        <if test="capitalBillBeginTime == null and capitalBillEndTime == null">
            AND C.TRADER_TIME >= *************
        </if>
        GROUP BY  A.BANK_BILL_ID
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>

    <select id="queryNeedPushPaymentWeChatAndAlipayNew" resultMap="baseResultMap">
        select A.BANK_BILL_ID,
        A.BANK_TAG,
        A.TRAN_FLOW,
        A.TRANDATE,
        A.TRANTIME,
        A.REAL_TRANDATETIME,
        A.AMT,
        A.FLAG1,
        A.STATUS,
        A.MATCHED_OBJECT,
        A.RECEIPT_URL,
        A.HAVE_PUSHED_KINGDEE,
        A.ACC_NAME1,
        A.MATCHED_AMOUNT,
        D.BUSSINESS_TYPE,
        D.ORDER_TYPE,
        A.ACC_BANKNO,
        A.ACCNO2,
        A.CADBANK_NM,
        A.ACC_NAME1,
        D.ORDER_NO,
        C.TRADER_SUBJECT,
        C.CAPITAL_BILL_ID,
        C.AMOUNT
        from T_BANK_BILL A
        LEFT JOIN T_CAPITAL_BILL C ON A.BANK_BILL_ID = C.BANK_BILL_ID
        LEFT JOIN T_CAPITAL_BILL_DETAIL D ON C.CAPITAL_BILL_ID = D.CAPITAL_BILL_ID
        LEFT JOIN T_TRADER E ON D.TRADER_ID = E.TRADER_ID
        LEFT JOIN KING_DEE_PAY_BILL B ON CONCAT('C_', CAST(A.BANK_BILL_ID AS CHAR)) = B.F_BILL_NO
        where A.FLAG1 = 0
        AND A.BANK_TAG in (4, 5)
        AND A.AMT = A.MATCHED_AMOUNT
        AND (A.IS_FEE = 0 OR A.IS_FEE IS NULL)
        AND A.BANK_BILL_ID IS NOT NULL
        AND A.STATUS = 0
        AND A.MATCHED_AMOUNT != 0
        AND A.TRANDATE >= '2023-01-01'
        AND B.F_BILL_NO is null
        AND C.TRADER_TYPE = 2
        AND C.TRADER_MODE in (520,522)
        <if test="capitalBillBeginTime != null">
            AND C.TRADER_TIME <![CDATA[>=]]> #{capitalBillBeginTime}
        </if>
        <if test="capitalBillEndTime != null">
            AND C.TRADER_TIME <![CDATA[<=]]> #{capitalBillEndTime}
        </if>
        <if test="capitalBillBeginTime == null and capitalBillEndTime == null">
            AND C.TRADER_TIME >= *************
        </if>
        GROUP BY  A.BANK_BILL_ID
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>
    <select id="queryIgnoredPayRefundBill" resultType="com.vedeng.erp.kingdee.batch.dto.BatchBankBillDto">
        SELECT
            tbb.BANK_BILL_ID,
            tbb.TRANDATE,
            tbb.REAL_TRANDATETIME,
            tbbir.CONTACT_UNIT,
            tbb.TRAN_FLOW,
            tbb.BANK_TAG,
            tbb.AMT,
            tbb.MATCHED_AMOUNT,
            tbb.CAPITAL_SEARCH_FLOW
        FROM
            T_BANK_BILL tbb
                LEFT JOIN T_BANK_BILL_IGNORE_RECORD tbbir ON
                tbb.BANK_BILL_ID = tbbir.BANK_BILL_ID
                LEFT JOIN KING_DEE_PAY_REFUND_BILL kdprb ON
                CONCAT('B_', CAST(tbb.BANK_BILL_ID AS CHAR)) = kdprb.F_BILL_NO
        WHERE
            tbb.FLAG1 = 1
          AND (tbb.IS_FEE = 0
            OR tbb.IS_FEE IS NULL)
          AND tbb.STATUS = 1
          AND tbb.TRANDATE >= '2023-01-01'
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>

    <select id="querySettledPayRefundBill" resultType="com.vedeng.erp.kingdee.batch.dto.BatchBankBillDto">
        select tbb.BANK_BILL_ID,
               tbb.TRANDATE,
               tbb.REAL_TRANDATETIME,
               tbb.TRAN_FLOW,
               tbb.BANK_TAG,
               tcb.CAPITAL_BILL_ID,
               tcb.AMOUNT,
               tcbd.ORDER_NO,
               tcbd.TRADER_ID,
               tcbd.RELATED_ID,
               tcbd.ORDER_TYPE,
               tbb.CAPITAL_SEARCH_FLOW
        from T_BANK_BILL tbb
                 left join T_CAPITAL_BILL tcb on tcb.BANK_BILL_ID = tbb.BANK_BILL_ID
                 left join T_CAPITAL_BILL_DETAIL tcbd on tcb.CAPITAL_BILL_ID = tcbd.CAPITAL_BILL_ID
                 left join KING_DEE_PAY_REFUND_BILL kdprb on CONCAT('C_', CAST(tbb.BANK_BILL_ID AS CHAR)) = kdprb.F_BILL_NO
        where tbb.FLAG1 = 1
          AND (tbb.IS_FEE = 0 OR tbb.IS_FEE IS NULL)
          AND tbb.STATUS = 0
          AND tbb.MATCHED_AMOUNT != 0
          AND tbb.TRANDATE >= '2023-01-01'
          AND tbb.AMT = tbb.MATCHED_AMOUNT
          AND tcb.TRADER_TYPE = 1
          AND tcb.TRADER_MODE in (520, 521, 522)
          AND tcb.CAPITAL_BILL_ID IS NOT NULL
          AND kdprb.F_BILL_NO is null
          AND tcb.TRADER_TIME >= *************
          AND tcbd.ORDER_TYPE in (3, 5)
        group by tbb.BANK_BILL_ID
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>

    <select id="queryNeedPushReceiptBankBillNew" resultMap="baseResultMap">
        select A.BANK_BILL_ID,
        A.BANK_TAG,
        A.TRAN_FLOW,
        A.TRANDATE,
        A.TRANTIME,
        A.REAL_TRANDATETIME,
        A.AMT,
        A.FLAG1,
        A.STATUS,
        A.MATCHED_OBJECT,
        A.RECEIPT_URL,
        A.ACC_NAME1,
        A.MATCHED_AMOUNT,
        A.ACC_BANKNO,
        A.ACCNO2,
        A.CADBANK_NM,
        A.ACC_NAME1,
        A.CAPITAL_SEARCH_FLOW,
        C.TRADER_SUBJECT,
        C.CAPITAL_BILL_ID,
        C.AMOUNT,
        D.BUSSINESS_TYPE,
        D.ORDER_NO,
        D.ORDER_TYPE,
        D.TRADER_ID
        from T_BANK_BILL A
        left join T_CAPITAL_BILL C on A.BANK_BILL_ID = C.BANK_BILL_ID
        left join T_CAPITAL_BILL_DETAIL D on C.CAPITAL_BILL_ID = D.CAPITAL_BILL_ID
        left join KING_DEE_RECEIVE_BILL B on CONCAT('C_', CAST(A.BANK_BILL_ID AS CHAR)) = B.F_BILL_NO
        where A.FLAG1 = 1
        AND (A.IS_FEE = 0 OR A.IS_FEE IS NULL)
        AND A.STATUS = 0
        AND A.MATCHED_AMOUNT != 0
        AND A.TRANDATE >= '2023-01-01'
        AND A.AMT = A.MATCHED_AMOUNT
        AND C.TRADER_TYPE = 1
        AND C.TRADER_MODE in (520, 521, 522)
        AND C.CAPITAL_BILL_ID IS NOT NULL
        AND B.F_BILL_NO is null
        <if test="capitalBillBeginTime != null">
            AND C.TRADER_TIME <![CDATA[>=]]> #{capitalBillBeginTime}
        </if>
        <if test="capitalBillEndTime != null">
            AND C.TRADER_TIME <![CDATA[<=]]> #{capitalBillEndTime}
        </if>
        <if test="capitalBillBeginTime == null and capitalBillEndTime == null">
            AND C.TRADER_TIME >= *************
        </if>
        group by A.BANK_BILL_ID
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>

    <select id="selectByPrimaryKey" resultMap="baseResultMap">
        select * from T_BANK_BILL where BANK_BILL_ID = #{bankBillId,jdbcType=INTEGER}
    </select>
    <select id="queryIgnoredReceiveRefundBill" resultType="com.vedeng.erp.kingdee.batch.dto.BatchBankBillDto">
        SELECT
            tbb.BANK_BILL_ID,
            tbb.TRANDATE,
            tbb.REAL_TRANDATETIME,
            tbbir.CONTACT_UNIT,
            tbb.TRAN_FLOW,
            tbb.BANK_TAG,
            tbb.AMT,
            tbb.ACCNO2,
            tbb.ACC_NAME1,
            tbb.CADBANK_NM,
            tbb.ACC_BANKNO,
            tbb.MATCHED_AMOUNT
        FROM
            T_BANK_BILL tbb
                LEFT JOIN T_BANK_BILL_IGNORE_RECORD tbbir ON
                tbb.BANK_BILL_ID = tbbir.BANK_BILL_ID
                LEFT JOIN KING_DEE_RECEIVE_REFUND_BILL kdrrb ON
                CONCAT('B_', CAST(tbb.BANK_BILL_ID AS CHAR)) = kdrrb.F_BILL_NO
        WHERE
            tbb.FLAG1 = 0
          AND (tbb.IS_FEE = 0
            OR tbb.IS_FEE IS NULL)
          AND tbb.STATUS = 1
          AND tbb.TRANDATE >= '2023-01-01'
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>
    <select id="querySettledReceiveRefundBill" resultType="com.vedeng.erp.kingdee.batch.dto.BatchBankBillDto">
        SELECT
            tcb.CAPITAL_BILL_ID,
            tbb.BANK_BILL_ID ,
            tbb.TRANDATE,
            tbb.REAL_TRANDATETIME,
            tcb.TRADER_SUBJECT,
            tbb.TRAN_FLOW,
            tbb.BANK_TAG,
            tcb.AMOUNT,
            tbb.ACCNO2,
            tbb.ACC_NAME1,
            tbb.CADBANK_NM,
            tbb.ACC_BANKNO,
            tcbd.ORDER_NO,
            tcbd.ORDER_TYPE,
            tcbd.TRADER_ID
        from T_BANK_BILL tbb
                 left join T_CAPITAL_BILL tcb on tcb.BANK_BILL_ID = tbb.BANK_BILL_ID
                 left join T_CAPITAL_BILL_DETAIL tcbd on tcb.CAPITAL_BILL_ID = tcbd.CAPITAL_BILL_ID
                LEFT JOIN KING_DEE_RECEIVE_REFUND_BILL kdrrb ON
                CONCAT('C_', CAST(tbb.BANK_BILL_ID AS CHAR)) = kdrrb.F_BILL_NO
        WHERE
            tbb.FLAG1 = 0
          AND tcb.TRADER_TYPE = 2
          AND (tbb.IS_FEE IS NULL
            OR tbb.IS_FEE = 0)
          AND tbb.STATUS = 0
          AND tbb.MATCHED_AMOUNT != 0
          AND tbb.AMT = tbb.MATCHED_AMOUNT
          AND tcb.CAPITAL_BILL_ID IS NOT NULL
          AND tcb.TRADER_MODE IN (520,521,522)
          AND tbb.BANK_TAG in (4,5)
          AND tcb.TRADER_TIME >= *************
          AND kdrrb.F_BILL_NO IS NULL
          AND tbb.TRANDATE >= '2023-01-01'
          GROUP BY tbb.BANK_BILL_ID
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>

    <select id="queryReceiveRefundBillFill" resultMap="baseResultMap">
        SELECT
        A.BANK_BILL_ID, A.BANK_TAG, A.TRAN_FLOW, A.TRANDATE, A.TRANTIME ,A.AMT, A.FLAG1, A.STATUS,A.MATCHED_OBJECT,A.RECEIPT_URL,A.RECEIPT_NAME,
        A.HAVE_PUSHED_KINGDEE,A.ACC_NAME1,A.MATCHED_AMOUNT,
        B.TITLE AS ignoreReason,D.CAPITAL_BILL_ID
        FROM T_BANK_BILL A
        LEFT JOIN T_SYS_OPTION_DEFINITION B ON A.MATCHED_OBJECT = B.SYS_OPTION_DEFINITION_ID
        left join T_CAPITAL_BILL D on A.BANK_BILL_ID = D.BANK_BILL_ID
        LEFT JOIN KING_DEE_RECEIVE_REFUND_BILL C on CONCAT('C_',A.BANK_BILL_ID) = C.F_BILL_NO
        WHERE
        A.FLAG1 = #{flag1,jdbcType=INTEGER}
        AND A.STATUS = 0
        AND A.MATCHED_AMOUNT != 0
        <if test="beginTime != null">
            and A.TRANDATE <![CDATA[>=]]> #{beginTime}
        </if>
        <if test="endTime != null">
            and A.TRANDATE <![CDATA[<=]]> #{endTime}
        </if>
        and C.F_BILL_NO is not null
        and A.TRANDATE >= '2023-01-01'
        and (A.IS_FEE = 0 or A.IS_FEE is null)
        and A.RECEIPT_URL is not null
        group by A.BANK_BILL_ID
        union
        SELECT
        A.BANK_BILL_ID, A.BANK_TAG, A.TRAN_FLOW, A.TRANDATE, A.TRANTIME ,A.AMT, A.FLAG1, A.STATUS,A.MATCHED_OBJECT,A.RECEIPT_URL,A.RECEIPT_NAME,
        A.HAVE_PUSHED_KINGDEE,A.ACC_NAME1,A.MATCHED_AMOUNT,
        B.TITLE AS ignoreReason,D.CAPITAL_BILL_ID
        FROM T_BANK_BILL A
        LEFT JOIN T_SYS_OPTION_DEFINITION B ON A.MATCHED_OBJECT = B.SYS_OPTION_DEFINITION_ID
        left join T_CAPITAL_BILL D on A.BANK_BILL_ID = D.BANK_BILL_ID
        LEFT JOIN KING_DEE_RECEIVE_REFUND_BILL C on CONCAT('B_',A.BANK_BILL_ID) = C.F_BILL_NO
        WHERE
        A.FLAG1 = #{flag1,jdbcType=INTEGER}
        <if test="beginTime != null">
            and A.TRANDATE <![CDATA[>=]]> #{beginTime}
        </if>
        <if test="endTime != null">
            and A.TRANDATE <![CDATA[<=]]> #{endTime}
        </if>
        and C.F_BILL_NO is not null
        and A.TRANDATE >= '2023-01-01'
        and (A.IS_FEE = 0 or A.IS_FEE is null)
        and A.RECEIPT_URL is not null
        group by A.BANK_BILL_ID
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>

    <select id="queryPayBillFill" resultMap="baseResultMap">
        SELECT
        A.BANK_BILL_ID, A.BANK_TAG, A.TRAN_FLOW, A.TRANDATE, A.TRANTIME ,A.AMT, A.FLAG1, A.STATUS,A.MATCHED_OBJECT,A.RECEIPT_URL,A.RECEIPT_NAME,
        A.HAVE_PUSHED_KINGDEE,A.ACC_NAME1,A.MATCHED_AMOUNT,
        B.TITLE AS ignoreReason,D.CAPITAL_BILL_ID
        FROM T_BANK_BILL A
        LEFT JOIN T_SYS_OPTION_DEFINITION B ON A.MATCHED_OBJECT = B.SYS_OPTION_DEFINITION_ID
        left join T_CAPITAL_BILL D on A.BANK_BILL_ID = D.BANK_BILL_ID
        LEFT JOIN KING_DEE_PAY_BILL C on CONCAT('C_',A.BANK_BILL_ID) = C.F_BILL_NO
        WHERE
        A.FLAG1 = #{flag1,jdbcType=INTEGER}
        AND A.STATUS = 0
        AND A.MATCHED_AMOUNT != 0
        <if test="beginTime != null">
            and A.TRANDATE <![CDATA[>=]]> #{beginTime}
        </if>
        <if test="endTime != null">
            and A.TRANDATE <![CDATA[<=]]> #{endTime}
        </if>
        and C.F_BILL_NO is not null
        and A.TRANDATE >= '2023-01-01'
        and (A.IS_FEE = 0 or A.IS_FEE is null)
        and A.RECEIPT_URL is not null
        group by A.BANK_BILL_ID
        union
        SELECT
        A.BANK_BILL_ID, A.BANK_TAG, A.TRAN_FLOW, A.TRANDATE, A.TRANTIME ,A.AMT, A.FLAG1, A.STATUS,A.MATCHED_OBJECT,A.RECEIPT_URL,A.RECEIPT_NAME,
        A.HAVE_PUSHED_KINGDEE,A.ACC_NAME1,A.MATCHED_AMOUNT,
        B.TITLE AS ignoreReason,D.CAPITAL_BILL_ID
        FROM T_BANK_BILL A
        LEFT JOIN T_SYS_OPTION_DEFINITION B ON A.MATCHED_OBJECT = B.SYS_OPTION_DEFINITION_ID
        left join T_CAPITAL_BILL D on A.BANK_BILL_ID = D.BANK_BILL_ID
        LEFT JOIN KING_DEE_PAY_BILL C on CONCAT('B_',A.BANK_BILL_ID) = C.F_BILL_NO
        WHERE
        A.FLAG1 = #{flag1,jdbcType=INTEGER}
        <if test="beginTime != null">
            and A.TRANDATE <![CDATA[>=]]> #{beginTime}
        </if>
        <if test="endTime != null">
            and A.TRANDATE <![CDATA[<=]]> #{endTime}
        </if>
        and C.F_BILL_NO is not null
        and A.TRANDATE >= '2023-01-01'
        and (A.IS_FEE = 0 or A.IS_FEE is null)
        and A.RECEIPT_URL is not null
        group by A.BANK_BILL_ID
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>

    <select id="queryPayRefundBillFill" resultMap="baseResultMap">
        SELECT
        A.BANK_BILL_ID, A.BANK_TAG, A.TRAN_FLOW, A.TRANDATE, A.TRANTIME ,A.AMT, A.FLAG1, A.STATUS,A.MATCHED_OBJECT,A.RECEIPT_URL,A.RECEIPT_NAME,
        A.HAVE_PUSHED_KINGDEE,A.ACC_NAME1,A.MATCHED_AMOUNT,
        B.TITLE AS ignoreReason,D.CAPITAL_BILL_ID
        FROM T_BANK_BILL A
        LEFT JOIN T_SYS_OPTION_DEFINITION B ON A.MATCHED_OBJECT = B.SYS_OPTION_DEFINITION_ID
        left join T_CAPITAL_BILL D on A.BANK_BILL_ID = D.BANK_BILL_ID
        LEFT JOIN KING_DEE_PAY_REFUND_BILL C on CONCAT('C_',A.BANK_BILL_ID) = C.F_BILL_NO
        WHERE
        A.FLAG1 = #{flag1,jdbcType=INTEGER}
        AND A.STATUS = 0
        AND A.MATCHED_AMOUNT != 0
        <if test="beginTime != null">
            and A.TRANDATE <![CDATA[>=]]> #{beginTime}
        </if>
        <if test="endTime != null">
            and A.TRANDATE <![CDATA[<=]]> #{endTime}
        </if>
        and C.F_BILL_NO is not null
        and A.TRANDATE >= '2023-01-01'
        and (A.IS_FEE = 0 or A.IS_FEE is null)
        and A.RECEIPT_URL is not null
        group by A.BANK_BILL_ID
        union
        SELECT
        A.BANK_BILL_ID, A.BANK_TAG, A.TRAN_FLOW, A.TRANDATE, A.TRANTIME ,A.AMT, A.FLAG1, A.STATUS,A.MATCHED_OBJECT,A.RECEIPT_URL,A.RECEIPT_NAME,
        A.HAVE_PUSHED_KINGDEE,A.ACC_NAME1,A.MATCHED_AMOUNT,
        B.TITLE AS ignoreReason,D.CAPITAL_BILL_ID
        FROM T_BANK_BILL A
        LEFT JOIN T_SYS_OPTION_DEFINITION B ON A.MATCHED_OBJECT = B.SYS_OPTION_DEFINITION_ID
        left join T_CAPITAL_BILL D on A.BANK_BILL_ID = D.BANK_BILL_ID
        LEFT JOIN KING_DEE_PAY_REFUND_BILL C on CONCAT('B_',A.BANK_BILL_ID) = C.F_BILL_NO
        WHERE
        A.FLAG1 = #{flag1,jdbcType=INTEGER}
        <if test="beginTime != null">
            and A.TRANDATE <![CDATA[>=]]> #{beginTime}
        </if>
        <if test="endTime != null">
            and A.TRANDATE <![CDATA[<=]]> #{endTime}
        </if>
        and C.F_BILL_NO is not null
        and A.TRANDATE >= '2023-01-01'
        and (A.IS_FEE = 0 or A.IS_FEE is null)
        and A.RECEIPT_URL is not null
        group by A.BANK_BILL_ID
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>
</mapper>
