<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:choose>
    <c:when test="${afterSalesVo.type eq 584}">
        <c:set var="title" value="售后详情-维修" scope="application"/>
        <c:set var="flag" value="wx"></c:set>
    </c:when>
    <c:otherwise>
        <c:set var="title" value="售后详情-安调" scope="application"/>
        <c:set var="flag" value="at"></c:set>
    </c:otherwise>
</c:choose>
<%@ include file="../../common/common.jsp" %>
<script type="text/javascript"
        src='<%= basePath %>static/js/orderstream/aftersales/view_afterSales.js?rnd=${resourceVersionKey}'></script>
<script src="<%= basePath %>static/js/orderstream/jquery.js"></script>
<script src="<%= basePath %>static/js/orderstream/afterSales_index.js?rnd=${resourceVersionKey}"></script>
<link rel="stylesheet" href="<%= basePath %>static/css/orderstream/style.css">
<script type="text/javascript" src='<%= basePath %>static/js/order/saleorder/saleorder_invoice_common.js?rnd=${resourceVersionKey}'></script>
<link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css" media="all">
<script src="/webjars/ezadmin/plugins/layui/layui.js" charset="utf-8"></script>
<script type="text/javascript">
    $(function () {
        var url = page_url + '/order/aftersalesUpgrade/viewAfterSalesDetail.do?afterSalesId=' + $("#afterSalesId").val();
        if ($(window.frameElement).attr('src').indexOf("viewAfterSalesDetail") < 0) {
            $(window.frameElement).attr('data-url', url);
        }
    });
</script>
<div class="main-container">

    <%--用户已点击过关闭-》完结字段改为关闭--%>
    <c:choose>
        <c:when test="${afterSalesVo.atferSalesStatus eq 3}">
            <div class="t-line-wrap J-line-wrap"
                 data-json='[
                 {"label":"确认","status":1},
                 {"label":"处理","status":1},
                 {"label":"关闭","status":1}]'>
            </div>
        </c:when>
        <c:otherwise>
            <div class="t-line-wrap J-line-wrap"
                 data-json='[
             {"label":"确认","status":${topStatusList.get(0)}},
             {"label":"处理","status":${topStatusList.get(1)}},
             {"label":"完结","status":${topStatusList.get(2)}}]'>
            </div>
        </c:otherwise>
    </c:choose>
        <input type="hidden" name="afterSalesVoJSon" id="afterSalesVoJSon" value="${afterSalesVoJSon}"/>
    <%--编辑订单-提交审核-申请完结-关闭订单 按钮--%>
    <div class="parts">
        <div class="table-buttons">
            <form action="" method="post" id="myform">
                <input type="hidden" name="afterSalesId" id="afterSalesId" value="${afterSalesVo.afterSalesId}"/>
                <input type="hidden" name="orderId" value="${afterSalesVo.orderId}"/>
                <input type="hidden" name="subjectType" value="${afterSalesVo.subjectType}"/>
                <input type="hidden" name="type" value="${afterSalesVo.type}"/>
                <input type="hidden" name="formToken" value="${formToken}"/>
                <input type="hidden" name="taskId" value="${taskInfo.id == null ? 0: taskInfo.id}"/>


                <%--售后单在待确认状态，申请人与售后处理人可点击，否则置灰--%>
                <c:choose>
                    <%--<c:when test="${afterSalesVo.atferSalesStatus eq 0 &&
                    (user.positType eq 310 || user.positType eq 312 || user.positType eq 589) &&
                     ((null==taskInfo and null==taskInfo.processInstanceId and endStatus != '审核完成') or (null!=taskInfo and taskInfo.assignee==null))}">--%>
                    <c:when test="${afterSalesVo.atferSalesStatus eq 0 &&
                 (user.positType eq 310 || user.positType eq 312 || user.positType eq 589) &&
                  ((null==taskInfo and null==taskInfo.processInstanceId and endStatus != '审核完成') or (null!=taskInfo and taskInfo.assignee==null))}">
                    <button type="button" class="bt-bg-style bg-light-blue bt-small" onclick="editAfterSales(1);">
                            编辑订单
                        </button>
                    </c:when>

                    <c:otherwise>
                        <button type="button" class="bt-bg-style bg-light-grey bt-small">
                            编辑订单
                        </button>
                    </c:otherwise>
                </c:choose>

                <%--售后单在待确认状态，售后处理人可点击，否则置灰--%>
                <c:choose>
                    <c:when test="${afterSalesVo.atferSalesStatus eq 0 &&
                 (user.positType eq 310 || user.positType eq 312 || user.positType eq 589) &&
                    (afterSalesVo.status eq 0 || afterSalesVo.status eq 3)}">
                        <button type="button" class="bt-bg-style bg-light-blue bt-small" onclick="applyAudit();">
                            提交审核
                        </button>
                    </c:when>
                    <c:otherwise>
                        <button type="button" class="bt-bg-style bg-light-grey bt-small">
                            提交审核
                        </button>
                    </c:otherwise>
                </c:choose>

                <c:choose>
                    <c:when test="${isSupply ne 1 && ((null!=taskInfo and null!=taskInfo.getProcessInstanceId() and null!=taskInfo.assignee) or !empty candidateUserMap[taskInfo.id]) &&
                            (taskInfo.assignee == user.username or isSpecialUser eq 1 or candidateUserMap['belong'])}">
                        <%--售后单在待确认、审核中状态下，审核节点对应审核账号可点击，否则置灰--%>
                        <button type="button" class="bt-bg-style bg-light-blue bt-small mr10 pop-new-data" layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"./complement.do?taskId=${taskInfo.id}&pass=true&type=2&saleorderId=${afterSalesVo.orderId}&afterSalesId=${afterSalesVo.afterSalesId}"}'>审核通过</button>
                        <button type="button" class="bt-bg-style bg-light-blue bt-small mr10 pop-new-data" layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"./complement.do?taskId=${taskInfo.id}&pass=false&type=2&afterSalesId=${afterSalesVo.afterSalesId}"}'>审核不通过</button>
                    </c:when>
                    <c:otherwise>
                        <button type="button" class="bt-bg-style bg-light-grey bt-small mr10">
                            审核通过
                        </button>
                        <button type="button" class="bt-bg-style bg-light-grey bt-small mr10">
                            审核不通过
                        </button>
                    </c:otherwise>
                </c:choose>

                <%--   售后单入库状态为“无入库/全部入库”、退票状态为“无退票/全部退票”退款状态为“无退款/全部退款”开票状态为“全部开票/无开票”，
                   且售后单未在完结审核流程中，此时售后处理人可点击，否则置灰--%>
                <c:choose>
                    <%--<c:when test="${(afterSalesVo.refundInvoiceStatus eq 0 || afterSalesVo.refundInvoiceStatus eq 3) &&
                    (user.positType eq 310 || user.positType eq 312 || user.positType eq 589) && afterSalesVo.atferSalesStatus eq 1 &&
                    ((null==taskInfoOver and null==taskInfoOver.getProcessInstanceId())or (null!=taskInfoOver and taskInfoOver.assignee==null and empty candidateUserMapOver[taskInfoOver.id]))}">--%>
                    <c:when test="${ (afterSalesVo.atferSalesStatus eq 1)
                              && not empty afterSalesVo.afterSalesInstallstionVoList
                              && (empty afterSalesVo.amountPayStatus || afterSalesVo.amountPayStatus eq 0 || afterSalesVo.amountPayStatus eq 3  )
                              && (empty afterSalesVo.amountCollectionStatus || afterSalesVo.amountCollectionStatus eq 0 || afterSalesVo.amountCollectionStatus eq 3  )
                              && (empty afterSalesVo.invoiceMakeoutStatus || afterSalesVo.invoiceMakeoutStatus eq 0 || afterSalesVo.invoiceMakeoutStatus eq 2  )
                              && ((null==taskInfoOver and null==taskInfoOver.getProcessInstanceId())or (null!=taskInfoOver and taskInfoOver.assignee==null and empty candidateUserMapOver[taskInfoOver.id]))
                              && (user.positType eq 312 ) }">
                        <c:choose>
                            <c:when test="${afterSalesVo.type eq 584}">
                                <button type="button" class="bt-bg-style bg-light-blue bt-small pop-new-data"
                                        layerParams='{"width":"780px","height":"310px","title":"选择售后原因","link":"./saleorderComplete.do?afterSalesId=${afterSalesVo.afterSalesId}&afterSalesType=758&type=${afterSalesVo.type}&orderId=${afterSalesVo.orderId }&subjectType=${afterSalesVo.subjectType }&formToken=${formToken }&traderId=${afterSalesVo.traderId }"}'>
                                    申请完结
                                </button>
                            </c:when>
                            <c:otherwise>
                                <button type="button" class="bt-bg-style bg-light-green bt-small" onclick="confirmComplete();">申请完结</button>
                            </c:otherwise>
                        </c:choose>
                    </c:when>
                    <c:otherwise>
                        <button type="button" class="bt-bg-style bg-light-grey bt-small">申请完结</button>
                    </c:otherwise>
                </c:choose>

                <c:choose>
                    <%--(user.positType eq 310 || user.positType eq 312 || user.positType eq 589)--%>
                   <%-- 1.待确认状态：未在订单审核流
                    2.进行中：未在付款审核流，收款为无付款，付款为未付款/无付款 ，开票为无开票， 未在完结关闭审核流，售后部门--%>
                    <c:when test="${((afterSalesVo.atferSalesStatus eq 0
                            && afterSalesVo.status ne 1
                    ) || (afterSalesVo.atferSalesStatus eq 1 && afterSalesVo.closeStatus eq 1
                    && (empty afterSalesVo.amountPayStatus || afterSalesVo.amountPayStatus eq 0 || afterSalesVo.amountPayStatus eq 1 )
                    && (empty afterSalesVo.amountCollectionStatus || afterSalesVo.amountCollectionStatus eq 0 )
                    && (empty afterSalesVo.invoiceMakeoutStatus || afterSalesVo.invoiceMakeoutStatus eq 0 )
                     && !((null!=taskInfoPay and null!=taskInfoPay.getProcessInstanceId() and null!=taskInfoPay.assignee) or !empty candidateUserMapPay[taskInfoPay.id])
                     &&  ((null==taskInfoOver and null==taskInfoOver.getProcessInstanceId())or (null!=taskInfoOver and taskInfoOver.assignee==null and empty candidateUserMapOver[taskInfoOver.id]))
                    ))  && afterSalesVo.atferSalesStatus ne 3 && afterSalesVo.atferSalesStatus ne 2
                    && (user.positType eq 312  || (afterSalesVo.atferSalesStatus eq 0 && user.userId eq afterSalesVo.creator))}">
                    <%--<c:when test="${afterSalesVo.status ne 1 && afterSalesVo.closeStatus eq 1 && afterSalesVo.atferSalesStatus ne 3 && afterSalesVo.atferSalesStatus ne 2 &&
                    (user.positType eq 312)&&
                    (null==taskInfoOver and null==taskInfoOver.getProcessInstanceId())or (null!=taskInfoOver and taskInfoOver.assignee==null and empty candidateUserMapOver[taskInfoOver.id])}">--%>
                        <button type="button" class="bt-bg-style bg-light-blue bt-small" onclick="colse();">
                            关闭订单
                        </button>
                    </c:when>
                    <c:otherwise>
                        <button type="button" class="bt-bg-style bg-light-grey bt-small">
                            关闭订单
                        </button>
                    </c:otherwise>
                </c:choose>

                <%--审核流且当前审核为当前登录者--%>
                <c:choose>
                    <c:when test="${((null!=taskInfoOver and null!=taskInfoOver.getProcessInstanceId() and null!=taskInfoOver.assignee) or !empty candidateUserMapOver[taskInfoOver.id])
                    && (taskInfoOver.assignee == user.username or candidateUserMapOver['belong'])}">
                        <button type="button" class="bt-bg-style bg-light-blue bt-small mr10 pop-new-data" layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"./complement.do?taskId=${taskInfoOver.id}&pass=true&type=2&sku=${sku}&orderId=${afterSalesVo.orderId}&afterSalesId=${afterSalesVo.afterSalesId}"}'>完结/关闭审核通过</button>
                        <button type="button" class="bt-bg-style bg-light-blue bt-small mr10 pop-new-data" layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"./complement.do?taskId=${taskInfoOver.id}&pass=false&type=2&afterSalesId=${afterSalesVo.afterSalesId}"}'>完结/关闭审核不通过</button>
                    </c:when>
                    <c:otherwise>
                        <button type="button" class="bt-bg-style bg-light-grey bt-small">
                            完结/关闭审核通过
                        </button>
                        <button type="button" class="bt-bg-style bg-light-grey bt-small">
                            完结/关闭审核不通过
                        </button>
                    </c:otherwise>
                </c:choose>

                <c:choose>
                    <%--<c:if test="${afterSalesVo.atferSalesStatus ne 3 && afterSalesVo.atferSalesStatus ne 2}">--%>
                    <%--<c:if test="${isSupply ne 1}">--%>
                    <%--<c:when test="${afterSalesVo.isCanApplyInvoice eq 1
                    && (user.positType eq 310 || user.positType eq 312 || user.positType eq 589)}">
                    </c:when>--%>
                    <%--权限变更,增加一个条件订单状态为“进行中”且未在完结关闭审核流中，权限只给到售后部门 --%>
                    <c:when test="${afterSalesVo.isCanApplyInvoice eq 1 && (afterSalesVo.atferSalesStatus eq 1 or afterSalesVo.atferSalesStatus eq 2)
                    && ((null==taskInfoOver and null==taskInfoOver.getProcessInstanceId())or (null!=taskInfoOver and taskInfoOver.assignee==null and empty candidateUserMapOver[taskInfoOver.id]))
                    && ( user.positType eq 312)}">
                        <button type="button" class="bt-bg-style bg-light-blue bt-small mr10" onclick="isTraderAllowInvoiceAfter(${afterSalesVo.afterSalesId},${afterSalesVo.invoiceType})">申请开票</button>
                        <span id="invoiceApplyAfter" class="pop-new-data-noclose" layerParams='{"width":"70%","height":"800px","title":"","link":"/invoice/invoiceApply/afterSale.do?afterSalesId=${afterSalesVo.afterSalesId}"}'></span>
                    </c:when>
<%--                    <c:when test="${afterSalesVo.isCanApplyInvoice eq 0 && afterSalesVo.serviceAmount gt 0 && afterSalesVo.isModifyServiceAmount eq 0}">--%>
<%--                        <button type="button" class="bt-bg-style bt-small bg-light-grey mr10">已申请开票</button>--%>
<%--                    </c:when>--%>
                    <c:otherwise>
                        <button type="button" class="bt-bg-style bg-light-grey bt-small">
                            申请开票
                        </button>
                    </c:otherwise>
                </c:choose>

                <c:if test="${afterSalesVo.isShowDispatchButton eq 1}">
                    <c:choose>
                        <c:when test="${medicalHelpCompany}">
                            <button type="button" class="bt-bg-style bg-light-blue bt-small"
                                    onclick="productSelectionDispatch();">
                                下派医修帮
                            </button>
                        </c:when>
                        <c:otherwise>
                            <button type="button" class="bt-bg-style bg-light-blue bt-small" onclick="warnDispatch();">
                                下派医修帮
                            </button>
                        </c:otherwise>
                    </c:choose>
                </c:if>
                <c:if test="${afterSalesVo.isShowDispatchButton eq 0 }">
                    <button type="button" class="bt-bg-style bg-light-blue bt-small" onclick="cancelDispatch();">
                        取消下派
                    </button>
                </c:if>

            </form>

        </div>
    </div>

    <%--基本信息模块--%>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                基本信息
            </div>
        </div>
        <table class="table">
            <tbody>
            <tr>
                <td class="wid20">售后单号</td>
                <td>${afterSalesVo.afterSalesNo}</td>
                <td class="wid20">售后类型</td>
                <td>${afterSalesVo.typeName}</td>
            </tr>

            <tr>
                <td class="wid20">订单状态</td>
                <td>
                    <c:if test="${afterSalesVo.atferSalesStatus eq 0}">待确认</c:if>
                    <c:if test="${afterSalesVo.atferSalesStatus eq 1}">进行中</c:if>
                    <c:if test="${afterSalesVo.atferSalesStatus eq 2}">已完结</c:if>
                    <c:if test="${afterSalesVo.atferSalesStatus eq 3}">已关闭</c:if>
                </td>

                <td>审核状态</td>
                <td>
                    <c:if test="${afterSalesVo.status eq 0}">待审核</c:if>
                    <c:if test="${afterSalesVo.status eq 1}">审核中</c:if>
                    <c:if test="${afterSalesVo.status eq 2}">审核通过</c:if>
                    <c:if test="${afterSalesVo.status eq 3}">审核不通过</c:if>
                </td>
            </tr>


            <tr>
                <td>生效状态</td>
                <td>
                    <c:if test="${afterSalesVo.validStatus eq 0}">未生效</c:if>
                    <c:if test="${afterSalesVo.validStatus eq 1}">已生效</c:if>
                </td>

                <td>售后处理人</td>
                <td>${afterSalesVo.serviceUserName}</td>
            </tr>


            <tr>
                <td>申请人</td>
                <td>${afterSalesVo.creatorName}</td>

                <td>申请时间</td>
                <td><date:date value="${afterSalesVo.addTime}"/></td>
            </tr>

            <tr>
                <td>生效时间</td>
                <td><date:date value="${afterSalesVo.validTime}"/></td>

                <td>完结时间</td>
                <td><c:if test="${afterSalesVo.atferSalesStatus == 2}">
                    <date:date value="${afterSalesVo.modTime}"/></c:if></td>
            </tr>

            <tr>
                <td>完结/关闭原因</td>
                <td>${afterSalesVo.afterSalesStatusResonName}</td>
                <td>完结/关闭人</td>
                <td>${afterSalesVo.afterSalesStatusUserName}</td>
            </tr>

            <tr>
                <td>完结/关闭备注</td>
                <td colspan="3" class="text-left">${afterSalesVo.afterSalesStatusComments}</td>
            </tr>
            </tbody>
        </table>
    </div>

    <%--售后信息模块--%>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                售后信息
            </div>
            <c:choose>
                <%--<c:when test="${afterSalesVo.status eq 0 && user.positType eq 312
                && ((null==taskInfoOver and null==taskInfoOver.getProcessInstanceId())or (null!=taskInfoOver and taskInfoOver.assignee==null and empty candidateUserMapOver[taskInfoOver.id]))}">--%>
                <c:when test="${(afterSalesVo.atferSalesStatus eq 0 || afterSalesVo.atferSalesStatus eq 1) && afterSalesVo.status ne 1 && (user.positType eq 310 || user.positType eq 312 || user.positType eq 589)}">
                    <div class="title-click  pop-new-data" layerParams='{"width":"700px","height":"480px","title":"编辑售后信息","link":"/order/afterSalesCommon/editAfterSalesInfo.do?afterSalesId=${afterSalesVo.afterSalesId}&&areaId=${afterSalesVo.areaId}&&type=${afterSalesVo.type}"}'>编辑售后信息</div>
                </c:when>
            </c:choose>
        </div>
        <table class="table">
            <tbody>
            <tr>
                <td class="wid20">售后原因</td>
                <td>${afterSalesVo.reasonName}</td>
                <td class="wid20">第一责任部门</td>
                <td>${afterSalesVo.firstResponsibleDepartmentStr}</td>
            </tr>

            <tr>
                <td class="wid20">售后联系人</td>
                <td>${afterSalesVo.afterConnectUserName}</td>

                <td>售后联系人电话</td>
                <td>
                    ${afterSalesVo.afterConnectPhone}
                    <c:if test="${not empty afterSalesVo.afterConnectPhone}">
                        <i class="icontel cursor-pointer" title="点击拨号"
                           onclick="callout('${afterSalesVo.afterConnectPhone}',${afterSalesVo.traderId},1,4,${afterSalesVo.afterSalesId},${afterSalesVo.traderContactId});"></i>
                    </c:if>
                </td>
            </tr>

            <tr>
                <td class="wid20">售后单报单人</td>
                <td>${afterSalesVo.traderContactName}</td>

                <td>售后单报单人电话</td>
                <td>
                    ${afterSalesVo.traderContactTelephone}
                    <c:if test="${not empty afterSalesVo.traderContactTelephone}">
                        <i class="icontel cursor-pointer" title="点击拨号"
                           onclick="callout('${afterSalesVo.traderContactTelephone}',${afterSalesVo.traderId},1,4,${afterSalesVo.afterSalesId},${afterSalesVo.traderContactId});"></i>
                    </c:if>
                </td>
            </tr>

            <tr>
                <td>售后报单人手机号</td>
                <td>
                    ${afterSalesVo.traderContactMobile}
                    <c:if test="${not empty afterSalesVo.traderContactMobile}">
                        <i class="icontel cursor-pointer" title="点击拨号"
                           onclick="callout('${afterSalesVo.traderContactMobile}',${afterSalesVo.traderId},1,4,${afterSalesVo.afterSalesId},${afterSalesVo.traderContactId});"></i>
                    </c:if>
                </td>
            </tr>
            <tr>
                <td>售后地区</td>
                <td>${afterSalesVo.area}</td>
                <td>售后地址</td>
                <td>${afterSalesVo.address}</td>
            </tr>
            <tr>
                <td>详情说明</td>

                <c:choose>
                    <c:when test="${not empty afterSalesVo.comments && afterSalesVo.comments.length() < 50}">
                        <td colspan="3" class="text-left" >
                                <div style="margin-left: 250px;">
                                        ${afterSalesVo.comments}
                                </div>
                        </td>
                    </c:when>
                    <c:otherwise>
                        <td colspan="3" class="text-left" >${afterSalesVo.comments}</td>
                    </c:otherwise>
                </c:choose>
            </tr>
            <tr>
                <td>附件</td>
                <td colspan="3" class="text-left">
                    <%@ include file="view_afterSales_files.jsp" %>
                </td>

            </tr>
            </tbody>
        </table>
    </div>

    <%--所属订单信息模块--%>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                所属订单信息
            </div>
        </div>
        <table class="table">
            <tbody>
            <tr>
                <td class="wid20">所属销售单号</td>
                <td>
                    <div class="customername pos_rel">
                           <span class="brand-color1 addtitle" style="float:none;"
                                 tabTitle='{"num":"viewsaleorder${afterSalesVo.orderNo}","title":"订单信息",
                                "link":"./orderstream/saleorder/detail.do?saleOrderId=${afterSalesVo.orderId}"}'>${afterSalesVo.orderNo}</span><i
                            class="iconbluemouth"></i>
                        <div class="pos_abs customernameshow" style="display: none;">
                            付款状态：<c:if test="${empty afterSalesVo.amountPayStatus || afterSalesVo.amountPayStatus eq 0}">未付款</c:if>
                            <c:if test="${afterSalesVo.amountPayStatus eq 1}">部分付款</c:if>
                            <c:if test="${afterSalesVo.amountPayStatus eq 2}">全部付款</c:if><br>
                            发货状态：<c:if test="${afterSalesVo.deliveryStatus eq 0}">未发货</c:if>
                            <c:if test="${afterSalesVo.deliveryStatus eq 1}">部分发货</c:if>
                            <c:if test="${afterSalesVo.deliveryStatus eq 2}">全部发货</c:if><br>
                            开票状态：
                            <c:if test="${empty afterSalesVo.invoiceMakeoutStatus ||afterSalesVo.invoiceMakeoutStatus eq 0}">
                                无开票
                            </c:if>
                            <c:if test="${afterSalesVo.invoiceMakeoutStatus eq 1}">未开票</c:if>

                            <c:if test="${afterSalesVo.invoiceMakeoutStatus eq 2}">全部开票</c:if>
                            <br>
                            收货状态：<c:if test="${afterSalesVo.arrivalStatus eq 0}">未收货</c:if>
                            <c:if test="${afterSalesVo.arrivalStatus eq 1}">部分收货</c:if>
                            <c:if test="${afterSalesVo.arrivalStatus eq 2}">全部收货</c:if>
                        </div>
                    </div>
                </td>
                <td class="wid20">销售单金额</td>
                <td><fmt:formatNumber type="number" value="${afterSalesVo.totalAmount}" pattern="0.00"
                                      maxFractionDigits="2"/></td>
            </tr>
            <tr>
                <td>归属销售</td>
                <td>${afterSalesVo.userName}</td>
                <td>归属部门</td>
                <td>${afterSalesVo.orgName}</td>
            </tr>
            <tr>
                <td>订单状态</td>
                <td><c:if test="${afterSalesVo.saleorderStatus eq 0}">未生效</c:if>
                    <c:if test="${afterSalesVo.saleorderStatus eq 1}">已生效</c:if></td>
                <td>生效时间</td>
                <td><date:date value="${afterSalesVo.saleorderValidTime}" /></td>
            </tr>
            <tr>
                <td>客户名称</td>
                <td>
                    <div class="customername pos_rel">
                              <span class="brand-color1 addtitle" style="float:none;"
                                    tabTitle='{"num":"viewcustomer${afterSalesVo.traderId}","title":"客户信息",
                                    "link":"./trader/customer/baseinfo.do?traderId=${afterSalesVo.traderId}"}'>${afterSalesVo.traderName}</span><i
                            class="iconbluemouth"></i>
                        <div class="pos_abs customernameshow" style="display: none;">
                            客户性质：<c:if test="${afterSalesVo.customerNature eq 465}">分销</c:if>
                            <c:if test="${afterSalesVo.customerNature eq 466}">终端</c:if><br>
                            交易次数：${afterSalesVo.orderCount}<br>
                            交易金额：<fmt:formatNumber type="number" value="${afterSalesVo.orderTotalAmount}" pattern="0.00"
                                                   maxFractionDigits="2"/><br>
                            上次交易日期：<date:date value="${afterSalesVo.lastOrderTime}"/>
                        </div>
                    </div>
                </td>
                <td>客户等级</td>
                <td>
                    ${afterSalesVo.customerLevelStr}
                </td>
            </tr>
            </tbody>
        </table>
    </div>

    <%--商品与工程师模块--%>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                商品与售后安调公司
            </div>
            <c:if test="${isSupply ne 1}">
                <c:if test="${afterSalesVo.atferSalesStatus eq 1 and null == taskInfoOver}">
                    <c:if test="${empty afterSalesVo.afterSalesInstallstionVoList}">
                        <div style="float:right;">
                            <a class="addtitle" style="margin-right: 10px;color: #3384ef;" href="javascript:void(0);" tabtitle='{"num":"viewsaleorder1","link":"/order/afterSalesCommon/addAfterSalesAtWxEngineer.do?afterSalesId=${afterSalesVo.afterSalesId}&&areaId=${afterSalesVo.areaId}&&subjectType=${afterSalesVo.subjectType}&&isAtwx=1","title":"新增售后安调公司"}'>新增售后安调公司</a>
                        </div>
                    </c:if>
                </c:if>
            </c:if>
        </div>
        <c:set var="isAtwx" value="1"/>
        <%@ include file="add_product_engineer_common.jsp"%>
    </div>

    <%--用户端售后状态模块--%>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                用户端售后状态
            </div>
            <c:choose>
                <c:when test="${afterSalesVo.validStatus ne 1 || afterSalesVo.clientStatus eq 3 || afterSalesVo.atferSalesStatus eq 2}">
                    <img id="questionMark" src="<%= basePath %>static/images/question_mark.png" style="width: 20px;float: right;height: 20px;line-height: 20px;" title="售后订单生效时方可推送前台状态,&quot;工程师已完工&quot;为最后节点"/>
                </c:when>
                <c:otherwise>
                    <c:if test="${afterSalesVo.clientStatus eq 0}">
                        <div type="button" class="title-click" onclick="updateClientStatus(${afterSalesVo.afterSalesId},${afterSalesVo.clientStatus},1);">"下派工程师"推送</div>
                    </c:if>
                    <c:if test="${afterSalesVo.clientStatus eq 1}">
                        <div type="button" class="title-click" onclick="updateClientStatus(${afterSalesVo.afterSalesId},${afterSalesVo.clientStatus},2);">"工程师预约"推送</div>
                    </c:if>
                    <c:if test="${afterSalesVo.clientStatus eq 2}">
                        <div type="button" class="title-click" onclick="updateClientStatus(${afterSalesVo.afterSalesId},${afterSalesVo.clientStatus},3);">"工程师完工"推送</div>
                    </c:if>
                </c:otherwise>
            </c:choose>
        </div>
        <%@ include file="afterSales_client_status.jsp"%>
    </div>

    <%--跟进记录模块--%>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                跟进记录
            </div>
            <c:choose>
                <c:when test="${afterSalesVo.status eq 2 && afterSalesVo.atferSalesStatus eq 1 && user.positType eq 312}">
                    <div class="title-click  pop-new-data" layerParams='{"width":"600px","height":"380px","title":"新增售后跟进记录","link":"/order/afterSalesCommon/addFollowUpRecord.do?afterSalesId=${afterSalesVo.afterSalesId}"}'>新增跟进记录</div>
                </c:when>
                <c:otherwise>
                    <button type="button" class="bt-bg-style bg-light-grey bt-small" style="float: right;">新增售后跟进记录</button>
                </c:otherwise>
            </c:choose>
        </div>
        <%@ include file="add_followUp_common.jsp"%>
    </div>

    <%--合同回传--%>
    <%--<c:if test="${afterSalesVo.atferSalesStatus eq 1 && (user.positType eq 310 || user.positType eq 312 || user.positType eq 589) &&
            ((null==taskInfoOver and null==taskInfoOver.getProcessInstanceId())or
            (null!=taskInfoOver and taskInfoOver.assignee==null and empty candidateUserMapOver[taskInfoOver.id]))}">--%>
        <div class="parts content1">
            <div class="title-container">
                <div class="table-title nobor">
                    合同回传
                </div>
                <c:choose>
                    <c:when test="${(afterSalesVo.atferSalesStatus eq 1 || afterSalesVo.atferSalesStatus eq 2) && (user.positType eq 310 || user.positType eq 312 || user.positType eq 589)}" >
                        <div class="title-click nobor pop-new-data" layerParams='{"width":"600px","height":"300px","title":"合同回传",
                					"link":"/order/afterSalesCommon/contractReturnInit.do?afterSalesId=${afterSalesVo.afterSalesId}"}'>
                            上传合同
                        </div>
                    </c:when>
                    <c:otherwise>
                        <button type="button" class="bt-bg-style bg-light-grey bt-small" style="float: right;">上传合同</button>
                    </c:otherwise>
                </c:choose>

            </div>
            <table class="table table-bordered table-striped table-condensed table-centered">
                <thead>
                <tr>
                    <th>合同</th>
                    <th class="table-small">操作人</th>
                    <th class="table-small">时间</th>
                    <th class="table-small">操作</th>
                </tr>
                </thead>
                <tbody>
                <c:forEach items="${afterSalesVo.afterContractAttachmentList}" var="list" varStatus="status">
                    <tr>
                        <td class="font-blue"><a href="http://${list.domain}${list.uri}"
                                                 target="_blank">${list.name}</a></td>
                        <td>${list.username}</td>
                        <td><date:date value="${list.addTime}"/></td>
                        <td>
                            <div class="caozuo">
                                <span class="caozuo-red" onclick="contractReturnDel(${list.attachmentId})">删除</span>
                            </div>
                        </td>
                    </tr>
                </c:forEach>
                <c:if test="${empty afterSalesVo.afterContractAttachmentList}">
                    <!-- 查询无结果弹出 -->
                    <tr>
                        <td colspan='4'>暂无记录！</td>
                    </tr>
                </c:if>
                </tbody>
            </table>
        </div>
    <%--</c:if>--%>

    <%--售后服务费信息模块--%>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">售后服务信息</div>
            <%--<c:if test="${ afterSalesVo.atferSalesStatus eq 1 || afterSalesVo.atferSalesStatus eq 1}">--%>
            <c:choose>
                <c:when test="${ afterSalesVo.atferSalesStatus eq 1 &&
                            (isEnableEditService eq 1) &&
                        ((null==taskInfoOver and null==taskInfoOver.getProcessInstanceId())or (null!=taskInfoOver and taskInfoOver.assignee==null and empty candidateUserMapOver[taskInfoOver.id]))
                             && (user.positType eq 312 )}">
                    <div class="title-click  pop-new-data" layerParams='{"width":"600px","height":"380px","title":"编辑售后服务费信息",
                    "link":"/order/afterSalesCommon/editInstallstionPage.do?afterSalesDetailId=${afterSalesVo.afterSalesDetailId}&&afterSalesId=${afterSalesVo.afterSalesId}&&serviceAmount=${afterSalesVo.serviceAmount}&&isSendInvoice=${afterSalesVo.isSendInvoice}&&invoiceType=${afterSalesVo.invoiceType}&flag=${flag}"}'>编辑售后服务费信息</div>
                </c:when>
                <c:otherwise>
                    <div class="title-click bg-light-grey bt-bg-style " style="color: white;" layerParams='{"width":"600px","height":"380px","title":"编辑售后服务费信息",
                    "link":""}'>编辑售后服务费信息</div>
                </c:otherwise>
            </c:choose>
         </div>
        <%@ include file="add_afterSales_service_fee_common.jsp"%>
    </div>

    <%--收款交易记录模块--%>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">收款交易记录</div>
        </div>
        <%@ include file="add_collection_transaction_common.jsp"%>
    </div>

    <%--付款申请记录模块--%>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">付款申请记录</div>
            <c:if test="${afterSalesVo.atferSalesStatus ne 3 && afterSalesVo.atferSalesStatus ne 2 && payStatus ne 0}">
                <c:if test="${isSupply ne 1}">
                    <%--<div class="title-click nobor addtitle" tabTitle='{"num":"viewafterSalesId<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
		                "link":"../../../order/afterSalesCommon/editAfterSalesPayApply.do?afterSalesId=${afterSalesVo.afterSalesId}&traderSubject=535","title":"新增付款申请"}'>新增付款申请</div>--%>
                    <c:if test="${afterSalesVo.status eq 2 && not empty afterSalesVo.afterSalesInstallstionVoList
                    && ((null==taskInfoOver and null==taskInfoOver.getProcessInstanceId())or (null!=taskInfoOver and taskInfoOver.assignee==null and empty candidateUserMapOver[taskInfoOver.id])) }">

                        <c:choose>
                            <c:when test="${ ((null!=taskInfoPay and null!=taskInfoPay.getProcessInstanceId() and null!=taskInfoPay.assignee) or !empty candidateUserMapPay[taskInfoPay.id])}">
                                <c:choose>
                                    <c:when test="${endStatusPay eq '财务制单' and (taskInfoPay.assignee eq curr_user.username or candidateUserMapPay['belong'])}"></c:when>
                                    <c:when test="${endStatusPay eq '财务审核' and (taskInfoPay.assignee eq curr_user.username or candidateUserMapPay['belong'])}"></c:when>
                                    <c:otherwise>
                                        <div class="title-click nobor addtitle" tabTitle='{"num":"viewafterSalesId<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
		                "link":"./order/afterSalesCommon/applyPayPage.do?afterSalesId=${afterSalesVo.afterSalesId}&traderSubject=535","title":"新增付款申请"}'>新增付款申请</div>
                                    </c:otherwise>
                                </c:choose>
                            </c:when>
                            <c:otherwise>
                                <div class="title-click nobor addtitle" tabTitle='{"num":"viewafterSalesId<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
		                "link":"./order/afterSalesCommon/applyPayPage.do?afterSalesId=${afterSalesVo.afterSalesId}&traderSubject=535","title":"新增付款申请"}'>新增付款申请</div>
                            </c:otherwise>
                        </c:choose>

                    </c:if>

                </c:if>
            </c:if>
        </div>
        <%@ include file="add_payment_request_common.jsp"%>
    </div>

    <%--付款交易记录模块--%>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">付款交易记录</div>
        </div>
        <%@ include file="add_payment_transaction_common.jsp"%>
    </div>

    <c:if test="${afterSalesVo.status eq 2}">
        <%--开票申请模块--%>
        <tags:aftersale_invoice_apply_info invoiceApplyList="${invoiceApplyList}"/>
    </c:if>

    <%--发票记录模块--%>
    <c:if test="${afterSalesVo.status eq 2}">
        <div class="parts">
            <div class="title-container">
                <div class="table-title nobor">发票记录</div>
                    <%--<div class="title-click nobor"  onclick="editAfterSaleOrder()">新增开票申请</div>--%>
            </div>
            <table class="table">
                <thead>
                <tr>
                    <td colspan="12">
                        <div style="float: left;margin-left: 30px;background: orange;clip-path: circle(50%); height: 1em; width: 1em;margin-top: 2px;">
                            <div style="background: white; width: 0.1em;height: 0.5em; margin: 25% 50%;"></div>
                        </div>
                        <div style="float: left; margin-left: 20px;">开票状态：
                            <c:if test="${empty afterSalesVo.invoiceMakeoutStatus || afterSalesVo.invoiceMakeoutStatus eq 0}">
                                无开票
                            </c:if>
                            <c:if test="${afterSalesVo.invoiceMakeoutStatus eq 1}">未开票</c:if>
                            <%--<c:if test="${afterSalesVo.invoiceMakeoutStatus eq 2}">部分开票</c:if>--%>
                            <c:if test="${afterSalesVo.invoiceMakeoutStatus eq 2}">全部开票</c:if>
                        </div>
                    </td>
                </tr>
                <tr>
                    <th>发票号</th>
                    <th>发票代码</th>
                    <th>票种</th>
                    <th>红蓝字</th>
                    <th>发票金额</th>
                    <th>开票日期</th>
                    <th>操作时间</th>
                    <th>操作人</th>
                    <th>快递公司</th>
                    <th>快递单号</th>
                    <th>快递状态</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                <c:if test="${not empty afterSalesVo.afterOpenInvoiceList}">
                    <c:forEach items="${afterSalesVo.afterOpenInvoiceList}" var="aoi">
                        <tr>
                            <td>${aoi.invoiceNo}</td>
                            <td>${aoi.invoiceCode}</td>
                            <td>
                                <c:if test="${aoi.invoiceType eq 429}">17%增值税专用发票</c:if>
                                <c:if test="${aoi.invoiceType eq 430}">17%增值税普通发票</c:if>
                                <c:if test="${aoi.invoiceType eq 682}">16%增值税专用发票</c:if>
                                <c:if test="${aoi.invoiceType eq 681}">16%增值税普通发票</c:if>
                                <c:if test="${aoi.invoiceType eq 972}">13%增值税专用发票</c:if>
                                <c:if test="${aoi.invoiceType eq 971}">13%增值税普通发票</c:if>
                                <c:if test="${aoi.invoiceType eq 683}">6%增值税普通发票</c:if>
                                <c:if test="${aoi.invoiceType eq 684}">6%增值税专用发票</c:if>
                                <c:if test="${aoi.invoiceType eq 685}">3%增值税普通发票</c:if>
                                <c:if test="${aoi.invoiceType eq 686}">3%增值税专用发票</c:if>
                                <c:if test="${aoi.invoiceType eq 687}">0%增值税普通发票</c:if>
                                <c:if test="${aoi.invoiceType eq 688}">0%增值税专用发票</c:if>
                            </td>

                            <td>
                                <c:choose>
                                    <c:when test="${aoi.colorType eq 1}">
                                        <c:choose>
                                            <c:when test="${aoi.isEnable eq 0}">
                                                <span style="color: red">红字作废</span>
                                            </c:when>
                                            <c:otherwise>
                                                <span style="color: red">红字有效</span>
                                            </c:otherwise>
                                        </c:choose>
                                    </c:when>
                                    <c:otherwise>
                                        <c:choose>
                                            <c:when test="${aoi.isEnable eq 0}">
                                                <span style="color: red">蓝字作废</span>
                                            </c:when>
                                            <c:otherwise>
                                                蓝字有效
                                            </c:otherwise>
                                        </c:choose>
                                    </c:otherwise>
                                </c:choose>
                            </td>
                            <td><fmt:formatNumber type="number" value="${aoi.amount}" pattern="0.00" maxFractionDigits="2" /></td>
                            <td><date:date value="${aoi.validTime}" /></td>
                            <td><date:date value="${aoi.addTime}" /></td>
                            <td>${aoi.creatorName}</td>
                            <td>${aoi.logisticsName}</td>
                            <td>${aoi.logisticsNo}</td>
                            <td>
                                <c:choose>
                                    <c:when test="${aoi.arrivalStatus eq 0}">
                                        未收货
                                    </c:when>
                                    <c:when test="${aoi.arrivalStatus eq 1}">
                                        部分收货
                                    </c:when>
                                    <c:when test="${aoi.arrivalStatus eq 2}">
                                        全部收货
                                    </c:when>
                                </c:choose>
                            </td>
                            <td>
                                <a href= "javascript:void(0);"
                                   style="color: rgb(51, 132, 239)"
                                   onclick="viewAndDownloadInvoice('${aoi.ossFileUrl}','${aoi.invoiceCode}',
                                           '${aoi.invoiceNo}','${aoi.invoiceId}')">查看发票</a>
                            </td>
                        </tr>
                    </c:forEach>
                </c:if>
                <c:if test="${empty afterSalesVo.afterOpenInvoiceList}">
                    <!-- 查询无结果弹出 -->
                    <tr>
                        <td colspan='12'>暂无记录！</td>
                    </tr>
                </c:if>
                </tbody>
            </table>
        </div>
    </c:if>


    <%--录票记录模块--%>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                录票记录
            </div>
        </div>
        <%@ include file="add_record_ticket_common.jsp"%>
    </div>

    <%--支出记录模块--%>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                支出记录
            </div>
            <c:choose>
                <c:when test="${afterSalesVo.atferSalesStatus eq 1 && user.positType eq 312}">
                    <div class="title-click  pop-new-data" layerParams='{"width":"600px","height":"380px","title":"新增售后支出记录","link":"/order/afterSalesCommon/addExpenditureRecord.do?afterSalesId=${afterSalesVo.afterSalesId}"}'>新增支出记录</div>
                </c:when>
                <c:otherwise>
                    <button type="button" class="bt-bg-style bg-light-grey bt-small" style="float: right;">新增售后支出记录</button>
                </c:otherwise>
            </c:choose>
        </div>
        <%@ include file="add_expenditure_common.jsp"%>
    </div>

    <%--收入记录模块--%>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                收入记录
            </div>
            <c:choose>
                <c:when test="${afterSalesVo.atferSalesStatus eq 1 && user.positType eq 312}">
                    <div class="title-click  pop-new-data" layerParams='{"width":"600px","height":"380px","title":"新增售后收入记录","link":"/order/afterSalesCommon/addRevenueRecord.do?afterSalesId=${afterSalesVo.afterSalesId}"}'>新增收入记录</div>
                </c:when>
                <c:otherwise>
                    <button type="button" class="bt-bg-style bg-light-grey bt-small" style="float: right;">新增售后收入记录</button>
                </c:otherwise>
            </c:choose>
        </div>
        <%@ include file="add_revenue_common.jsp"%>
    </div>

    <!-- 沟通记录只能让售后看 -->
    <c:if test="${sessionScope.curr_user.positType eq 312}">
        <div class="parts">
            <div class="title-container">
                <div class="table-title nobor">
                    沟通记录
                </div>
                <c:if test="${afterSalesVo.status eq 2}">
                    <div class="title-click nobor  pop-new-data" layerParams='{"width":"850px","height":"460px","title":"新增沟通记录",
                        "link":"<%= basePath %>/aftersales/order/addCommunicatePage.do?afterSalesId=${afterSalesVo.afterSalesId}&&traderId=${afterSalesVo.traderId }&&traderType=1"}'>
                        新增
                    </div>
                </c:if>
            </div>
            <table class="table table-bordered table-striped table-condensed table-centered">
                <thead>
                <tr>
                    <th class="wid10">沟通时间</th>
                    <th class="">录音</th>
                    <th class="">联系人</th>
                    <th class="">联系方式</th>
                    <th class="">沟通方式</th>
                    <th class="wid30">沟通内容（AI分析整理）</th>
                    <th class="">操作人</th>
                    <th class="wid8">下次联系日期</th>
                    <th class="wid15">下次沟通内容</th>
                    <th class="">备注</th>
                    <th class="wid10">创建时间</th>
                    <th class="wid6">操作</th>
                </tr>
                </thead>
                <tbody>
                <c:if test="${not empty communicateList}">
                    <c:forEach items="${communicateList}" var="communicateRecord" varStatus="">
                        <tr>
                            <td><date:date value="${communicateRecord.begintime} "/>~<date:date
                                    value="${communicateRecord.endtime}" format="HH:mm:ss"/></td>
                            <td><c:if
                                    test="${not empty communicateRecord.coidUri }">${communicateRecord.communicateRecordId }</c:if></td>
                            <td>${communicateRecord.contactName}</td>
                            <td>${communicateRecord.phone}</td>
                            <td>${communicateRecord.communicateModeName}</td>
                            <td>
                                <ul class="communicatecontent ml0">
                                    <c:choose>
                                        <c:when test="${not empty communicateRecord.tag }">
                                            <c:forEach items="${communicateRecord.tag }" var="tag">
                                                <li class="bluetag" title="${tag.tagName}">${tag.tagName}</li>
                                            </c:forEach>
                                        </c:when>
                                        <c:otherwise>
                                            <li>${communicateRecord.contactContent }</li>
                                        </c:otherwise>
                                    </c:choose>
                                </ul>
                            </td>
                            <td>${communicateRecord.user.username}</td>
                            <c:choose>
                                <c:when test="${communicateRecord.isDone == 0 }">
                                    <td class="font-red">${communicateRecord.nextContactDate }</td>
                                </c:when>
                                <c:otherwise>
                                    <td>${communicateRecord.nextContactDate }</td>
                                </c:otherwise>
                            </c:choose>
                            <td>${communicateRecord.nextContactContent}</td>
                            <td>${communicateRecord.comments}</td>
                            <td><date:date value="${communicateRecord.addTime} "/></td>

                            <td class="caozuo">
                                <c:if test="${isSupply ne 1}">
                                    <c:if test="${afterSalesVo.atferSalesStatus ne 3 && afterSalesVo.atferSalesStatus ne 2 && afterSalesVo.verifyStatus ne 0}">
                                <span class="border-blue pop-new-data" layerParams='{"width":"60%","height":"63%","title":"编辑沟通记录",
                                    "link":"<%= basePath %>/aftersales/order/editcommunicate.do?orderFlag=${afterSalesVo.atferSalesStatus }&flag=${afterSalesVo.status }&communicateRecordId=${communicateRecord.communicateRecordId}&afterSalesId=${afterSalesVo.afterSalesId}&&traderId=${afterSalesVo.traderId }&&traderType=1"}'>编辑</span>
                                    </c:if>
                                </c:if>
                            </td>
                        </tr>
                    </c:forEach>
                </c:if>
                <c:if test="${empty communicateList}">
                    <!-- 查询无结果弹出 -->
                    <tr>
                        <td colspan='12'>暂无记录！</td>
                    </tr>
                </c:if>
                </tbody>
            </table>
        </div>
    </c:if>

    <%--回访记录模块--%>
    <div class="parts">
        <%@ include file="afterSales_return_visit.jsp"%>
    </div>

    <%--安调服务记录模块--%>
    <c:if test="${afterSalesVo.type ne 584}">
        <div class="parts">
            <%@ include file="afterSales_install_service_record.jsp"%>
        </div>
    </c:if>

    <%--各项审核记录模块--%>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                各项审核记录
            </div>
        </div>
        <%@ include file="add_auditRecords_common.jsp"%>
    </div>
</div>
