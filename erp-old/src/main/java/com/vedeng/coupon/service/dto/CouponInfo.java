package com.vedeng.coupon.service.dto;

import java.math.BigDecimal;

public class CouponInfo {

    private String couponId;

    private String couponCode;

    private BigDecimal denomination;

    private BigDecimal useThreshold;

    private String limitTypeStr;

    private String effevtiveStartTime;

    private String effevtiveEndTime;

    public String getCouponId() {
        return couponId;
    }

    public void setCouponId(String couponId) {
        this.couponId = couponId;
    }

    public String getCouponCode() {
        return couponCode;
    }

    public void setCouponCode(String couponCode) {
        this.couponCode = couponCode;
    }

    public BigDecimal getDenomination() {
        return denomination;
    }

    public void setDenomination(BigDecimal denomination) {
        this.denomination = denomination;
    }

    public BigDecimal getUseThreshold() {
        return useThreshold;
    }

    public void setUseThreshold(BigDecimal useThreshold) {
        this.useThreshold = useThreshold;
    }

    public String getLimitTypeStr() {
        return limitTypeStr;
    }

    public void setLimitTypeStr(String limitTypeStr) {
        this.limitTypeStr = limitTypeStr;
    }

    public String getEffevtiveStartTime() {
        return effevtiveStartTime;
    }

    public void setEffevtiveStartTime(String effevtiveStartTime) {
        this.effevtiveStartTime = effevtiveStartTime;
    }

    public String getEffevtiveEndTime() {
        return effevtiveEndTime;
    }

    public void setEffevtiveEndTime(String effevtiveEndTime) {
        this.effevtiveEndTime = effevtiveEndTime;
    }
}
