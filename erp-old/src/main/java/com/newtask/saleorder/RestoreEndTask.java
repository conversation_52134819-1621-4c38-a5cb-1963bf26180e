package com.newtask.saleorder;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.http.HttpClientUtils;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.util.StringUtil;
import com.vedeng.order.dao.SaleorderMapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@JobHandler(value = "RestoreEndTask")
@Component
public class RestoreEndTask extends AbstractJobHandler {

    Logger logger = LoggerFactory.getLogger(RestoreEndTask.class);

    public static final long ONE_WEEK = 7 * 24 * 60 * 60 * 1000;

    public static final int BATCH_SIZE = 200;

    @Value("${http_url}")
    protected String httpUrl;

    @Value("${client_id}")
    protected String clientId;

    @Value("${client_key}")
    protected String clientKey;

    @Autowired
    private SaleorderMapper saleorderMapper;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        if(StringUtil.isNotBlank(param)){
            if (param.startsWith("restoreAll")) {
                String[] split = param.split(";");
                Integer minId = Integer.parseInt(split[1]);
                Integer limit = Integer.parseInt(split[2]);
                for (;;){
                    logger.info("restoreAll批量完结销售订单minId:{},limit:{}", minId, limit);
                    List<Integer> ids = saleorderMapper.getAllSaleOrderIds(minId, limit);
                    if (CollUtil.isEmpty(ids)) {
                        logger.info("restoreAll批量完结销售订单结束");
                        break;
                    }
                    restore(ids);
                    minId = ids.get(ids.size() - 1);
                }
                return SUCCESS;
            }else{
                String[] saleOrderIds = param.split(",");
                List<Integer> saleOrderIdList = Arrays.stream(saleOrderIds).map(Integer::parseInt).collect(Collectors.toList());
                logger.info("批量完结销售订单,saleOrderList:{}", JSONUtil.toJsonStr(saleOrderIdList));
                restore(saleOrderIdList);
                return SUCCESS;
            }
        }


        long lastWeek = System.currentTimeMillis() - ONE_WEEK;
        String lastWeekDate = DateFormatUtils.format(new Date(lastWeek), "yyyy-MM-dd HH:mm:ss");
        List<Integer> lastWeekSaleOrderIds = saleorderMapper.getLastWeekIdsByUpdateDateTime(lastWeekDate);
        if (lastWeekSaleOrderIds.size() > 0) {
            restore(lastWeekSaleOrderIds);
        }
        return SUCCESS;
    }

    private void restore(List<Integer> lastWeekSaleOrderIds) {
        if (CollectionUtils.isNotEmpty(lastWeekSaleOrderIds)) {
            if (lastWeekSaleOrderIds.size() > BATCH_SIZE) {
                List<List<Integer>> lists = Lists.partition(lastWeekSaleOrderIds, BATCH_SIZE); // 分批处理
                for (List<Integer> list : lists) {
                    addRestore(list);
                }
            } else {
                addRestore(lastWeekSaleOrderIds);
            }

        }

    }

    private void addRestore(List<Integer> lastWeekSaleOrderIds) {
        ResultInfo<?> result = null;
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<Object>> TypeRef = new TypeReference<ResultInfo<Object>>() {
        };
        String url = httpUrl + "order/saleorder/restoreSaleOrderIsEnd.htm";
        try {
            result = (ResultInfo<?>) HttpClientUtils.post(url, lastWeekSaleOrderIds, clientId, clientKey, TypeRef);
        } catch (IOException e) {
            logger.error("", e);
        }

    }


}
