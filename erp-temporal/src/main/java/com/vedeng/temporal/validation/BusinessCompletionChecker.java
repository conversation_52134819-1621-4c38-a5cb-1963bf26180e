package com.vedeng.temporal.validation;

import com.vedeng.temporal.domain.dto.PollingRequest;

import java.util.Map;

/**
 * 业务完成检查器函数式接口
 * 
 * 用于自定义业务完成判断逻辑，支持任意复杂的验证条件。
 * 这是一个极简的解决方案，避免了复杂的配置和验证器框架。
 * 
 * 使用示例：
 * <pre>
 * // 简单状态检查
 * BusinessCompletionChecker checker = (response, request) -> 
 *     "APPROVED".equals(response.get("auditStatus"));
 * 
 * // 复杂条件组合
 * BusinessCompletionChecker checker = (response, request) -> {
 *     String status = (String) response.get("auditStatus");
 *     Double paidAmount = (Double) response.get("paidAmount");
 *     Double totalAmount = (Double) response.get("totalAmount");
 *     
 *     return "APPROVED".equals(status) && 
 *            paidAmount != null && totalAmount != null && 
 *            paidAmount >= totalAmount;
 * };
 * 
 * // 时间条件检查
 * BusinessCompletionChecker checker = (response, request) -> {
 *     String createTimeStr = (String) response.get("createTime");
 *     if (createTimeStr == null) return false;
 *     
 *     try {
 *         LocalDateTime createTime = LocalDateTime.parse(createTimeStr);
 *         return createTime.isAfter(LocalDateTime.now().minusDays(7));
 *     } catch (Exception e) {
 *         return false;
 *     }
 * };
 * </pre>
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-07
 */
@FunctionalInterface
public interface BusinessCompletionChecker {
    
    /**
     * 检查业务是否已完成
     * 
     * @param apiResponse API响应数据，包含业务单据的所有字段信息
     * @param request 原始的业务状态查询请求，包含业务上下文信息
     * @return true 如果业务已完成，false 如果业务未完成
     * @throws RuntimeException 如果检查过程中发生异常
     */
    boolean isCompleted(Map<String, Object> apiResponse, PollingRequest request);
    
    /**
     * 组合多个检查器 - AND 逻辑
     * 所有检查器都返回 true 时才返回 true
     * 
     * @param other 另一个检查器
     * @return 组合后的检查器
     */
    default BusinessCompletionChecker and(BusinessCompletionChecker other) {
        return (response, request) -> 
            this.isCompleted(response, request) && other.isCompleted(response, request);
    }
    
    /**
     * 组合多个检查器 - OR 逻辑
     * 任一检查器返回 true 时就返回 true
     * 
     * @param other 另一个检查器
     * @return 组合后的检查器
     */
    default BusinessCompletionChecker or(BusinessCompletionChecker other) {
        return (response, request) -> 
            this.isCompleted(response, request) || other.isCompleted(response, request);
    }
    
    /**
     * 取反检查器
     * 
     * @return 取反后的检查器
     */
    default BusinessCompletionChecker negate() {
        return (response, request) -> !this.isCompleted(response, request);
    }
    

}
