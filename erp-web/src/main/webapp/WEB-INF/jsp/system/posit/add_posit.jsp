<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript" src='<%= basePath %>static/js/posit/add_posit.js?rnd=${resourceVersionKey}'></script>
<script src="/webjars/ezadmin/plugins/layui/layui.js"></script>
<link href="/webjars/ezadmin/plugins/layui/css/layui.css?v=2.7.6" rel="stylesheet">
<link href="/webjars/ezadmin/plugins/viewer/viewer.min.css" rel="stylesheet">
<%--
<link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
--%>
<link href="/webjars/ezadmin/layui/css/ezlist.css?v=1711100573488" rel="stylesheet">
<link href="/webjars/ezadmin/plugins/cascader/cascader.css" rel="stylesheet">
<script src="/webjars/ezadmin/plugins/cascader/cascader.js?1=1" type="text/javascript" ></script>
<script src="/webjars/ezadmin/plugins/pingyin/pinyin_dict_firstletter.js" ></script>
<script src="/webjars/ezadmin/plugins/pingyin/pinyin_dict_notone.js" ></script>
<script src="/webjars/ezadmin/plugins/pingyin/pinyinUtil.js" ></script>

<style>
	.el-input__inner{height:30px ;line-height:30px;padding-left: 5px;}
	.el-cascader{
		width: 400px;
	}
	li i{
		height: auto;
		background: none;
	}

</style>
<div class="addElement">
	<form action="" method="post" id="addPositForm">
		<ul class="add-detail">
			<li>
				<div class="infor_name">
					<span>*</span> 
					<lable for='positionName'>职位名称</lable>
				</div>
				<div class="f_left">
					<input type="text" name='positionName' id='positionName' class="input-middle" />
				</div>
				<div class="clear"></div>
			</li>
			<li>
				<div class="infor_name">
					<span>*</span> 
					<lable for='orgId'>所属部门</lable>
				</div>
				<div class="f_left">
					<input value="" type="hidden" name="orgId" id="orgId" class="hidden-reset ez-laycascader" ez_url="/system/org/orgList.do" ez_value="orgId" ez_label="orgName" ez_children="child" autocomplete="off">

				<%--<select name="orgId" id="orgId" class="input-middle">
						<option value="0">请选择</option>
						<c:forEach items="${orgList }" var="org">
							<option value="${org.orgId }">
							${org.orgNames}
							</option>
						</c:forEach>
					</select>--%>
				</div>
				<div class="clear"></div>
			</li>
			<li>
				<div class="infor_name">
					<span>*</span>
					<lable for='positionName'>职位级别</lable>
				</div>
				<div class="f_left">
					<select id="level" name="level" class="input-middle">
						<option value="">请选择</option>
						<c:forEach var="list" items="${positLevelList}">
							<option value="${list.sysOptionDefinitionId}">${list.title}</option>
						</c:forEach>
					</select>
				</div>
				<div class="clear"></div>
			</li>
			<li>
				<div class="infor_name mt0">
					<lable for='positionName'>职位类型</lable>
				</div>
				<div class="f_left">	
					<select id="level" name="type" class="input-middle">
						<option value="0">无</option>
						<c:forEach var="list" items="${listType}">
							<option value="${list.sysOptionDefinitionId}">${list.title}</option>
						</c:forEach>
					</select>			
				</div>
				<div class="clear"></div>
			</li>
			<div class="clear"></div>
		</ul>
		<div class="add-tijiao tcenter">
			<input type="hidden" name="formToken" value="${formToken}"/>
			<button type="submit">提交</button>
			<button type="button" class="dele" id="cancle">取消</button>
		</div>
	</form>
</div>
<script  type="text/javascript" >
	$(document).ready(function() {
		$(".ez-laycascader").each(function(){
			var _this=$(this);
			renderCascader(_this);
		})
	})
	function istrue(c){
		return  (c||'true')=='true'||(c||'1')=='1';
	}
	function createParentChildStructure(data) {
		// 解析 JSON 字符串
		// const data = JSON.parse(jsonData);

		// 创建一个字典来保存所有节点，按照它们的 K 值索引
		const dataMap = data.reduce((map, node) => {
			map[node.orgId] = { ...node, child: [] };
		return map;
	}, {});
		// 最终的父节点数组
		const result = [];

		// 遍历所有节点，构建父子关系
		data.forEach(node => {
			// 如果 level 值为 "3"，则是顶层节点，添加到结果数组中
			if (node.level === "3" || node.level == 3) {
			result.push(dataMap[node.orgId]);
		} else if (node.parentId in dataMap) {
			// 如果 P 值在 dataMap 中，则找到其父节点，并将当前节点添加为子节点
			dataMap[node.parentId].child.push(dataMap[node.orgId]);
		}
	});

		return result;
	}
	function renderCascader(cas){
		layui.use('layCascader', function () {
			try {
				var layCascader = layui.layCascader;
				var _this = $(cas);
				var url = _this.attr("ez_url");
				var ezurl = _this.attr("ezlist_url");
				var value = _this.attr("ez_value") || 'VALUE';
				var label = _this.attr("ez_label") || 'LABEL';
				var children = _this.attr("ez_children") || 'CHILDREN';
				var multiple = istrue(_this.attr("multi"));
				var itemsJson = _this.attr("itemsJson");
				// debugger
				var itemPlaceholder = _this.attr("placeholder") || '请选择';
				var paramValue = [] ;
				if(_this.val().length >0){
					paramValue=parseInt(_this.val());
				}else{

				}
				//("["+_this.val()+"]"):'[]';
				var collapseTags = istrue(_this.attr("collapsetags"));
				var showAllLevels = istrue(_this.attr("showalllevels"));

				$.get(url || ezurl, function (data) {
					var jsonData = JSON.parse(data);
					// var res = data.data;
					//console.log(jsonData.data);
					var res  = createParentChildStructure(jsonData.data);
					var prop = {};
					prop.value = value;
					prop.label = label;
					prop.children = children;
					prop.multiple = false;
					prop.checkStrictly = true;

					//console.log(prop);
					if (ezurl) {
						res = flatToTree(res, 0);
					}
					var org = layCascader({
						name:"orgId",
						elem: _this[0],
						props: prop,
						filterable: true,
						filterMethod: function (node, val) {//重写搜索方法。
							if (val == node.data[label]) {//把value相同的搜索出来

								return true;
							}
							if ((node.data[label] + node.data[label]).indexOf(val) != -1) {//名称中包含的搜索出来
								return true;
							}
							//  console.log(node.data.orgName+node.data.orgNames+'##'+(node.data.orgId+'').indexOf(val));
							return !ezpingyin(val, (node.data[label] + node.data[label]), (node.data[value] + ''));
						},
						clearable: true,
						placeholder: itemPlaceholder,
						collapseTags: collapseTags,
						showAllLevels: showAllLevels,
						value: paramValue,
						options: res,
						showLastLevels: false, // 显示完整路径
						multiple: false // 单选模式
					});
					org.change(function (value, node) {
						//layer.msg('value:' + value + ',Node:' + JSON.stringify(node.data));
						$("#orgId").val(value);
					});
				})

			}catch (e) {
				console.log(e);
			}

		})

	}

</script>
