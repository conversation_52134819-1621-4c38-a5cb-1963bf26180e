package com.vedeng.erp.trader.web.api;

import cn.hutool.core.collection.CollUtil;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.trader.service.TraderPaidAccountService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 交易者已交易账号
 *
 * <AUTHOR>
 */
@ExceptionController
@RestController
@RequestMapping("/traderPaidAccount")
@Slf4j
public class TraderPaidAccountApi {

    @Autowired
    private TraderPaidAccountService traderPaidAccountService;

    /**
     * 导入
     */
    @RequestMapping(value = "/import", method = RequestMethod.POST)
    public R<?> importExcel(@RequestPart("file") MultipartFile file) throws Exception {
        try {
            traderPaidAccountService.importFile(file);
        } catch (ServiceException e) {
            throw new ServiceException(e.getMessage());
        } catch (Exception e) {
            throw new ServiceException("上传失败，请检查文件");
        }
        return R.success();
    }


    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    public R<?> delete(@RequestParam Integer id) {
        traderPaidAccountService.delete(CollUtil.newHashSet(id));
        return R.success();
    }


}

