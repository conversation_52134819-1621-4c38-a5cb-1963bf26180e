package com.vedeng.erp.trader.service;

import com.vedeng.erp.trader.domain.dto.QuoteorderDto;
import com.vedeng.erp.trader.domain.entity.AuthorizationApiApply;
import com.vedeng.erp.trader.domain.entity.QuoteorderEntity;

/**
 * <AUTHOR>
 * @description 报价单接口
 * @date 2022/7/21 18:44
 **/
public interface QuoteOrderService {

    /**
     * 判断报价单是否已存在
     * @param bussinessChanceId 商机id
     * @return 报价单id
     */
    Integer selectQuoteorderIdByBusinessChanceId(Integer bussinessChanceId);

    /**
     * 保存报价单
     * @param quoteorderDto 对象
     * @return QuoteorderDto
     */
    QuoteorderDto saveQuoteOrder(QuoteorderDto quoteorderDto);

    /**
     * 根据报价单号，获取报价信息
     * @param quotationNo
     * @return
     */
	QuoteorderEntity queryEntityByNo(String quotationNo);

	/**
	 * 获取授权书
	 * @param authorizationNo
	 * @return
	 */
	AuthorizationApiApply getAuthorizationApplyByNum(String authorizationNo);

	QuoteorderEntity selectByPrimaryKey(Integer quoteorderId);

}
