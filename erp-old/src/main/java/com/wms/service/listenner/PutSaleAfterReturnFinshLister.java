package com.wms.service.listenner;


import com.vedeng.aftersales.dao.AfterSalesGoodsMapper;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.AfterSalesGoods;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.OrderConstant;
import com.vedeng.common.constant.goods.GoodsConstants;
import com.vedeng.common.constant.stock.StockOperateTypeConst;
import com.vedeng.logistics.service.impl.WarehouseStockServiceImpl;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderGoods;
import com.vedeng.stock.api.stock.dto.StockInfoDto;
import com.vedeng.stock.api.stock.dto.WarehouseDto;
import com.wms.constant.LogicalEnum;
import com.wms.constant.WmsLogicalOperateTypeEnum;
import com.wms.dao.WmsLogicalOrdergoodsMapper;
import com.wms.dto.StockCalculateDto;
import com.wms.model.po.WmsLogicalOrdergoods;
import com.wms.service.CancelTypeService;
import com.wms.service.LogicalSaleorderChooseService;
import com.wms.service.stockcalculate.AfterSaleReturnCaculateImpl;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName AfterReturnFinshPutSaleLister.java
 * @Description TODO 销售退货完结监听器
 * @createTime 2020年08月18日 15:31:00
 */
@Service("putSaleAfterReturnFinshLister")
public class PutSaleAfterReturnFinshLister extends AbstractErpListenner{

    public static Logger logger = LoggerFactory.getLogger(PutSaleAfterReturnFinshLister.class);

    @Resource
    private SaleorderMapper saleorderMapper;

    @Resource
    private AfterSalesGoodsMapper afterSalesGoodsMapper;

    @Resource
    private SaleorderGoodsMapper saleorderGoodsMapper;

    @Resource
    private AfterSalesMapper afterSalesMapper;

    @Resource
    private WmsLogicalOrdergoodsMapper wmsLogicalOrdergoodsMapper;

    @Autowired
    private LogicalSaleorderChooseService logicalSaleorderChooseService;

    @Autowired
    private WarehouseStockServiceImpl warehouseStockServiceImpl;

    @Autowired
    private AfterSaleReturnCaculateImpl stockinfoCaculateInterface;

    @Autowired
    private CancelTypeService cancelTypeService;

    @Override
    protected void doOnActionHappen(Object[] params) throws Exception {
        AfterSalesVo afterSalesInfo = (AfterSalesVo)params[0];

        User user = (User)params[1];

        AfterSales afterSales = afterSalesMapper.selectByPrimaryKey(afterSalesInfo.getAfterSalesId());
        if(!StockOperateTypeConst.AFTERORDER_BACK_FINSH.equals(afterSales.getType())){
            return;
        }
        AfterSalesGoods afterGoods = new AfterSalesGoods();
        afterGoods.setAfterSalesId(afterSales.getAfterSalesId());
        Saleorder  saleorder = saleorderMapper.getSaleOrderById(afterSales.getOrderId());
        List<SaleorderGoods> saleorderGoodsList = saleorderGoodsMapper.getSaleorderGoodsById(saleorder);
        String saleorderNo = saleorder.getSaleorderNo();

        if(!OrderConstant.ORDER_TYPE_HC.equals(saleorder.getOrderType()) && !OrderConstant.ORDER_ALL_PAYMENT.equals(saleorder.getPaymentStatus())){
            return;
        }

        List<WmsLogicalOrdergoods>  putLogicalGoodsList = new ArrayList<>();

        saleorderGoodsList.stream().forEach(saleorderGoods -> {
            dealWithSaleorderGoods(putLogicalGoodsList,saleorderGoods,saleorderNo);
        });

        //销售出库单重新下传wms
        logicalSaleorderChooseService.chooseLogicalSaleorder(saleorder,user);
        cancelTypeService.cancelInputSaleReturnMethod(afterSales.getAfterSalesNo(), "售后单关闭");
//        logicalSaleorderChooseService.cancelAfterOrder(afterSales);
        warehouseStockServiceImpl.updateOccupyStockService(saleorder,0);
    }

    private void dealWithSaleorderGoods(List<WmsLogicalOrdergoods> putLogicalGoodsList,
                                        SaleorderGoods saleorderGoods,
                                        String  saleorderNo) {
        if( saleorderGoods.getDeliveryDirect().equals(1)
                || saleorderGoods.getGoodsId().equals(GoodsConstants.FREIGHT)) {
            return;
        }

        //原数量-历史退数量-本次退-销售出库  > 0 销售单出库    < 0 退货单入库   == 0 无需下发
        Integer historyReturnNum = getHistoryReturnNum(saleorderGoods.getSaleorderGoodsId());
        int resultNum = saleorderGoods.getNum() - historyReturnNum - saleorderGoods.getDeliveryNum();

        //占用数量减少到resultNum,更新逻辑仓商品表
        List<StockCalculateDto> stockCalculateDtos = dealWithStockOccupy(saleorderGoods.getSaleorderGoodsId(), resultNum > 0 ? resultNum : 0);

        logger.info("销售退货完结监听器 订单号:{},订单商品id:{},resultNum:{},num:{},historyReturnNum:{},DeliveryNum:{}"
                ,saleorderNo,saleorderGoods.getSaleorderGoodsId(),resultNum,saleorderGoods.getNum(),historyReturnNum,saleorderGoods.getDeliveryNum());
        if(resultNum > 0){
            //销售出库
            List<WmsLogicalOrdergoods> logicalInfoByTypeAndRelate = getNowLogicalChooseInfo(saleorderGoods.getSaleorderGoodsId());
            putSaleorderLogicalGoods(logicalInfoByTypeAndRelate, putLogicalGoodsList);
        }
        synchronizeStockData(stockCalculateDtos,saleorderNo);

    }

    private List<StockCalculateDto> dealWithStockOccupy(Integer saleorderGoodsId,Integer num) {
        int targeNum = num;
        //将逻辑商品仓选择占用的数量变为targeNum
        List<WmsLogicalOrdergoods> nowLogicalChooseInfo = getNowLogicalChooseInfo(saleorderGoodsId);

        //更新选择逻辑仓数量
        //先从合格库撤销数量
        nowLogicalChooseInfo = nowLogicalChooseInfo.stream()
                .sorted(Comparator.comparing(WmsLogicalOrdergoods::getLogicalWarehouseId))
                .filter(item -> !item.getOccupyNum().equals(0))
                .collect(Collectors.toList());

        List<StockCalculateDto> stockCalculateList  = new ArrayList<>();
        //总占用
        int allOccupyNum = 0;

        for (WmsLogicalOrdergoods wmsLogicalOrdergoods : nowLogicalChooseInfo) {
            allOccupyNum = allOccupyNum + wmsLogicalOrdergoods.getOccupyNum();
        }
        //需要减去的占用
        int needSubNum = allOccupyNum - targeNum;
        logger.info("销售退货重新计算销售单占用 saleorderGoodsId:{},targeNum:{},allOccupyNum:{}",saleorderGoodsId,targeNum,allOccupyNum);
        for (WmsLogicalOrdergoods ordergoods : nowLogicalChooseInfo) {
            WmsLogicalOrdergoods update = new WmsLogicalOrdergoods();
            update.setLogicalOrderGoodsId(ordergoods.getLogicalOrderGoodsId());
            update.setNum(ordergoods.getNum());
            Integer occupyNum = ordergoods.getOccupyNum();

            if(needSubNum == 0){continue;}

            StockCalculateDto stockCalculateDto = new StockCalculateDto();
            stockCalculateDto.setSku(ordergoods.getSku());
            stockCalculateDto.setLogicalWarehouseId(ordergoods.getLogicalWarehouseId());
            stockCalculateDto.setOccupyNum(0);

            if(needSubNum >= occupyNum){
                update.setOccupyNum(0);
                update.setNum(ordergoods.getDeliveryNum());
                needSubNum = needSubNum - occupyNum;

                stockCalculateDto.setOccupyNum(occupyNum);
            }else{
                update.setOccupyNum(ordergoods.getOccupyNum() - needSubNum);
                update.setNum(ordergoods.getDeliveryNum() + update.getOccupyNum());

                stockCalculateDto.setOccupyNum(needSubNum);
                needSubNum = 0;
            }
            logger.info("销售退货重新计算销售单占用 更新 saleorderGoodsId:{},logicalOrderGoodsId:{},occupyNum:{},num:{},needSubNum:{}"
                    ,ordergoods.getRelatedId(),ordergoods.getLogicalOrderGoodsId(),update.getOccupyNum(),update.getNum(),needSubNum);
            wmsLogicalOrdergoodsMapper.updateByPrimaryKeySelective(update);

            stockCalculateList.add(stockCalculateDto);
        }
        return stockCalculateList;
    }

    //获取当前已选择逻辑仓数据
    private List<WmsLogicalOrdergoods> getNowLogicalChooseInfo(Integer saleorderGoodsId) {
        WmsLogicalOrdergoods search = new WmsLogicalOrdergoods();
        search.setRelatedId(saleorderGoodsId);
        search.setOperateType(WmsLogicalOperateTypeEnum.SALEORDER_TYPE.getOperateTypeCode());
        //已选择逻辑仓数据
        List<WmsLogicalOrdergoods> logicalInfo = wmsLogicalOrdergoodsMapper.getLogicalInfoByTypeAndRelate(search);
        return logicalInfo;
    }

    private Integer getHistoryReturnNum(Integer saleorderGoodsId) {
        return  afterSalesGoodsMapper.getSaleorderAftersaleReturnGoods(saleorderGoodsId);
    }

    private void putSaleorderLogicalGoods(List<WmsLogicalOrdergoods> saleorderLogicalChooseInfo, List<WmsLogicalOrdergoods> putSaleorderGoods) {
        //重新封装销售单选择逻辑仓数据
        saleorderLogicalChooseInfo.stream().forEach(wmsLogicalOrdergoods -> {
            int needNum = wmsLogicalOrdergoods.getNum() - wmsLogicalOrdergoods.getDeliveryNum();
            if(needNum > 0){
                wmsLogicalOrdergoods.setNum(needNum);
                putSaleorderGoods.add(wmsLogicalOrdergoods);
            }
        });
    }

    private void synchronizeStockData(List<StockCalculateDto> stockCalculateDtos, String saleorderNo) {
        try {
            if(CollectionUtils.isEmpty(stockCalculateDtos)){
                return;
            }
            Saleorder saleorder = new Saleorder();
            saleorder.setSaleorderNo(saleorderNo);
            saleorder = saleorderMapper.getSaleorderBySaleorderNo(saleorder);
            Integer actionId = saleorder.getActionId();

            //库存策略计算
            StockInfoDto stockInfoDto  = new StockInfoDto();
            stockInfoDto.setRelatedNo(saleorderNo);
            List<WarehouseDto> warehouseDtos = stockinfoCaculateInterface.calculateStockInfo(stockCalculateDtos);
            for (WarehouseDto warehouseDto : warehouseDtos) {
                if(LogicalEnum.HDC.getLogicalWarehouseId().equals(warehouseDto.getLogicalWarehouseId())){
                    warehouseDto.setActionId(actionId);
                    stockInfoDto.setOccupyType(StockOperateTypeConst.AFTERORDER_BACK_FINSH);
                }
            }
            stockInfoDto.setWarehouseStockList(warehouseDtos);

            //更新库存服务
            warehouseStockServiceImpl.updateStockInfo(stockInfoDto);
        } catch (Exception e) {
            logger.error("销售退货单审核监听器 同步库存数据error:",e);
        }
    }
}
