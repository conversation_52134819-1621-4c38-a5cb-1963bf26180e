package com.vedeng.erp.kingdee.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.erp.kingdee.domain.command.KingDeeInPutFeeSpecialInvoiceCommand;
import com.vedeng.erp.kingdee.domain.entity.KingDeeInPutFeeSpecialInvoiceEntity;
import com.vedeng.erp.kingdee.dto.InPutFeeSpecialInvoiceDto;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeInPutFeeSpecialInvoiceMapper;
import com.vedeng.erp.kingdee.mapstruct.KingDeeInPutFeeSpecialInvoiceCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeInPutFeeSpecialInvoiceConvertor;
import com.vedeng.erp.kingdee.service.KingDeeIFSInvoiceApiService;
import com.vedeng.erp.kingdee.service.KingDeeIFSInvoiceService;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.RepoStatus;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.SuccessEntity;
import com.vedeng.infrastructure.kingdee.domain.command.OperateExtCommand;
import com.vedeng.infrastructure.kingdee.domain.command.UpdateExtCommand;
import com.vedeng.infrastructure.kingdee.service.impl.KingDeeMqBaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;

/**
 * @description: 进项费用专用发票类
 * @return:
 * @author: Suqin
 * @date: 2022/12/2 17:32
 **/
@Service
@Slf4j
public class KingDeeIFSInvoiceServiceImpl extends KingDeeMqBaseServiceImpl<InPutFeeSpecialInvoiceDto> implements KingDeeIFSInvoiceService, KingDeeIFSInvoiceApiService {

    @Autowired
    KingDeeInPutFeeSpecialInvoiceMapper kingDeeInPutFeeSpecialInvoiceMapper;

    @Autowired
    KingDeeInPutFeeSpecialInvoiceCommandConvertor kingDeeInPutFeeSpecialInvoiceCommandConvertor;

    @Autowired
    KingDeeInPutFeeSpecialInvoiceConvertor kingDeeInPutFeeSpecialInvoiceConvertor;

    private static final Logger logger = LoggerFactory.getLogger(KingDeeIFSInvoiceServiceImpl.class);

    @Override
    public void update(InPutFeeSpecialInvoiceDto inPutFeeSpecialInvoiceDto) {

        KingDeeInPutFeeSpecialInvoiceEntity entity = kingDeeInPutFeeSpecialInvoiceMapper.selectByFQzokBddjtid(inPutFeeSpecialInvoiceDto.getFQzokBddjtid());
        if (entity == null) {
            logger.info("当前客户信息尚未推送过金蝶，客户信息：{}", JSONObject.toJSONString(inPutFeeSpecialInvoiceDto));
            return;
        }

        inPutFeeSpecialInvoiceDto.setFid(entity.getFid());
        OperateExtCommand operateExtCommand = new OperateExtCommand(inPutFeeSpecialInvoiceDto.getFormId(), inPutFeeSpecialInvoiceDto.getFid(),
                KingDeeConstant.ORG_ID.toString(), null);
        ArrayList<SuccessEntity> successUnAudit = kingDeeBaseApi.unAudit(operateExtCommand);

        if (CollUtil.isNotEmpty(successUnAudit)) {
            logger.info("反审核成功:{}",successUnAudit);
        }
        KingDeeInPutFeeSpecialInvoiceCommand command = kingDeeInPutFeeSpecialInvoiceCommandConvertor.toCommand(inPutFeeSpecialInvoiceDto);
        command.setFID(entity.getFid());
        command.setFDATE(entity.getFdate());
        RepoStatus update = kingDeeBaseApi.update(new UpdateExtCommand<>(command, inPutFeeSpecialInvoiceDto.getFormId()));
        ArrayList<SuccessEntity> successEntities = update.getSuccessEntitys();
        if (CollUtil.isNotEmpty(successEntities)) {
            KingDeeInPutFeeSpecialInvoiceEntity invoiceEntity = kingDeeInPutFeeSpecialInvoiceConvertor.toEntity(inPutFeeSpecialInvoiceDto);
            invoiceEntity.setInPutFeeSpecialInvoiceId(entity.getInPutFeeSpecialInvoiceId());
            kingDeeInPutFeeSpecialInvoiceMapper.updateByPrimaryKeySelective(invoiceEntity);
        }


        logger.info("修改进项费用专用发票时间推送金蝶：{}", JSON.toJSONString(inPutFeeSpecialInvoiceDto));

    }
}
