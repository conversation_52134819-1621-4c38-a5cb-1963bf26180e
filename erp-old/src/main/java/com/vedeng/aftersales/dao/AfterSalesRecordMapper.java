package com.vedeng.aftersales.dao;

import com.vedeng.aftersales.model.AfterSalesRecord;
import com.vedeng.aftersales.model.vo.AfterSalesRecordVo;

import java.util.List;

public interface AfterSalesRecordMapper {
    int deleteByPrimaryKey(Integer afterSalesRecordId);

    int insert(AfterSalesRecord record);

    int insertSelective(AfterSalesRecord record);

    AfterSalesRecord selectByPrimaryKey(Integer afterSalesRecordId);

    int updateByPrimaryKeySelective(AfterSalesRecord record);

    int updateByPrimaryKey(AfterSalesRecord record);
    
    /**
     * <b>Description:</b><br> 根据AfterSalesid查询记录
     * @param afterSalesRecord
     * @return
     * @Note
     * <b>Author:</b> east
     * <br><b>Date:</b> 2017年10月18日 下午2:17:55
     */
    List<AfterSalesRecordVo> getAfterSalesRecordVoList(AfterSalesRecord afterSalesRecord);
}