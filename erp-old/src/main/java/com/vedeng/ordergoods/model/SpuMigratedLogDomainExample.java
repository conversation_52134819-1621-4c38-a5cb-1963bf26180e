package com.vedeng.ordergoods.model;

import java.util.ArrayList;
import java.util.List;

public class SpuMigratedLogDomainExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_SPU_MIGRATION_LOG
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_SPU_MIGRATION_LOG
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_SPU_MIGRATION_LOG
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SPU_MIGRATION_LOG
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    public SpuMigratedLogDomainExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SPU_MIGRATION_LOG
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SPU_MIGRATION_LOG
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SPU_MIGRATION_LOG
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SPU_MIGRATION_LOG
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SPU_MIGRATION_LOG
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SPU_MIGRATION_LOG
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SPU_MIGRATION_LOG
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SPU_MIGRATION_LOG
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SPU_MIGRATION_LOG
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SPU_MIGRATION_LOG
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_SPU_MIGRATION_LOG
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andSpuMigtationLogIdIsNull() {
            addCriterion("SPU_MIGTATION_LOG_ID is null");
            return (Criteria) this;
        }

        public Criteria andSpuMigtationLogIdIsNotNull() {
            addCriterion("SPU_MIGTATION_LOG_ID is not null");
            return (Criteria) this;
        }

        public Criteria andSpuMigtationLogIdEqualTo(Integer value) {
            addCriterion("SPU_MIGTATION_LOG_ID =", value, "spuMigtationLogId");
            return (Criteria) this;
        }

        public Criteria andSpuMigtationLogIdNotEqualTo(Integer value) {
            addCriterion("SPU_MIGTATION_LOG_ID <>", value, "spuMigtationLogId");
            return (Criteria) this;
        }

        public Criteria andSpuMigtationLogIdGreaterThan(Integer value) {
            addCriterion("SPU_MIGTATION_LOG_ID >", value, "spuMigtationLogId");
            return (Criteria) this;
        }

        public Criteria andSpuMigtationLogIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("SPU_MIGTATION_LOG_ID >=", value, "spuMigtationLogId");
            return (Criteria) this;
        }

        public Criteria andSpuMigtationLogIdLessThan(Integer value) {
            addCriterion("SPU_MIGTATION_LOG_ID <", value, "spuMigtationLogId");
            return (Criteria) this;
        }

        public Criteria andSpuMigtationLogIdLessThanOrEqualTo(Integer value) {
            addCriterion("SPU_MIGTATION_LOG_ID <=", value, "spuMigtationLogId");
            return (Criteria) this;
        }

        public Criteria andSpuMigtationLogIdIn(List<Integer> values) {
            addCriterion("SPU_MIGTATION_LOG_ID in", values, "spuMigtationLogId");
            return (Criteria) this;
        }

        public Criteria andSpuMigtationLogIdNotIn(List<Integer> values) {
            addCriterion("SPU_MIGTATION_LOG_ID not in", values, "spuMigtationLogId");
            return (Criteria) this;
        }

        public Criteria andSpuMigtationLogIdBetween(Integer value1, Integer value2) {
            addCriterion("SPU_MIGTATION_LOG_ID between", value1, value2, "spuMigtationLogId");
            return (Criteria) this;
        }

        public Criteria andSpuMigtationLogIdNotBetween(Integer value1, Integer value2) {
            addCriterion("SPU_MIGTATION_LOG_ID not between", value1, value2, "spuMigtationLogId");
            return (Criteria) this;
        }

        public Criteria andSpuIdIsNull() {
            addCriterion("SPU_ID is null");
            return (Criteria) this;
        }

        public Criteria andSpuIdIsNotNull() {
            addCriterion("SPU_ID is not null");
            return (Criteria) this;
        }

        public Criteria andSpuIdEqualTo(Integer value) {
            addCriterion("SPU_ID =", value, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdNotEqualTo(Integer value) {
            addCriterion("SPU_ID <>", value, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdGreaterThan(Integer value) {
            addCriterion("SPU_ID >", value, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("SPU_ID >=", value, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdLessThan(Integer value) {
            addCriterion("SPU_ID <", value, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdLessThanOrEqualTo(Integer value) {
            addCriterion("SPU_ID <=", value, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdIn(List<Integer> values) {
            addCriterion("SPU_ID in", values, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdNotIn(List<Integer> values) {
            addCriterion("SPU_ID not in", values, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdBetween(Integer value1, Integer value2) {
            addCriterion("SPU_ID between", value1, value2, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdNotBetween(Integer value1, Integer value2) {
            addCriterion("SPU_ID not between", value1, value2, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuNoIsNull() {
            addCriterion("SPU_NO is null");
            return (Criteria) this;
        }

        public Criteria andSpuNoIsNotNull() {
            addCriterion("SPU_NO is not null");
            return (Criteria) this;
        }

        public Criteria andSpuNoEqualTo(String value) {
            addCriterion("SPU_NO =", value, "spuNo");
            return (Criteria) this;
        }

        public Criteria andSpuNoNotEqualTo(String value) {
            addCriterion("SPU_NO <>", value, "spuNo");
            return (Criteria) this;
        }

        public Criteria andSpuNoGreaterThan(String value) {
            addCriterion("SPU_NO >", value, "spuNo");
            return (Criteria) this;
        }

        public Criteria andSpuNoGreaterThanOrEqualTo(String value) {
            addCriterion("SPU_NO >=", value, "spuNo");
            return (Criteria) this;
        }

        public Criteria andSpuNoLessThan(String value) {
            addCriterion("SPU_NO <", value, "spuNo");
            return (Criteria) this;
        }

        public Criteria andSpuNoLessThanOrEqualTo(String value) {
            addCriterion("SPU_NO <=", value, "spuNo");
            return (Criteria) this;
        }

        public Criteria andSpuNoLike(String value) {
            addCriterion("SPU_NO like", value, "spuNo");
            return (Criteria) this;
        }

        public Criteria andSpuNoNotLike(String value) {
            addCriterion("SPU_NO not like", value, "spuNo");
            return (Criteria) this;
        }

        public Criteria andSpuNoIn(List<String> values) {
            addCriterion("SPU_NO in", values, "spuNo");
            return (Criteria) this;
        }

        public Criteria andSpuNoNotIn(List<String> values) {
            addCriterion("SPU_NO not in", values, "spuNo");
            return (Criteria) this;
        }

        public Criteria andSpuNoBetween(String value1, String value2) {
            addCriterion("SPU_NO between", value1, value2, "spuNo");
            return (Criteria) this;
        }

        public Criteria andSpuNoNotBetween(String value1, String value2) {
            addCriterion("SPU_NO not between", value1, value2, "spuNo");
            return (Criteria) this;
        }

        public Criteria andCategoryIdPreIsNull() {
            addCriterion("CATEGORY_ID_PRE is null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdPreIsNotNull() {
            addCriterion("CATEGORY_ID_PRE is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdPreEqualTo(Integer value) {
            addCriterion("CATEGORY_ID_PRE =", value, "categoryIdPre");
            return (Criteria) this;
        }

        public Criteria andCategoryIdPreNotEqualTo(Integer value) {
            addCriterion("CATEGORY_ID_PRE <>", value, "categoryIdPre");
            return (Criteria) this;
        }

        public Criteria andCategoryIdPreGreaterThan(Integer value) {
            addCriterion("CATEGORY_ID_PRE >", value, "categoryIdPre");
            return (Criteria) this;
        }

        public Criteria andCategoryIdPreGreaterThanOrEqualTo(Integer value) {
            addCriterion("CATEGORY_ID_PRE >=", value, "categoryIdPre");
            return (Criteria) this;
        }

        public Criteria andCategoryIdPreLessThan(Integer value) {
            addCriterion("CATEGORY_ID_PRE <", value, "categoryIdPre");
            return (Criteria) this;
        }

        public Criteria andCategoryIdPreLessThanOrEqualTo(Integer value) {
            addCriterion("CATEGORY_ID_PRE <=", value, "categoryIdPre");
            return (Criteria) this;
        }

        public Criteria andCategoryIdPreIn(List<Integer> values) {
            addCriterion("CATEGORY_ID_PRE in", values, "categoryIdPre");
            return (Criteria) this;
        }

        public Criteria andCategoryIdPreNotIn(List<Integer> values) {
            addCriterion("CATEGORY_ID_PRE not in", values, "categoryIdPre");
            return (Criteria) this;
        }

        public Criteria andCategoryIdPreBetween(Integer value1, Integer value2) {
            addCriterion("CATEGORY_ID_PRE between", value1, value2, "categoryIdPre");
            return (Criteria) this;
        }

        public Criteria andCategoryIdPreNotBetween(Integer value1, Integer value2) {
            addCriterion("CATEGORY_ID_PRE not between", value1, value2, "categoryIdPre");
            return (Criteria) this;
        }

        public Criteria andCategoryNamePreIsNull() {
            addCriterion("CATEGORY_NAME_PRE is null");
            return (Criteria) this;
        }

        public Criteria andCategoryNamePreIsNotNull() {
            addCriterion("CATEGORY_NAME_PRE is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryNamePreEqualTo(String value) {
            addCriterion("CATEGORY_NAME_PRE =", value, "categoryNamePre");
            return (Criteria) this;
        }

        public Criteria andCategoryNamePreNotEqualTo(String value) {
            addCriterion("CATEGORY_NAME_PRE <>", value, "categoryNamePre");
            return (Criteria) this;
        }

        public Criteria andCategoryNamePreGreaterThan(String value) {
            addCriterion("CATEGORY_NAME_PRE >", value, "categoryNamePre");
            return (Criteria) this;
        }

        public Criteria andCategoryNamePreGreaterThanOrEqualTo(String value) {
            addCriterion("CATEGORY_NAME_PRE >=", value, "categoryNamePre");
            return (Criteria) this;
        }

        public Criteria andCategoryNamePreLessThan(String value) {
            addCriterion("CATEGORY_NAME_PRE <", value, "categoryNamePre");
            return (Criteria) this;
        }

        public Criteria andCategoryNamePreLessThanOrEqualTo(String value) {
            addCriterion("CATEGORY_NAME_PRE <=", value, "categoryNamePre");
            return (Criteria) this;
        }

        public Criteria andCategoryNamePreLike(String value) {
            addCriterion("CATEGORY_NAME_PRE like", value, "categoryNamePre");
            return (Criteria) this;
        }

        public Criteria andCategoryNamePreNotLike(String value) {
            addCriterion("CATEGORY_NAME_PRE not like", value, "categoryNamePre");
            return (Criteria) this;
        }

        public Criteria andCategoryNamePreIn(List<String> values) {
            addCriterion("CATEGORY_NAME_PRE in", values, "categoryNamePre");
            return (Criteria) this;
        }

        public Criteria andCategoryNamePreNotIn(List<String> values) {
            addCriterion("CATEGORY_NAME_PRE not in", values, "categoryNamePre");
            return (Criteria) this;
        }

        public Criteria andCategoryNamePreBetween(String value1, String value2) {
            addCriterion("CATEGORY_NAME_PRE between", value1, value2, "categoryNamePre");
            return (Criteria) this;
        }

        public Criteria andCategoryNamePreNotBetween(String value1, String value2) {
            addCriterion("CATEGORY_NAME_PRE not between", value1, value2, "categoryNamePre");
            return (Criteria) this;
        }

        public Criteria andCategoryIdAfterIsNull() {
            addCriterion("CATEGORY_ID_AFTER is null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdAfterIsNotNull() {
            addCriterion("CATEGORY_ID_AFTER is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdAfterEqualTo(Integer value) {
            addCriterion("CATEGORY_ID_AFTER =", value, "categoryIdAfter");
            return (Criteria) this;
        }

        public Criteria andCategoryIdAfterNotEqualTo(Integer value) {
            addCriterion("CATEGORY_ID_AFTER <>", value, "categoryIdAfter");
            return (Criteria) this;
        }

        public Criteria andCategoryIdAfterGreaterThan(Integer value) {
            addCriterion("CATEGORY_ID_AFTER >", value, "categoryIdAfter");
            return (Criteria) this;
        }

        public Criteria andCategoryIdAfterGreaterThanOrEqualTo(Integer value) {
            addCriterion("CATEGORY_ID_AFTER >=", value, "categoryIdAfter");
            return (Criteria) this;
        }

        public Criteria andCategoryIdAfterLessThan(Integer value) {
            addCriterion("CATEGORY_ID_AFTER <", value, "categoryIdAfter");
            return (Criteria) this;
        }

        public Criteria andCategoryIdAfterLessThanOrEqualTo(Integer value) {
            addCriterion("CATEGORY_ID_AFTER <=", value, "categoryIdAfter");
            return (Criteria) this;
        }

        public Criteria andCategoryIdAfterIn(List<Integer> values) {
            addCriterion("CATEGORY_ID_AFTER in", values, "categoryIdAfter");
            return (Criteria) this;
        }

        public Criteria andCategoryIdAfterNotIn(List<Integer> values) {
            addCriterion("CATEGORY_ID_AFTER not in", values, "categoryIdAfter");
            return (Criteria) this;
        }

        public Criteria andCategoryIdAfterBetween(Integer value1, Integer value2) {
            addCriterion("CATEGORY_ID_AFTER between", value1, value2, "categoryIdAfter");
            return (Criteria) this;
        }

        public Criteria andCategoryIdAfterNotBetween(Integer value1, Integer value2) {
            addCriterion("CATEGORY_ID_AFTER not between", value1, value2, "categoryIdAfter");
            return (Criteria) this;
        }

        public Criteria andCategoryNameAfterIsNull() {
            addCriterion("CATEGORY_NAME_AFTER is null");
            return (Criteria) this;
        }

        public Criteria andCategoryNameAfterIsNotNull() {
            addCriterion("CATEGORY_NAME_AFTER is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryNameAfterEqualTo(String value) {
            addCriterion("CATEGORY_NAME_AFTER =", value, "categoryNameAfter");
            return (Criteria) this;
        }

        public Criteria andCategoryNameAfterNotEqualTo(String value) {
            addCriterion("CATEGORY_NAME_AFTER <>", value, "categoryNameAfter");
            return (Criteria) this;
        }

        public Criteria andCategoryNameAfterGreaterThan(String value) {
            addCriterion("CATEGORY_NAME_AFTER >", value, "categoryNameAfter");
            return (Criteria) this;
        }

        public Criteria andCategoryNameAfterGreaterThanOrEqualTo(String value) {
            addCriterion("CATEGORY_NAME_AFTER >=", value, "categoryNameAfter");
            return (Criteria) this;
        }

        public Criteria andCategoryNameAfterLessThan(String value) {
            addCriterion("CATEGORY_NAME_AFTER <", value, "categoryNameAfter");
            return (Criteria) this;
        }

        public Criteria andCategoryNameAfterLessThanOrEqualTo(String value) {
            addCriterion("CATEGORY_NAME_AFTER <=", value, "categoryNameAfter");
            return (Criteria) this;
        }

        public Criteria andCategoryNameAfterLike(String value) {
            addCriterion("CATEGORY_NAME_AFTER like", value, "categoryNameAfter");
            return (Criteria) this;
        }

        public Criteria andCategoryNameAfterNotLike(String value) {
            addCriterion("CATEGORY_NAME_AFTER not like", value, "categoryNameAfter");
            return (Criteria) this;
        }

        public Criteria andCategoryNameAfterIn(List<String> values) {
            addCriterion("CATEGORY_NAME_AFTER in", values, "categoryNameAfter");
            return (Criteria) this;
        }

        public Criteria andCategoryNameAfterNotIn(List<String> values) {
            addCriterion("CATEGORY_NAME_AFTER not in", values, "categoryNameAfter");
            return (Criteria) this;
        }

        public Criteria andCategoryNameAfterBetween(String value1, String value2) {
            addCriterion("CATEGORY_NAME_AFTER between", value1, value2, "categoryNameAfter");
            return (Criteria) this;
        }

        public Criteria andCategoryNameAfterNotBetween(String value1, String value2) {
            addCriterion("CATEGORY_NAME_AFTER not between", value1, value2, "categoryNameAfter");
            return (Criteria) this;
        }

        public Criteria andOriginPathIsNull() {
            addCriterion("ORIGIN_PATH is null");
            return (Criteria) this;
        }

        public Criteria andOriginPathIsNotNull() {
            addCriterion("ORIGIN_PATH is not null");
            return (Criteria) this;
        }

        public Criteria andOriginPathEqualTo(String value) {
            addCriterion("ORIGIN_PATH =", value, "originPath");
            return (Criteria) this;
        }

        public Criteria andOriginPathNotEqualTo(String value) {
            addCriterion("ORIGIN_PATH <>", value, "originPath");
            return (Criteria) this;
        }

        public Criteria andOriginPathGreaterThan(String value) {
            addCriterion("ORIGIN_PATH >", value, "originPath");
            return (Criteria) this;
        }

        public Criteria andOriginPathGreaterThanOrEqualTo(String value) {
            addCriterion("ORIGIN_PATH >=", value, "originPath");
            return (Criteria) this;
        }

        public Criteria andOriginPathLessThan(String value) {
            addCriterion("ORIGIN_PATH <", value, "originPath");
            return (Criteria) this;
        }

        public Criteria andOriginPathLessThanOrEqualTo(String value) {
            addCriterion("ORIGIN_PATH <=", value, "originPath");
            return (Criteria) this;
        }

        public Criteria andOriginPathLike(String value) {
            addCriterion("ORIGIN_PATH like", value, "originPath");
            return (Criteria) this;
        }

        public Criteria andOriginPathNotLike(String value) {
            addCriterion("ORIGIN_PATH not like", value, "originPath");
            return (Criteria) this;
        }

        public Criteria andOriginPathIn(List<String> values) {
            addCriterion("ORIGIN_PATH in", values, "originPath");
            return (Criteria) this;
        }

        public Criteria andOriginPathNotIn(List<String> values) {
            addCriterion("ORIGIN_PATH not in", values, "originPath");
            return (Criteria) this;
        }

        public Criteria andOriginPathBetween(String value1, String value2) {
            addCriterion("ORIGIN_PATH between", value1, value2, "originPath");
            return (Criteria) this;
        }

        public Criteria andOriginPathNotBetween(String value1, String value2) {
            addCriterion("ORIGIN_PATH not between", value1, value2, "originPath");
            return (Criteria) this;
        }

        public Criteria andTargetPathIsNull() {
            addCriterion("TARGET_PATH is null");
            return (Criteria) this;
        }

        public Criteria andTargetPathIsNotNull() {
            addCriterion("TARGET_PATH is not null");
            return (Criteria) this;
        }

        public Criteria andTargetPathEqualTo(String value) {
            addCriterion("TARGET_PATH =", value, "targetPath");
            return (Criteria) this;
        }

        public Criteria andTargetPathNotEqualTo(String value) {
            addCriterion("TARGET_PATH <>", value, "targetPath");
            return (Criteria) this;
        }

        public Criteria andTargetPathGreaterThan(String value) {
            addCriterion("TARGET_PATH >", value, "targetPath");
            return (Criteria) this;
        }

        public Criteria andTargetPathGreaterThanOrEqualTo(String value) {
            addCriterion("TARGET_PATH >=", value, "targetPath");
            return (Criteria) this;
        }

        public Criteria andTargetPathLessThan(String value) {
            addCriterion("TARGET_PATH <", value, "targetPath");
            return (Criteria) this;
        }

        public Criteria andTargetPathLessThanOrEqualTo(String value) {
            addCriterion("TARGET_PATH <=", value, "targetPath");
            return (Criteria) this;
        }

        public Criteria andTargetPathLike(String value) {
            addCriterion("TARGET_PATH like", value, "targetPath");
            return (Criteria) this;
        }

        public Criteria andTargetPathNotLike(String value) {
            addCriterion("TARGET_PATH not like", value, "targetPath");
            return (Criteria) this;
        }

        public Criteria andTargetPathIn(List<String> values) {
            addCriterion("TARGET_PATH in", values, "targetPath");
            return (Criteria) this;
        }

        public Criteria andTargetPathNotIn(List<String> values) {
            addCriterion("TARGET_PATH not in", values, "targetPath");
            return (Criteria) this;
        }

        public Criteria andTargetPathBetween(String value1, String value2) {
            addCriterion("TARGET_PATH between", value1, value2, "targetPath");
            return (Criteria) this;
        }

        public Criteria andTargetPathNotBetween(String value1, String value2) {
            addCriterion("TARGET_PATH not between", value1, value2, "targetPath");
            return (Criteria) this;
        }

        public Criteria andMigrationReasonIsNull() {
            addCriterion("MIGRATION_REASON is null");
            return (Criteria) this;
        }

        public Criteria andMigrationReasonIsNotNull() {
            addCriterion("MIGRATION_REASON is not null");
            return (Criteria) this;
        }

        public Criteria andMigrationReasonEqualTo(String value) {
            addCriterion("MIGRATION_REASON =", value, "migrationReason");
            return (Criteria) this;
        }

        public Criteria andMigrationReasonNotEqualTo(String value) {
            addCriterion("MIGRATION_REASON <>", value, "migrationReason");
            return (Criteria) this;
        }

        public Criteria andMigrationReasonGreaterThan(String value) {
            addCriterion("MIGRATION_REASON >", value, "migrationReason");
            return (Criteria) this;
        }

        public Criteria andMigrationReasonGreaterThanOrEqualTo(String value) {
            addCriterion("MIGRATION_REASON >=", value, "migrationReason");
            return (Criteria) this;
        }

        public Criteria andMigrationReasonLessThan(String value) {
            addCriterion("MIGRATION_REASON <", value, "migrationReason");
            return (Criteria) this;
        }

        public Criteria andMigrationReasonLessThanOrEqualTo(String value) {
            addCriterion("MIGRATION_REASON <=", value, "migrationReason");
            return (Criteria) this;
        }

        public Criteria andMigrationReasonLike(String value) {
            addCriterion("MIGRATION_REASON like", value, "migrationReason");
            return (Criteria) this;
        }

        public Criteria andMigrationReasonNotLike(String value) {
            addCriterion("MIGRATION_REASON not like", value, "migrationReason");
            return (Criteria) this;
        }

        public Criteria andMigrationReasonIn(List<String> values) {
            addCriterion("MIGRATION_REASON in", values, "migrationReason");
            return (Criteria) this;
        }

        public Criteria andMigrationReasonNotIn(List<String> values) {
            addCriterion("MIGRATION_REASON not in", values, "migrationReason");
            return (Criteria) this;
        }

        public Criteria andMigrationReasonBetween(String value1, String value2) {
            addCriterion("MIGRATION_REASON between", value1, value2, "migrationReason");
            return (Criteria) this;
        }

        public Criteria andMigrationReasonNotBetween(String value1, String value2) {
            addCriterion("MIGRATION_REASON not between", value1, value2, "migrationReason");
            return (Criteria) this;
        }

        public Criteria andAddAttrsIsNull() {
            addCriterion("ADD_ATTRS is null");
            return (Criteria) this;
        }

        public Criteria andAddAttrsIsNotNull() {
            addCriterion("ADD_ATTRS is not null");
            return (Criteria) this;
        }

        public Criteria andAddAttrsEqualTo(String value) {
            addCriterion("ADD_ATTRS =", value, "addAttrs");
            return (Criteria) this;
        }

        public Criteria andAddAttrsNotEqualTo(String value) {
            addCriterion("ADD_ATTRS <>", value, "addAttrs");
            return (Criteria) this;
        }

        public Criteria andAddAttrsGreaterThan(String value) {
            addCriterion("ADD_ATTRS >", value, "addAttrs");
            return (Criteria) this;
        }

        public Criteria andAddAttrsGreaterThanOrEqualTo(String value) {
            addCriterion("ADD_ATTRS >=", value, "addAttrs");
            return (Criteria) this;
        }

        public Criteria andAddAttrsLessThan(String value) {
            addCriterion("ADD_ATTRS <", value, "addAttrs");
            return (Criteria) this;
        }

        public Criteria andAddAttrsLessThanOrEqualTo(String value) {
            addCriterion("ADD_ATTRS <=", value, "addAttrs");
            return (Criteria) this;
        }

        public Criteria andAddAttrsLike(String value) {
            addCriterion("ADD_ATTRS like", value, "addAttrs");
            return (Criteria) this;
        }

        public Criteria andAddAttrsNotLike(String value) {
            addCriterion("ADD_ATTRS not like", value, "addAttrs");
            return (Criteria) this;
        }

        public Criteria andAddAttrsIn(List<String> values) {
            addCriterion("ADD_ATTRS in", values, "addAttrs");
            return (Criteria) this;
        }

        public Criteria andAddAttrsNotIn(List<String> values) {
            addCriterion("ADD_ATTRS not in", values, "addAttrs");
            return (Criteria) this;
        }

        public Criteria andAddAttrsBetween(String value1, String value2) {
            addCriterion("ADD_ATTRS between", value1, value2, "addAttrs");
            return (Criteria) this;
        }

        public Criteria andAddAttrsNotBetween(String value1, String value2) {
            addCriterion("ADD_ATTRS not between", value1, value2, "addAttrs");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("ADD_TIME is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("ADD_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Long value) {
            addCriterion("ADD_TIME =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Long value) {
            addCriterion("ADD_TIME <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Long value) {
            addCriterion("ADD_TIME >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("ADD_TIME >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Long value) {
            addCriterion("ADD_TIME <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Long value) {
            addCriterion("ADD_TIME <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Long> values) {
            addCriterion("ADD_TIME in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Long> values) {
            addCriterion("ADD_TIME not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Long value1, Long value2) {
            addCriterion("ADD_TIME between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Long value1, Long value2) {
            addCriterion("ADD_TIME not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNull() {
            addCriterion("CREATOR is null");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNotNull() {
            addCriterion("CREATOR is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorEqualTo(Integer value) {
            addCriterion("CREATOR =", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotEqualTo(Integer value) {
            addCriterion("CREATOR <>", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThan(Integer value) {
            addCriterion("CREATOR >", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThanOrEqualTo(Integer value) {
            addCriterion("CREATOR >=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThan(Integer value) {
            addCriterion("CREATOR <", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThanOrEqualTo(Integer value) {
            addCriterion("CREATOR <=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorIn(List<Integer> values) {
            addCriterion("CREATOR in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotIn(List<Integer> values) {
            addCriterion("CREATOR not in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorBetween(Integer value1, Integer value2) {
            addCriterion("CREATOR between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotBetween(Integer value1, Integer value2) {
            addCriterion("CREATOR not between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("UPDATE_TIME is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("UPDATE_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Long value) {
            addCriterion("UPDATE_TIME =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Long value) {
            addCriterion("UPDATE_TIME <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Long value) {
            addCriterion("UPDATE_TIME >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("UPDATE_TIME >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Long value) {
            addCriterion("UPDATE_TIME <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Long value) {
            addCriterion("UPDATE_TIME <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Long> values) {
            addCriterion("UPDATE_TIME in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Long> values) {
            addCriterion("UPDATE_TIME not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Long value1, Long value2) {
            addCriterion("UPDATE_TIME between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Long value1, Long value2) {
            addCriterion("UPDATE_TIME not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdaterIsNull() {
            addCriterion("UPDATER is null");
            return (Criteria) this;
        }

        public Criteria andUpdaterIsNotNull() {
            addCriterion("UPDATER is not null");
            return (Criteria) this;
        }

        public Criteria andUpdaterEqualTo(Integer value) {
            addCriterion("UPDATER =", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotEqualTo(Integer value) {
            addCriterion("UPDATER <>", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterGreaterThan(Integer value) {
            addCriterion("UPDATER >", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterGreaterThanOrEqualTo(Integer value) {
            addCriterion("UPDATER >=", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterLessThan(Integer value) {
            addCriterion("UPDATER <", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterLessThanOrEqualTo(Integer value) {
            addCriterion("UPDATER <=", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterIn(List<Integer> values) {
            addCriterion("UPDATER in", values, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotIn(List<Integer> values) {
            addCriterion("UPDATER not in", values, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterBetween(Integer value1, Integer value2) {
            addCriterion("UPDATER between", value1, value2, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotBetween(Integer value1, Integer value2) {
            addCriterion("UPDATER not between", value1, value2, "updater");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIsNull() {
            addCriterion("IS_DELETE is null");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIsNotNull() {
            addCriterion("IS_DELETE is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeleteEqualTo(Byte value) {
            addCriterion("IS_DELETE =", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotEqualTo(Byte value) {
            addCriterion("IS_DELETE <>", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteGreaterThan(Byte value) {
            addCriterion("IS_DELETE >", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteGreaterThanOrEqualTo(Byte value) {
            addCriterion("IS_DELETE >=", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteLessThan(Byte value) {
            addCriterion("IS_DELETE <", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteLessThanOrEqualTo(Byte value) {
            addCriterion("IS_DELETE <=", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIn(List<Byte> values) {
            addCriterion("IS_DELETE in", values, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotIn(List<Byte> values) {
            addCriterion("IS_DELETE not in", values, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteBetween(Byte value1, Byte value2) {
            addCriterion("IS_DELETE between", value1, value2, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotBetween(Byte value1, Byte value2) {
            addCriterion("IS_DELETE not between", value1, value2, "isDelete");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_SPU_MIGRATION_LOG
     *
     * @mbggenerated do_not_delete_during_merge Mon Aug 03 19:48:25 CST 2020
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_SPU_MIGRATION_LOG
     *
     * @mbggenerated Mon Aug 03 19:48:25 CST 2020
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}