package com.vedeng.erp.trader.service;

import com.vedeng.erp.trader.dto.TraderCustomerAgentBrandDto;
import com.vedeng.erp.trader.dto.TraderCustomerBussinessBrandDto;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/8/11 17:27
 **/
public interface TraderCustomerAgentBrandService {

    /**
     * 批量新增
     * @param traderCustomerAgentBrandDtoList 对象
     */
    void addAll(List<TraderCustomerAgentBrandDto> traderCustomerAgentBrandDtoList);

    /**
     * 根据客户id删除
     * @param traderCustomerId 客户id
     */
    void deleteByTraderCustomerId(Integer traderCustomerId);
}
