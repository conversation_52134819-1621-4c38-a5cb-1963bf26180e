.vd-ui-phone-related .phone-related-show-wrap {
  position: relative;
  width: 100%;
  padding-left: 10px;
}
.vd-ui-phone-related .phone-related-show-wrap .phone-related-show.pb {
  padding-bottom: 10px;
}
.vd-ui-phone-related .phone-related-show-wrap .phone-related-show .phone-value {
  font-size: 12px;
  color: #000;
  height: 38px;
  line-height: 38px;
}
.vd-ui-phone-related .phone-related-show-wrap .phone-placeholder {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.vd-ui-phone-related .phone-related-show-wrap .phone-placeholder > span {
  font-size: 12px;
  color: #ccc;
}
.vd-ui-phone-related .phone-related-show-wrap .phone-placeholder > i {
  font-size: 16px;
  color: #666;
}
.phone-related-panel {
  height: calc(100vh - 98px);
  height: calc(100vh - 98px - constant(safe-area-inset-bottom));
  height: calc(100vh - 98px - env(safe-area-inset-bottom));
}
.phone-related-panel .phone-related-ul {
  height: calc(100% - 53px - 53px);
  overflow-y: auto;
}
.phone-related-panel .phone-related-ul .related-list .pr-item {
  padding: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #000;
  border-bottom: solid 1px #F5F7FA;
}
.phone-related-panel .phone-related-ul .related-list .pr-item.active {
  background: #E0F3FF;
}
.phone-related-panel .phone-related-ul .related-list .pr-item .name {
  flex: 1;
  min-width: 0;
  margin-right: 10px;
}
.phone-related-panel .phone-related-ul .related-list .pr-item .mobile {
  width: 90px;
  flex-shrink: 0;
  white-space: nowrap;
  text-align: right;
}
