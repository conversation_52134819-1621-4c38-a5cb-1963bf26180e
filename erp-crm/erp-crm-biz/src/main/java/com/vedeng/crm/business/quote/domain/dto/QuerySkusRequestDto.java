package com.vedeng.crm.business.quote.domain.dto;

import lombok.Data;
import com.vedeng.common.core.utils.validator.group.DefaultGroup;

import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class QuerySkusRequestDto {

    @NotNull(message = "报价单id不能为空", groups = DefaultGroup.class)
    private Integer quoteorderId;

    @NotBlank(message = "skuNo不能为空", groups = DefaultGroup.class)
    private List<String> skuNos;
}
