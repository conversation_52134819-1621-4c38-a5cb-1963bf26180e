package com.vedeng.erp.finance.mapper;

import com.vedeng.erp.finance.domain.dto.AcceptanceBillDto;
import com.vedeng.erp.finance.domain.entity.AcceptanceBillEntity;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface AcceptanceBillMapper {
    /**
     * delete by primary key
     *
     * @param acceptanceBilId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long acceptanceBilId);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(AcceptanceBillEntity record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(AcceptanceBillEntity record);

    /**
     * select by primary key
     *
     * @param acceptanceBilId primary key
     * @return object by primary key
     */
    AcceptanceBillEntity selectByPrimaryKey(Long acceptanceBilId);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(AcceptanceBillEntity record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(AcceptanceBillEntity record);

    List<AcceptanceBillDto> findByAll(AcceptanceBillDto acceptanceBillDto);

    List<AcceptanceBillEntity> findByBillNumber(@Param("billNumber") String billNumber);

    List<AcceptanceBillEntity> findByBankBillIdIn(@Param("bankBillIdCollection") Collection<Integer> bankBillIdCollection);

    /**
     * 获取非终止状态的承兑汇票 billStatus not (5,6)
     */
    List<AcceptanceBillEntity> findAcceptanceBillNonTerminatedState();

    /**
     * 获取贴现中的所有数据
     * @return
     */
    List<AcceptanceBillEntity> findDraftDiscountProcessing();

    List<AcceptanceBillEntity> findSignupList();
}