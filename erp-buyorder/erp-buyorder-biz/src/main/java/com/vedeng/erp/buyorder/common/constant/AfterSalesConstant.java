package com.vedeng.erp.buyorder.common.constant;

public class AfterSalesConstant {

    public static final Integer CONFIRM_CODE = 0;
    public static final Integer PROCRESSING_CODE = 1;
    public static final Integer COMPLETED_CODE = 2;
    public static final Integer CLOSED_CODE = 3;

    public static final Integer VALID_CONFIRM_CODE = 0;
    public static final Integer VALID_PROCRESSING_CODE = 1;
    public static final Integer VALID_SUCCESS_CODE = 2;
    public static final Integer VALID_FAIL_CODE = 3;

    public static final Integer CLOSEABLE_CODE = 1;

    public static final Integer NONEED_REFOUND_CODE = 0;
    public static final Integer NON_REFOUND_CODE = 1;
    public static final Integer PART_REFOUND_CODE = 2;

    public static final Integer SUPPLIER_CODE = 2;
    public static final Integer COMPANY_CODE = 1;

    public static final Integer ALL_RECEIVE_CODE = 2;
    public static final Integer NO_RECEIVE_CODE = 0;

    public static final Integer UN_DELIVERY_DIRECT_CODE = 0;
    public static final Integer IS_DELIVERY_DIRECT_CODE = 1;

    public static final Integer ALL_REFOUND_CODE = 3;
    public static final Integer NO_REFOUND_CODE = 0;

    public static final Integer ALL_INVOICE_REFOUND_CODE = 3;

    public static final Integer TH_BUYORDER_AFTER_SALE_CODE = 546;
    public static final Integer HH_BUYORDER_AFTER_SALE_CODE = 547;


    public static final Integer RETURN_INVOICE_STATUS = 1;

    public static final Integer IS_REFUND_INVOICE_NO = 0;




}
