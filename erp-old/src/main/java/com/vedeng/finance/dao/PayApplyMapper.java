package com.vedeng.finance.dao;

import com.vedeng.finance.dto.PayVedengBankDto;
import com.vedeng.finance.model.BankBill;
import com.vedeng.finance.model.PayApply;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @description: VDERP-1215 付款申请增加批量操作功能.
 * @version: 1.0.
 * @date: 2019/9/16 13:14.
 * @author: Tomcat.Hui.
 */
public interface PayApplyMapper {

    PayApply selectByPrimaryKey(Integer payApplyId);

    Integer queryAutoPayConfig(Integer id);

    /**
     * 获取当前最新的申请记录
     * @param payApply
     * @return
     */
    PayApply getPayApplyMaxRecord(PayApply payApply);


    /**
     * 根据条件动态修改申请表数据
     * @param payApply
     * @return
     */
    int updatePayStutas(PayApply payApply);
    int updateBillMethod(PayApply payApply);

    /**
     * g根据申请id获得数据
     * @param payApply
     * @return
     */
    PayApply getPayApplyRecord(PayApply payApply);


    BigDecimal getPayApplyTotalAmount(Map<String, Object> map);

    BigDecimal getPayApplyPayTotalAmount(Map<String, Object> map);

    List<PayApply> getPayApplyListPage(Map<String, Object> map);

    List<BankBill> getMatchInfo(@Param("amt") BigDecimal amt,@Param("accName1") String accName1,@Param("searchBeginTime") String searchBeginTime,@Param("searchEndTime") String searchEndTime);

    /**
     * 补充付款申请对应的工程师信息
     * @param afterSalesId
     * @return
     */
    PayApply getPayApplyMoreInfo(@Param("afterSalesId") Integer afterSalesId);

    int insertSelective(PayApply record);

    int updateByPrimaryKeySelective(PayApply payApply);

    Map<String,BigDecimal> queryPassedByBuyorderExpenseGoodsId(Integer buyorderExpenseGoodsId);

    /**迁移db
     * <b>Description:</b><br> 获取付款申请
     * @param payApply
     * @return
     * @Note
     * <b>Author:</b> leo.yang
     * <br><b>Date:</b> 2017年9月7日 下午4:14:30
     */
    List<PayApply> getPayApplyList(PayApply payApply);

    /**
     * 根据主键id获取数据
     */
    List<PayApply> getPayApplyListById(@Param("ids") List<Integer> ids);
    /**迁移db
     * <b>Description:</b><br> 查询供应商当前待审核付款申请总额
     * @param
     * @return
     * @Note
     * <b>Author:</b>
     * <br><b>Date:</b> 2018年1月23日 上午11:25:22
     */
    BigDecimal getApplyPayTotalAmountByTraderSupplierId(@Param("traderId")Integer traderId,@Param("companyId")Integer companyId);

    /**
     * 获取付款银行
     *
     * @return list
     */
    List<PayVedengBankDto> getPayVedengBankList();


    /**
     * 更新制单的付款银行和备注信息
     *
     */
    void updatePayApplyIsBillInfo(@Param("payApplyId")Integer payApplyId,
                                  @Param("validComments")String validComments,
                                  @Param("payVedengBankId")Integer payVedengBankId, @Param("payBankTypeName")String payBankTypeName);


    void updatePayApplyComment(@Param("payApplyId")Integer payApplyId,
                                  @Param("validComments")String validComments);

    /**
     * 根据付款银行ID获取银行名称
     * @param payVedengBankId
     * @return
     */
    PayVedengBankDto getPayVedengBankByPayBankId(@Param("payVedengBankId")Integer payVedengBankId);

    /**
     * 根据付款申请类型、关联表id、审核状态查询对应的付款申请集合
     *
     * @param payType     付款申请类型
     * @param relatedId   关联表id
     * @param validStatus 审核状态
     * @return List<PayApply>
     */
    List<PayApply> getPayApplyListByOrderIdAndPayType(@Param("payType") Integer payType, @Param("relatedId") Integer relatedId, @Param("validStatus") Integer validStatus);
}
