package com.wms.service.chain.step;

import com.vedeng.aftersales.dao.AfterSalesDetailMapper;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.vo.AfterSalesDetailVo;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.util.DateUtil;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.vo.SaleorderGoodsVo;
import com.vedeng.trader.dao.TraderMapper;
import com.vedeng.trader.model.Trader;
import com.wms.constant.WMSContant;
import com.wms.constant.WmsInterfaceOrderType;
import com.wms.dto.*;
 
import com.wms.model.po.WmsSendOrder;
import com.wms.service.LogicalSaleorderChooseService;
import com.wms.service.WMSInterfaceFactory;
import com.wms.service.WmsInterface;
import com.wms.service.chain.AbstractHandlerStep;
import com.wms.service.chain.HandlerStepContext;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 发送WMS请求处理器
 */
@Service
public class PurchaseReturnOutWmsRequestHandler extends AbstractHandlerStep {

    private static final Logger LOGGER = LoggerFactory.getLogger(PurchaseReturnOutWmsRequestHandler.class);

    @Resource
    private WMSInterfaceFactory wmsInterfaceFactory;

    @Resource
    private BuyorderMapper buyorderMapper;

    @Resource
    private TraderMapper traderMapper;

    @Resource
    private UserMapper userMapper;

    @Resource
    private LogicalSaleorderChooseService logicalSaleorderChooseService;

    @Resource
    private AfterSalesDetailMapper afterSalesDetailMapper;

    @Override
    protected void doDealWith(HandlerStepContext context){

        AfterSalesVo afterSaleInfo = context.get("afterSalesInfo");
        List<PutSaleOrderGoodsDto> outputOrderGoodsList = context.get("outputOrderGoodsList");
        User user = context.get("user");

        User userDb = userMapper.getUserInfoByUserId(user.getUserId());

        Buyorder buyorder = buyorderMapper.selectByPrimaryKey(afterSaleInfo.getOrderId());
        AfterSalesDetailVo afterSalesDetail = afterSalesDetailMapper.getAfterSalesDetailByAfterSalesId(afterSaleInfo.getAfterSalesId());

        PutSaleOrderDto putSaleOrderDto = new PutSaleOrderDto();
        putSaleOrderDto.setDocNo(afterSaleInfo.getAfterSalesNo());
        putSaleOrderDto.setOrderType(WmsInterfaceOrderType.OUT_PURCHASE_RETURN);
        putSaleOrderDto.setOrderTime(DateUtil.convertString(afterSaleInfo.getAddTime(),"yyyy-MM-dd HH:mm:ss"));
        putSaleOrderDto.setExpectedShipmentTime1(DateUtil.convertString(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss"));
        //下发wms部门缺失问题处理start
        if(user.getPositionName() == null || user.getPositionName().equals("")){
            putSaleOrderDto.setSoReferenceA("/");//合并代码
        }else {
            putSaleOrderDto.setSoReferenceA(user.getPositionName());
        }
        //下发wms部门缺失问题处理end
        putSaleOrderDto.setSoReferenceB(user.getUsername());


        Trader trader = traderMapper.getTraderByTraderId(buyorder.getTraderId());

        //客户ID或供应商ID
        putSaleOrderDto.setConsigneeId(buyorder.getTraderId() + Strings.EMPTY);
        //客户名称或供应商名称
        putSaleOrderDto.setConsigneeName(trader.getTraderName());

        String[] areas = afterSalesDetail.getArea().trim().split(" ");
        //直辖市的下发split会出错---处理start
        //省
        putSaleOrderDto.setConsigneeProvince(areas[0]);
        //城市
        if(areas.length >= 2){
            putSaleOrderDto.setConsigneeCity(areas[1]);
        }else {
            putSaleOrderDto.setConsigneeCity("");//直辖市塞空字符串
        }
        //区
        if(areas.length >= 3){
            putSaleOrderDto.setConsigneeDistrict(areas[2]);
        }else {
            putSaleOrderDto.setConsigneeDistrict("");//直辖市塞空字符串
        }
        //直辖市的下发split会出错---处理end
        //收货详细地址
        putSaleOrderDto.setConsigneeAddress1(afterSalesDetail.getAddress());
        //收货人联系人
        putSaleOrderDto.setConsigneeContact(afterSalesDetail.getTraderContactName());
        //收货人电话
        putSaleOrderDto.setConsigneeTel1(afterSalesDetail.getTraderContactTelephone());
        //收货人手机
        putSaleOrderDto.setConsigneeTel2(afterSalesDetail.getTraderContactMobile());

        putSaleOrderDto.setDetails(outputOrderGoodsList);


        LOGGER.info("采购单:"+buyorder.getBuyorderNo()+",下发退货出库单===============start");

        //下发存表，V_WMS_SEND_ORDER
        WmsSendOrder wmsSendOrder = logicalSaleorderChooseService.getWmsSendOrder(afterSaleInfo.getAfterSalesNo(),afterSaleInfo.getAfterSalesId(), ErpConst.FOUR,user);
        try{
            WmsInterface wmsInterface = wmsInterfaceFactory.getWmsInterface(WMSContant.PUT_ORIGINAL_SALESORDER);
            WmsResponse wmsResponse = wmsInterface.request(putSaleOrderDto);

            //取消失败
            if("0".equals(wmsResponse.getReturnFlag())){
                //throw new Exception("WMS下发采购退货出库单失败");
                LOGGER.info("WMS下发采购退货出库单失败");
            }else {
                //下发成功，更新V_WMS_SEND_ORDER状态
                logicalSaleorderChooseService.saveWmsSendOrder(wmsSendOrder);
            }
        }catch (Exception e){
            LOGGER.error("WMS下发采购退货出库单失败,{}",e);
        }

    }
}

