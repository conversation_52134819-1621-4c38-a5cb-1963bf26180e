<!doctype html>
<html lang="en"> 
 <head> 
  <meta charset="UTF-8"> 
  <title>GE商机-导出</title> 
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet"> 
 </head> 
 <body id="64optiYdPSQ" datasource="erp-reportdatasource" fixednumber="0" fixednumberright="0" append_column_url="" append_row_url="" empty_show="" firstcol="">
  <div id="appendHead"></div> 
  <div class="layui-fluid"> 
   <div class="layui-tab"> 
    <ul class="layui-tab-title" id="tab"> 
    </ul> 
   </div> 
   <form class="layui-form" id="search"> 
    <div class=" layui-inline "><label class="layui-form-label">报单日期</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="17" name="ADD_TIME" placeholder="" style="" alias="a" jdbctype="DATE" data="" datatype="" oper="BETWEEN" validate_rules="null" validate_messages="null"></object> 
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">终端医院名称</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="19" name="TERMINAL_TRADER_NAME" placeholder="" style="" alias="a" jdbctype="VARCHAR" data="" datatype="" oper="LIKE" validate_rules="null" validate_messages="null"></object> 
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">经销商名称</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="19" name="TRADER_NAME" placeholder="" style="" alias="a" jdbctype="VARCHAR" data="" datatype="" oper="LIKE" validate_rules="null" validate_messages="null"></object> 
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">意向型号</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="19" name="GOODS_NAME" placeholder="" style="" alias="a" jdbctype="VARCHAR" data="" datatype="" oper="LIKE" validate_rules="null" validate_messages="null"></object> 
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">报单状态</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="21" name="STATUS" placeholder="" style="" alias="a" jdbctype="VARCHAR" data="[{&quot;V&quot;:&quot;待审核&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;可跟进&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;不可跟进&quot;,&quot;K&quot;:&quot;2&quot;}]" datatype="JSON" oper="EQ" validate_rules="null" validate_messages="null"></object> 
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">是否维护GE商机</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="21" name="IS_DEFEND" placeholder="" style="" alias="a" jdbctype="" data="[{&quot;V&quot;:&quot;否&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;是&quot;,&quot;K&quot;:&quot;1&quot;}]" datatype="JSON" oper="" validate_rules="null" validate_messages="null"></object> 
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">报价单号</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="19" name="QUOTEORDER_NO" placeholder="" style="" alias="a" jdbctype="VARCHAR" data="" datatype="" oper="LIKE" validate_rules="null" validate_messages="null"></object> 
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">GE商机号</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="19" name="GE_BUSSINESS_CHANCE_NO" placeholder="" style="" alias="a" jdbctype="" data="" datatype="" oper="LIKE" validate_rules="null" validate_messages="null"></object> 
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">商机状态</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="21" name="BUSINESS_CHANCE_STATUS" placeholder="" style="" alias="c" jdbctype="VARCHAR" data="[{&quot;V&quot;:&quot;跟进中&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;赢单&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;失单&quot;,&quot;K&quot;:&quot;3&quot;}]" datatype="JSON" oper="" validate_rules="null" validate_messages="null"></object> 
     </div>
    </div>
   </form> 
   <hr class="layui-border-blue"> 
   <div class="btn-group   bd-highlight" id="tableButton"> 
   </div> 
   <table id="table" class="layui-table" style=" width:100%"> 
    <thead> 
     <tr id="column"> 
      <th type="rowbutton" id="rowbutton"><object item_name="" name="" url="/businesschance/ge/viewgebusinesschance.do?geBussinessChanceId=${GE_BUSSINESS_CHANCE_ID}" opentype="PARENT" windowname="" style area type="15">查看</object><object item_name="" name="" url="/ge/authorization/editview.do?geBussinessChanceId=${GE_BUSSINESS_CHANCE_ID}" opentype="PARENT" windowname="" style area type="15">申请授权书</object></th> 
      <th item_name="ADD_TIME" name="ADD_TIME" body="26" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">报单日期</th>
      <th item_name="GE_BUSSINESS_CHANCE_NO" name="GE_BUSSINESS_CHANCE_NO" body="24" order="0" url="/businesschance/ge/viewgebusinesschance.do?geBussinessChanceId=${GE_BUSSINESS_CHANCE_ID}" opentype="PARENT" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">GE商机号</th>
      <th item_name="QUOTEORDER_NO" name="QUOTEORDER_NO" body="26" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">报价单号</th>
      <th item_name="TERMINAL_TRADER_NAME" name="TERMINAL_TRADER_NAME" body="26" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">终端医院名称</th>
      <th item_name="HOSTPITAL_TYPE" name="HOSTPITAL_TYPE" body="26" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">医院性质</th>
      <th item_name="TITLE" name="TITLE" body="26" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">商机来源</th>
      <th item_name="TRADER_NAME" name="TRADER_NAME" body="26" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">经销商名称</th>
      <th item_name="GOODS_NAME" name="GOODS_NAME" body="26" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">意向型号</th>
      <th item_name="SALES_AREA" name="SALES_AREA" body="26" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">所属地区</th>
      <th item_name="ADDRESS" name="ADDRESS" body="26" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">具体地址</th>
      <th item_name="STATUS" name="STATUS" body="26" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">报单状态</th>
      <th item_name="IS_DEFEND" name="IS_DEFEND" body="26" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">是否维护GE商机</th>
      <th item_name="BUSINESS_CHANCE_STATUS" name="BUSINESS_CHANCE_STATUS" body="26" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">商机状态</th>
      <th item_name="SALES_AMOUNT" name="SALES_AMOUNT" body="26" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">销售额（万元）</th>
      <th item_name="HOSPITAL_SIZE" name="HOSPITAL_SIZE" body="26" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">医院等级规模</th>
      <th item_name="BED_NUM" name="BED_NUM" body="26" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">床位数（张）</th>
      <th item_name="FORMAT(C.YEAR_IN_SUM,2)" name="FORMAT(C.YEAR_IN_SUM,2)" body="26" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">年营收（万元）</th>
      <th item_name="CT_DAILY_NUM" name="CT_DAILY_NUM" body="26" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">CT患者量（日）</th>
      <th item_name="IS_NEW_HOSPITAL" name="IS_NEW_HOSPITAL" body="26" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">是否新建医院</th>
      <th item_name="NEW_HOSPITAL_PERCENT" name="NEW_HOSPITAL_PERCENT" body="26" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">新建院用场地进度</th>
      <th item_name="EXPECT_BUY_TIME" name="EXPECT_BUY_TIME" body="26" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">预计采购月份</th>
      <th item_name="COMPETE_NAME" name="COMPETE_NAME" body="26" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">现有竞争对手</th>
      <th item_name="COMPETE_SKU_NAME" name="COMPETE_SKU_NAME" body="26" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">竞争对手产品</th>
      <th item_name="COMPETE_SKU_PRICE" name="COMPETE_SKU_PRICE" body="26" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">竞品价格（万元）</th>
      <th item_name="IS_NEW_INSTALL" name="IS_NEW_INSTALL" body="26" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">是否新增</th>
      <th item_name="ORIGIN_SKU_BAND" name="ORIGIN_SKU_BAND" body="26" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">原有品牌</th>
      <th item_name="ORIGIN_SKU_MODEL" name="ORIGIN_SKU_MODEL" body="26" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">原有型号</th>
      <th item_name="INSTALL_TIME" name="INSTALL_TIME" body="26" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">装机时间</th>
      <th item_name="SALE_COMPANY_NAME" name="SALE_COMPANY_NAME" body="26" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">销售公司</th>
      <th item_name="WIN_ORDER_PERCENT" name="WIN_ORDER_PERCENT" body="26" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">赢单率</th>
      <th item_name="MONEY_SOURCE" name="MONEY_SOURCE" body="26" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">资金来源</th>
      <th item_name="MONEY_SITUATION" name="MONEY_SITUATION" body="26" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">资金情况</th>
      <th item_name="FORMAT(C.PREPARE_AMOUNT,2)" name="FORMAT(C.PREPARE_AMOUNT,2)" body="26" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">预算/预备资金金额（万元）</th>
      <th item_name="BUY_TYPE" name="BUY_TYPE" body="26" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">采购形式</th>
      <th item_name="IS_APPLY_INSPECT" name="IS_APPLY_INSPECT" body="26" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">是否安排考察</th>
      <th item_name="IS_ENGINE_COMPLETE" name="IS_ENGINE_COMPLETE" body="26" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">机房是否已经布置完成</th>
      <th item_name="QUOTE_METHOD" name="QUOTE_METHOD" body="26" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">报价方案</th>
      <th item_name="QUOTE_METHOD_PRICE" name="QUOTE_METHOD_PRICE" body="26" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">方案价格（万元）</th>
      <th item_name="PROJECT_PHASE" name="PROJECT_PHASE" body="26" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">项目阶段</th>
      <th item_name="NEED_COMPLETE" name="NEED_COMPLETE" body="26" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">待解决关键事项</th>
      <th item_name="NEED_TIME" name="NEED_TIME" body="26" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">需求时间</th>
      <th item_name="ASSIST_DEPARTMENT" name="ASSIST_DEPARTMENT" body="26" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">协助部门</th>
      <th item_name="PROJECT_EVOLVE" name="PROJECT_EVOLVE" body="26" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">解决进展</th>
      <th item_name="EVOLVE_TIME" name="EVOLVE_TIME" body="26" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">解决时间</th>
      <th item_name="NEXT_PLAN" name="NEXT_PLAN" body="26" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">下一步计划</th>
      <th item_name="NEED_SUPPORT" name="NEED_SUPPORT" body="26" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">需求要支持（GE或总部）</th>
      <th item_name="IS_HAVING_ACCOUNT" name="IS_HAVING_ACCOUNT" body="26" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">是否有account</th>
      <th item_name="ACCOUNT_NAME" name="ACCOUNT_NAME" body="26" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">account名</th>
      <th item_name="ACCOUNT_ADDRESS" name="ACCOUNT_ADDRESS" body="26" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">account地址</th>
      <th item_name="GSTATUS" name="GSTATUS" body="26" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">报单状态</th>
     </tr> 
    </thead> 
    <tbody> 
     <tr> 
      <td> express:</td> 
      <td colspan="100"> <pre id="express" class="layui-code" orderby="order by a.ADD_TIME desc" groupby="">select 
a.GE_BUSSINESS_CHANCE_ID,
DATE_FORMAT(a.ADD_TIME,'%Y-%m-%d') ADD_TIME,
a.GE_BUSSINESS_CHANCE_NO,
a.QUOTEORDER_NO,
a.TERMINAL_TRADER_NAME,
case when a.HOSPITAL_TYPE =1 then '公立'
when a.HOSPITAL_TYPE =2 then '非公' end HOSTPITAL_TYPE,
b.TITLE, -- 商机来源
a.TRADER_NAME, 
a.GOODS_NAME, -- 意向型号
a.SALES_AREA,
a.ADDRESS,
case when   a.STATUS=0  then '待审核'
when  a.STATUS=1 then '可跟进'
when a.STATUS=2 then '不可跟进' end STATUS,
case when a.IS_DEFEND =0 then '否'
when a.IS_DEFEND =1 then '是' end IS_DEFEND ,

case when c.BUSINESS_CHANCE_STATUS =1 then '跟进中'
when c.BUSINESS_CHANCE_STATUS =2 then '赢单'
when c.BUSINESS_CHANCE_STATUS =3 then '失单' end BUSINESS_CHANCE_STATUS, -- 商机状态 1跟进中 2赢单 3失单'
FORMAT(c.SALES_AMOUNT,2) SALES_AMOUNT, -- 销售额(万元)
d.TITLE HOSPITAL_SIZE, -- 医院等级规模
c.BED_NUM, -- 床位数
FORMAT(c.YEAR_IN_SUM,2), -- 年营收(万元)
c.CT_DAILY_NUM, -- CT患者量(日)
case when c.IS_NEW_HOSPITAL = 1 then '是'
when c.IS_NEW_HOSPITAL = 0 then '否' else '否' end IS_NEW_HOSPITAL, -- 是否新建医院
case when c.NEW_HOSPITAL_PERCENT = 0 then '0%' -- 新建医院场地进度
when c.NEW_HOSPITAL_PERCENT = 1 then '20%' 
when c.NEW_HOSPITAL_PERCENT = 2 then '50%' 
when c.NEW_HOSPITAL_PERCENT = 3 then '70%'
when c.NEW_HOSPITAL_PERCENT = 4 then '100%' end NEW_HOSPITAL_PERCENT,
MONTH(c.EXPECT_BUY_TIME) EXPECT_BUY_TIME, -- 预计采购月份
c.COMPETE_NAME, -- 现有竞争对手
c.COMPETE_SKU_NAME, -- 竞争对手产品
FORMAT(c.COMPETE_SKU_PRICE,2)  COMPETE_SKU_PRICE, --  竞品价格（万元）
case when c.IS_NEW_INSTALL = 1 then '是' else '否' end IS_NEW_INSTALL, -- 是否新增装机信息
c.ORIGIN_SKU_BAND, -- 原有品牌
c.ORIGIN_SKU_MODEL, -- 原有型号
c.INSTALL_TIME, -- 装机时间
c.SALE_COMPANY_NAME,  -- 销售公司
case when c.WIN_ORDER_PERCENT = 1 then '高' -- 赢单率
when c.WIN_ORDER_PERCENT = 2 then '中'
when c.WIN_ORDER_PERCENT = 3 then '低'
else '' end WIN_ORDER_PERCENT,
c.MONEY_SOURCE ,-- 资金来源
c.MONEY_SITUATION, -- 资金情况
FORMAT(c.PREPARE_AMOUNT,2), -- 预算/预备资金金额（万元）
e.TITLE BUY_TYPE, -- 采购形式
case when c.IS_APPLY_INSPECT = 1 then '是'
else '否' end IS_APPLY_INSPECT, -- 是否安排考察
case when c.IS_ENGINE_COMPLETE = 1 then '是'
else '否' end IS_ENGINE_COMPLETE, -- 机房是否布置完成
c.QUOTE_METHOD, -- 报价方案
FORMAT(c.QUOTE_METHOD_PRICE,2) QUOTE_METHOD_PRICE, -- 方案价格（万元）
f.TITLE PROJECT_PHASE, -- 项目阶段
c.NEED_COMPLETE, -- 待解决事项
c.NEED_TIME, -- 需求时间
c.ASSIST_DEPARTMENT, -- 协助部门
c.PROJECT_EVOLVE, -- 解决进展
c.EVOLVE_TIME, -- 解决时间
c.NEXT_PLAN, -- 下一步计划
c.NEED_SUPPORT,  -- 需求要支持（GE或总部）
case when g.IS_HAVING_ACCOUNT = 1 then '是' -- 是否有account
else '否'  end IS_HAVING_ACCOUNT,
g.ACCOUNT_NAME, -- account名
g.ACCOUNT_ADDRESS, -- account地址
case when g.`STATUS` = 1 then '可跟进'  
when g.`STATUS` = 2 then '不可跟进'
else '' end GSTATUS
 from T_GE_BUSINESS_CHANCE a left join T_SYS_OPTION_DEFINITION b on a.GE_BUSINESS_CHANCE_SOURCE=b.SYS_OPTION_DEFINITION_ID
left join T_GE_BUSINESS_CHANCE_DETAIL c on a.GE_BUSSINESS_CHANCE_ID =c.GE_BUSSINESS_CHANCE_ID
left join T_SYS_OPTION_DEFINITION d on c.HOSPITAL_SIZE=d.SYS_OPTION_DEFINITION_ID
left join T_SYS_OPTION_DEFINITION e on c.BUY_TYPE=d.SYS_OPTION_DEFINITION_ID
left join T_SYS_OPTION_DEFINITION f on c.PROJECT_PHASE = f.SYS_OPTION_DEFINITION_ID
left join T_GE_BUSINESS_CHANCE_FEEDBACK g on g.GE_BUSSINESS_CHANCE_ID = a.GE_BUSSINESS_CHANCE_ID
and c.IS_DELETE=0
where 1=1 and a.IS_DELETE=0</pre> </td> 
     </tr> 
     <tr> 
      <td> count:</td> 
      <td colspan="100"> <pre id="count" class="layui-code"></pre> </td> 
     </tr> 
    </tbody> 
   </table> 
  </div> 
  <div id="appendFoot">
   <script>

 $(".dropdown-menu").each(function(){
 var $tr = $(this).parents("tr:first");
var bdStatus = $tr.find('td').eq(11);
if($.trim(bdStatus.html())=="可跟进"  ){

}else{
	$(this).find('.dropdown-item').eq(1).hide();
}
})

</script>
  </div> 
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script> 
  <script>
    layui.use(function () {

    })
</script>  
 </body>
</html>