<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.smallhospital.dao.ELContractSkuModifyMapper">
    <resultMap id="BaseResultMap" type="com.smallhospital.model.ElContractSku">
        <id column="EL_CONTRACT_SKU_ID" jdbcType="INTEGER" property="elContractSkuId" />
        <result column="CONTRACT_MODIFY_ID" jdbcType="INTEGER" property="contractModifyId" />
        <result column="SKU_ID" jdbcType="INTEGER" property="skuId" />
        <result column="CONTRACT_PRICE" jdbcType="DECIMAL" property="contractPrice" />
        <result column="SPU_ID" jdbcType="INTEGER" property="spuId" />
        <result column="PRICE" jdbcType="DECIMAL" property="price" />
        <result column="SHOW_NAME" jdbcType="VARCHAR" property="showName" />
        <result column="REMARK" jdbcType="VARCHAR" property="remark" />

        <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
        <result column="UPDATE_TIME" jdbcType="BIGINT" property="updateTime" />
        <result column="CREATOR" jdbcType="INTEGER" property="creator" />
        <result column="UPDATOR" jdbcType="INTEGER" property="updator" />
    </resultMap>


    <sql id="Base_Column_List">
    EL_CONTRACT_SKU_ID, CONTRACT_MODIFY_ID, SKU_ID, CONTRACT_PRICE, REMARK,  ADD_TIME, UPDATE_TIME, CREATOR, UPDATOR
  </sql>

    <select id="findByModifyContractId" resultType="com.smallhospital.model.vo.ElContractSkuVO">
        select
        T.EL_CONTRACT_SKU_ID, T.CONTRACT_MODIFY_ID, T.SKU_ID,T.PRICE, T.CONTRACT_PRICE, T.REMARK,  T.ADD_TIME, T.UPDATE_TIME, T.CREATOR, T.UPDATOR,V.SPU_ID,V.SHOW_NAME
        from T_EL_CONTRACT_SKU_MODIFY T
        LEFT JOIN V_CORE_SKU V ON T.SKU_ID=V.SKU_ID
        where T.IS_ENABLE=1 AND T.CONTRACT_MODIFY_ID = #{contractModifyId,jdbcType=INTEGER}
    </select>

    <delete id="deleteByContractModifyId">
    UPDATE T_EL_CONTRACT_SKU_MODIFY SET IS_ENABLE=0
    where CONTRACT_MODIFY_ID = #{contractModifyId,jdbcType=INTEGER}
  </delete>

    <delete id="deleteByPrimaryKey">
    UPDATE from T_EL_CONTRACT_SKU_MODIFY SET IS_ENABLE=0
    where EL_CONTRACT_SKU_ID = #{modifySkuId,jdbcType=INTEGER}
  </delete>


    <insert id="batchAddContractSkus" parameterType="java.util.List">
        INSERT INTO T_EL_CONTRACT_SKU_MODIFY (CONTRACT_MODIFY_ID, SKU_ID, CONTRACT_PRICE, REMARK,ADD_TIME, UPDATE_TIME, CREATOR, UPDATOR)
        VALUES
        <foreach collection="list" item="sku" separator=",">
            (
            #{sku.contractModifyId},
            #{sku.skuId},
            #{sku.contractPrice},
            #{sku.remark},
            #{sku.addTime},
            #{sku.updateTime},
            #{sku.creator},
            #{sku.updator}
            )
        </foreach>
    </insert>

    <insert id="insert" keyColumn="EL_CONTRACT_SKU_ID" keyProperty="elContractSkuId" parameterType="com.smallhospital.model.ElContractSku" useGeneratedKeys="true">
    insert into T_EL_CONTRACT_SKU_MODIFY (CONTRACT_MODIFY_ID, SKU_ID,CONTRACT_PRICE,
      REMARK, ADD_TIME, UPDATE_TIME,CREATOR, UPDATOR)
    values (#{contractModifyId,jdbcType=INTEGER}, #{skuId,jdbcType=INTEGER}, #{contractPrice,jdbcType=DECIMAL},
      #{remark,jdbcType=VARCHAR}, #{addTime,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT},#{creator,jdbcType=INTEGER}, #{updator,jdbcType=INTEGER})
  </insert>

    <insert id="insertSelective" keyColumn="EL_CONTRACT_SKU_ID" keyProperty="elContractSkuId" parameterType="com.smallhospital.model.ElContractSku" useGeneratedKeys="true">
        insert into T_EL_CONTRACT_SKU_MODIFY
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="contractModifyId != null">
                CONTRACT_MODIFY_ID,
            </if>
            <if test="skuId != null">
                SKU_ID,
            </if>
            <if test="contractPrice != null">
                CONTRACT_PRICE,
            </if>
            <if test="remark != null">
                REMARK,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="updateTime != null">
                UPDATE_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="updator != null">
                UPDATOR,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="contractModifyId != null">
                #{contractModifyId,jdbcType=INTEGER},
            </if>
            <if test="skuId != null">
                #{skuId,jdbcType=INTEGER},
            </if>
            <if test="contractPrice != null">
                #{contractPrice,jdbcType=DECIMAL},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="updator != null">
                #{updator,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

</mapper>