package com.vedeng.erp.saleorder.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * T_SALE_ORDER_TERMINAL 销售单生效后完善终端信息
 *
 * <AUTHOR>
@Setter
@Getter
public class SaleOrderTerminalEntity extends BaseEntity {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 销售单id
     */
    private Integer saleOrderId;

    /**
     * 终端名称
     */
    private String terminalName;

    /**
     * 大数据终端id（通过大数据查询出对应的终端信息）
     */
    private String dwhTerminalId;

    /**
     * 统一社会信用代码
     */
    private String unifiedSocialCreditIdentifier;

    /**
     * 组织机构代码
     */
    private String organizationCode;

    /**
     * 是否删除
     */
    private Integer isDeleted;

}