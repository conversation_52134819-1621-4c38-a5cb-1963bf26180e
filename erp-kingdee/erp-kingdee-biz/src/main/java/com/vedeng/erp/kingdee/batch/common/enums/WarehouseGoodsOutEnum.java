package com.vedeng.erp.kingdee.batch.common.enums;


/**
 * @description: 出库单枚举类
 * @author: Zeno.zuo
 * @email: <EMAIL>
 * @date: 2022/11/15 11:00
 **/
public enum WarehouseGoodsOutEnum {

    /**
     * 出库单类型枚举
     * 2出库
     * 4销售换货出库
     * 6采购退货出库
     * 7采购换货出库
     * 10外借出库
     * 13报废出库
     * 14领用出库
     * 16 盘亏出库
     * 100 销售退票产生的虚拟出库
     * 18 样品出库
     * 19 库存转换出库
     */
    SALE_WAREHOUSE_OUT(2, "SO", "销售出库", "saleWarehouseGoodsOutDetailServiceImpl"),
    SALE_EXCHANGE_WAREHOUSE_OUT(4, "SS", "销售换货出库", "saleExchangeWarehouseGoodsOutDetailServiceImpl"),
    PURCHASE_RETURN_WAREHOUSE_OUT(6, "PT", "采购售后退货出库", "purchaseReturnWarehouseGoodsOutDetailServiceImpl"),
    PURCHASE_EXCHANGE_WAREHOUSE_OUT(7, "PS", "采购售后换货出库", "purchaseExchangeWarehouseGoodsOutDetailServiceImpl"),
    LEND_WAREHOUSE_OUT(10, "JS", "外借出库", "lendWarehouseGoodsOutDetailServiceImpl"),
    SCRAP_WAREHOUSE_OUT(13, "SB", "报废出库", "scrapWarehouseGoodsOutDetailServiceImpl"),
    RECEIVE_WAREHOUSE_OUT(14, "SA", "领用出库", "receiveWarehouseGoodsOutDetailServiceImpl"),
    INVENTORY_LOSS_WAREHOUSE_OUT(16, "PK", "盘亏出库", "inventoryLossWarehouseGoodsOutDetailServiceImpl"),
    SAMPLE_WAREHOUSE_OUT(18, "YPO", "样品出库", "sampleWarehouseGoodsOutDetailServiceImpl"),
    AFTER_SALE_INVOICE_VIRTURE_OUT(100, "XNCK", "虚拟出库", "saleExchangeWarehouseGoodsOutDetailServiceImpl"),
    UNIT_CONVERSION_OUT(19, "UO", "库存转换出库", "wmsUnitConversionOrderLogicImpl"),
    ;

    private final Integer erpCode;
    private final String wmsCode;
    private final String type;

    private final String service;

    WarehouseGoodsOutEnum(Integer erpCode, String wmsCode, String type, String service) {
        this.erpCode = erpCode;
        this.wmsCode = wmsCode;
        this.type = type;
        this.service = service;
    }

    public Integer getErpCode() {
        return erpCode;
    }

    public String getWmsCode() {
        return wmsCode;
    }

    public String getType() {
        return type;
    }

    public String getService() {
        return service;
    }

    /**
     * 根据 erpCode 获取 type
     *
     * @param erpCode 出库单类型erpCode
     * @return 出库单类型名称
     */
    public static String getTypeByErpCode(Integer erpCode) {
        for (WarehouseGoodsOutEnum warehouseGoodsOutEnum : WarehouseGoodsOutEnum.values()) {
            if (warehouseGoodsOutEnum.erpCode.equals(erpCode)) {
                return warehouseGoodsOutEnum.type;
            }
        }
        return "";
    }

    /**
     * 根据 wmsCode 获取 erpCode
     *
     * @param wmsCode
     * @return erpCode
     */
    public static Integer getErpCodeByCode(String wmsCode) {
        for (WarehouseGoodsOutEnum warehouseGoodsOutEnum : WarehouseGoodsOutEnum.values()) {
            if (warehouseGoodsOutEnum.wmsCode.equals(wmsCode)) {
                return warehouseGoodsOutEnum.erpCode;
            }
        }
        return -1;
    }

    /**
     * 根据 erpCode 获取对应的 service
     *
     * @param erpCode
     * @return service
     */
    public static String getServiceByErpCode(Integer erpCode) {
        for (WarehouseGoodsOutEnum warehouseGoodsOutEnum : WarehouseGoodsOutEnum.values()) {
            if (warehouseGoodsOutEnum.erpCode.equals(erpCode)) {
                return warehouseGoodsOutEnum.service;
            }
        }
        return "";
    }
}
