package com.vedeng.erp.finance.web.api;

import cn.hutool.core.util.BooleanUtil;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.base.ResultInfo;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.erp.finance.domain.dto.SaleInvoiceLimitRequestDto;
import com.vedeng.erp.finance.domain.dto.SaleInvoiceLimitResponseDto;
import com.vedeng.erp.finance.enums.TaxesInterfaceCodeEnum;
import com.vedeng.erp.finance.service.FullyDigitalInvoiceService;
import com.vedeng.erp.finance.service.TaxesOpenApiService;
import com.vedeng.erp.system.dto.SysOptionDefinitionDto;
import com.vedeng.erp.system.service.SysOptionDefinitionApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@ExceptionController
@RestController
@Slf4j
@RequestMapping("/fullyDigitalInvoice/api")
public class FullyDigitalInvoiceApi {
    @Autowired
    private FullyDigitalInvoiceService fullyDigitalInvoiceService;
    @Autowired
    private SysOptionDefinitionApiService sysOptionDefinitionApiService;
    @Autowired
    private TaxesOpenApiService taxesOpenApiService;
    private static final Integer INVOICE_SWITCH_SYS_ID =4460;

    /**
     * 数电发票二维码交付功能
     * @return
     */
    @RequestMapping(method = RequestMethod.GET,value ="/getInvoiceQrCode")
    @NoNeedAccessAuthorization
    public R<?> getInvoiceQrCode(Integer invoiceId){
        return R.success(fullyDigitalInvoiceService.getInvoiceQrCode(invoiceId));
    }

    @NoNeedAccessAuthorization
    @RequestMapping(method = RequestMethod.GET,value = "/getInvoiceLimit")
    @ResponseBody
    public R<?> getInvoiceLimit(){
        SaleInvoiceLimitRequestDto requestDto = new SaleInvoiceLimitRequestDto();
        SaleInvoiceLimitResponseDto responseDto = (SaleInvoiceLimitResponseDto)taxesOpenApiService.openapi(requestDto,
                TaxesInterfaceCodeEnum.LIMIT);
        return R.success(responseDto);
    }

    /**
     * 获取数电全局开关
     * @return
     */
    @NoNeedAccessAuthorization
    @RequestMapping(method = RequestMethod.GET,value = "/getCurrentInvoiceSwitch")
    public R<?> getCurrentInvoiceSwitch(){
        return R.success(BooleanUtil.toInteger(fullyDigitalInvoiceService.isEnableGlobal()));
    }

    @NoNeedAccessAuthorization
    @RequestMapping(method = RequestMethod.GET,value = "/changeInvoiceSwitch")
    @ResponseBody
    public ResultInfo changeInvoiceSwitch(Boolean status){
        SysOptionDefinitionDto sysOptionDefinitionDto = new SysOptionDefinitionDto();
        sysOptionDefinitionDto.setSysOptionDefinitionId(INVOICE_SWITCH_SYS_ID);
        sysOptionDefinitionDto.setStatus(Boolean.TRUE.equals(status) ? Integer.valueOf(1) : Integer.valueOf(0));
        log.info("变更数电发票开关,变更人:{},变更状态:{}", CurrentUser.getCurrentUser().getUsername(),sysOptionDefinitionDto.getStatus());
        int updatedRows = sysOptionDefinitionApiService.updateInvoiceSwitch(sysOptionDefinitionDto);
        if (updatedRows == 0){
            return ResultInfo.error("变更数电发票开关失败，请重试");
        }
        return ResultInfo.success();
    }
}
