package com.vedeng.price.dao;

import com.pricecenter.dto.PriceChangeAffectOrderIndexDto;
import com.vedeng.common.page.Page;
import com.vedeng.price.model.PriceChangeAffectOrder;
import com.vedeng.price.model.PriceChangeReadRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface PriceChangeAffectOrderMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_PRICE_CHANGE_AFFECT_ORDER
     *
     * @mbggenerated Tue Aug 24 15:38:49 CST 2021
     */
    int deleteByPrimaryKey(Long priceChangeAffectOrderId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_PRICE_CHANGE_AFFECT_ORDER
     *
     * @mbggenerated Tue Aug 24 15:38:49 CST 2021
     */
    int insert(PriceChangeAffectOrder record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_PRICE_CHANGE_AFFECT_ORDER
     *
     * @mbggenerated Tue Aug 24 15:38:49 CST 2021
     */
    PriceChangeAffectOrder selectByPrimaryKey(Long priceChangeAffectOrderId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_PRICE_CHANGE_AFFECT_ORDER
     *
     * @mbggenerated Tue Aug 24 15:38:49 CST 2021
     */
    List<PriceChangeAffectOrder> selectAll();

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_PRICE_CHANGE_AFFECT_ORDER
     *
     * @mbggenerated Tue Aug 24 15:38:49 CST 2021
     */
    int updateByPrimaryKey(PriceChangeAffectOrder record);
    /**
     * @Description 批量插入
     * <AUTHOR>
     * @Date 17:48 2021/8/24
     * @Param [record]
     * @return int
     **/
    int insertByList(List<PriceChangeAffectOrder> list);

    List<PriceChangeAffectOrderIndexDto> getAffectOrderBySkuPriceModifyRecordIdListPage(Map<String, Object> map);

    void dealAffrctOrder(Integer priceChangeAffectOrderId);

    Integer isNeedNotify(Map<String, Object> map);

    List<Integer> queryChangePriceSku(Map<String, Object> map);

    Integer isNeedNotifyOrder( @Param("skuids") List<Integer> skuIds, @Param("userId") Integer userId);

    Integer isNeedNotifyQuoteorder(@Param("skuids") List<Integer> skuIds, @Param("userId") Integer userId);
}