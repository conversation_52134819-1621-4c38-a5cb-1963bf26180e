package com.vedeng.erp.kingdee.domain.command;

import com.vedeng.infrastructure.kingdee.domain.command.KingDeeNumberCommand;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 金蝶采购 普票 实际接受参数类
 * @date 2022/11/09 10:11
 */
@Data
public class KingDeePurchaseVatPlainInvoiceCommand {
    /**
     * 单据内码
     */
    private String FID;
    /**
     * 业务日期  录票时间
     */
    private String FDATE;
    /**
     * 贝登erp对应的单据头ID
     */
    private String F_QZOK_BDDJTID;
    /**
     * 发票号23456
     */
    private String FINVOICENO;
    /**
     *  发票日期 2022-09-22 00:00:00
     */
    private String FINVOICEDATE;
    /**
     * 供应商
     */
    private KingDeeNumberCommand FSUPPLIERID = new KingDeeNumberCommand();
    /**
     * 单据状态 默认:Z
     */
    private String FDOCUMENTSTATUS;
    /**
     * 单据类型 CGPTFP01_SYS            采购增值税普通发票
     */
    private KingDeeNumberCommand FBillTypeID = new KingDeeNumberCommand();
    /**
     * 结算组织 默认101,配置化
     */
    private KingDeeNumberCommand FSETTLEORGID = new KingDeeNumberCommand();
    /**
     * 采购组织 默认101,配置化
     */
    private KingDeeNumberCommand FPURCHASEORGID = new KingDeeNumberCommand();
    /**
     * 作废状态 A （正常）
     */
    private String FCancelStatus;
    /**
     * 红蓝字标识 0 蓝字  1 红字
     */
    private String fRedBlue;
    /**
     * 发票代码12345
     */
    private String F_QZOK_FPDM;
    /**
     * 发票类型 1电票 2 纸票
     */
    private String F_QZOK_FPLX;
    /**
     * 发票明细
     */
    private List<PurchaseVatPlainInvoiceDetailCommand> FPURCHASEICENTRY = new ArrayList<>();

    @Data
    public static class PurchaseVatPlainInvoiceDetailCommand{
        /**
         * 物料编码 SKU
         */
        private KingDeeNumberCommand FMATERIALID = new KingDeeNumberCommand();
        /**
         * 计价数量 填上发票的入库数量，
         * 如果是红字发票，则数量*-1
         */
        private String FPRICEQTY;

        /**
         * 采购普通发票税率默认填写0税率
         */
        private BigDecimal ftaxrate;
        /**
         * 含税单价
         */
        private String FAUXTAXPRICE;
        /**
         * 贝登单据行ID
         */
        private String F_QZOK_BDDJHID;
        /**
         * 原始订单号
         */
        private String F_QZOK_YSDDH;
        /**
         * 归属业务单号
         */
        private String F_QZOK_GSYWDH;
        /**
         * 业务类型
         */
        private String F_QZOK_YWLX;
        /**
         * 源单类型
         * 关联应付单： AP_Payable
         * 关联普通发票：IV_PURCHASEOC
         */
        private String FSOURCETYPE;
        /**
         * fpurchaseicentryLink
         */
        private List<PurchaseVatPlainInvoiceDetailLinkCommand> FPURCHASEICENTRY_Link = new ArrayList<>();
    }

    @Data
    public static class PurchaseVatPlainInvoiceDetailLinkCommand{
        /**
         * 实体主键
         */
        private String FLinkId;
        /**
         * fpurchaseicentryLinkFflowid
         */
        private String FPURCHASEICENTRY_Link_FFlowId;
        /**
         * fpurchaseicentryLinkFflowlineid
         */
        private String FPURCHASEICENTRY_Link_FFlowLineId;
        /**
         * 应付单：IV_PayableToPurchaseIC
         * 普通发票：IV_BlueToRedPurchaseOC
         */
        private String FPURCHASEICENTRY_Link_FRuleId;
        /**
         * 源单表内码
         */
        private String FPURCHASEICENTRY_Link_FSTableId;
        /**
         * 源单表
         * 应付单：T_AP_PAYABLEENTRY
         * 普通发票：T_IV_PURCHASEICENTRY1
         */
        private String FPURCHASEICENTRY_Link_FSTableName;
        /**
         *  源单内码
         *  应付单:金蝶应付单表头ID
         * 普通发票：此红票对应蓝票金蝶表头ID
         */
        private String FPURCHASEICENTRY_Link_FSBillId;
        /**
         * 源单分录内码
         * 应付单:金蝶应付单表体行ID
         * 普通发票：此红票对应蓝票金蝶表体行ID
         */
        private String FPURCHASEICENTRY_Link_FSId;
        /**
         * 原始携带量 应收单的表体行数量，即开票数量（红票负数
         */
        private String FPURCHASEICENTRY_Link_FBASICUNITQTYOld;
        /**
         * 修改携带量（实开数量）	是 应收单的表体行数量，即开票数量（红票负数）
         */
        private String FPURCHASEICENTRY_Link_FBASICUNITQTY;
        /**
         * 原始携带金额 FPURCHASEICENTRY_Link_FALLAMOUNTFOROld	原始携带金额	是		应收单的表体行数量*含税单价（红票负数）
         * FPURCHASEICENTRY_Link_FALLAMOUNTFOR 	修改携带量（实开金额）	是		开票数量*含税单价（红票负数）
         */
        private BigDecimal FPURCHASEICENTRY_Link_FALLAMOUNTFOROld;
        /**
         * 修改携带量（实开金额）开票数量*含税单价（红票负数）
         */
        private BigDecimal FPURCHASEICENTRY_Link_FALLAMOUNTFOR;
    }

}
