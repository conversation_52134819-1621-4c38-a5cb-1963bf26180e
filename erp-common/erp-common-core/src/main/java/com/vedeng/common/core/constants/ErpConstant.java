package com.vedeng.common.core.constants;

import com.google.common.collect.Lists;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: erp公共常量类
 * @date 2022/2/10 17:25
 */
public class ErpConstant {

    // 异常提示信息
    public final static String ERROR_MSG = "erperror";

    public final static String BD = "南京贝登医疗股份有限公司";

    public static final String EMPTY = "";

    /**
     * session中的user
     */
    public static final String CURRENT_USER = "current_user";


    /**
     * 默认用户id
     */
    public static final int DEFAULT_USER_ID = 1;

    /**
     * 默认用户username
     */
    public static final String DEFAULT_USERNAME = "admin";

    /**
     * Admin
     */
    public static final Integer ADMIN_ID = 1;

    /**
     * njAdmin
     */
    public static final Integer NJADMIN_ID = 2;

    /**
     * true
     */
    public static final int T = 1;

    /**
     * false
     */
    public static final int F = 0;

    /**
     * id默认值
     */
    public static final int DEFAULT_ID = 0;

    public static final String PERIOD = ".";
    public static final Integer NEGATIVE_ONE = -1;
    public static final String ZEROSTR = ".00";

    public static final Integer ZERO = 0;

    public static final Integer ONE = 1;

    public static final Integer TWO = 2;

    public static final Integer THREE = 3;

    public static final Integer FOUR = 4;

    public static final Integer FIVE = 5;

    public static final Integer SIX = 6;
    public static final Integer SEVEN = 7;

    /**
     * 长度
     */
    public static final int STR_SIZE_LIMIT = 255;

    public static final String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

    /**
     * 销售字典535
     */
    public static final Integer SALE = 535;

    /**
     * 采购字典536
     */
    public static final Integer PURCHASE = 536;



    /**
     * 审核状态 0待审核 1审核通过 2审核不通过
     */
    public static final Integer WAIT_AUDIT = 0;
    public static final Integer AUDIT_PASS = 1;
    public static final Integer AUDIT_NO_PASS = 2;
    /**
     * 审核流结束事件
     */
    public static final String END_EVENT = "endEvent";

    public static final Integer ID_316 = 316;
    public static final Integer ID_317 = 317;
    public static final Integer ID_318 = 318;
    public static final Integer ID_1008 = 1008;
    public static final Integer ID_423 = 423;
    public static final Integer ID_310 = 310; //职位销售
    public static final Integer ID_311 = 311; //职位采购
    public static final Integer ID_427 = 427;
    public static final Integer ID_426 = 426;

    // 终端
    public static final Integer ID_466 = 466;
    // 分销
    public static final Integer ID_465 = 465;
    
    //总机询价
    public static final Integer ID_391 = 391;
    
    //自主询价
    public static final Integer ID_394 = 394;
    /**
     * 联系人名片
     */
    public static final Integer BUSINESS_CARDS = 3600;
    public static final String REVIEW_OF_ADVANCE_INVOICING = "ERP:BATCH:ADVANCE:INVOICE:AUDIT:ID:";
    public static final String REVIEW_OF_ADVANCE_INVOICING_TOTAL = "ERP:BATCH:ADVANCE:INVOICE:AUDIT:TOTAL:";
    public static final String REVIEW_OF_ADVANCE_INVOICING_LOAD = "ERP:BATCH:ADVANCE:INVOICE:AUDIT:LOAD:";
    public static final String REVIEW_OF_ADVANCE_INVOICING_TASK = "ERP:BATCH:ADVANCE:INVOICE:AUDIT:TASK";


    public static final String INVOICING_OPEN_TOTAL = "ERP:BATCH:INVOICE:OPEN:TOTAL:";
    public static final String INVOICING_OPEN_LOAD = "ERP:BATCH:INVOICE:OPEN:LOAD:";
    public static final String INVOICING_OPEN_TASK = "ERP:BATCH:INVOICE:OPEN:TASK";

    public static final String INVOICING_AFTERMARKET_OPEN_TASK = "ERP:BATCH:INVOICE:AFTERMARKET:OPEN:TASK";


    public static final String INVOICE_APPLY_REJECTED_MSG_TEMPLATE = "<font color=\"warning\">** 开票驳回通知 **</font>\n您申请的订单{}的开票申请已被驳回\n驳回原因：{}";
    public static final String QUERENDAN_REJECTED_MSG_TEMPLATE = "您发起的{}的{}订单的确认单审批被驳回，驳回原因：{}";
    public static final String HETONGHUICHUAN_REJECTED_MSG_TEMPLATE = "您发起的{}的{}订单的合同审批审批被驳回，驳回原因：{}";


    /**
     * 开票申请 标识
     */
    public static final String INVOICE_CREATE_TYPE_MANUAL = "MANUAL";

    /**
     * 排除日志内容集合
     */
    public static final List<String> EXCLUDED_LOG_CONTENT_LIST = Collections.unmodifiableList(Lists.newArrayList("已失效"));

    /**
     * 新疆/西藏/内蒙古/青海 REGION_ID
     */
    public static final List<Integer> REMOTE_AREA_REGION_ID = Collections.unmodifiableList(Lists.newArrayList(
            19, 21, 28, 29,
            258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 275, 276, 277, 278, 279, 280, 281, 282, 3660, 3661, 3662, 3663, 3664, 3665, 344, 345, 346, 347, 348, 349, 350, 3483, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 3492, 3493, 3494, 3495, 3496, 3497, 3498, 3499, 3500, 3501,
            2162, 2163, 2164, 2165, 2166, 2167, 2168, 2169, 2170, 2171, 2172, 2173, 2174, 2175, 2176, 2177, 2178, 2179, 2180, 2181, 2182, 2183, 2184, 2185, 2186, 2187, 2188, 2189, 2190, 2191, 2192, 2193, 2194, 2195, 2196, 2197, 2198, 2199, 2200, 2201, 2202, 2203, 2204, 2205, 2206, 2207, 2208, 2209, 3509, 2210, 2211, 2212, 2213, 2214, 2215, 2216, 2217, 2218, 2219, 2220, 2221, 2222, 3510, 2223, 2224, 2225, 2226, 2227, 2228, 2229, 2230, 2231, 2232, 2233, 2234, 2235, 2236, 2237, 2238, 2239, 2240, 2241, 2242, 2243, 2244, 2245, 2246, 2247, 2248, 2249, 2250, 2251, 2252, 2253, 2254, 2255, 2256, 2257, 2258, 2259, 2260, 2261, 2262, 2289, 2290, 2291, 2292, 2293, 2294, 2295, 2296, 2297, 2298, 2299, 2300, 2301, 2302, 2303, 2304, 2305, 2306, 2307, 2308, 2309, 2310, 2311, 3628, 2312, 2313, 2314, 2315, 2316, 2317, 2318, 2319, 2320, 2321, 2322, 2323, 2324, 2325, 2326, 2327, 2328, 2329, 2330, 2331, 2931, 2932, 2933, 2934, 2935, 2936, 2937, 2938, 2939, 2940, 2941, 2942, 2943, 2944, 2945, 2946, 2947, 2948, 2949, 2950, 2951, 2952, 2953, 2954, 2955, 2956, 2957, 2958, 2959, 2960, 2961, 2962, 2963, 2964, 2965, 2966, 2967, 2968, 2969, 2970, 2971, 2972, 2973, 2974, 2975, 2976, 2977, 2978, 2979, 2980, 2981, 2982, 2983, 2984, 2985, 2986, 2987, 2988, 2989, 2990, 2991, 2992, 2993, 2994, 2995, 2996, 2997, 2998, 2999, 3000, 3001, 3002, 3003, 3004, 3005, 3006, 3007, 3008, 3009, 3010, 3011, 3012, 3013, 3014, 3015, 3016, 3017, 3018, 3019, 3020, 3022, 3023, 3024, 3025, 3026, 3027, 3028, 3029, 3030, 3031, 3032, 3033, 3034, 3035, 3036, 3037, 3038, 3039, 3040, 3041, 3042, 3043, 3044, 3634, 3045, 3046, 3047, 3048, 3049, 3050, 3051, 3052, 3053, 3054, 3055, 3056, 3057, 3058, 3059, 3060, 3061, 3062, 3063, 3064, 3065, 3630, 3631, 3632, 3066, 3067, 3068, 3069, 3070, 3071, 3072, 3073, 3074, 3633, 3075, 3077, 3078, 3080, 3081, 3082, 3083, 3084, 3086, 3088, 3089, 3092, 3094, 3095, 3096, 3097, 3098, 3099, 3418, 3430, 3635, 3087, 3090, 3636, 3637, 3638, 3639, 3640, 3641, 3642, 3021, 3076, 3079, 3085, 3091, 3093, 3643, 3644, 3645, 3646, 3647, 3648, 3649, 3650, 3651, 3652, 3653, 3654, 3655, 3656, 3657, 3658, 3659));

    /**
     * 子公司供应商的traderId
     */
    public static final Integer SUBSIDIARY_TRADER_ID = 613042;

    public static final BigDecimal TEN_THOUSAND = new BigDecimal("10000");
    /**
     * 银行承兑汇票
     */
    public static final Integer BANK_ACCEPTANCE = 10001;
}
