package com.vedeng.api.standard.validation;

import java.util.ArrayList;
import java.util.List;

/**
 * 验证结果类
 * 封装验证的结果信息
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-27
 */
public class ValidationResult {
    
    /**
     * 是否验证通过
     */
    private boolean valid;
    
    /**
     * 错误消息列表
     */
    private List<String> errorMessages;
    
    /**
     * 警告消息列表
     */
    private List<String> warningMessages;
    
    /**
     * 验证规则名称
     */
    private String ruleName;
    
    /**
     * 额外的验证信息
     */
    private Object additionalInfo;
    
    /**
     * 私有构造函数
     */
    private ValidationResult() {
        this.errorMessages = new ArrayList<>();
        this.warningMessages = new ArrayList<>();
    }
    
    /**
     * 创建成功的验证结果
     * 
     * @return 成功的验证结果
     */
    public static ValidationResult success() {
        ValidationResult result = new ValidationResult();
        result.valid = true;
        return result;
    }
    
    /**
     * 创建成功的验证结果（带规则名称）
     * 
     * @param ruleName 规则名称
     * @return 成功的验证结果
     */
    public static ValidationResult success(String ruleName) {
        ValidationResult result = success();
        result.ruleName = ruleName;
        return result;
    }
    
    /**
     * 创建失败的验证结果
     * 
     * @param errorMessage 错误消息
     * @return 失败的验证结果
     */
    public static ValidationResult failure(String errorMessage) {
        ValidationResult result = new ValidationResult();
        result.valid = false;
        result.errorMessages.add(errorMessage);
        return result;
    }
    
    /**
     * 创建失败的验证结果（带规则名称）
     * 
     * @param ruleName 规则名称
     * @param errorMessage 错误消息
     * @return 失败的验证结果
     */
    public static ValidationResult failure(String ruleName, String errorMessage) {
        ValidationResult result = failure(errorMessage);
        result.ruleName = ruleName;
        return result;
    }
    
    /**
     * 添加错误消息
     * 
     * @param errorMessage 错误消息
     * @return 当前对象（支持链式调用）
     */
    public ValidationResult addError(String errorMessage) {
        this.valid = false;
        this.errorMessages.add(errorMessage);
        return this;
    }
    
    /**
     * 添加警告消息
     * 
     * @param warningMessage 警告消息
     * @return 当前对象（支持链式调用）
     */
    public ValidationResult addWarning(String warningMessage) {
        this.warningMessages.add(warningMessage);
        return this;
    }
    
    /**
     * 设置额外信息
     * 
     * @param additionalInfo 额外信息
     * @return 当前对象（支持链式调用）
     */
    public ValidationResult setAdditionalInfo(Object additionalInfo) {
        this.additionalInfo = additionalInfo;
        return this;
    }
    
    /**
     * 合并其他验证结果
     * 
     * @param other 其他验证结果
     * @return 当前对象（支持链式调用）
     */
    public ValidationResult merge(ValidationResult other) {
        if (other != null) {
            if (!other.valid) {
                this.valid = false;
            }
            this.errorMessages.addAll(other.errorMessages);
            this.warningMessages.addAll(other.warningMessages);
        }
        return this;
    }
    
    /**
     * 获取第一个错误消息
     * 
     * @return 第一个错误消息，如果没有错误则返回null
     */
    public String getFirstErrorMessage() {
        return errorMessages.isEmpty() ? null : errorMessages.get(0);
    }
    
    /**
     * 获取所有错误消息的字符串表示
     * 
     * @return 错误消息字符串
     */
    public String getErrorMessagesAsString() {
        return String.join("; ", errorMessages);
    }
    
    /**
     * 是否有警告
     * 
     * @return 是否有警告
     */
    public boolean hasWarnings() {
        return !warningMessages.isEmpty();
    }
    
    // Getter methods
    public boolean isValid() {
        return valid;
    }
    
    public List<String> getErrorMessages() {
        return errorMessages;
    }
    
    public List<String> getWarningMessages() {
        return warningMessages;
    }
    
    public String getRuleName() {
        return ruleName;
    }
    
    public Object getAdditionalInfo() {
        return additionalInfo;
    }
    
    @Override
    public String toString() {
        return "ValidationResult{" +
                "valid=" + valid +
                ", ruleName='" + ruleName + '\'' +
                ", errorMessages=" + errorMessages +
                ", warningMessages=" + warningMessages +
                '}';
    }
}
