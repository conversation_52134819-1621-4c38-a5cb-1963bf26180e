package com.vedeng.finance.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.redis.redission.RedissonLockUtils;
import com.vedeng.common.redis.utils.RedisUtil;
import com.vedeng.erp.finance.dto.InvoiceApplyDetailDto;
import com.vedeng.erp.finance.dto.InvoiceApplyDto;
import com.vedeng.erp.finance.dto.InvoiceCheckRequestDto;
import com.vedeng.erp.finance.dto.InvoiceCheckResultDto;
import com.vedeng.erp.finance.enums.CheckChainEnum;
import com.vedeng.erp.finance.service.InvoiceApplyApiService;
import com.vedeng.erp.finance.service.InvoiceCheckApiService;
import com.vedeng.finance.service.InvoiceApplyAuditService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2024/1/15 10:07
 **/
@Service
@Slf4j
public class InvoiceApplyAuditServiceImpl implements InvoiceApplyAuditService {


    @Autowired
    private InvoiceCheckApiService invoiceCheckApiService;

    @Autowired
    private InvoiceApplyApiService invoiceApplyApiService;

    @Override
    public void doAdvancePageAudit(boolean isLoading) {

        // 分页
        int pageSize = 100;
        int pageNum = 1;
        Page<Object> page = new Page<>();
        page.setPageSize(pageSize);
        page.setPageNum(pageNum);

        PageInfo<InvoiceApplyDto> data = invoiceApplyApiService.getAdvanceInvoiceApply(page);
        // 总条数
        if (isLoading) {
            long total = data.getTotal();
            RedisUtil.KeyOps.delete(ErpConstant.REVIEW_OF_ADVANCE_INVOICING_TOTAL);
            RedisUtil.KeyOps.delete(ErpConstant.REVIEW_OF_ADVANCE_INVOICING_LOAD);
            RedisUtil.StringOps.incrBy(ErpConstant.REVIEW_OF_ADVANCE_INVOICING_TOTAL, total);
            RedisUtil.StringOps.incrBy(ErpConstant.REVIEW_OF_ADVANCE_INVOICING_LOAD, 0);
        }

        pageNum = data.getPages();

        while (true) {
            log.info("分页执行进度 {},{}", pageSize, pageNum);
            // 调用开票
            Page<Object> pageDes = new Page<>();
            pageDes.setPageSize(pageSize);
            pageDes.setPageNum(pageNum);
            PageInfo<InvoiceApplyDto> advanceInvoiceApply = invoiceApplyApiService.getAdvanceInvoiceApply(pageDes);
            List<InvoiceApplyDto> list = advanceInvoiceApply.getList();
            if (list.isEmpty()) {
                break;
            }
            list.forEach(x-> {
                try {
                    InvoiceApplyAuditServiceImpl bean = SpringUtil.getBean(InvoiceApplyAuditServiceImpl.class);
                    bean.doAudit(x);
                } catch (Exception e) {
                    log.error("提前开票审核：{}, doWithPage 执行失败", JSON.toJSONString(x),e);
                }
                if (isLoading) {
                    RedisUtil.StringOps.incrBy(ErpConstant.REVIEW_OF_ADVANCE_INVOICING_LOAD, 1);
                }

            });

            if (!advanceInvoiceApply.isHasPreviousPage()) {
                break;
            }
            pageNum = advanceInvoiceApply.getPrePage();
            log.info("分页查询进度 {},{}", pageSize, pageNum);
        }

    }

    public void doAudit(InvoiceApplyDto x) {

        // 加锁
        String key = ErpConstant.REVIEW_OF_ADVANCE_INVOICING + x.getInvoiceApplyId();
        boolean lock = RedissonLockUtils.tryLock(key);

        if (!lock) {
            log.info("加锁失败，invoiceApplyId:{},存在处理中的审核业务", x.getInvoiceApplyId());
        }
        try {
            // 校验状态
            InvoiceApplyDto invoiceApply = invoiceApplyApiService.getInvoiceApply(x.getInvoiceApplyId());
            if (Objects.isNull(invoiceApply) || invoiceApply.getAdvanceValidStatus() != 0) {
                log.info("InvoiceApplyDto:id:{},提前开票状态已经改变", x.getInvoiceApplyId());
                return;
            }
            InvoiceCheckRequestDto data = new InvoiceCheckRequestDto();
            BeanUtil.copyProperties(invoiceApply, data);
            List<InvoiceApplyDetailDto> invoiceApplyDetailDtoList = invoiceApply.getInvoiceApplyDetailDtoList();
            List<InvoiceCheckRequestDto.InvoiceCheckRequestDetailDto> invoiceCheckRequestDetailDtoList = invoiceApplyDetailDtoList.stream().map(a -> {
                InvoiceCheckRequestDto.InvoiceCheckRequestDetailDto detailDto = new InvoiceCheckRequestDto.InvoiceCheckRequestDetailDto();
                detailDto.setDetailGoodsId(a.getDetailgoodsId());
                detailDto.setInvoiceApplyDetailId(a.getInvoiceApplyDetailId());
                detailDto.setNum(a.getNum());
                detailDto.setPrice(a.getPrice());
                detailDto.setTotalAmount(a.getTotalAmount());
                return detailDto;
            }).collect(Collectors.toList());
            data.setDetailList(invoiceCheckRequestDetailDtoList);
            data.setCheckChainEnum(Integer.valueOf(504).equals(invoiceApply.getType()) ? CheckChainEnum.INVOICE_APPLY_AFTER : CheckChainEnum.INVOICE_APPLY_SALES);


            // 业务校验
            InvoiceCheckResultDto invoiceCheckResultDto = invoiceCheckApiService.applyCheck(data);
            log.info("提前开票id:{},校验结果：{}", x.getInvoiceApplyId(), JSON.toJSONString(invoiceCheckResultDto));
            // 更改状态
            if (invoiceCheckResultDto.getSuccess()) {
                CurrentUser currentUser = CurrentUser.getCurrentUser();
                InvoiceApplyDto update = new InvoiceApplyDto();
                long time = new Date().getTime();
                update.setInvoiceApplyId(x.getInvoiceApplyId());
                update.setAdvanceValidTime(time);
                update.setAdvanceValidUserid(currentUser.getId());
                update.setAdvanceValidStatus(1);
                update.setUpdater(currentUser.getId());
                update.setModTime(time);
                log.info("提前开票审核通过：{}", JSON.toJSONString(update));
                invoiceApplyApiService.doAdvanceAuditPass(update);
                // 审核通过后触发开票配置-开票规则校验
                data.setCheckChainEnum(Integer.valueOf(504).equals(invoiceApply.getType()) ? CheckChainEnum.INVOICE_OPEN_AFTER : CheckChainEnum.INVOICE_OPEN_SALES);
                invoiceCheckApiService.openCheck(data);
            }

        } catch (Exception e) {
            log.error("提前开票id:{},异常：",x.getInvoiceApplyId(),e);
        }finally {
            RedissonLockUtils.unlock(key);
        }

    }
}
