package com.vedeng.erp.aftersale.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.*;



/**
 * Table: T_AFTER_SALES_TO_YXB
 */
@Getter
@Setter
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AfterSalesToYxbEntity extends BaseEntity {
    /**
     * Column: AFTER_SALES_TO_YXB_ID
     * Type: INT UNSIGNED
     * Remark: id
     */
    private Integer afterSalesToYxbId;

    /**
     * Column: INTERFACE_TYPE
     * Type: INT
     * Default value: -1
     * Remark: 接口类型 0 推送 1 取消推送 2 关闭 3 完结
     */
    private Integer interfaceType;

    /**
     * Column: SUCCESS
     * Type: INT
     * Default value: 1
     * Remark: 1 成功 0 失败
     */
    private Integer success;

    /**
     * Column: AFTER_SALES_ORDER_ID
     * Type: INT
     * Default value: -1
     * Remark: 售后单id
     */
    private Integer afterSalesOrderId;

    /**
     * Column: AFTER_SALES_ORDER_NO
     * Type: VARCHAR(50)
     * Remark: 售后单号
     */
    private String afterSalesOrderNo;

    /**
     * Column: IS_DELETE
     * Type: INT
     * Default value: 0
     * Remark: 是否删除 0 否 1 是
     */
    private Integer isDelete;

    /**
     * Column: PARAM
     * Type: JSON(0)
     * Remark: 入参
     */
    private String param;

    /**
     * Column: RESULT
     * Type: JSON(0)
     * Remark: 结果
     */
    private String result;

}