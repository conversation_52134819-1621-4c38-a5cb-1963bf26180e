package com.vedeng.trader.model;

import java.io.Serializable;
import lombok.Data;

/**
 * T_WEB_ACCOUNT_INVITATION_LOG
 * <AUTHOR>
@Data
public class WebAccountInvitationLog implements Serializable {
    private Long id;

    /**
     * 邀请人手机号码
     */
    private String inviterMobile;

    /**
     * 邀请人所属客户ID
     */
    private Integer inviteeTraderId;

    /**
     * 用户注册时平台编号 1:贝登
     */
    private Integer rerigterPlatform;

    /**
     * 注册人手机号码
     */
    private String registerMobile;

    private Long createTime;

    private Long updateTime;

    private Integer creator;

    private Integer updater;

    private Boolean isDelete;

    private static final long serialVersionUID = 1L;
}