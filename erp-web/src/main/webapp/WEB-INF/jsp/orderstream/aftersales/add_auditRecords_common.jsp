<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<link rel="stylesheet" href="<%= basePath %>static/css/orderstream/aftersales-detail.css">
<div class="parts">
    <table class="table table-bordered table-striped table-condensed table-centered">
        <thead>
        <tr>
            <th class="a-th-choice athchoice">
                <button class="a-bu-choice athchoicebu" onclick="auditRecordsCommonClose(1,this);">审核记录
                    <c:choose>
                        <c:when test="${afterSalesVo.atferSalesStatus eq 0 && afterSalesVo.status eq 1}">
                            <span style="margin-left: 10px;" class="red"> [审]</span>
                        </c:when>
                    </c:choose>
                </button>
            </th>
            <th class="a-th-noChoice athchoice">
                <button class="a-bu-noChoice athchoicebu" onclick="auditRecordsCommonClose(2,this);">售后完结/关闭审核记录
                    <c:choose>
                        <c:when test="${afterSalesVo.atferSalesStatus eq 1 && ((null!=taskInfoOver and null!=taskInfoOver.getProcessInstanceId() and null!=taskInfoOver.assignee) or !empty candidateUserMapOver[taskInfoOver.id])}">
                            <span style="margin-left: 10px;" class="red"> [审]</span>
                        </c:when>
                    </c:choose>
                </button>
            </th>
            <%--<c:if test="${afterSalesVo.type eq 539}">
                <th class="a-th-noChoice athchoice">
                    <button class="a-bu-noChoice athchoicebu" onclick="auditRecordsCommonClose(3,this);">付款审核记录</button>
                </th>
            </c:if>--%>
        </tr>
        <tr>
            <th>操作人</th>
            <th>操作时间</th>
            <th>操作事项</th>
            <th>备注</th>
        </tr>
        </thead>
        <tbody id="auditRecordsCommon">
            <c:if test="${not empty historicActivityInstance}">
                <c:set var="auditRecordsValue" value=""></c:set>
                <c:forEach var="hi" items="${historicActivityInstance}" varStatus="status">
                    <c:if test="${not empty  hi.activityName}">
                        <tr>
                            <td>
                                <c:choose>
                                    <c:when test="${hi.activityType == 'startEvent'}">
                                        ${startUser}
                                    </c:when>
                                    <c:when test="${hi.activityType == 'intermediateThrowEvent'}">
                                    </c:when>
                                    <c:otherwise>
                                        <c:if test="${historicActivityInstance.size() == status.count}">
                                            ${verifyUsers}
                                        </c:if>
                                        <c:if test="${historicActivityInstance.size() != status.count}">
                                            <c:forEach items="${assigneeVos}" var="assigneeVo">
                                                <c:if test="${assigneeVo.assignee eq hi.assignee}">
                                                    ${assigneeVo.realName}
                                                    <c:set var="auditRecordsValue" value="${assigneeVo.realName}"></c:set>
                                                </c:if>
                                            </c:forEach>
                                            <%--	${hi.assignee}  --%>
                                            <c:choose>
                                                <c:when test="${empty auditRecordsValue || auditRecordsValue eq ''}">
                                                    ${hi.assignee}
                                                </c:when>
                                                <c:otherwise>
                                                    <c:set var="auditRecordsValue" value=""></c:set>
                                                </c:otherwise>
                                            </c:choose>
                                        </c:if>
                                    </c:otherwise>
                                </c:choose>


                            </td>
                            <td><fmt:formatDate value="${hi.endTime}" pattern="yyyy-MM-dd HH:mm:ss"/></td>
                            <td>
                                <c:choose>
                                    <c:when test="${hi.activityType == 'startEvent'}">
                                        开始
                                    </c:when>
                                    <c:when test="${hi.activityType == 'intermediateThrowEvent'}">
                                        结束
                                    </c:when>
                                    <c:otherwise>
                                        ${hi.activityName}
                                    </c:otherwise>
                                </c:choose>
                            </td>
                            <td class="font-red">${commentMap[hi.taskId]}</td>
                        </tr>
                    </c:if>
                </c:forEach>
            </c:if>
            <c:if test="${empty historicActivityInstance}">
                <!-- 查询无结果弹出 -->
                <tr>
                    <td colspan='4'>暂无记录！</td>
                </tr>
            </c:if>
        </tbody>
        <tbody id="auditRecordsOverCommon" style="display: none;">
            <c:if test="${not empty historicActivityInstanceOver }">
                <c:set var="auditRecordsOverValue" value=""></c:set>
                <c:forEach var="hio" items="${historicActivityInstanceOver}" varStatus="statuso">
                    <c:if test="${not empty  hio.activityName}">
                        <tr>
                            <td>
                                <c:choose>
                                    <c:when test="${hio.activityType == 'startEvent'}">
                                        ${startUserOver}
                                    </c:when>
                                    <c:when test="${hio.activityType == 'intermediateThrowEvent'}">
                                    </c:when>
                                    <c:otherwise>
                                        <c:if test="${historicActivityInstanceOver.size() == statuso.count}">
                                            ${verifyUsersOver}
                                        </c:if>
                                        <c:if test="${historicActivityInstanceOver.size() != statuso.count}">
                                            <c:forEach items="${assigneeVos}" var="assigneeVoo">
                                                <c:if test="${assigneeVoo.assignee eq hio.assignee}">
                                                    ${assigneeVoo.realName}
                                                    <c:set var="auditRecordsOverValue" value="${assigneeVoo.realName}"></c:set>
                                                </c:if>
                                            </c:forEach>
                                            <%--	${hio.assignee}  --%>
                                            <c:choose>
                                                <c:when test="${empty auditRecordsOverValue || auditRecordsOverValue eq ''}">
                                                    ${hio.assignee}
                                                </c:when>
                                                <c:otherwise>
                                                    <c:set var="auditRecordsOverValue" value=""></c:set>
                                                </c:otherwise>
                                            </c:choose>
                                        </c:if>
                                    </c:otherwise>
                                </c:choose>


                            </td>
                            <td><fmt:formatDate value="${hio.endTime}" pattern="yyyy-MM-dd HH:mm:ss"/></td>
                            <td>
                                <c:choose>
                                    <c:when test="${hio.activityType == 'startEvent'}">
                                        开始
                                    </c:when>
                                    <c:when test="${hio.activityType == 'intermediateThrowEvent'}">
                                        结束
                                    </c:when>
                                    <c:otherwise>
                                        ${hio.activityName}
                                    </c:otherwise>
                                </c:choose>
                            </td>
                            <td class="font-red">${commentMapOver[hio.taskId]}</td>
                        </tr>
                    </c:if>
                </c:forEach>
            </c:if>
            <c:if test="${empty historicActivityInstanceOver}">
                <!-- 查询无结果弹出 -->
                <tr>
                    <td colspan='4'>暂无记录！</td>
                </tr>
            </c:if>
        </tbody>
        <%--<c:if test="${afterSalesVo.type eq 542 || afterSalesVo.type eq 539}">
        <tbody id="auditRecordsPayCommon"  style="display: none;">
            <c:if test="${not empty historicActivityInstancePay }">
                <c:set var="auditRecordsPayValue" value=""></c:set>
                <c:forEach var="hip" items="${historicActivityInstancePay}" varStatus="statusp">
                    <c:if test="${not empty  hip.activityName}">
                        <tr>
                            <td>
                                <c:choose>
                                    <c:when test="${hip.activityType == 'startEvent'}">
                                        ${startUserPay}
                                    </c:when>
                                    <c:when test="${hip.activityType == 'intermediateThrowEvent'}">
                                    </c:when>
                                    <c:otherwise>
                                        <c:if test="${historicActivityInstancePay.size() == statusp.count}">
                                            ${verifyUsersPay}
                                        </c:if>
                                        <c:if test="${historicActivityInstancePay.size() != statusp.count}">
                                            <c:forEach items="${assigneeVos}" var="assigneeVop">
                                                <c:if test="${assigneeVop.assignee eq hip.assignee}">
                                                    ${assigneeVop.realName}
                                                    <c:set var="auditRecordsPayValue" value="${assigneeVop.realName}"></c:set>
                                                </c:if>
                                            </c:forEach>
                                            &lt;%&ndash;	${hip.assignee}  &ndash;%&gt;
                                            <c:choose>
                                                <c:when test="${empty auditRecordsPayValue || auditRecordsPayValue eq ''}">
                                                    ${hip.assignee}
                                                </c:when>
                                                <c:otherwise>
                                                    <c:set var="auditRecordsPayValue" value=""></c:set>
                                                </c:otherwise>
                                            </c:choose>
                                        </c:if>
                                    </c:otherwise>
                                </c:choose>


                            </td>
                            <td><fmt:formatDate value="${hip.endTime}" pattern="yyyy-MM-dd HH:mm:ss"/></td>
                            <td>
                                <c:choose>
                                    <c:when test="${hip.activityType == 'startEvent'}">
                                        开始
                                    </c:when>
                                    <c:when test="${hip.activityType == 'intermediateThrowEvent'}">
                                        结束
                                    </c:when>
                                    <c:otherwise>
                                        ${hip.activityName}
                                    </c:otherwise>
                                </c:choose>
                            </td>
                            <td class="font-red">${commentMapPay[hip.taskId]}</td>
                        </tr>
                    </c:if>
                </c:forEach>
            </c:if>
            <c:if test="${empty historicActivityInstancePay}">
                <!-- 查询无结果弹出 -->
                <tr>
                    <td colspan='4'>暂无记录！</td>
                </tr>
            </c:if>
        </tbody>
        </c:if>--%>
    </table>
</div>