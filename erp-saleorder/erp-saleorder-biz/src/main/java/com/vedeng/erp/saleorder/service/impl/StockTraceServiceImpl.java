package com.vedeng.erp.saleorder.service.impl;

import com.vedeng.erp.buyorder.dto.BuyOrderDto;
import com.vedeng.erp.buyorder.service.BuyorderInfoQueryService;
import com.vedeng.erp.common.Constants;
import com.vedeng.erp.saleorder.api.StockTraceService;
import com.vedeng.erp.saleorder.dao.StockTraceMapper;
import com.vedeng.erp.saleorder.dto.StockTraceDto;
import com.vedeng.erp.saleorder.dto.StockTraceResult;
import com.vedeng.erp.saleorder.dto.WarehouseLog;
import com.vedeng.erp.saleorder.enums.TraceTypeEnum;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName StockTraceServiceImpl.java
 * @Description TODO 货物追踪
 * @createTime 2022年09月05日 15:20:00
 */
@Service
public class StockTraceServiceImpl implements StockTraceService {

    @Resource
    private StockTraceMapper stockTraceMapper;

    @Resource
    private BuyorderInfoQueryService buyorderInfoQueryService;

    @Override
    public List<StockTraceResult> trace(StockTraceDto stockTrace) {

        List<StockTraceResult> resultList = new ArrayList<>();

        switch (TraceTypeEnum.valueOfType(stockTrace.getType())){
            case SALE:
                dealWithSale(stockTrace,resultList);
                break;
            case BUY:
                dealWithBuy(stockTrace,resultList);
                break;
        }

        return resultList;
    }

    private void dealWithBuy(StockTraceDto stockTrace, List<StockTraceResult> resultList) {
        BuyOrderDto buyOrderDto = buyorderInfoQueryService.queryInfoByNo(stockTrace.getOrderNo());
        if(buyOrderDto != null && Constants.ONE.equals(buyOrderDto.getDeliveryDirect())){
            List<StockTraceResult> directList = stockTraceMapper.getSaleByBuorderSku(stockTrace);
            resultList.addAll(directList);
        } else {
            List<WarehouseLog> warehouseInlist = stockTraceMapper.getInWarehouseList(stockTrace);
            for (WarehouseLog warehouseLog : warehouseInlist) {
                List<WarehouseLog> outList = stockTraceMapper.getOutWarehouseListByBarcodeId(warehouseLog.getBarcodeId());

            }
        }
    }

    private void dealWithSale(StockTraceDto stockTrace, List<StockTraceResult> resultList) {
        Integer deliveryDirect = stockTraceMapper.getSaleSkuDeliverDirect(stockTrace);
        if (Constants.ONE.equals(deliveryDirect)) {
            List<StockTraceResult> directList = stockTraceMapper.getSaleByBuorderSku(stockTrace);
            resultList.addAll(directList);
        }else {

        }
    }
}
