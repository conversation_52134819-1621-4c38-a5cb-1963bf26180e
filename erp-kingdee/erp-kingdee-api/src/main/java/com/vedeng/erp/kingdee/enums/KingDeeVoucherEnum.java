package com.vedeng.erp.kingdee.enums;

import com.vedeng.common.core.constants.ErpConstant;

import java.util.Arrays;
import java.util.Optional;

public enum KingDeeVoucherEnum {

    AR_RECEIVEBILL(1, "AR_RECEIVEBILL", 1, "收款单"),
    AP_PAYBILL(2, "AP_PAYBILL", 1, "付款单"),
    AP_REFUNDBILL(3, "AP_REFUNDBILL", 1, "付款退款单"),
    AR_REFUNDBILL(4, "AR_REFUNDBILL", 1, "收款退款单"),
    QZOK_BOS_YFTZD(5, "QZOK_BOS_YFTZD", 2, "应付余额调整单"),
    QZOK_BOS_YSTZD(6, "QZOK_BOS_YSTZD", 2, "应收余额调整单"),
    ;


    /**
     * 交易类型(1-收款 2-付款 3-付款退款 4-收款退款 5-应付余额调整 6-应收余额调整)
     */
    private Integer transactionType;

    /**
     * 金蝶表单id
     * 付款单-AP_PAYBILL
     * 收款单-AR_RECEIVEBILL
     * 付款退款单-AP_REFUNDBILL
     * 收款退款单-AR_REFUNDBILL
     * 应付余额调整单-QZOK_BOS_YFTZD
     * 应收余额调整单-QZOK_BOS_YSTZD
     */
    private String formId;

    /**
     * 来源（1-银行 2-余额）
     */
    private Integer source;

    /**
     * 描述
     */
    private String desc;

    KingDeeVoucherEnum(Integer transactionType, String formId, Integer source, String desc) {
        this.transactionType = transactionType;
        this.formId = formId;
        this.source = source;
        this.desc = desc;
    }

    public Integer getTransactionType() {
        return transactionType;
    }

    public String getFormId() {
        return formId;
    }

    public Integer getSource() {
        return source;
    }

    public String getDesc() {
        return desc;
    }

    public static KingDeeVoucherEnum getByTransactionType(Integer transactionType) {
        Optional<KingDeeVoucherEnum> optional = Arrays.stream(KingDeeVoucherEnum.values()).filter(e -> e.getTransactionType().equals(transactionType)).findFirst();
        return optional.orElse(null);
    }
    public static KingDeeVoucherEnum getByFormId(String formId) {
        Optional<KingDeeVoucherEnum> optional = Arrays.stream(KingDeeVoucherEnum.values()).filter(e -> e.getFormId().equals(formId)).findFirst();
        return optional.orElse(null);
    }
    public static boolean isBankByFormId(String formId) {
        KingDeeVoucherEnum kingDeeVoucherEnum = getByFormId(formId);
        return kingDeeVoucherEnum != null && ErpConstant.ONE.equals(kingDeeVoucherEnum.getSource());
    }
    public static boolean isBalanceByFormId(String formId) {
        KingDeeVoucherEnum kingDeeVoucherEnum = getByFormId(formId);
        return kingDeeVoucherEnum != null && ErpConstant.TWO.equals(kingDeeVoucherEnum.getSource());
    }
}
