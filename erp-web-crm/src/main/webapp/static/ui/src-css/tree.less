* {
    box-sizing: border-box;
}

.vd-ui-tree {
    .vd-ui-tree-wrap {
        .vd-ui-tree-select {
            display: flex;
            flex-wrap: wrap;
            margin-top: 10px;

            .vd-ui-select-item {
                height: 33px;
                display: flex;
                align-items: center;
                padding-left: 5px;
                border-radius: 3px;
                background: #F5F7FA;
                margin-right: 5px;
                margin-bottom: 5px;

                .vd-ui-select-avatar {
                    width: 20px;
                    height: 20px;
                    object-fit: cover;
                    flex-shrink: 0;
                    border-radius: 2px;
                    overflow: hidden;
                }

                .vd-ui-select-username {
                    padding: 5px 0;
                    margin-left: 5px;
                }

                .vd-ui-select-del {
                    width: 30px;
                    height: 33px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    cursor: pointer;

                    > i {
                        font-size: 16px;
                        color: #999;
                    }

                    &:hover {
                        i {
                            color: #F60;
                        }
                    }
                }
            }
            
        }

        .vd-ui-add-wrap {
            display: flex;
            align-items: center;
            padding-top: 3px;

            // &.noUser {
            // }

            .vd-ui-add {
                display: inline-flex;
                color: #09F;
                align-items: center;
                cursor: pointer;
    
                .icon-add {
                    font-size: 16px;
                }
                .vd-ui-add-btn {
                }
    
                &:hover {
                    color: #f60;
                }
            }

            .vd-ui-select-num {
                color: #999;
                margin-left: 5px;
            }
        }
    }

    // tree弹窗
    .vd-ui-tree-modal {
        width: 100%;
        height: 100%;
        position: fixed;
        left: 0;
        top: 0;
    }

    &.vd-ui-tree-vertical {
        .vd-ui-tree-wrap .vd-ui-tree-select {
            display: block;
            margin-top: 5px;

            .vd-ui-select-item  {
                background: #fff;
                border-bottom: solid 1px #F5F7FA;
                margin-right: 0;
                margin-bottom: 0;
                display: flex;
                padding-right: 10px;
                padding-left: 10px;

                .vd-ui-select-username {
                    flex: 1;
                    margin-left: 0;
                }

                .vd-ui-select-del {
                    display: none;
                    width: 36px;
                }

                &:hover {
                    background: #F5F7FA;

                    .vd-ui-select-del {
                        display: flex;
                    }
                }
            }
        }
    }
}

.vd-ui-tree-model-content {
    overflow: hidden;

    .vd-ui-modal-wrap {
        height: 606px;
        flex-shrink: 0;
        background: #fff;
        overflow: hidden;
        box-shadow: 0 3px 14px rgba(0, 0, 0, 0.1);

        .vd-ui-modal-title {
            position: relative;
            height: 44px;
            font-size: 16px;
            color: #333333;
            line-height: 44px;
            text-align: left;
            padding: 0 20px;

            background: #F5F7FA;
            border-radius: 5px 5px 0px 0px;

            .vd-ui-modal-close {
                width: 44px;
                height: 44px;
                position: absolute;
                right: 0;
                top: 0;
                display: flex;
                justify-content: center;
                align-items: center;
                color: #ccc;
                cursor: pointer;

                > i {
                    font-size: 24px;
                }

                &:hover {
                    color: #666;
                }
            }
        }

        .vd-ui-tree-nav {
            display: flex;
            border-bottom: solid 1px #E1E5E8;

            .vd-ui-tree-search {
                flex: 1;
                min-width: 0;
                height: 54px;
                padding: 10px 20px;
                border-right: solid 1px #E1E5E8;

                .input-wrap {
                    height: 33px;
                    padding-left: 10px;
                    display: flex;
                    align-items: center;
                    border-radius: 3px;
                    border: solid 1px #BABFC2;

                    &:hover {
                        border-color: #09f;
                    }

                    > input {
                        flex: 1;
                        min-width: 0;
                        height: 31px;
                        border: none;
                        background: none;
                        outline: none;
                    }

                    > i {
                        font-size: 16px;
                        color: #666;
                        width: 30px;
                        height: 100%;
                        display: flex;
                        justify-content: center;
                        align-items: center;

                        &.icon-error2 {
                            width: 36px;
                            cursor: pointer;

                            &:hover {
                                color: #f60;
                            }
                        }
                        &.icon-search {
                            width: 36px;
                        }
                    }
                }
            }

            .vd-ui-tree-select-num {
                flex: 1;
                min-width: 0;
                line-height: 54px;
                padding: 0 20px;
            }
        }

        .vd-ui-tree-panel {
            display: flex;
            height: 551px;

            .vd-ui-tree-panel-left {
                flex: 1;
                min-width: 0;
                border-right: solid 1px #E1E5E8;
                padding-top: 5px;

                .vd-ui-tree-select-list {
                    margin: 0;
                    list-style: none;
                    padding: 0 2px;
                    height: 546px;
                    overflow-y: auto;

                    &::-webkit-scrollbar {
                        width: 6px;
                        height: 6px;
                    }
                
                    &::-webkit-scrollbar-track {
                        background: transparent;
                        width: 6px;
                        height: 6px;
                    }
                
                    &::-webkit-scrollbar-thumb {
                        background: #D7DADE;
                        width: 6px;
                        height: 6px;
                        border-radius: 3px;
                
                        &:hover {
                            background: #BABFC2;
                        }
                
                        &:active {
                            background: #969B9E;
                        }
                    }
                }
            }

            .vd-ui-tree-panel-right {
                flex: 1;
                min-width: 0;
                padding-top: 5px;

                .vd-ui-tree-choose-wrap {
                    padding: 0 10px;
                    height: 490px;
                    overflow-y: auto;

                    &::-webkit-scrollbar {
                        width: 6px;
                        height: 6px;
                    }
                
                    &::-webkit-scrollbar-track {
                        background: transparent;
                        width: 6px;
                        height: 6px;
                    }
                
                    &::-webkit-scrollbar-thumb {
                        background: #D7DADE;
                        width: 6px;
                        height: 6px;
                        border-radius: 3px;
                
                        &:hover {
                            background: #BABFC2;
                        }
                
                        &:active {
                            background: #969B9E;
                        }
                    }

                    .ui-tree-choose-item {
                        height: 33px;
                        padding: 0 10px;
                        display: flex;
                        align-items: center;
                        position: relative;

                        .ui-tree-choose-avatar {
                            width: 20px;
                            height: 20px;
                            object-fit: cover;
                            border-radius: 2px;
                            overflow: hidden;
                            margin-right: 10px;
                        }

                        // .ui-tree-choose-name {
                        //     margin-left: px;
                        // }

                        .icon-delete {
                            width: 36px;
                            height: 33px;
                            display: none;
                            justify-content: center;
                            align-items: center;
                            font-size: 16px;
                            color: #999;
                            cursor: pointer;
                            position: absolute;
                            right: 0;
                            top: 0;

                            &:hover {
                                color: #f60;
                            }
                        }

                        &:hover {
                            border-radius: 3px;
                            background: #efefef;

                            .icon-delete {
                                display: flex;
                            }
                        }
                    }
                }

                .vd-ui-tree-choose-btns {
                    height: 55px;
                    padding: 10px;
                    background: #fff;
                    text-align: right;

                    > button {
                        width: 58px;
                        height: 35px;
                        border-radius: 3px;
                        font-size: 14px;
                        margin-right: 10px;
                        cursor: pointer;

                        &.confirm {
                            color: #FFF;
                            background-color: #09F;
                            border: solid 1px #09F;
                            &:hover {
                                border-color: #0087e0;
                                background-color: #0087e0;
                            }
                        }

                        &.cancel {
                            color: #333;
                            background-color: #F5F7FA;
                            border: solid 1px #BABFC2;
                            &:hover {
                                background: #EBEFF2;
                            }
                        }
                    }
                }
            }
        }
    }
}



// 层级面板
.vd-ui-tree-node {
    position: relative;
    padding-left: 8px;

    .ui-tree-node-department {
        height: 30px;
        padding-left: 10px;
        border-radius: 3px;
        display: flex;
        align-items: center;
        margin-bottom: 3px;
        cursor: pointer;

        &:hover {
            background: #efefef;
        }

        .ui-tree-node-dept-checkbox {
            width: 16px;
            height: 16px;
            flex-shrink: 0;
            border: solid 1px #ddd;
            border-radius: 3px;
            margin-right: 10px;
    
            &.active {
                border: solid 1px #09f;
                background: #09f;
                display: flex;
                justify-content: center;
                align-items: center;
        
                .icon-selected2 {
                    font-size: 12px;
                    color: #fff;
                }
                .icon-deduct {
                    font-size: 12px;
                    color: #fff;
                }
            }
        }

        .icon-arrow {
            font-size: 16px;
            color: #666;
        }

        .icon-file {
            width: 20px;
            height: 20px;
            margin-left: 2px;
        }

        .department-name {
            font-size: 14px;
            margin-left: 5px;
        }
    }

    .ui-tree-node-user {
        padding-left: 8px;

        .ul-tree-node-user-list {
            padding: 0;
            margin: 0;
            list-style: none;

            .ul-tree-node-user-item {
                height: 33px;
                border-radius: 3px;
                display: flex;
                align-items: center;
                padding-left: 10px;
                cursor: pointer;

                &:hover {
                    background: #efefef;
                }

                .ui-tree-node-user-checkbox {
                    width: 16px;
                    height: 16px;
                    flex-shrink: 0;
                    border: solid 1px #ddd;
                    border-radius: 3px;
                    margin-right: 10px;

                    .icon-selected2 {
                        font-size: 12px;
                        color: #fff;
                        display: none;
                    }

                    &.active {
                        border: solid 1px #09f;
                        background: #09f;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                
                        .icon-selected2 {
                            display: block;
                        }
                    }
                }

                .ui-tree-node-user-avatar {
                    width: 20px;
                    height: 20px;
                    border-radius: 2px;
                    overflow: hidden;
                    margin-right: 5px;
                }

                .ui-tree-node-user-name {
                    font-size: 14px;
                    color: #333;
                }
            }
        }
    }

    .ui-tree-node-child {
        padding-left: 18px;
    }
}

// 搜索面板
.vd-ui-filter-wrap {
    height: 100%;
    padding: 0 8px;

    .ui-filter-loading {
        box-sizing: border-box;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        .icon-loading {
            display: inline-block;
            font-size: 32px;
            color: #09f;
            animation: loading 1.8s linear infinite;
        }

        .icon-info1 {
            font-size: 32px;
            color: #09f;
        }

        > span {
            color: #999;
            margin-top: 9px;
        }

        @keyframes loading {
            0% {
                transform : rotate(0deg);
            }

            100% {
                transform : rotate(360deg);
            }
        }
    }

    .ui-filter-list {

        .ui-filter-item {
            height: 33px;
            padding: 0 10px;
            display: flex;
            align-items: center;
            cursor: pointer;

            &:hover {
                border-radius: 3px;
                background: #efefef;
            }

            .ui-filter-item-checkbox {
                width: 16px;
                height: 16px;
                flex-shrink: 0;
                border: solid 1px #ddd;
                border-radius: 3px;
                margin-right: 12px;

                .icon-selected2 {
                    display: none;
                    font-size: 12px;
                    color: #fff;
                }

                &.active {
                    border: solid 1px #09f;
                    background: #09f;
                    display: flex;
                    justify-content: center;
                    align-items: center;

                    .icon-selected2 {
                        display: block;
                    }
                }
            }

            .ui-filter-user {
                display: flex;
                align-items: center;

                .ui-filter-avatar {
                    width: 20px;
                    height: 20px;
                    border-radius: 2px;
                    overflow: hidden;
                    margin-right: 10px;
                }

                .ui-filter-name {
                    font-size: 14px;
                    color: #333;
                }
            }
        }
    }
}