<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchFlowOrderContractDtoMapper">


    <!-- 查询采购流转单合同附件 -->
    <select id="queryFlowOrderBuyContractFile" resultType="com.vedeng.erp.kingdee.batch.dto.BatchFlowOrderContractDto">
        SELECT
            fo.FLOW_ORDER_ID as flowOrderId,
            fo.FLOW_ORDER_NO as flowOrderNo,
            fo.BASE_ORDER_ID as baseOrderId,
            fo.BASE_ORDER_NO as baseOrderNo,
            fo.BASE_BUSINESS_TYPE as baseBusinessType,
            foi.FLOW_ORDER_INFO_ID as flowOrderInfoId,
            foi.FLOW_ORDER_INFO_NO as flowOrderInfoNo,
            foi.CONTRACT_FILE_URL as contractFileUrl,
            foi.CONTRACT_FILE_NAME as contractFileName
        FROM
            T_FLOW_ORDER fo
        JOIN
            T_FLOW_NODE fn ON fo.FLOW_ORDER_ID = fn.FLOW_ORDER_ID
        JOIN
            T_FLOW_ORDER_INFO foi ON fn.FLOW_NODE_ID = foi.FLOW_NODE_ID
        LEFT JOIN
            KING_DEE_FILE_DATA kdfd ON kdfd.ERP_ID = cast(foi.FLOW_ORDER_INFO_ID AS CHAR) AND kdfd.FORM_ID = 'PUR_PurchaseOrder'
        WHERE
            fo.IS_DELETE = 0
            AND fo.AUDIT_STATUS = 1
            AND fo.BASE_BUSINESS_TYPE = 1
            AND fn.IS_DELETE = 0
            AND foi.IS_DELETE = 0
            AND foi.CONTRACT_FILE_URL IS NOT NULL
            AND foi.CONTRACT_FILE_URL != ''
            AND kdfd.ID IS NULL
        <if test="validTimeBegin != null">
            AND foi.ADD_TIME <![CDATA[>=]]> #{validTimeBegin}
        </if>
        <if test="validTimeEnd != null">
            AND foi.ADD_TIME <![CDATA[<=]]> #{validTimeEnd}
        </if>
    </select>

    <!-- 查询销售流转单合同附件 -->
    <select id="queryFlowOrderSaleContractFile" resultType="com.vedeng.erp.kingdee.batch.dto.BatchFlowOrderContractDto">
        SELECT
            fo.FLOW_ORDER_ID as flowOrderId,
            fo.FLOW_ORDER_NO as flowOrderNo,
            fo.BASE_ORDER_ID as baseOrderId,
            fo.BASE_ORDER_NO as baseOrderNo,
            fo.BASE_BUSINESS_TYPE as baseBusinessType,
            foi.FLOW_ORDER_INFO_ID as flowOrderInfoId,
            foi.FLOW_ORDER_INFO_NO as flowOrderInfoNo,
            foi.CONTRACT_FILE_URL as contractFileUrl,
            foi.CONTRACT_FILE_NAME as contractFileName
        FROM
            T_FLOW_ORDER fo
        JOIN
            T_FLOW_NODE fn ON fo.FLOW_ORDER_ID = fn.FLOW_ORDER_ID
        JOIN
            T_FLOW_ORDER_INFO foi ON fn.FLOW_NODE_ID = foi.FLOW_NODE_ID
        LEFT JOIN
            KING_DEE_FILE_DATA kdfd ON kdfd.ERP_ID = cast(foi.FLOW_ORDER_INFO_ID AS CHAR) AND kdfd.FORM_ID = 'SAL_SaleOrder'
        WHERE
            fo.IS_DELETE = 0
            AND fo.AUDIT_STATUS = 1
            AND fo.BASE_BUSINESS_TYPE = 2
            AND fn.IS_DELETE = 0
            AND foi.IS_DELETE = 0
            AND foi.CONTRACT_FILE_URL IS NOT NULL
            AND foi.CONTRACT_FILE_URL != ''
            AND kdfd.ID IS NULL
        <if test="validTimeBegin != null">
            AND foi.ADD_TIME <![CDATA[>=]]>'2025-05-29 14:43:20'
        </if>
        <if test="validTimeEnd != null">
            AND foi.ADD_TIME <![CDATA[<=]]> '2025-05-29 14:43:25'
        </if>
    </select>
</mapper>
