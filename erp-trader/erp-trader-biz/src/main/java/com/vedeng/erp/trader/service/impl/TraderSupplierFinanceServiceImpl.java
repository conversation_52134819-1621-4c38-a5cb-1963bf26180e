package com.vedeng.erp.trader.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.alibaba.fastjson.JSON;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.trader.domain.dto.TraderSupplierDto;
import com.vedeng.erp.trader.domain.dto.TraderSupplierFinanceDetail;
import com.vedeng.erp.trader.domain.dto.TraderSupplierFinanceExcelDto;
import com.vedeng.erp.trader.domain.entity.TraderSupplierFinance;
import com.vedeng.erp.trader.mapper.TraderSupplierFinanceMapper;
import com.vedeng.erp.trader.mapper.TraderSupplierMapper;
import com.vedeng.erp.trader.service.TraderSupplierFinanceService;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.trader.dao.TraderMapper;
import com.vedeng.trader.model.TraderSupplier;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TraderSupplierFinanceServiceImpl implements TraderSupplierFinanceService {

	private static final int CAPACITY = 16;

	private static final int PAGE_SIZE = 500;


    /**
     * excel 最大数据量 10W
     */
    private final Integer MAX_DATA_LENGTH = 100000;

	@Resource
	private BuyorderMapper buyorderMapper;

	@Autowired
	private TraderSupplierFinanceMapper traderSupplierFinanceMapper;

    private static final Integer TWO = 2;
    @Resource
    private TraderMapper traderMapper;

    @Autowired
    @Qualifier("newTraderSupplierMapper")
    private TraderSupplierMapper traderSupplierMapper;

	@Override
	public void syncTrader(Long startDate, Long endDate) {
		//分页查询，每次500条
		Map<String,Object> queryParams = new HashMap<>(CAPACITY);
		Integer page = 0;
		Integer pageSize = PAGE_SIZE;
		queryParams.put("pageSize", pageSize);
		queryParams.put("startDate", startDate);
		queryParams.put("endDate", endDate);
		queryParams.put("traderId", null);
		List<Integer> traderIdList = buyorderMapper.selectByAddTime(queryParams);
        while (!CollectionUtils.isEmpty(traderIdList)) {
        	log.info("获取订单数据第：{}页",page+1);
        	page = page+1;
        	//根据traderList 进行初始化TraderFinal表
        	List<Integer> traderIdExistsList = traderSupplierFinanceMapper.selectByTraderIds(traderIdList);
        	int traderId = traderIdList.stream().max(Comparator.comparing(x -> x)).orElse(null);
        	if(CollectionUtils.isNotEmpty(traderIdExistsList)) {
        		traderIdList = traderIdList.stream().filter(m->!traderIdExistsList.contains(m)).collect(Collectors.toList());
        		if(CollectionUtils.isNotEmpty(traderIdList)) {
        			log.info("插入对应的供应商ID:{}",JSON.toJSONString(traderIdList));
        			traderSupplierFinanceMapper.batchInsertSelect(traderIdList);
        		}
        	}else {
        		log.info("插入对应的供应商ID:{}",JSON.toJSONString(traderIdList));
        		traderSupplierFinanceMapper.batchInsertSelect(traderIdList);
        	}
        	queryParams.put("traderId", traderId);
        	traderIdList = buyorderMapper.selectByAddTime(queryParams);
        }

	}

    @Override
    public TraderSupplierFinanceDetail getTraderSupplierFinancialById(Integer traderCustomerFinanceId) {
        return traderSupplierFinanceMapper.getTraderSupplierFinancialById(traderCustomerFinanceId);
    }

    @Override
    public void updateTraderSupplierFinance(TraderSupplierFinance supplierFinance, User user) {
        supplierFinance.setModTime(new Date());
        supplierFinance.setUpdateRemark("");
        supplierFinance.setUpdater(user.getUserId());
        supplierFinance.setUpdaterName(user.getUsername());
        supplierFinance.setIsPush(ErpConstant.ZERO);
        traderSupplierFinanceMapper.updateByPrimaryKeySelective(supplierFinance);
    }

    @Override
    public void importTraderSupplierFinancial(MultipartFile file, User user) throws Exception {
        List<TraderSupplierFinanceExcelDto> updateList = new ArrayList<>();
        List<TraderSupplierFinanceExcelDto> addList = new ArrayList<>();
        AtomicInteger row = new AtomicInteger(1);
        EasyExcel.read(file.getInputStream(), TraderSupplierFinanceExcelDto.class, new SupplierExcelReadListener<TraderSupplierFinanceExcelDto>(dataList ->{
            dataList.forEach(data -> {
                row.getAndIncrement();
                if(row.get() > MAX_DATA_LENGTH){
                    throw new ServiceException(StrUtil.format("数据量超过10W!"));
                }
                if (Objects.isNull(data.getTraderId())
                        ||Objects.isNull(data.getTraderType())) {
                    throw new ServiceException(StrUtil.format("第{}行数据不可为空",row.get()));
                }

                //文件供应商ID为系统中不存在，或存在但不符合已审核通过状态，不在【供应商列表(财务专用)】列表中的，提示：XXX行供应商ID不存在
                TraderSupplierDto traderSupplier = traderSupplierMapper.getTraderSupplierInfoById(data.getTraderId());
                if(Objects.isNull(traderSupplier)){
                    throw new ServiceException(StrUtil.format("第{}行供应商ID不存在",row.get()));
                }
                //判断此用户是否已导入，已导入更新。未导入不处理
                TraderSupplierFinance traderSupplierFinance = traderSupplierFinanceMapper.getTraderSupplierFinancialByTraderId(data.getTraderId());
                if (traderSupplierFinance != null) {
                    data.setTraderSupplierFinanceId(traderSupplierFinance.getTraderSupplierFinanceId());
                    data.setUpdater(user.getUserId());
                    data.setUpdaterName(user.getUsername());
                    data.setModTime(new Date());
                    data.setIsPush(ErpConstant.ZERO);
                    updateList.add(data);
                }else {
                    addList.add(data);
                }
            });
        })).sheet().doRead();
        if (CollectionUtils.isNotEmpty(updateList)) {
            traderSupplierFinanceMapper.updateBatchSelective(updateList);
        }else if(CollectionUtils.isEmpty(updateList) && CollectionUtils.isEmpty(addList)){
            throw new ServiceException(StrUtil.format("上传的是空模板"));
        }
    }
}
