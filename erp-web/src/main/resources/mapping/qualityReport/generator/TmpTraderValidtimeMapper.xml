<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.qualityReport.dao.generate.TmpTraderValidtimeMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.qualityReport.model.generate.TmpTraderValidtimeDo">
    <id column="TMP_TRADER_VALIDTIME_ID" jdbcType="INTEGER" property="tmpTraderValidtimeId" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="VALID_TIME" jdbcType="TIMESTAMP" property="validTime" />
    <result column="VALID_STATUS" jdbcType="BOOLEAN" property="validStatus" />
  </resultMap>
  <sql id="Base_Column_List">
    TMP_TRADER_VALIDTIME_ID, TRADER_ID, VALID_TIME, VALID_STATUS
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from TMP_TRADER_VALIDTIME
    where TMP_TRADER_SUPPLIER_VALID_ID = #{tmpTraderValidtimeId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from TMP_TRADER_VALIDTIME
    where TMP_TRADER_SUPPLIER_VALID_ID = #{tmpTraderValidtimeId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.qualityReport.model.generate.TmpTraderValidtimeDo">
    insert into TMP_TRADER_VALIDTIME (TMP_TRADER_SUPPLIER_VALID_ID, TRADER_ID, 
      VALID_TIME, VALID_STATUS)
    values (#{tmpTraderValidtimeId,jdbcType=INTEGER}, #{traderId,jdbcType=INTEGER},
      #{validTime,jdbcType=TIMESTAMP}, #{validStatus,jdbcType=BOOLEAN})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.qualityReport.model.generate.TmpTraderValidtimeDo">
    insert into TMP_TRADER_VALIDTIME
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tmpTraderSupplierValidId != null">
        TMP_TRADER_VALIDTIME_ID,
      </if>
      <if test="traderId != null">
        TRADER_ID,
      </if>
      <if test="validTime != null">
        VALID_TIME,
      </if>
      <if test="validStatus != null">
        VALID_STATUS,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tmpTraderSupplierValidId != null">
        #{tmpTraderValidtimeId,jdbcType=INTEGER},
      </if>
      <if test="traderId != null">
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="validTime != null">
        #{validTime,jdbcType=TIMESTAMP},
      </if>
      <if test="validStatus != null">
        #{validStatus,jdbcType=BOOLEAN},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.qualityReport.model.generate.TmpTraderValidtimeDo">
    update TMP_TRADER_VALIDTIME
    <set>
      <if test="traderId != null">
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="validTime != null">
        VALID_TIME = #{validTime,jdbcType=TIMESTAMP},
      </if>
      <if test="validStatus != null">
        VALID_STATUS = #{validStatus,jdbcType=BOOLEAN},
      </if>
    </set>
    where TMP_TRADER_SUPPLIER_VALID_ID = #{tmpTraderValidtimeId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.qualityReport.model.generate.TmpTraderValidtimeDo">
    update TMP_TRADER_VALIDTIME
    set TRADER_ID = #{traderId,jdbcType=INTEGER},
      VALID_TIME = #{validTime,jdbcType=TIMESTAMP},
      VALID_STATUS = #{validStatus,jdbcType=BOOLEAN}
    where TMP_TRADER_SUPPLIER_VALID_ID = #{tmpTraderValidtimeId,jdbcType=INTEGER}
  </update>
</mapper>