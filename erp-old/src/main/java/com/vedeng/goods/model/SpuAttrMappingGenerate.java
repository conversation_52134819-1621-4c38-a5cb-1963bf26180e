package com.vedeng.goods.model;

import java.util.Date;

public class SpuAttrMappingGenerate {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_SPU_ATTR_MAPPING.SPU_ATTR_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer spuAttrId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_SPU_ATTR_MAPPING.SPU_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer spuId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_SPU_ATTR_MAPPING.BASE_ATTRIBUTE_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer baseAttributeId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_SPU_ATTR_MAPPING.STATUS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer status;

    private Integer isPrimary;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_SPU_ATTR_MAPPING.UPDATER
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer updater;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_SPU_ATTR_MAPPING.CREATOR
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer creator;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_SPU_ATTR_MAPPING.ADD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Date addTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_SPU_ATTR_MAPPING.MOD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Date modTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_SPU_ATTR_MAPPING.BASE_ATTRIBUTE_VALUE_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer baseAttributeValueId;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_SPU_ATTR_MAPPING.SPU_ATTR_ID
     *
     * @return the value of V_SPU_ATTR_MAPPING.SPU_ATTR_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getSpuAttrId() {
        return spuAttrId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_SPU_ATTR_MAPPING.SPU_ATTR_ID
     *
     * @param spuAttrId the value for V_SPU_ATTR_MAPPING.SPU_ATTR_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setSpuAttrId(Integer spuAttrId) {
        this.spuAttrId = spuAttrId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_SPU_ATTR_MAPPING.SPU_ID
     *
     * @return the value of V_SPU_ATTR_MAPPING.SPU_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getSpuId() {
        return spuId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_SPU_ATTR_MAPPING.SPU_ID
     *
     * @param spuId the value for V_SPU_ATTR_MAPPING.SPU_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setSpuId(Integer spuId) {
        this.spuId = spuId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_SPU_ATTR_MAPPING.BASE_ATTRIBUTE_ID
     *
     * @return the value of V_SPU_ATTR_MAPPING.BASE_ATTRIBUTE_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getBaseAttributeId() {
        return baseAttributeId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_SPU_ATTR_MAPPING.BASE_ATTRIBUTE_ID
     *
     * @param baseAttributeId the value for V_SPU_ATTR_MAPPING.BASE_ATTRIBUTE_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setBaseAttributeId(Integer baseAttributeId) {
        this.baseAttributeId = baseAttributeId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_SPU_ATTR_MAPPING.STATUS
     *
     * @return the value of V_SPU_ATTR_MAPPING.STATUS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_SPU_ATTR_MAPPING.STATUS
     *
     * @param status the value for V_SPU_ATTR_MAPPING.STATUS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setStatus(Integer status) {
        this.status = status;
    }


    public Integer getIsPrimary() {
        return isPrimary;
    }

    public void setIsPrimary(Integer isPrimary) {
        this.isPrimary = isPrimary;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_SPU_ATTR_MAPPING.UPDATER
     *
     * @return the value of V_SPU_ATTR_MAPPING.UPDATER
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getUpdater() {
        return updater;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_SPU_ATTR_MAPPING.UPDATER
     *
     * @param updater the value for V_SPU_ATTR_MAPPING.UPDATER
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_SPU_ATTR_MAPPING.CREATOR
     *
     * @return the value of V_SPU_ATTR_MAPPING.CREATOR
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_SPU_ATTR_MAPPING.CREATOR
     *
     * @param creator the value for V_SPU_ATTR_MAPPING.CREATOR
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_SPU_ATTR_MAPPING.ADD_TIME
     *
     * @return the value of V_SPU_ATTR_MAPPING.ADD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Date getAddTime() {
        return addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_SPU_ATTR_MAPPING.ADD_TIME
     *
     * @param addTime the value for V_SPU_ATTR_MAPPING.ADD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_SPU_ATTR_MAPPING.MOD_TIME
     *
     * @return the value of V_SPU_ATTR_MAPPING.MOD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Date getModTime() {
        return modTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_SPU_ATTR_MAPPING.MOD_TIME
     *
     * @param modTime the value for V_SPU_ATTR_MAPPING.MOD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setModTime(Date modTime) {
        this.modTime = modTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_SPU_ATTR_MAPPING.BASE_ATTRIBUTE_VALUE_ID
     *
     * @return the value of V_SPU_ATTR_MAPPING.BASE_ATTRIBUTE_VALUE_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getBaseAttributeValueId() {
        return baseAttributeValueId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_SPU_ATTR_MAPPING.BASE_ATTRIBUTE_VALUE_ID
     *
     * @param baseAttributeValueId the value for V_SPU_ATTR_MAPPING.BASE_ATTRIBUTE_VALUE_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setBaseAttributeValueId(Integer baseAttributeValueId) {
        this.baseAttributeValueId = baseAttributeValueId;
    }
}