package com.vedeng.order.model.adk;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TAdkGoodsExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_ADK_GOODS
     *
     * @mbg.generated Tue Apr 09 20:42:17 CST 2019
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_ADK_GOODS
     *
     * @mbg.generated Tue Apr 09 20:42:17 CST 2019
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_ADK_GOODS
     *
     * @mbg.generated Tue Apr 09 20:42:17 CST 2019
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_ADK_GOODS
     *
     * @mbg.generated Tue Apr 09 20:42:17 CST 2019
     */
    public TAdkGoodsExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_ADK_GOODS
     *
     * @mbg.generated Tue Apr 09 20:42:17 CST 2019
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_ADK_GOODS
     *
     * @mbg.generated Tue Apr 09 20:42:17 CST 2019
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_ADK_GOODS
     *
     * @mbg.generated Tue Apr 09 20:42:17 CST 2019
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_ADK_GOODS
     *
     * @mbg.generated Tue Apr 09 20:42:17 CST 2019
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_ADK_GOODS
     *
     * @mbg.generated Tue Apr 09 20:42:17 CST 2019
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_ADK_GOODS
     *
     * @mbg.generated Tue Apr 09 20:42:17 CST 2019
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_ADK_GOODS
     *
     * @mbg.generated Tue Apr 09 20:42:17 CST 2019
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_ADK_GOODS
     *
     * @mbg.generated Tue Apr 09 20:42:17 CST 2019
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_ADK_GOODS
     *
     * @mbg.generated Tue Apr 09 20:42:17 CST 2019
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_ADK_GOODS
     *
     * @mbg.generated Tue Apr 09 20:42:17 CST 2019
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_ADK_GOODS
     *
     * @mbg.generated Tue Apr 09 20:42:17 CST 2019
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andAdkGoodsIdIsNull() {
            addCriterion("adk_goods_id is null");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsIdIsNotNull() {
            addCriterion("adk_goods_id is not null");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsIdEqualTo(Integer value) {
            addCriterion("adk_goods_id =", value, "adkGoodsId");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsIdNotEqualTo(Integer value) {
            addCriterion("adk_goods_id <>", value, "adkGoodsId");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsIdGreaterThan(Integer value) {
            addCriterion("adk_goods_id >", value, "adkGoodsId");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("adk_goods_id >=", value, "adkGoodsId");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsIdLessThan(Integer value) {
            addCriterion("adk_goods_id <", value, "adkGoodsId");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsIdLessThanOrEqualTo(Integer value) {
            addCriterion("adk_goods_id <=", value, "adkGoodsId");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsIdIn(List<Integer> values) {
            addCriterion("adk_goods_id in", values, "adkGoodsId");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsIdNotIn(List<Integer> values) {
            addCriterion("adk_goods_id not in", values, "adkGoodsId");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsIdBetween(Integer value1, Integer value2) {
            addCriterion("adk_goods_id between", value1, value2, "adkGoodsId");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsIdNotBetween(Integer value1, Integer value2) {
            addCriterion("adk_goods_id not between", value1, value2, "adkGoodsId");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsCodeIsNull() {
            addCriterion("adk_goods_code is null");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsCodeIsNotNull() {
            addCriterion("adk_goods_code is not null");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsCodeEqualTo(String value) {
            addCriterion("adk_goods_code =", value, "adkGoodsCode");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsCodeNotEqualTo(String value) {
            addCriterion("adk_goods_code <>", value, "adkGoodsCode");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsCodeGreaterThan(String value) {
            addCriterion("adk_goods_code >", value, "adkGoodsCode");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsCodeGreaterThanOrEqualTo(String value) {
            addCriterion("adk_goods_code >=", value, "adkGoodsCode");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsCodeLessThan(String value) {
            addCriterion("adk_goods_code <", value, "adkGoodsCode");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsCodeLessThanOrEqualTo(String value) {
            addCriterion("adk_goods_code <=", value, "adkGoodsCode");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsCodeLike(String value) {
            addCriterion("adk_goods_code like", value, "adkGoodsCode");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsCodeNotLike(String value) {
            addCriterion("adk_goods_code not like", value, "adkGoodsCode");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsCodeIn(List<String> values) {
            addCriterion("adk_goods_code in", values, "adkGoodsCode");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsCodeNotIn(List<String> values) {
            addCriterion("adk_goods_code not in", values, "adkGoodsCode");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsCodeBetween(String value1, String value2) {
            addCriterion("adk_goods_code between", value1, value2, "adkGoodsCode");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsCodeNotBetween(String value1, String value2) {
            addCriterion("adk_goods_code not between", value1, value2, "adkGoodsCode");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsNameIsNull() {
            addCriterion("adk_goods_name is null");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsNameIsNotNull() {
            addCriterion("adk_goods_name is not null");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsNameEqualTo(String value) {
            addCriterion("adk_goods_name =", value, "adkGoodsName");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsNameNotEqualTo(String value) {
            addCriterion("adk_goods_name <>", value, "adkGoodsName");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsNameGreaterThan(String value) {
            addCriterion("adk_goods_name >", value, "adkGoodsName");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsNameGreaterThanOrEqualTo(String value) {
            addCriterion("adk_goods_name >=", value, "adkGoodsName");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsNameLessThan(String value) {
            addCriterion("adk_goods_name <", value, "adkGoodsName");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsNameLessThanOrEqualTo(String value) {
            addCriterion("adk_goods_name <=", value, "adkGoodsName");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsNameLike(String value) {
            addCriterion("adk_goods_name like", value, "adkGoodsName");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsNameNotLike(String value) {
            addCriterion("adk_goods_name not like", value, "adkGoodsName");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsNameIn(List<String> values) {
            addCriterion("adk_goods_name in", values, "adkGoodsName");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsNameNotIn(List<String> values) {
            addCriterion("adk_goods_name not in", values, "adkGoodsName");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsNameBetween(String value1, String value2) {
            addCriterion("adk_goods_name between", value1, value2, "adkGoodsName");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsNameNotBetween(String value1, String value2) {
            addCriterion("adk_goods_name not between", value1, value2, "adkGoodsName");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsModelIsNull() {
            addCriterion("adk_goods_model is null");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsModelIsNotNull() {
            addCriterion("adk_goods_model is not null");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsModelEqualTo(String value) {
            addCriterion("adk_goods_model =", value, "adkGoodsModel");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsModelNotEqualTo(String value) {
            addCriterion("adk_goods_model <>", value, "adkGoodsModel");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsModelGreaterThan(String value) {
            addCriterion("adk_goods_model >", value, "adkGoodsModel");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsModelGreaterThanOrEqualTo(String value) {
            addCriterion("adk_goods_model >=", value, "adkGoodsModel");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsModelLessThan(String value) {
            addCriterion("adk_goods_model <", value, "adkGoodsModel");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsModelLessThanOrEqualTo(String value) {
            addCriterion("adk_goods_model <=", value, "adkGoodsModel");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsModelLike(String value) {
            addCriterion("adk_goods_model like", value, "adkGoodsModel");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsModelNotLike(String value) {
            addCriterion("adk_goods_model not like", value, "adkGoodsModel");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsModelIn(List<String> values) {
            addCriterion("adk_goods_model in", values, "adkGoodsModel");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsModelNotIn(List<String> values) {
            addCriterion("adk_goods_model not in", values, "adkGoodsModel");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsModelBetween(String value1, String value2) {
            addCriterion("adk_goods_model between", value1, value2, "adkGoodsModel");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsModelNotBetween(String value1, String value2) {
            addCriterion("adk_goods_model not between", value1, value2, "adkGoodsModel");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsUnitIsNull() {
            addCriterion("adk_goods_unit is null");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsUnitIsNotNull() {
            addCriterion("adk_goods_unit is not null");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsUnitEqualTo(String value) {
            addCriterion("adk_goods_unit =", value, "adkGoodsUnit");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsUnitNotEqualTo(String value) {
            addCriterion("adk_goods_unit <>", value, "adkGoodsUnit");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsUnitGreaterThan(String value) {
            addCriterion("adk_goods_unit >", value, "adkGoodsUnit");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsUnitGreaterThanOrEqualTo(String value) {
            addCriterion("adk_goods_unit >=", value, "adkGoodsUnit");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsUnitLessThan(String value) {
            addCriterion("adk_goods_unit <", value, "adkGoodsUnit");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsUnitLessThanOrEqualTo(String value) {
            addCriterion("adk_goods_unit <=", value, "adkGoodsUnit");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsUnitLike(String value) {
            addCriterion("adk_goods_unit like", value, "adkGoodsUnit");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsUnitNotLike(String value) {
            addCriterion("adk_goods_unit not like", value, "adkGoodsUnit");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsUnitIn(List<String> values) {
            addCriterion("adk_goods_unit in", values, "adkGoodsUnit");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsUnitNotIn(List<String> values) {
            addCriterion("adk_goods_unit not in", values, "adkGoodsUnit");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsUnitBetween(String value1, String value2) {
            addCriterion("adk_goods_unit between", value1, value2, "adkGoodsUnit");
            return (Criteria) this;
        }

        public Criteria andAdkGoodsUnitNotBetween(String value1, String value2) {
            addCriterion("adk_goods_unit not between", value1, value2, "adkGoodsUnit");
            return (Criteria) this;
        }

        public Criteria andErpGoodsSkuIsNull() {
            addCriterion("erp_goods_sku is null");
            return (Criteria) this;
        }

        public Criteria andErpGoodsSkuIsNotNull() {
            addCriterion("erp_goods_sku is not null");
            return (Criteria) this;
        }

        public Criteria andErpGoodsSkuEqualTo(String value) {
            addCriterion("erp_goods_sku =", value, "erpGoodsSku");
            return (Criteria) this;
        }

        public Criteria andErpGoodsSkuNotEqualTo(String value) {
            addCriterion("erp_goods_sku <>", value, "erpGoodsSku");
            return (Criteria) this;
        }

        public Criteria andErpGoodsSkuGreaterThan(String value) {
            addCriterion("erp_goods_sku >", value, "erpGoodsSku");
            return (Criteria) this;
        }

        public Criteria andErpGoodsSkuGreaterThanOrEqualTo(String value) {
            addCriterion("erp_goods_sku >=", value, "erpGoodsSku");
            return (Criteria) this;
        }

        public Criteria andErpGoodsSkuLessThan(String value) {
            addCriterion("erp_goods_sku <", value, "erpGoodsSku");
            return (Criteria) this;
        }

        public Criteria andErpGoodsSkuLessThanOrEqualTo(String value) {
            addCriterion("erp_goods_sku <=", value, "erpGoodsSku");
            return (Criteria) this;
        }

        public Criteria andErpGoodsSkuLike(String value) {
            addCriterion("erp_goods_sku like", value, "erpGoodsSku");
            return (Criteria) this;
        }

        public Criteria andErpGoodsSkuNotLike(String value) {
            addCriterion("erp_goods_sku not like", value, "erpGoodsSku");
            return (Criteria) this;
        }

        public Criteria andErpGoodsSkuIn(List<String> values) {
            addCriterion("erp_goods_sku in", values, "erpGoodsSku");
            return (Criteria) this;
        }

        public Criteria andErpGoodsSkuNotIn(List<String> values) {
            addCriterion("erp_goods_sku not in", values, "erpGoodsSku");
            return (Criteria) this;
        }

        public Criteria andErpGoodsSkuBetween(String value1, String value2) {
            addCriterion("erp_goods_sku between", value1, value2, "erpGoodsSku");
            return (Criteria) this;
        }

        public Criteria andErpGoodsSkuNotBetween(String value1, String value2) {
            addCriterion("erp_goods_sku not between", value1, value2, "erpGoodsSku");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("status like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("status not like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andErpGoodsIdIsNull() {
            addCriterion("erp_goods_id is null");
            return (Criteria) this;
        }

        public Criteria andErpGoodsIdIsNotNull() {
            addCriterion("erp_goods_id is not null");
            return (Criteria) this;
        }

        public Criteria andErpGoodsIdEqualTo(Integer value) {
            addCriterion("erp_goods_id =", value, "erpGoodsId");
            return (Criteria) this;
        }

        public Criteria andErpGoodsIdNotEqualTo(Integer value) {
            addCriterion("erp_goods_id <>", value, "erpGoodsId");
            return (Criteria) this;
        }

        public Criteria andErpGoodsIdGreaterThan(Integer value) {
            addCriterion("erp_goods_id >", value, "erpGoodsId");
            return (Criteria) this;
        }

        public Criteria andErpGoodsIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("erp_goods_id >=", value, "erpGoodsId");
            return (Criteria) this;
        }

        public Criteria andErpGoodsIdLessThan(Integer value) {
            addCriterion("erp_goods_id <", value, "erpGoodsId");
            return (Criteria) this;
        }

        public Criteria andErpGoodsIdLessThanOrEqualTo(Integer value) {
            addCriterion("erp_goods_id <=", value, "erpGoodsId");
            return (Criteria) this;
        }

        public Criteria andErpGoodsIdIn(List<Integer> values) {
            addCriterion("erp_goods_id in", values, "erpGoodsId");
            return (Criteria) this;
        }

        public Criteria andErpGoodsIdNotIn(List<Integer> values) {
            addCriterion("erp_goods_id not in", values, "erpGoodsId");
            return (Criteria) this;
        }

        public Criteria andErpGoodsIdBetween(Integer value1, Integer value2) {
            addCriterion("erp_goods_id between", value1, value2, "erpGoodsId");
            return (Criteria) this;
        }

        public Criteria andErpGoodsIdNotBetween(Integer value1, Integer value2) {
            addCriterion("erp_goods_id not between", value1, value2, "erpGoodsId");
            return (Criteria) this;
        }

        public Criteria andAddNoIsNull() {
            addCriterion("add_no is null");
            return (Criteria) this;
        }

        public Criteria andAddNoIsNotNull() {
            addCriterion("add_no is not null");
            return (Criteria) this;
        }

        public Criteria andAddNoEqualTo(String value) {
            addCriterion("add_no =", value, "addNo");
            return (Criteria) this;
        }

        public Criteria andAddNoNotEqualTo(String value) {
            addCriterion("add_no <>", value, "addNo");
            return (Criteria) this;
        }

        public Criteria andAddNoGreaterThan(String value) {
            addCriterion("add_no >", value, "addNo");
            return (Criteria) this;
        }

        public Criteria andAddNoGreaterThanOrEqualTo(String value) {
            addCriterion("add_no >=", value, "addNo");
            return (Criteria) this;
        }

        public Criteria andAddNoLessThan(String value) {
            addCriterion("add_no <", value, "addNo");
            return (Criteria) this;
        }

        public Criteria andAddNoLessThanOrEqualTo(String value) {
            addCriterion("add_no <=", value, "addNo");
            return (Criteria) this;
        }

        public Criteria andAddNoLike(String value) {
            addCriterion("add_no like", value, "addNo");
            return (Criteria) this;
        }

        public Criteria andAddNoNotLike(String value) {
            addCriterion("add_no not like", value, "addNo");
            return (Criteria) this;
        }

        public Criteria andAddNoIn(List<String> values) {
            addCriterion("add_no in", values, "addNo");
            return (Criteria) this;
        }

        public Criteria andAddNoNotIn(List<String> values) {
            addCriterion("add_no not in", values, "addNo");
            return (Criteria) this;
        }

        public Criteria andAddNoBetween(String value1, String value2) {
            addCriterion("add_no between", value1, value2, "addNo");
            return (Criteria) this;
        }

        public Criteria andAddNoNotBetween(String value1, String value2) {
            addCriterion("add_no not between", value1, value2, "addNo");
            return (Criteria) this;
        }

        public Criteria andAddNameIsNull() {
            addCriterion("add_name is null");
            return (Criteria) this;
        }

        public Criteria andAddNameIsNotNull() {
            addCriterion("add_name is not null");
            return (Criteria) this;
        }

        public Criteria andAddNameEqualTo(String value) {
            addCriterion("add_name =", value, "addName");
            return (Criteria) this;
        }

        public Criteria andAddNameNotEqualTo(String value) {
            addCriterion("add_name <>", value, "addName");
            return (Criteria) this;
        }

        public Criteria andAddNameGreaterThan(String value) {
            addCriterion("add_name >", value, "addName");
            return (Criteria) this;
        }

        public Criteria andAddNameGreaterThanOrEqualTo(String value) {
            addCriterion("add_name >=", value, "addName");
            return (Criteria) this;
        }

        public Criteria andAddNameLessThan(String value) {
            addCriterion("add_name <", value, "addName");
            return (Criteria) this;
        }

        public Criteria andAddNameLessThanOrEqualTo(String value) {
            addCriterion("add_name <=", value, "addName");
            return (Criteria) this;
        }

        public Criteria andAddNameLike(String value) {
            addCriterion("add_name like", value, "addName");
            return (Criteria) this;
        }

        public Criteria andAddNameNotLike(String value) {
            addCriterion("add_name not like", value, "addName");
            return (Criteria) this;
        }

        public Criteria andAddNameIn(List<String> values) {
            addCriterion("add_name in", values, "addName");
            return (Criteria) this;
        }

        public Criteria andAddNameNotIn(List<String> values) {
            addCriterion("add_name not in", values, "addName");
            return (Criteria) this;
        }

        public Criteria andAddNameBetween(String value1, String value2) {
            addCriterion("add_name between", value1, value2, "addName");
            return (Criteria) this;
        }

        public Criteria andAddNameNotBetween(String value1, String value2) {
            addCriterion("add_name not between", value1, value2, "addName");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("add_time is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("add_time is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Date value) {
            addCriterion("add_time =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Date value) {
            addCriterion("add_time <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Date value) {
            addCriterion("add_time >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("add_time >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Date value) {
            addCriterion("add_time <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Date value) {
            addCriterion("add_time <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Date> values) {
            addCriterion("add_time in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Date> values) {
            addCriterion("add_time not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Date value1, Date value2) {
            addCriterion("add_time between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Date value1, Date value2) {
            addCriterion("add_time not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andUpdateNoIsNull() {
            addCriterion("update_no is null");
            return (Criteria) this;
        }

        public Criteria andUpdateNoIsNotNull() {
            addCriterion("update_no is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateNoEqualTo(String value) {
            addCriterion("update_no =", value, "updateNo");
            return (Criteria) this;
        }

        public Criteria andUpdateNoNotEqualTo(String value) {
            addCriterion("update_no <>", value, "updateNo");
            return (Criteria) this;
        }

        public Criteria andUpdateNoGreaterThan(String value) {
            addCriterion("update_no >", value, "updateNo");
            return (Criteria) this;
        }

        public Criteria andUpdateNoGreaterThanOrEqualTo(String value) {
            addCriterion("update_no >=", value, "updateNo");
            return (Criteria) this;
        }

        public Criteria andUpdateNoLessThan(String value) {
            addCriterion("update_no <", value, "updateNo");
            return (Criteria) this;
        }

        public Criteria andUpdateNoLessThanOrEqualTo(String value) {
            addCriterion("update_no <=", value, "updateNo");
            return (Criteria) this;
        }

        public Criteria andUpdateNoLike(String value) {
            addCriterion("update_no like", value, "updateNo");
            return (Criteria) this;
        }

        public Criteria andUpdateNoNotLike(String value) {
            addCriterion("update_no not like", value, "updateNo");
            return (Criteria) this;
        }

        public Criteria andUpdateNoIn(List<String> values) {
            addCriterion("update_no in", values, "updateNo");
            return (Criteria) this;
        }

        public Criteria andUpdateNoNotIn(List<String> values) {
            addCriterion("update_no not in", values, "updateNo");
            return (Criteria) this;
        }

        public Criteria andUpdateNoBetween(String value1, String value2) {
            addCriterion("update_no between", value1, value2, "updateNo");
            return (Criteria) this;
        }

        public Criteria andUpdateNoNotBetween(String value1, String value2) {
            addCriterion("update_no not between", value1, value2, "updateNo");
            return (Criteria) this;
        }

        public Criteria andUpdateNameIsNull() {
            addCriterion("update_name is null");
            return (Criteria) this;
        }

        public Criteria andUpdateNameIsNotNull() {
            addCriterion("update_name is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateNameEqualTo(String value) {
            addCriterion("update_name =", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameNotEqualTo(String value) {
            addCriterion("update_name <>", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameGreaterThan(String value) {
            addCriterion("update_name >", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameGreaterThanOrEqualTo(String value) {
            addCriterion("update_name >=", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameLessThan(String value) {
            addCriterion("update_name <", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameLessThanOrEqualTo(String value) {
            addCriterion("update_name <=", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameLike(String value) {
            addCriterion("update_name like", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameNotLike(String value) {
            addCriterion("update_name not like", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameIn(List<String> values) {
            addCriterion("update_name in", values, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameNotIn(List<String> values) {
            addCriterion("update_name not in", values, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameBetween(String value1, String value2) {
            addCriterion("update_name between", value1, value2, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameNotBetween(String value1, String value2) {
            addCriterion("update_name not between", value1, value2, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_ADK_GOODS
     *
     * @mbg.generated do_not_delete_during_merge Tue Apr 09 20:42:17 CST 2019
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_ADK_GOODS
     *
     * @mbg.generated Tue Apr 09 20:42:17 CST 2019
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}