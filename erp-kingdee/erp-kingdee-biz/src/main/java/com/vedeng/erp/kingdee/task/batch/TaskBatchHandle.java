package com.vedeng.erp.kingdee.task.batch;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.JobParametersBuilder;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 金蝶批处理任务基础方法
 * @date 2022/12/6 16:57
 */
public class TaskBatchHandle {

    /**
     * param 详细
     * {"beginTime":"2022-11-01 00:00:00",
     * "endTime":"2022-12-01 00:00:00",
     * "timestamp":"1666687179395"}
     */
    public JobParameters buildJobParameters(String param) {
        String beginTime = null;
        String endTime = null;
        long timestamp = System.currentTimeMillis();
        if (StrUtil.isNotBlank(param)) {
            JSONObject jsonObject = JSON.parseObject(param);
            beginTime = jsonObject.getString("beginTime");
            endTime = jsonObject.getString("endTime");
            timestamp = jsonObject.getLong("timestamp") != null ? jsonObject.getLong("timestamp") : System.currentTimeMillis();
        }
        return new JobParametersBuilder()
                .addLong("timestamp", timestamp)
                .addString("beginTime", beginTime)
                .addString("endTime", endTime)
                .toJobParameters();
    }
}
