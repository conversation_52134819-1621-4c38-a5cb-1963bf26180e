package com.vedeng.oa.service.filedeliveryaddress.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import com.vedeng.authorization.dao.RegionMapper;
import com.vedeng.erp.trader.domain.entity.TraderAddressEntity;
import com.vedeng.erp.trader.domain.entity.TraderContactEntity;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.vedeng.oa.dao.FileDeliveryAddressExtMapper;
import com.vedeng.oa.model.FileDeliveryAddress;
import com.vedeng.oa.model.FileDeliveryAddressExample;
import com.vedeng.oa.service.filedelivery.api.FileDeliveryAddressApiService;
import com.vedeng.oa.service.filedelivery.vo.FileDeliveryAddressVO;

import com.vedeng.erp.trader.domain.entity.TraderEntity;
import com.vedeng.erp.trader.mapper.TraderAddressMapper;
import com.vedeng.erp.trader.mapper.TraderContactMapper;
import com.vedeng.erp.trader.mapper.TraderMapper;

import javax.annotation.Resource;

/**
 * 文件寄送地址服务
 * <AUTHOR>
 *
 */
@Service
public class FileDeliveryAddressApiServiceImpl implements FileDeliveryAddressApiService {

	@Autowired
	private FileDeliveryAddressExtMapper fileDeliveryAddressExtMapper;
	
	@Autowired
	private TraderAddressMapper traderAddressMapper;
	
	@Autowired
	private TraderContactMapper  traderContactMapper;
	
	@Autowired
	private TraderMapper  traderMapper;

	@Resource
	private RegionMapper regionMapper;
	
	
	
	@Override
	@Transactional(rollbackFor = Exception.class)
	public  FileDeliveryAddressVO  saveFileDeliveryAddress(FileDeliveryAddressVO fileDeliveryAddressVO,Integer userId) {
		List<FileDeliveryAddress> fileDeliveryAddressList = new ArrayList<>();
			FileDeliveryAddress fileDeliveryAddress = new FileDeliveryAddress();
			fileDeliveryAddress.setAddTime(new Date());
			fileDeliveryAddress.setModTime(new Date());
			fileDeliveryAddress.setApplyId(userId);
			//联系人填写方式
			fileDeliveryAddress.setContactType(fileDeliveryAddressVO.getContactType());
			fileDeliveryAddress.setCreator(userId);
			//TODO Neil 寄送状态设置默认未寄送（寄送状态0未寄送1已寄送2面单生成），审核通过、打印时更新
			fileDeliveryAddress.setDeliveryStatus(0);
			fileDeliveryAddress.setFileDeliveryId(fileDeliveryAddressVO.getFileDeliveryId());
			fileDeliveryAddress.setIsDelete(0);
			//TODO Neil 快递单号，审核通过时更新
			fileDeliveryAddress.setLogisticsNo("");


			//填写方式是手动填写
			if(fileDeliveryAddressVO.getContactType()==2){
				fileDeliveryAddressVO.setTraderContactAddressId(null);
				fileDeliveryAddressVO.setTraderContactId(null);
				fillAddressInfo(fileDeliveryAddress,fileDeliveryAddressVO);
				fileDeliveryAddressList.add(fileDeliveryAddress);

			}else{
				//判断traderId是否为空 设置客户信息 start
				Integer traderId = fileDeliveryAddressVO.getTraderId();
				String traderName = fileDeliveryAddressVO.getTraderName();
				if(Objects.nonNull(traderId)) {
					TraderEntity traderEntity = traderMapper.selectByPrimaryKey(traderId);
					traderName = traderEntity.getTraderName();
				}

				fileDeliveryAddress.setTraderId(traderId);
				fileDeliveryAddress.setTraderName(traderName);
				fileDeliveryAddress.setTraderType(fileDeliveryAddressVO.getTraderType());
				//设置客户信息 end

				//设置联系人
				TraderContactEntity traderContactEntity=traderContactMapper.selectByTraderContactId(fileDeliveryAddressVO.getTraderContactId());
				fileDeliveryAddress.setTraderContactName(traderContactEntity.getName());
				fileDeliveryAddress.setTraderContactMobile(traderContactEntity.getMobile());
				fileDeliveryAddress.setTraderContactId(fileDeliveryAddressVO.getTraderContactId());

				//设置地址
				fileDeliveryAddress.setTraderContactAddressId(fileDeliveryAddressVO.getTraderContactAddressId());
				TraderAddressEntity addressDb=	traderAddressMapper.selectByPrimaryKey(fileDeliveryAddressVO.getTraderContactAddressId());
				//地址信息，包含省市区
				fileDeliveryAddress.setTraderContactAddress(getRegionNames(fileDeliveryAddressVO.getTraderContactAddressId(),fileDeliveryAddressVO.getAreaId())+""+addressDb.getAddress());
				//地址信息，不包含省市区
				fileDeliveryAddress.setTraderContactAddressInfo(addressDb.getAddress());
			}
			if(fileDeliveryAddressVO.getId()!=null){
				fileDeliveryAddress.setId(fileDeliveryAddressVO.getId());
				fileDeliveryAddressExtMapper.updateByPrimaryKeySelective(fileDeliveryAddress);
			}else{
				fileDeliveryAddressExtMapper.insert(fileDeliveryAddress);
			}
		BeanUtils.copyProperties(fileDeliveryAddress,fileDeliveryAddressVO);
		return fileDeliveryAddressVO;
	}

	private void fillAddressInfo(FileDeliveryAddress address,FileDeliveryAddressVO fileDeliveryAddressVO){
		if(fileDeliveryAddressVO.getContactType()==2){
			address.setFileDeliveryId(fileDeliveryAddressVO.getFileDeliveryId());
			address.setTraderType(fileDeliveryAddressVO.getTraderType());
			address.setTraderName(fileDeliveryAddressVO.getTraderName());
			address.setContactType(fileDeliveryAddressVO.getContactType());
			address.setTraderContactName(fileDeliveryAddressVO.getTraderContactName());
			address.setTraderContactMobile(fileDeliveryAddressVO.getTraderContactMobile());
			address.setAreaId(fileDeliveryAddressVO.getAreaId());
			address.setTraderId(fileDeliveryAddressVO.getTraderId());
			if(fileDeliveryAddressVO.getTraderId()!=null){
				TraderEntity traderEntity = traderMapper.selectByPrimaryKey(fileDeliveryAddressVO.getTraderId());
				address.setTraderName(traderEntity.getTraderName());
			}
			//
			//地址信息，包含省市区
			address.setTraderContactAddress(getRegionNames(fileDeliveryAddressVO.getTraderContactAddressId(),address.getAreaId())+""+fileDeliveryAddressVO.getTraderContactAddressInfo());
			//地址信息，不包含省市区
			address.setTraderContactAddressInfo(fileDeliveryAddressVO.getTraderContactAddressInfo());
		}
	}
	private String getRegionNames(Integer traderAddressId,Integer areaId){
		if(traderAddressId!=null&&traderAddressId>0){
			TraderAddressEntity traderAddressEntity=traderAddressMapper.selectByPrimaryKey(traderAddressId);
			areaId=traderAddressEntity.getAreaId();
		}
		if(Objects.nonNull(areaId)) {
			return  regionMapper.getRegionNameStringByMinRegionId(areaId);
		}
		return "";
	}


	@Override
	public List<FileDeliveryAddressVO> getFileDeliveryAddress(Integer fileDeliveryId) {
		FileDeliveryAddressExample example = new FileDeliveryAddressExample();
		example.createCriteria().andFileDeliveryIdEqualTo(fileDeliveryId).andIsDeleteEqualTo(0);
		List<FileDeliveryAddress>  fileDeliveryAddressList = fileDeliveryAddressExtMapper.selectByExample(example);
		
		List<FileDeliveryAddressVO> fileDeliveryAddressVOList = new ArrayList<>();
		for (FileDeliveryAddress fileDeliveryAddress : fileDeliveryAddressList) {
			FileDeliveryAddressVO fileDeliveryAddressVO = new FileDeliveryAddressVO();
			fileDeliveryAddressVO.setAddTime(com.vedeng.common.util.DateUtil.DateToString(fileDeliveryAddress.getAddTime(), "yyyy-mm-dd HH:mm:ss") );
			fileDeliveryAddressVO.setApplyId(fileDeliveryAddress.getApplyId());
			fileDeliveryAddressVO.setAreaId(fileDeliveryAddress.getAreaId());
			fileDeliveryAddressVO.setCityId(fileDeliveryAddress.getCityId());
			fileDeliveryAddressVO.setContactType(fileDeliveryAddress.getContactType());
			fileDeliveryAddressVO.setCreator(fileDeliveryAddress.getCreator());
			fileDeliveryAddressVO.setFileDeliveryId(fileDeliveryAddress.getFileDeliveryId());
			fileDeliveryAddressVO.setIsDelete(fileDeliveryAddress.getIsDelete());
			fileDeliveryAddressVO.setId(fileDeliveryAddress.getId());
			fileDeliveryAddressVO.setModTime(fileDeliveryAddress.getModTime());
			fileDeliveryAddressVO.setProvinceId(fileDeliveryAddress.getProvinceId());
			fileDeliveryAddressVO.setTraderContactAddress(fileDeliveryAddress.getTraderContactAddress());
			fileDeliveryAddressVO.setTraderContactAddressId(fileDeliveryAddress.getTraderContactAddressId());
			fileDeliveryAddressVO.setTraderContactAddressInfo(fileDeliveryAddress.getTraderContactAddressInfo());
			fileDeliveryAddressVO.setTraderContactId(fileDeliveryAddress.getTraderContactId());
			fileDeliveryAddressVO.setTraderContactMobile(fileDeliveryAddress.getTraderContactMobile());
			fileDeliveryAddressVO.setTraderContactName(fileDeliveryAddress.getTraderContactName());
			fileDeliveryAddressVO.setTraderId(fileDeliveryAddress.getTraderId());
			fileDeliveryAddressVO.setTraderName(fileDeliveryAddress.getTraderName());
			fileDeliveryAddressVO.setTraderType(fileDeliveryAddress.getTraderType());
			fileDeliveryAddressVO.setUpdateRemark(fileDeliveryAddress.getUpdateRemark());
			fileDeliveryAddressVO.setExpressLabelurl(fileDeliveryAddress.getExpressLabelurl());
			fileDeliveryAddressVO.setLogisticsNo(fileDeliveryAddress.getLogisticsNo());
			fileDeliveryAddressVO.setDeliveryStatus(fileDeliveryAddress.getDeliveryStatus());
			fileDeliveryAddressVO.setLogisticsNoEdit(fileDeliveryAddress.getLogisticsNoEdit());
			fileDeliveryAddressVOList.add(fileDeliveryAddressVO);
		}
		return fileDeliveryAddressVOList;
	}
	
	@Override
	public List<FileDeliveryAddressVO> getFileDeliveryAddressByIds(List<Integer> ids) {
		FileDeliveryAddressExample example = new FileDeliveryAddressExample();
		example.createCriteria().andIdIn(ids).andIsDeleteEqualTo(0);
		example.setOrderByClause("ID ASC");
		List<FileDeliveryAddress>  fileDeliveryAddressList = fileDeliveryAddressExtMapper.selectByExample(example);
		
		List<FileDeliveryAddressVO> fileDeliveryAddressVOList = new ArrayList<>();
		for (FileDeliveryAddress fileDeliveryAddress : fileDeliveryAddressList) {
			FileDeliveryAddressVO fileDeliveryAddressVO = new FileDeliveryAddressVO();
			fileDeliveryAddressVO.setAddTime(com.vedeng.common.util.DateUtil.DateToString(fileDeliveryAddress.getAddTime(), "yyyy-mm-dd HH:mm:ss") );
			fileDeliveryAddressVO.setApplyId(fileDeliveryAddress.getApplyId());
			fileDeliveryAddressVO.setAreaId(fileDeliveryAddress.getAreaId());
			fileDeliveryAddressVO.setCityId(fileDeliveryAddress.getCityId());
			fileDeliveryAddressVO.setContactType(fileDeliveryAddress.getContactType());
			fileDeliveryAddressVO.setCreator(fileDeliveryAddress.getCreator());
			fileDeliveryAddressVO.setFileDeliveryId(fileDeliveryAddress.getFileDeliveryId());
			fileDeliveryAddressVO.setIsDelete(fileDeliveryAddress.getIsDelete());
			fileDeliveryAddressVO.setId(fileDeliveryAddress.getId());
			fileDeliveryAddressVO.setModTime(fileDeliveryAddress.getModTime());
			fileDeliveryAddressVO.setProvinceId(fileDeliveryAddress.getProvinceId());
			fileDeliveryAddressVO.setTraderContactAddress(fileDeliveryAddress.getTraderContactAddress());
			fileDeliveryAddressVO.setTraderContactAddressId(fileDeliveryAddress.getTraderContactAddressId());
			fileDeliveryAddressVO.setTraderContactAddressInfo(fileDeliveryAddress.getTraderContactAddressInfo());
			fileDeliveryAddressVO.setTraderContactId(fileDeliveryAddress.getTraderContactId());
			fileDeliveryAddressVO.setTraderContactMobile(fileDeliveryAddress.getTraderContactMobile());
			fileDeliveryAddressVO.setTraderContactName(fileDeliveryAddress.getTraderContactName());
			fileDeliveryAddressVO.setTraderId(fileDeliveryAddress.getTraderId());
			fileDeliveryAddressVO.setTraderName(fileDeliveryAddress.getTraderName());
			fileDeliveryAddressVO.setTraderType(fileDeliveryAddress.getTraderType());
			fileDeliveryAddressVO.setUpdateRemark(fileDeliveryAddress.getUpdateRemark());
			fileDeliveryAddressVOList.add(fileDeliveryAddressVO);
		}
		return fileDeliveryAddressVOList;
	}



	@Override
	public int deleteFileDeliveryAddressById(Integer id,Integer userId) {
		FileDeliveryAddress record = new FileDeliveryAddress();
		record.setIsDelete(1);
		record.setModTime(new Date());
		record.setUpdater(userId);
		record.setId(id);
		return fileDeliveryAddressExtMapper.updateByPrimaryKeySelective(record );
	}

}
