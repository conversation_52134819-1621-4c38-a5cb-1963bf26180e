package com.vedeng.erp.aftersale.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 售后付款申请单
 * @date 2024/9/2 10:04
 */
@Data
public class CreateRefundApplyRequest {


    /**
     * 交易方式
     */
    private Integer traderMode;
    /**
     * 交易主体
     */
    private Integer traderSubject;

    /**
     * 售后单id
     */
    private Integer afterSalesId;

    /**
     * 银行账户id
     */
    private Integer bankId;

    /**
     * 银行收款别名id
     */
    private Long bankReceiptAliasId;

    /**
     * 付款金额
     */
    private BigDecimal payAmount;

    /**
     * 客户名称
     */
    private String traderName;

    /**
     * 开户账户
     */
    private String bank;

    /**
     * 银行账户
     */
    private String bankAccount;
    /**
     * 联行号
     */
    private String bankCode;

    /**
     * 内部备注
     */
    private String comment;

    /**
     * 申请人id
     */
    private Integer applyUserId;

    /**
     * 申请人名称
     */
    private String applyUserName;

    public String getBankAccount() {
        return bankAccount.replaceAll("\\s+", "");
    }
}
