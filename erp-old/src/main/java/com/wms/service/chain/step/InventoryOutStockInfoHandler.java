package com.wms.service.chain.step;

import com.alibaba.fastjson.JSON;
import com.vedeng.logistics.service.WarehouseStockService;
import com.vedeng.stock.api.stock.dto.StockInfoDto;
import com.vedeng.stock.api.stock.dto.WarehouseDto;
import com.wms.dao.WmsOutputOrderMapper;
import com.wms.dto.StockCalculateDto;
import com.wms.model.po.WmsLogicalOrdergoods;
import com.wms.model.po.WmsOutputOrder;
import com.wms.service.chain.AbstractHandlerStep;
import com.wms.service.chain.HandlerStepContext;
import com.wms.service.stockcalculate.OutputOrderAuditPassCaculateImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 盘亏单出库 库存服务同步
 * <AUTHOR>
 */
@Service
@Slf4j
public class InventoryOutStockInfoHandler extends AbstractHandlerStep {

    @Autowired
    private OutputOrderAuditPassCaculateImpl outputOrderAuditPassCaculateImpl;

    @Autowired
    private WmsOutputOrderMapper outputOrderMapper;

    @Autowired
    private WarehouseStockService warehouseStockService;

    @Override
    protected void doDealWith(HandlerStepContext context) throws Exception {

        List<WmsLogicalOrdergoods> logicalOrdergoods = context.get("wmsLogicalOrdergoodsList");

        Long inventoryOutOrderId = context.get("inventoryOutOrderId");

        WmsOutputOrder wmsOutputOrder = outputOrderMapper.selectByPrimaryKey(inventoryOutOrderId);

        //更新库存服务

        log.info("盘亏出库 发起库存同步：wmsOutputOrder:{}，logicalOrdergoods:{}", JSON.toJSONString(wmsOutputOrder),JSON.toJSONString(logicalOrdergoods));
        if(CollectionUtils.isEmpty(logicalOrdergoods)){
            log.error("盘亏出库 无逻辑库存表数据：wmsOutputOrder:{}，logicalOrdergoods:{}", JSON.toJSONString(wmsOutputOrder),JSON.toJSONString(logicalOrdergoods));
            return;
        }
        List<StockCalculateDto> stockCalculateList  = new ArrayList<>();
        logicalOrdergoods.forEach(ordergoods -> {
            StockCalculateDto stockCalculateDto = new StockCalculateDto();
            if(ordergoods.getOccupyNum().equals(0)){
                return;
            }
            stockCalculateDto.setSku(ordergoods.getSku());
            stockCalculateDto.setOccupyNum(ordergoods.getOccupyNum());
            stockCalculateDto.setStockNum(0);
            stockCalculateDto.setLogicalWarehouseId(ordergoods.getLogicalWarehouseId());
            stockCalculateList.add(stockCalculateDto);
        });
        log.info("盘亏出库 发起库存计算：wmsOutputOrderId:{}，stockCalculateList:{}", inventoryOutOrderId,JSON.toJSONString(stockCalculateList));
        //库存策略计算
        StockInfoDto stockInfoDto  = new StockInfoDto();
        stockInfoDto.setRelatedNo(wmsOutputOrder.getOrderNo());
        List<WarehouseDto> warehouseDtos = outputOrderAuditPassCaculateImpl.calculateStockInfo(stockCalculateList);
        log.info("盘亏出库 发起库存计算：wmsOutputOrderId:{}，结果:{}", inventoryOutOrderId,JSON.toJSONString(warehouseDtos));
        stockInfoDto.setWarehouseStockList(warehouseDtos);

        //更新库存服务
        warehouseStockService.updateStockInfo(stockInfoDto);

    }
}
