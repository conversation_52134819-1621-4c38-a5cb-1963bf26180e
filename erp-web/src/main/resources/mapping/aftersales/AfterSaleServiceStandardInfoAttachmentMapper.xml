<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.aftersales.dao.AfterSaleServiceStandardInfoAttachmentMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.aftersales.model.AfterSaleServiceStandardInfoAttachment" >
    <id column="SERVICE_STANDARD_INFO_ATTACHMENT_ID" property="serviceStandardInfoAttachmentId" jdbcType="BIGINT" />
    <result column="SERVICE_STANDARD_INFO_ID" property="serviceStandardInfoId" jdbcType="BIGINT" />
    <result column="DOMAIN" property="domain" jdbcType="VARCHAR" />
    <result column="URI" property="uri" jdbcType="VARCHAR" />
    <result column="FILE_NAME" property="fileName" jdbcType="VARCHAR" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="UPDATOR" property="updator" jdbcType="INTEGER" />
    <result column="ADD_TIME" property="addTime" jdbcType="VARCHAR" />
    <result column="MOD_TIME" property="modTime" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    SERVICE_STANDARD_INFO_ATTACHMENT_ID, SERVICE_STANDARD_INFO_ID, DOMAIN, URI, FILE_NAME, 
    CREATOR, UPDATOR, ADD_TIME, MOD_TIME
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from T_AFTER_SALE_SERVICE_STANDARD_INFO_ATTACHMENT
    where SERVICE_STANDARD_INFO_ATTACHMENT_ID = #{serviceStandardInfoAttachmentId,jdbcType=BIGINT}
  </select>

  <select id="selectByStandardInfoId" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from T_AFTER_SALE_SERVICE_STANDARD_INFO_ATTACHMENT
    where SERVICE_STANDARD_INFO_ID = #{serviceStandardInfoId,jdbcType=BIGINT}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from T_AFTER_SALE_SERVICE_STANDARD_INFO_ATTACHMENT
    where SERVICE_STANDARD_INFO_ATTACHMENT_ID = #{serviceStandardInfoAttachmentId,jdbcType=BIGINT}
  </delete>

  <delete id="deleteByServiceStandardInfoId" parameterType="java.lang.Long" >
    delete from T_AFTER_SALE_SERVICE_STANDARD_INFO_ATTACHMENT
    where SERVICE_STANDARD_INFO_ID = #{serviceStandardInfoId,jdbcType=BIGINT}
  </delete>


  <insert id="batchInsertAttashment" parameterType="java.util.List">
    INSERT INTO T_AFTER_SALE_SERVICE_STANDARD_INFO_ATTACHMENT
    (
        SERVICE_STANDARD_INFO_ID,
        URI,
        DOMAIN,
        FILE_NAME,
        CREATOR,
        UPDATOR,
        ADD_TIME,
        MOD_TIME
    )
    VALUES
    <foreach collection="list" item="item" index="index" separator=",">
      (
        #{item.serviceStandardInfoId,jdbcType=INTEGER},
        #{item.uri,jdbcType=VARCHAR},
        #{item.domain,jdbcType=VARCHAR},
        #{item.fileName,jdbcType=VARCHAR},
        #{item.creator,jdbcType=VARCHAR},
        #{item.updator,jdbcType=VARCHAR},
        #{item.addTime,jdbcType=VARCHAR},
        #{item.modTime,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>



  <insert id="insert" parameterType="com.vedeng.aftersales.model.AfterSaleServiceStandardInfoAttachment" >
    insert into T_AFTER_SALE_SERVICE_STANDARD_INFO_ATTACHMENT (SERVICE_STANDARD_INFO_ATTACHMENT_ID, SERVICE_STANDARD_INFO_ID, 
      DOMAIN, URI, FILE_NAME, 
      CREATOR, UPDATOR, ADD_TIME, 
      MOD_TIME)
    values (#{serviceStandardInfoAttachmentId,jdbcType=BIGINT}, #{serviceStandardInfoId,jdbcType=BIGINT}, 
      #{domain,jdbcType=VARCHAR}, #{uri,jdbcType=VARCHAR}, #{fileName,jdbcType=VARCHAR}, 
      #{creator,jdbcType=INTEGER}, #{updator,jdbcType=INTEGER}, #{addTime,jdbcType=VARCHAR}, 
      #{modTime,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.aftersales.model.AfterSaleServiceStandardInfoAttachment" >
    insert into T_AFTER_SALE_SERVICE_STANDARD_INFO_ATTACHMENT
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="serviceStandardInfoAttachmentId != null" >
        SERVICE_STANDARD_INFO_ATTACHMENT_ID,
      </if>
      <if test="serviceStandardInfoId != null" >
        SERVICE_STANDARD_INFO_ID,
      </if>
      <if test="domain != null" >
        DOMAIN,
      </if>
      <if test="uri != null" >
        URI,
      </if>
      <if test="fileName != null" >
        FILE_NAME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="updator != null" >
        UPDATOR,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="modTime != null" >
        MOD_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="serviceStandardInfoAttachmentId != null" >
        #{serviceStandardInfoAttachmentId,jdbcType=BIGINT},
      </if>
      <if test="serviceStandardInfoId != null" >
        #{serviceStandardInfoId,jdbcType=BIGINT},
      </if>
      <if test="domain != null" >
        #{domain,jdbcType=VARCHAR},
      </if>
      <if test="uri != null" >
        #{uri,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null" >
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updator != null" >
        #{updator,jdbcType=INTEGER},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null" >
        #{modTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.aftersales.model.AfterSaleServiceStandardInfoAttachment" >
    update T_AFTER_SALE_SERVICE_STANDARD_INFO_ATTACHMENT
    <set >
      <if test="serviceStandardInfoId != null" >
        SERVICE_STANDARD_INFO_ID = #{serviceStandardInfoId,jdbcType=BIGINT},
      </if>
      <if test="domain != null" >
        DOMAIN = #{domain,jdbcType=VARCHAR},
      </if>
      <if test="uri != null" >
        URI = #{uri,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null" >
        FILE_NAME = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updator != null" >
        UPDATOR = #{updator,jdbcType=INTEGER},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null" >
        MOD_TIME = #{modTime,jdbcType=VARCHAR},
      </if>
    </set>
    where SERVICE_STANDARD_INFO_ATTACHMENT_ID = #{serviceStandardInfoAttachmentId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.aftersales.model.AfterSaleServiceStandardInfoAttachment" >
    update T_AFTER_SALE_SERVICE_STANDARD_INFO_ATTACHMENT
    set SERVICE_STANDARD_INFO_ID = #{serviceStandardInfoId,jdbcType=BIGINT},
      DOMAIN = #{domain,jdbcType=VARCHAR},
      URI = #{uri,jdbcType=VARCHAR},
      FILE_NAME = #{fileName,jdbcType=VARCHAR},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATOR = #{updator,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=VARCHAR},
      MOD_TIME = #{modTime,jdbcType=VARCHAR}
    where SERVICE_STANDARD_INFO_ATTACHMENT_ID = #{serviceStandardInfoAttachmentId,jdbcType=BIGINT}
  </update>
</mapper>