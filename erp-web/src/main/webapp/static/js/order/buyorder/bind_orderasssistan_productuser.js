$(function () {
    $("#search").click(function () {
        var orderAssistantId = $('#orderAssistant').val();
        if (orderAssistantId == null || orderAssistantId === "") {
            layer.alert('请选择人员');
            return;
        }
        window.location.href = window.location.pathname+"?orderAssitantUserId="+orderAssistantId;
    });
});

function UnbindRelation(obj) {
    var id = $(obj).attr("data-keyId");
    layer.confirm('删除后，待办任务将发生更新，确认删除?',{
        btn: ['取消', '确定']
    },function (index) {
        console.log(1);
        layer.close(index)
    },function () {
        console.log(22222);
        $.ajax({
            async: false,
            url: "./unbindOrderAssRelation.do",
            type: 'POST',
            contentType: "application/json;charset=utf-8",
            data: JSON.stringify({id:id}),
            dataType: 'json',
            success: function (result) {
                if (result.code == 0) {
                    console.log(result.message);
                    layer.msg(result.message,{
                        icon: 1,
                        time: 1000 //0.5秒关闭（如果不配置，默认是3秒）
                    },function(){
                        window.location.reload();
                    });

                } else {
                    layer.alert(result.message);
                }
            },
            error: function (result) {
                layer.alert("操作失败")
            }
        })
    });
}

function addNewOrderAssRelation() {
    var orderAssistantId = $('#orderAssistant').val();
    if (orderAssistantId == null || orderAssistantId === "") {
        layer.alert('请选择人员');
        return;
    }
    $("#terminalDiv")
        .attr(
            'layerParams',
            '{"width":"500px","height":"400px","title":"绑定经理和助理","link":"'
            + page_url
            + '/order/buyorder/bindOrderAssistantPage.do?orderAssitantUserId='
            + orderAssistantId + '"}');
    $("#terminalDiv").click();
}

