package com.vedeng.activiti.service;

import java.util.List;

import com.vedeng.order.model.SaleorderModifyApply;

/**
 * 
 * <b>Description:</b><br> 
 * <AUTHOR>
 * @Note
 * <b>ProjectName:</b> erp
 * <br><b>PackageName:</b> com.vedeng.activiti.service
 * <br><b>ClassName:</b> ActivitiAssigneeService
 * <br><b>Date:</b> 2017年11月21日 下午4:17:23
 */
public interface ActivitiAssigneeService {
    /**
     * 返回Session中的用户Name
     */
    String getSessionUser();
    
    /**
     * 
     * <b>Description:</b><br> 根据角色名称获取对应角色的人
     * @param level
     * @return
     * @Note
     * <b>Author:</b> Michael
     * <br><b>Date:</b> 2017年12月4日 上午9:29:57
     */
    List<String> getUserListByRole(String roleName);

    /**
     * 销售售后类型为“销售退货”“销售换货” || 销售退货单有SKU退货方式为“直发退货”“直发换货” || 直发退货的SKU发货数量大于0
     * @param roleName
     * @return
     */
    List<String> afterSaleGetSkuManageAndRole(String roleName,String param);

    /**
     * 根据userId拼接字符转获取审核人
     * @param userIdStr userId拼接字符串（逗号分隔）
     * @return
     */
    List<String> findByUserIdStr(String userIdStr);
   
    /**
     * 
     * <b>Description:</b><br> 根据对人的名称获取他的直接上级（直接上级）
     * @param level
     * @return
     * @Note
     * <b>Author:</b> Michael
     * <br><b>Date:</b> 2017年12月4日 上午9:29:57
     */
    String getUserParentsUser(String roleName);

    /**
     * 采购费用售后供应链主管+上级
     * @param userName
     * @return
     */
    List<String>  getExpenseAfterSalesUser(String userName);
    
    /**
     * 
     * <b>Description:</b><br> 根据部门orgId和对应的职位等级获取对应的人名字的集合
     * @param level
     * @return
     * @Note
     * <b>Author:</b> Michael
     * <br><b>Date:</b> 2017年12月4日 上午9:29:57
     */
    List<String> getUserByLevel(Integer orgId,Integer level);
    
    List<String> getUserByCategory(Integer categoryId);
    
    List<String> getUserByOrderId(Integer orderId,Integer type,Integer userType);

    String getAfterSaleUser(Integer orgId);
    
    List<String> getBuyorderUserIdBySMA(SaleorderModifyApply saleorderModifyApply);

    List<String> getBelongsBySkuID(Integer skuId);

    //获取一个部门的销售总监
    List<String> getChiefByUserId(String roleName);

    /**
     * <AUTHOR>
     * @desc 根据skuid查询产品经理
     * @param skuId
     * @return
     */
    List<String> getBelongManagerBySkuId(Integer skuId);

    /**
     * 根据orgId获取一级部门长
     */
    String getTopDepartmentLeader(String currentAssinee,int num);

    /**
     *根据职位获取userList
     */
    List<String> getUserListByPosition(String positionName);

    List<Integer> getUserIdListByPosition(String positionName);

    /**
     * 根据职位id获取userList
     * @param positionId
     * @return
     */
    List<String> getUserListByPositionId(Integer positionId);
}
