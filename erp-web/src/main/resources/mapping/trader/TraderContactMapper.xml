<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.trader.dao.TraderContactMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.trader.model.TraderContact" >
    <id column="TRADER_CONTACT_ID" property="traderContactId" jdbcType="INTEGER" />
    <result column="TRADER_ID" property="traderId" jdbcType="INTEGER" />
    <result column="TRADER_TYPE" property="traderType" jdbcType="BIT" />
    <result column="IS_ENABLE" property="isEnable" jdbcType="BIT" />
    <result column="SEX" property="sex" jdbcType="BIT" />
    <result column="NAME" property="name" jdbcType="VARCHAR" />
    <result column="DEPARTMENT" property="department" jdbcType="VARCHAR" />
    <result column="POSITION" property="position" jdbcType="VARCHAR" />
    <result column="TELEPHONE" property="telephone" jdbcType="VARCHAR" />
    <result column="FAX" property="fax" jdbcType="VARCHAR" />
    <result column="MOBILE" property="mobile" jdbcType="VARCHAR" />
    <result column="MOBILE2" property="mobile2" jdbcType="VARCHAR" />
    <result column="EMAIL" property="email" jdbcType="VARCHAR" />
    <result column="QQ" property="qq" jdbcType="VARCHAR" />
    <result column="WEIXIN" property="weixin" jdbcType="VARCHAR" />
    <result column="IS_ON_JOB" property="isOnJob" jdbcType="BIT" />
    <result column="IS_DEFAULT" property="isDefault" jdbcType="BIT" />
    <result column="BIRTHDAY" property="birthday" jdbcType="DATE" />
    <result column="IS_MARRIED" property="isMarried" jdbcType="BIT" />
    <result column="HAVE_CHILDREN" property="haveChildren" jdbcType="BIT" />
    <result column="EDUCATION" property="education" jdbcType="INTEGER" />
    <result column="CHARACTER" property="character" jdbcType="VARCHAR" />
    <result column="COMMENTS" property="comments" jdbcType="VARCHAR" />
    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
  </resultMap>

  <resultMap type="com.vedeng.trader.model.vo.TraderContactVo" id="VoResultMap" extends="BaseResultMap">
  	<result column="ACCOUNT" property="account" jdbcType="VARCHAR"/>
  	<result column="EDUCATION_NAME" property="educationName" jdbcType="VARCHAR"/>
    <result column="IS_VEDENG_JX" property="isVedengJx" jdbcType="INTEGER" />
      <result column="IS_VEDENG_MEMBER" property="isVedengMember" jdbcType="BIT" />
  </resultMap>

  <sql id="Base_Column_List" >
    TRADER_CONTACT_ID, TRADER_ID, TRADER_TYPE, IS_ENABLE, SEX, NAME, DEPARTMENT, POSITION,
    TELEPHONE, FAX, MOBILE, MOBILE2, EMAIL, QQ, WEIXIN, IS_ON_JOB, IS_DEFAULT, BIRTHDAY,
    IS_MARRIED, HAVE_CHILDREN, EDUCATION, `CHARACTER`, COMMENTS, ADD_TIME, CREATOR, MOD_TIME,
    UPDATER
  </sql>
    <select id="getTraderIdListByContactWay" parameterType="com.vedeng.trader.model.TraderContact" resultType="java.lang.Integer">
        select
        TRADER_ID
        from
        T_TRADER_CONTACT
        where
        1=1
        <if test="traderType != null and traderType != 0">
            and TRADER_TYPE = #{traderType,jdbcType=BIT}
        </if>
        <if test="mobile != null and mobile != ''">
            and (TELEPHONE like CONCAT('%',#{mobile},'%' )
            or MOBILE like CONCAT('%',#{mobile},'%' )
            or MOBILE2 like CONCAT('%',#{mobile},'%' )
            or QQ like CONCAT('%',#{mobile},'%' )
            or WEIXIN like CONCAT('%',#{mobile},'%' )
            or EMAIL like CONCAT('%',#{mobile},'%' )
            )
        </if>
        limit 1000
    </select>

    <select id="getTraderIdListByWxStatus" parameterType="com.vedeng.trader.model.vo.TraderContactVo" resultType="java.lang.Integer">
        select
        distinct TRADER_ID
        from
        T_TRADER_CONTACT
        where IS_DEFAULT = 1
        <if test="wxStatus != null and wxStatus == 1">
            and WEIXIN != '' and WEIXIN IS NOT NULL
        </if>
        <if test="wxStatus != null and wxStatus == 2">
            and (WEIXIN = '' or WEIXIN IS NULL )
        </if>
        <if test="traderType != null and traderType != 0">
            and TRADER_TYPE = #{traderType,jdbcType=BIT}
        </if>

    </select>

    <select id="getTraderContact" parameterType="com.vedeng.trader.model.TraderContact" resultMap="BaseResultMap">
        select
        TRADER_CONTACT_ID, TRADER_ID, TRADER_TYPE, IS_ENABLE, SEX, NAME, DEPARTMENT, POSITION,
        TELEPHONE,  MOBILE, MOBILE2,  IS_ON_JOB, IS_DEFAULT
        from
        T_TRADER_CONTACT
        where
        1=1

            and TRADER_ID = #{traderId,jdbcType=INTEGER}

        <if test="traderType != null">
            and TRADER_TYPE = #{traderType,jdbcType=BIT}
        </if>
        <if test="isEnable != null">
            and IS_ENABLE = #{isEnable,jdbcType=BIT}
        </if>
        <if test="isDefault != null">
            and IS_DEFAULT = #{isDefault,jdbcType=BIT}
        </if>
        order by
        IS_DEFAULT desc,ADD_TIME desc
    </select>

    <select id="selectByPrimaryKey" resultType="com.vedeng.trader.model.TraderContact" parameterType="java.lang.Integer" >
        select
        <include refid="Base_Column_List" />
        from T_TRADER_CONTACT
        where TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER}
    </select>

    <select id="getTraderContactVo" parameterType="com.vedeng.trader.model.TraderContact" resultMap="VoResultMap">
        select
        tc.TRADER_CONTACT_ID, tc.TRADER_ID, tc.TRADER_TYPE, tc.IS_ENABLE, tc.SEX, tc.NAME, tc.DEPARTMENT, tc.POSITION,
        tc.TELEPHONE, tc.FAX, tc.MOBILE, tc.MOBILE2, tc.EMAIL, tc.QQ, tc.WEIXIN, tc.IS_ON_JOB, tc.IS_DEFAULT, tc.BIRTHDAY,
        tc.IS_MARRIED, tc.HAVE_CHILDREN, tc.EDUCATION, tc.`CHARACTER`, tc.COMMENTS, tc.ADD_TIME, tc.CREATOR, tc.MOD_TIME,
        tc.UPDATER,sod1.TITLE AS EDUCATION_NAME
        from T_TRADER_CONTACT tc
        left join T_SYS_OPTION_DEFINITION sod1 on tc.EDUCATION = sod1.SYS_OPTION_DEFINITION_ID
        where 1 = 1
        <if test="traderContactId != null and traderContactId != 0">
            and tc.TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER}
        </if>
        <if test="traderId != null">
            and tc.TRADER_ID = #{traderId,jdbcType=INTEGER}
        </if>
        <if test="traderType != null">
            and tc.TRADER_TYPE = #{traderType,jdbcType=BIT}
        </if>
        <if test="isDefault != null">
            and tc.IS_DEFAULT = #{isDefault,jdbcType=BIT}
        </if>
        limit 1
    </select>


    <select id="getTraderContactByNameAndMobile" parameterType="com.vedeng.trader.model.TraderContact" resultMap="BaseResultMap">
        select
        TRADER_CONTACT_ID, TRADER_ID, TRADER_TYPE, IS_ENABLE, SEX, NAME, DEPARTMENT, POSITION,
        TELEPHONE,  MOBILE, MOBILE2,  IS_ON_JOB, IS_DEFAULT
        from
        T_TRADER_CONTACT
        where
             MOBILE = #{mobile,jdbcType=VARCHAR}
        <if test="name != null and name != ''">
            and NAME = #{name,jdbcType=VARCHAR}
        </if>
            and TRADER_ID = #{traderId,jdbcType=INTEGER}
        <if test="traderType != null">
            and TRADER_TYPE = #{traderType,jdbcType=BIT}
        </if>
    </select>


    <insert id="insertSelective" parameterType="com.vedeng.trader.model.TraderContact" useGeneratedKeys="true" keyProperty="traderContactId" >
        insert into T_TRADER_CONTACT
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="traderContactId != null" >
                TRADER_CONTACT_ID,
            </if>
            <if test="traderId != null" >
                TRADER_ID,
            </if>
            <if test="traderType != null" >
                TRADER_TYPE,
            </if>
            <if test="isEnable != null" >
                IS_ENABLE,
            </if>
            <if test="sex != null" >
                SEX,
            </if>
            <if test="name != null" >
                NAME,
            </if>
            <if test="department != null" >
                DEPARTMENT,
            </if>
            <if test="position != null" >
                POSITION,
            </if>
            <if test="telephone != null" >
                TELEPHONE,
            </if>
            <if test="fax != null" >
                FAX,
            </if>
            <if test="mobile != null" >
                MOBILE,
            </if>
            <if test="mobile2 != null" >
                MOBILE2,
            </if>
            <if test="email != null" >
                EMAIL,
            </if>
            <if test="qq != null" >
                QQ,
            </if>
            <if test="weixin != null" >
                WEIXIN,
            </if>
            <if test="isOnJob != null" >
                IS_ON_JOB,
            </if>
            <if test="isDefault != null" >
                IS_DEFAULT,
            </if>
            <if test="birthday != null" >
                BIRTHDAY,
            </if>
            <if test="isMarried != null" >
                IS_MARRIED,
            </if>
            <if test="haveChildren != null" >
                HAVE_CHILDREN,
            </if>
            <if test="education != null" >
                EDUCATION,
            </if>
            <if test="character != null" >
                CHARACTER,
            </if>
            <if test="comments != null" >
                COMMENTS,
            </if>
            <if test="addTime != null" >
                ADD_TIME,
            </if>
            <if test="creator != null" >
                CREATOR,
            </if>
            <if test="modTime != null" >
                MOD_TIME,
            </if>
            <if test="updater != null" >
                UPDATER,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="traderContactId != null" >
                #{traderContactId,jdbcType=INTEGER},
            </if>
            <if test="traderId != null" >
                #{traderId,jdbcType=INTEGER},
            </if>
            <if test="traderType != null" >
                #{traderType,jdbcType=BIT},
            </if>
            <if test="isEnable != null" >
                #{isEnable,jdbcType=BIT},
            </if>
            <if test="sex != null" >
                #{sex,jdbcType=BIT},
            </if>
            <if test="name != null" >
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="department != null" >
                #{department,jdbcType=VARCHAR},
            </if>
            <if test="position != null" >
                #{position,jdbcType=VARCHAR},
            </if>
            <if test="telephone != null" >
                #{telephone,jdbcType=VARCHAR},
            </if>
            <if test="fax != null" >
                #{fax,jdbcType=VARCHAR},
            </if>
            <if test="mobile != null" >
                #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="mobile2 != null" >
                #{mobile2,jdbcType=VARCHAR},
            </if>
            <if test="email != null" >
                #{email,jdbcType=VARCHAR},
            </if>
            <if test="qq != null" >
                #{qq,jdbcType=VARCHAR},
            </if>
            <if test="weixin != null" >
                #{weixin,jdbcType=VARCHAR},
            </if>
            <if test="isOnJob != null" >
                #{isOnJob,jdbcType=BIT},
            </if>
            <if test="isDefault != null" >
                #{isDefault,jdbcType=BIT},
            </if>
            <if test="birthday != null" >
                #{birthday,jdbcType=DATE},
            </if>
            <if test="isMarried != null" >
                #{isMarried,jdbcType=BIT},
            </if>
            <if test="haveChildren != null" >
                #{haveChildren,jdbcType=BIT},
            </if>
            <if test="education != null" >
                #{education,jdbcType=INTEGER},
            </if>
            <if test="character != null" >
                #{character,jdbcType=VARCHAR},
            </if>
            <if test="comments != null" >
                #{comments,jdbcType=VARCHAR},
            </if>
            <if test="addTime != null" >
                #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null" >
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="modTime != null" >
                #{modTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null" >
                #{updater,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>


    <select id="getTraderContactVoList" resultType="com.vedeng.trader.model.vo.TraderContactVo">
        SELECT
            tc.TRADER_CONTACT_ID,
            tc.TRADER_ID,
            tc.TRADER_TYPE,
            tc.IS_ENABLE,
            tc.SEX,
            tc.NAME,
            tc.DEPARTMENT,
            tc.POSITION,
            tc.TELEPHONE,
            tc.FAX,
            tc.MOBILE,
            tc.MOBILE2,
            tc.EMAIL,
            tc.QQ,
            tc.WEIXIN,
            tc.IS_ON_JOB,
            tc.IS_DEFAULT,
            tc.BIRTHDAY,
            tc.IS_MARRIED,
            tc.HAVE_CHILDREN,
            tc.EDUCATION,
            tc.`CHARACTER`,
            tc.COMMENTS,
            tc.ADD_TIME,
            tc.CREATOR,
            tc.MOD_TIME,
            tc.UPDATER,
            COALESCE(wa.IS_VEDENG_JX,wa1.IS_VEDENG_JX,- 1) AS IS_VEDENG_JX,
            COALESCE(wa.IS_VEDENG_MEMBER,wa1.IS_VEDENG_MEMBER,- 1) AS IS_VEDENG_MEMBER
        FROM
            T_TRADER_CONTACT tc
                LEFT JOIN T_WEB_ACCOUNT wa ON tc.MOBILE = wa.MOBILE
                LEFT JOIN T_WEB_ACCOUNT wa1 ON tc.MOBILE2 = wa1.MOBILE
        WHERE
            1 = 1
        and tc.TRADER_ID = #{traderId,jdbcType=INTEGER}

        <if test="traderType != null">
            and tc.TRADER_TYPE = #{traderType,jdbcType=INTEGER}
        </if>
        <if test="isEnable != null">
            and tc.IS_ENABLE = #{isEnable,jdbcType=BIT}
        </if>
        <if test="name != null and name != '' " >
            and tc.NAME like CONCAT('%',#{name},'%' )
        </if>
        GROUP BY
            tc.TRADER_CONTACT_ID
    </select>

    <select id="getBelongUserIdListByPhone" resultType="java.lang.Integer">
        SELECT
            T2.USER_ID
        FROM
            T_TRADER_CONTACT T1
            LEFT JOIN T_R_TRADER_J_USER T2 ON T1.TRADER_ID = T2.TRADER_ID
        WHERE
            T1.TRADER_TYPE = 1
          AND ( T1.TELEPHONE = #{phone,jdbcType=VARCHAR} OR
                T1.MOBILE = #{phone,jdbcType=VARCHAR} OR
                T1.MOBILE2 = #{phone,jdbcType=VARCHAR} )
        GROUP BY
            T2.USER_ID
    </select>

    <select id="getTraderCountByPhone" resultType="java.lang.Integer">
        SELECT
            COUNT(*)
        FROM
            T_TRADER_CONTACT
        WHERE
            MOBILE = #{phone,jdbcType=VARCHAR}
           OR MOBILE2 = #{phone,jdbcType=VARCHAR}
    </select>
    <select id="getTraderContactVoListForOrderSearch" resultType="com.vedeng.trader.model.vo.TraderContactVo">
        SELECT
        tc.TRADER_CONTACT_ID,
        tc.TRADER_ID,
        tc.TRADER_TYPE,
        tc.IS_ENABLE,
        tc.SEX,
        tc.NAME,
        tc.DEPARTMENT,
        tc.POSITION,
        tc.TELEPHONE,
        tc.FAX,
        tc.MOBILE,
        tc.MOBILE2,
        tc.EMAIL,
        tc.QQ,
        tc.WEIXIN,
        tc.IS_ON_JOB,
        tc.IS_DEFAULT,
        tc.BIRTHDAY,
        tc.IS_MARRIED,
        tc.HAVE_CHILDREN,
        tc.EDUCATION,
        tc.`CHARACTER`,
        tc.COMMENTS,
        tc.ADD_TIME,
        tc.CREATOR,
        tc.MOD_TIME,
        tc.UPDATER,
        COALESCE(wa.IS_VEDENG_JX,wa1.IS_VEDENG_JX,- 1) AS IS_VEDENG_JX,
        COALESCE(wa.IS_VEDENG_MEMBER,wa1.IS_VEDENG_MEMBER,- 1) AS IS_VEDENG_MEMBER
        FROM
        T_TRADER_CONTACT tc
        LEFT JOIN T_WEB_ACCOUNT wa ON tc.MOBILE = wa.MOBILE
        LEFT JOIN T_WEB_ACCOUNT wa1 ON tc.MOBILE2 = wa1.MOBILE
        WHERE
        1 = 1
        and tc.TRADER_ID = #{traderId,jdbcType=INTEGER}

        <if test="traderType != null">
            and tc.TRADER_TYPE = #{traderType,jdbcType=INTEGER}
        </if>
        <if test="isEnable != null">
            and tc.IS_ENABLE = #{isEnable,jdbcType=BIT}
        </if>
        <if test="name != null and name != '' " >
            and (tc.NAME like CONCAT('%',#{name},'%' ) or tc.MOBILE like CONCAT('%',#{name},'%' ) or tc.MOBILE2 like CONCAT('%',#{name},'%' ))
        </if>
        GROUP BY
        tc.TRADER_CONTACT_ID
    </select>
    
  <update id="updatePosition">
      update T_TRADER_CONTACT set POSITION=#{position,jdbcType=VARCHAR} where TRADER_CONTACT_ID=#{traderContactId,jdbcType=INTEGER}
    </update>


    <select id="getBelongTrader"
            parameterType="com.vedeng.trader.dto.TraderInfoReqDto"
            resultType="com.vedeng.trader.dto.TraderInfoRespDto">
        select * from (
        select t.TRADER_ID,tc.TRADER_CUSTOMER_ID,t.BELONG_PLATFORM, t.TRADER_NAME,'share' as FROM_WHERE,FROM_UNIXTIME( t.ADD_TIME / 1000, '%Y-%m-%d %H:%i:%S' ) as ADD_TIME ,tlfe.TRADER_LEVEL
        from T_R_SALES_J_TRADER a
        left join T_USER u on a.SALE_USER_ID = u.USER_ID
        left join T_TRADER_CONTACT c on c.TRADER_ID = a.TRADER_ID
        left join T_TRADER t on t.TRADER_ID = c.TRADER_ID
        left join T_TRADER_CUSTOMER tc on tc.TRADER_ID = t.TRADER_ID
        left join DWH_TRADER_LIST_FILTER_ERP tlfe ON tlfe.TRADER_CUSTOMER_ID=tc.TRADER_CUSTOMER_ID
        where a.IS_DELETED = 0
<!--        and u.NUMBER = #{number,jdbcType=VARCHAR}-->
        and u.USER_ID = #{userId,jdbcType=INTEGER}
        and u.IS_DISABLED = 0
        and c.IS_ENABLE = 1
        <!--          and t.IS_ENABLE = 1-->
        <!--          and tc.IS_ENABLE = 1-->
        and ( c.MOBILE in
        <foreach collection="phoneList" item="phone" separator="," close=")" open="(">
            #{phone,jdbcType=VARCHAR}
        </foreach>
        or c.TELEPHONE in
        <foreach collection="phoneList" item="phone" separator="," close=")" open="(">
            #{phone,jdbcType=VARCHAR}
        </foreach>
        or c.MOBILE2 in
        <foreach collection="phoneList" item="phone" separator="," close=")" open="(">
            #{phone,jdbcType=VARCHAR}
        </foreach>
        )
        union all
        select t.TRADER_ID,tc.TRADER_CUSTOMER_ID,t.BELONG_PLATFORM, t.TRADER_NAME,'belong' as FROM_WHERE,FROM_UNIXTIME( t.ADD_TIME / 1000, '%Y-%m-%d %H:%i:%S' ) as ADD_TIME,tlfe.TRADER_LEVEL
        from T_R_TRADER_J_USER a
        left join T_USER u on a.USER_ID = u.USER_ID
        left join T_TRADER t on t.TRADER_ID = a.TRADER_ID
        left join T_TRADER_CONTACT c on c.TRADER_ID = a.TRADER_ID
        left join T_TRADER_CUSTOMER tc on tc.TRADER_ID = t.TRADER_ID
        left join DWH_TRADER_LIST_FILTER_ERP tlfe ON tlfe.TRADER_CUSTOMER_ID=tc.TRADER_CUSTOMER_ID
        where a.TRADER_TYPE = 1
<!--        and u.NUMBER = #{number,jdbcType=VARCHAR}-->
        and u.USER_ID = #{userId,jdbcType=INTEGER}
        and u.IS_DISABLED = 0
        and c.IS_ENABLE = 1
        <!--          and t.IS_ENABLE = 1-->
        <!--           and tc.IS_ENABLE = 1-->
        and ( c.MOBILE in
        <foreach collection="phoneList" item="phone" separator="," close=")" open="(">
            #{phone,jdbcType=VARCHAR}
        </foreach>
        or c.TELEPHONE in
        <foreach collection="phoneList" item="phone" separator="," close=")" open="(">
            #{phone,jdbcType=VARCHAR}
        </foreach>
        or c.MOBILE2 in
        <foreach collection="phoneList" item="phone" separator="," close=")" open="(">
            #{phone,jdbcType=VARCHAR}
        </foreach>
        )
        ) n group by n.TRADER_ID limit 100

    </select>
</mapper>
