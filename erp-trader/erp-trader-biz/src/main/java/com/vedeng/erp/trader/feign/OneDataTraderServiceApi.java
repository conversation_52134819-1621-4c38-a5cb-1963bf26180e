package com.vedeng.erp.trader.feign;

import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.feign.annotations.FeignApi;
import com.vedeng.onedataapi.api.trader.TraderServiceApi;
import com.vedeng.onedataapi.api.trader.req.TraderListReqDto;
import com.vedeng.onedataapi.api.trader.res.TraderPageRes;
import feign.Headers;
import feign.RequestLine;

@FeignApi(serverName = "onedataapi")
public interface OneDataTraderServiceApi extends TraderServiceApi {

    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /distributor/list")
    @Override
    RestfulResult<TraderPageRes> listDistributorTrader(TraderListReqDto traderListReqDto);
}
