package com.vedeng.firstengage.model;

public class GoodsRegisrationExpiryDate {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS_REGISTRATION_EXPIRY_DATE.GOODS_REGISTRATION_EXPIRY_DATE_ID
     *
     * @mbggenerated Mon Nov 23 13:22:16 CST 2020
     */
    private Integer goodsRegistrationExpiryDateId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS_REGISTRATION_EXPIRY_DATE.REGISTRATION_NUMBER_ID
     *
     * @mbggenerated Mon Nov 23 13:22:16 CST 2020
     */
    private Integer registrationNumberId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS_REGISTRATION_EXPIRY_DATE.TYPE
     *
     * @mbggenerated Mon Nov 23 13:22:16 CST 2020
     */
    private Integer type;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS_REGISTRATION_EXPIRY_DATE.START_TIME
     *
     * @mbggenerated Mon Nov 23 13:22:16 CST 2020
     */
    private Long startTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS_REGISTRATION_EXPIRY_DATE.END_TIME
     *
     * @mbggenerated Mon Nov 23 13:22:16 CST 2020
     */
    private Long endTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS_REGISTRATION_EXPIRY_DATE.IS_DELETE
     *
     * @mbggenerated Mon Nov 23 13:22:16 CST 2020
     */
    private Integer isDelete;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS_REGISTRATION_EXPIRY_DATE.ADD_TIME
     *
     * @mbggenerated Mon Nov 23 13:22:16 CST 2020
     */
    private Long addTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS_REGISTRATION_EXPIRY_DATE.UPDATE_TIME
     *
     * @mbggenerated Mon Nov 23 13:22:16 CST 2020
     */
    private Long updateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS_REGISTRATION_EXPIRY_DATE.CREATOR
     *
     * @mbggenerated Mon Nov 23 13:22:16 CST 2020
     */
    private Integer creator;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS_REGISTRATION_EXPIRY_DATE.UPDATOR
     *
     * @mbggenerated Mon Nov 23 13:22:16 CST 2020
     */
    private Integer updator;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS_REGISTRATION_EXPIRY_DATE.GOODS_REGISTRATION_EXPIRY_DATE_ID
     *
     * @return the value of T_GOODS_REGISTRATION_EXPIRY_DATE.GOODS_REGISTRATION_EXPIRY_DATE_ID
     *
     * @mbggenerated Mon Nov 23 13:22:16 CST 2020
     */
    public Integer getGoodsRegistrationExpiryDateId() {
        return goodsRegistrationExpiryDateId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS_REGISTRATION_EXPIRY_DATE.GOODS_REGISTRATION_EXPIRY_DATE_ID
     *
     * @param goodsRegistrationExpiryDateId the value for T_GOODS_REGISTRATION_EXPIRY_DATE.GOODS_REGISTRATION_EXPIRY_DATE_ID
     *
     * @mbggenerated Mon Nov 23 13:22:16 CST 2020
     */
    public void setGoodsRegistrationExpiryDateId(Integer goodsRegistrationExpiryDateId) {
        this.goodsRegistrationExpiryDateId = goodsRegistrationExpiryDateId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS_REGISTRATION_EXPIRY_DATE.REGISTRATION_NUMBER_ID
     *
     * @return the value of T_GOODS_REGISTRATION_EXPIRY_DATE.REGISTRATION_NUMBER_ID
     *
     * @mbggenerated Mon Nov 23 13:22:16 CST 2020
     */
    public Integer getRegistrationNumberId() {
        return registrationNumberId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS_REGISTRATION_EXPIRY_DATE.REGISTRATION_NUMBER_ID
     *
     * @param registrationNumberId the value for T_GOODS_REGISTRATION_EXPIRY_DATE.REGISTRATION_NUMBER_ID
     *
     * @mbggenerated Mon Nov 23 13:22:16 CST 2020
     */
    public void setRegistrationNumberId(Integer registrationNumberId) {
        this.registrationNumberId = registrationNumberId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS_REGISTRATION_EXPIRY_DATE.TYPE
     *
     * @return the value of T_GOODS_REGISTRATION_EXPIRY_DATE.TYPE
     *
     * @mbggenerated Mon Nov 23 13:22:16 CST 2020
     */
    public Integer getType() {
        return type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS_REGISTRATION_EXPIRY_DATE.TYPE
     *
     * @param type the value for T_GOODS_REGISTRATION_EXPIRY_DATE.TYPE
     *
     * @mbggenerated Mon Nov 23 13:22:16 CST 2020
     */
    public void setType(Integer type) {
        this.type = type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS_REGISTRATION_EXPIRY_DATE.START_TIME
     *
     * @return the value of T_GOODS_REGISTRATION_EXPIRY_DATE.START_TIME
     *
     * @mbggenerated Mon Nov 23 13:22:16 CST 2020
     */
    public Long getStartTime() {
        return startTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS_REGISTRATION_EXPIRY_DATE.START_TIME
     *
     * @param startTime the value for T_GOODS_REGISTRATION_EXPIRY_DATE.START_TIME
     *
     * @mbggenerated Mon Nov 23 13:22:16 CST 2020
     */
    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS_REGISTRATION_EXPIRY_DATE.END_TIME
     *
     * @return the value of T_GOODS_REGISTRATION_EXPIRY_DATE.END_TIME
     *
     * @mbggenerated Mon Nov 23 13:22:16 CST 2020
     */
    public Long getEndTime() {
        return endTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS_REGISTRATION_EXPIRY_DATE.END_TIME
     *
     * @param endTime the value for T_GOODS_REGISTRATION_EXPIRY_DATE.END_TIME
     *
     * @mbggenerated Mon Nov 23 13:22:16 CST 2020
     */
    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS_REGISTRATION_EXPIRY_DATE.IS_DELETE
     *
     * @return the value of T_GOODS_REGISTRATION_EXPIRY_DATE.IS_DELETE
     *
     * @mbggenerated Mon Nov 23 13:22:16 CST 2020
     */
    public Integer getIsDelete() {
        return isDelete;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS_REGISTRATION_EXPIRY_DATE.IS_DELETE
     *
     * @param isDelete the value for T_GOODS_REGISTRATION_EXPIRY_DATE.IS_DELETE
     *
     * @mbggenerated Mon Nov 23 13:22:16 CST 2020
     */
    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS_REGISTRATION_EXPIRY_DATE.ADD_TIME
     *
     * @return the value of T_GOODS_REGISTRATION_EXPIRY_DATE.ADD_TIME
     *
     * @mbggenerated Mon Nov 23 13:22:16 CST 2020
     */
    public Long getAddTime() {
        return addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS_REGISTRATION_EXPIRY_DATE.ADD_TIME
     *
     * @param addTime the value for T_GOODS_REGISTRATION_EXPIRY_DATE.ADD_TIME
     *
     * @mbggenerated Mon Nov 23 13:22:16 CST 2020
     */
    public void setAddTime(Long addTime) {
        this.addTime = addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS_REGISTRATION_EXPIRY_DATE.UPDATE_TIME
     *
     * @return the value of T_GOODS_REGISTRATION_EXPIRY_DATE.UPDATE_TIME
     *
     * @mbggenerated Mon Nov 23 13:22:16 CST 2020
     */
    public Long getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS_REGISTRATION_EXPIRY_DATE.UPDATE_TIME
     *
     * @param updateTime the value for T_GOODS_REGISTRATION_EXPIRY_DATE.UPDATE_TIME
     *
     * @mbggenerated Mon Nov 23 13:22:16 CST 2020
     */
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS_REGISTRATION_EXPIRY_DATE.CREATOR
     *
     * @return the value of T_GOODS_REGISTRATION_EXPIRY_DATE.CREATOR
     *
     * @mbggenerated Mon Nov 23 13:22:16 CST 2020
     */
    public Integer getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS_REGISTRATION_EXPIRY_DATE.CREATOR
     *
     * @param creator the value for T_GOODS_REGISTRATION_EXPIRY_DATE.CREATOR
     *
     * @mbggenerated Mon Nov 23 13:22:16 CST 2020
     */
    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS_REGISTRATION_EXPIRY_DATE.UPDATOR
     *
     * @return the value of T_GOODS_REGISTRATION_EXPIRY_DATE.UPDATOR
     *
     * @mbggenerated Mon Nov 23 13:22:16 CST 2020
     */
    public Integer getUpdator() {
        return updator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS_REGISTRATION_EXPIRY_DATE.UPDATOR
     *
     * @param updator the value for T_GOODS_REGISTRATION_EXPIRY_DATE.UPDATOR
     *
     * @mbggenerated Mon Nov 23 13:22:16 CST 2020
     */
    public void setUpdator(Integer updator) {
        this.updator = updator;
    }
}