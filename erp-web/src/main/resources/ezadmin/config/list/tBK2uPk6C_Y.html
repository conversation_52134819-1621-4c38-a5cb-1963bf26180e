<!doctype html>
<html lang="en"> 
 <head> 
  <meta charset="UTF-8"> 
  <title>售后单更改银行账号</title> 
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet"> 
 </head> 
 <body id="tBK2uPk6C_Y" datasource="erp-datasourcetarget" fixednumber="2" append_column_url="" append_row_url="" empty_show="" firstcol="numbers">
  <div id="appendHead"></div> 
  <div class="layui-fluid"> 
   <div class="layui-tab"> 
    <ul class="layui-tab-title" id="tab"> 
    </ul> 
   </div> 
   <form class="layui-form" id="search">
    <div class=" layui-inline "><label class="layui-form-label">归属销售</label>
     <div class="layui-input-inline">
      <xm class=" layui-input list-search-item " type="select" name="CURRENT_USER_ID" placeholder="" style="" alias="sd" jdbctype="" data="${session.EZ_SESSION_MY_USER_MAP_KEY}" datatype="JSON" oper="IN"></xm> 
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">售后单号</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="19" name="AFTER_SALES_NO" placeholder="请输入" style="" alias="a" jdbctype="" data="" datatype="" oper="LIKE"></object> 
     </div>
    </div>
   </form> 
   <hr class="layui-border-blue"> 
   <div class="btn-group   bd-highlight" id="tableButton"> 
   </div> 
   <table id="table" class="layui-table" style=" width:100%"> 
    <thead> 
     <tr id="column">
      <th type="rowbutton" id="rowbutton"><button item_name="修改" name="修改" url="/ezadmin/form/form-ij8s2gix-1U?ID=${ID}" opentype="MODEL" windowname="" style type="single">修改</button></th>
      <th item_name="ID" name="ID" body="td-text" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">ID</th>
      <th item_name="PAYEE" name="PAYEE" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">收款方</th>
      <th item_name="AFTER_SALES_NO" name="AFTER_SALES_NO" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">售后单号</th>
      <th item_name="TRADER_SUBJECT" name="TRADER_SUBJECT" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;无&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;对公&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;对私&quot;,&quot;K&quot;:&quot;2&quot;}]" style="" head="18">交易主体</th>
      <th item_name="REFUND" name="REFUND" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;无&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;退到客户余额&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;退给客户&quot;,&quot;K&quot;:&quot;2&quot;}]" style="" head="18">款项退还</th>
      <th item_name="BANK" name="BANK" body="td-text" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">开户银行</th>
      <th item_name="BANK_ACCOUNT" name="BANK_ACCOUNT" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">银行账号</th>
      <th item_name="BANK_CODE" name="BANK_CODE" body="td-text" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">开户行支付联行号</th>
      <th item_name="TRADER_MODE" name="TRADER_MODE" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="KVSQLCACHE" data="select SYS_OPTION_DEFINITION_ID K,TITLE V
from T_SYS_OPTION_DEFINITION WHERE PARENT_ID=519" style="" head="18">交易方式</th>
     </tr> 
    </thead> 
    <tbody> 
     <tr> 
      <td colspan="100"> express: <pre id="express" class="layui-code" orderby="order by a.AFTER_SALES_ID DESC" groupby="">StringBuilder sql=new StringBuilder("
SELECT
        a.AFTER_SALES_ID ID,
	a.AFTER_SALES_NO,
	a.ATFER_SALES_STATUS,
       b.BANK,b.BANK_ACCOUNT,b.BANK_CODE,b.PAYEE,b.REFUND,b.TRADER_SUBJECT,TRADER_MODE
FROM
	T_AFTER_SALES a
	LEFT JOIN T_AFTER_SALES_DETAIL b ON a.AFTER_SALES_ID = b.AFTER_SALES_ID
	

WHERE
	a.TYPE   IN ( 543, 539 )
	AND a.COMPANY_ID = 1 
	  AND a.ATFER_SALES_STATUS  in (0,1)
");
 
list=search(sql);
return list;</pre> count: <pre id="count" class="layui-code"></pre></td> 
     </tr> 
    </tbody> 
   </table> 
  </div> 
  <div id="appendFoot"></div> 
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script> 
  <script>
    layui.use(function () {

    })
</script> 
 </body>
</html>