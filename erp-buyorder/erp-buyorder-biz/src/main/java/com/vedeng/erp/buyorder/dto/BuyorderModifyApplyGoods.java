package com.vedeng.erp.buyorder.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 销售订单修改申请产品
 * @TableName T_BUYORDER_MODIFY_APPLY_GOODS
 */
public class BuyorderModifyApplyGoods implements Serializable {
    /**
     * 
     */
    private Integer buyorderModifyApplyGoodsId;

    /**
     * 
     */
    private Integer buyorderModifyApplyId;

    /**
     * 采购订单产品ID
     */
    private Integer buyorderGoodsId;

    /**
     * 采购备注
     */
    private String insideComments;

    /**
     * 原采购备注
     */
    private String oldInsideComments;

    /**
     * 原采购预计发货时间
     */
    private Long oldSendGoodsTime;

    /**
     * 采购预计发货时间
     */
    private Long sendGoodsTime;

    /**
     * 原采购预计收货时间
     */
    private Long oldReceiveGoodsTime;

    /**
     * 采购预计收货时间
     */
    private Long receiveGoodsTime;

    /**
     * 原单价
     */
    private BigDecimal oldPrice;

    /**
     * 申请修改后的单价
     */
    private BigDecimal price;

    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    public Integer getBuyorderModifyApplyGoodsId() {
        return buyorderModifyApplyGoodsId;
    }

    /**
     * 
     */
    public void setBuyorderModifyApplyGoodsId(Integer buyorderModifyApplyGoodsId) {
        this.buyorderModifyApplyGoodsId = buyorderModifyApplyGoodsId;
    }

    /**
     * 
     */
    public Integer getBuyorderModifyApplyId() {
        return buyorderModifyApplyId;
    }

    /**
     * 
     */
    public void setBuyorderModifyApplyId(Integer buyorderModifyApplyId) {
        this.buyorderModifyApplyId = buyorderModifyApplyId;
    }

    /**
     * 采购订单产品ID
     */
    public Integer getBuyorderGoodsId() {
        return buyorderGoodsId;
    }

    /**
     * 采购订单产品ID
     */
    public void setBuyorderGoodsId(Integer buyorderGoodsId) {
        this.buyorderGoodsId = buyorderGoodsId;
    }

    /**
     * 采购备注
     */
    public String getInsideComments() {
        return insideComments;
    }

    /**
     * 采购备注
     */
    public void setInsideComments(String insideComments) {
        this.insideComments = insideComments;
    }

    /**
     * 原采购备注
     */
    public String getOldInsideComments() {
        return oldInsideComments;
    }

    /**
     * 原采购备注
     */
    public void setOldInsideComments(String oldInsideComments) {
        this.oldInsideComments = oldInsideComments;
    }

    /**
     * 原采购预计发货时间
     */
    public Long getOldSendGoodsTime() {
        return oldSendGoodsTime;
    }

    /**
     * 原采购预计发货时间
     */
    public void setOldSendGoodsTime(Long oldSendGoodsTime) {
        this.oldSendGoodsTime = oldSendGoodsTime;
    }

    /**
     * 采购预计发货时间
     */
    public Long getSendGoodsTime() {
        return sendGoodsTime;
    }

    /**
     * 采购预计发货时间
     */
    public void setSendGoodsTime(Long sendGoodsTime) {
        this.sendGoodsTime = sendGoodsTime;
    }

    /**
     * 原采购预计收货时间
     */
    public Long getOldReceiveGoodsTime() {
        return oldReceiveGoodsTime;
    }

    /**
     * 原采购预计收货时间
     */
    public void setOldReceiveGoodsTime(Long oldReceiveGoodsTime) {
        this.oldReceiveGoodsTime = oldReceiveGoodsTime;
    }

    /**
     * 采购预计收货时间
     */
    public Long getReceiveGoodsTime() {
        return receiveGoodsTime;
    }

    /**
     * 采购预计收货时间
     */
    public void setReceiveGoodsTime(Long receiveGoodsTime) {
        this.receiveGoodsTime = receiveGoodsTime;
    }

    /**
     * 原单价
     */
    public BigDecimal getOldPrice() {
        return oldPrice;
    }

    /**
     * 原单价
     */
    public void setOldPrice(BigDecimal oldPrice) {
        this.oldPrice = oldPrice;
    }

    /**
     * 申请修改后的单价
     */
    public BigDecimal getPrice() {
        return price;
    }

    /**
     * 申请修改后的单价
     */
    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        BuyorderModifyApplyGoods other = (BuyorderModifyApplyGoods) that;
        return (this.getBuyorderModifyApplyGoodsId() == null ? other.getBuyorderModifyApplyGoodsId() == null : this.getBuyorderModifyApplyGoodsId().equals(other.getBuyorderModifyApplyGoodsId()))
            && (this.getBuyorderModifyApplyId() == null ? other.getBuyorderModifyApplyId() == null : this.getBuyorderModifyApplyId().equals(other.getBuyorderModifyApplyId()))
            && (this.getBuyorderGoodsId() == null ? other.getBuyorderGoodsId() == null : this.getBuyorderGoodsId().equals(other.getBuyorderGoodsId()))
            && (this.getInsideComments() == null ? other.getInsideComments() == null : this.getInsideComments().equals(other.getInsideComments()))
            && (this.getOldInsideComments() == null ? other.getOldInsideComments() == null : this.getOldInsideComments().equals(other.getOldInsideComments()))
            && (this.getOldSendGoodsTime() == null ? other.getOldSendGoodsTime() == null : this.getOldSendGoodsTime().equals(other.getOldSendGoodsTime()))
            && (this.getSendGoodsTime() == null ? other.getSendGoodsTime() == null : this.getSendGoodsTime().equals(other.getSendGoodsTime()))
            && (this.getOldReceiveGoodsTime() == null ? other.getOldReceiveGoodsTime() == null : this.getOldReceiveGoodsTime().equals(other.getOldReceiveGoodsTime()))
            && (this.getReceiveGoodsTime() == null ? other.getReceiveGoodsTime() == null : this.getReceiveGoodsTime().equals(other.getReceiveGoodsTime()))
            && (this.getOldPrice() == null ? other.getOldPrice() == null : this.getOldPrice().equals(other.getOldPrice()))
            && (this.getPrice() == null ? other.getPrice() == null : this.getPrice().equals(other.getPrice()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getBuyorderModifyApplyGoodsId() == null) ? 0 : getBuyorderModifyApplyGoodsId().hashCode());
        result = prime * result + ((getBuyorderModifyApplyId() == null) ? 0 : getBuyorderModifyApplyId().hashCode());
        result = prime * result + ((getBuyorderGoodsId() == null) ? 0 : getBuyorderGoodsId().hashCode());
        result = prime * result + ((getInsideComments() == null) ? 0 : getInsideComments().hashCode());
        result = prime * result + ((getOldInsideComments() == null) ? 0 : getOldInsideComments().hashCode());
        result = prime * result + ((getOldSendGoodsTime() == null) ? 0 : getOldSendGoodsTime().hashCode());
        result = prime * result + ((getSendGoodsTime() == null) ? 0 : getSendGoodsTime().hashCode());
        result = prime * result + ((getOldReceiveGoodsTime() == null) ? 0 : getOldReceiveGoodsTime().hashCode());
        result = prime * result + ((getReceiveGoodsTime() == null) ? 0 : getReceiveGoodsTime().hashCode());
        result = prime * result + ((getOldPrice() == null) ? 0 : getOldPrice().hashCode());
        result = prime * result + ((getPrice() == null) ? 0 : getPrice().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", buyorderModifyApplyGoodsId=").append(buyorderModifyApplyGoodsId);
        sb.append(", buyorderModifyApplyId=").append(buyorderModifyApplyId);
        sb.append(", buyorderGoodsId=").append(buyorderGoodsId);
        sb.append(", insideComments=").append(insideComments);
        sb.append(", oldInsideComments=").append(oldInsideComments);
        sb.append(", oldSendGoodsTime=").append(oldSendGoodsTime);
        sb.append(", sendGoodsTime=").append(sendGoodsTime);
        sb.append(", oldReceiveGoodsTime=").append(oldReceiveGoodsTime);
        sb.append(", receiveGoodsTime=").append(receiveGoodsTime);
        sb.append(", oldPrice=").append(oldPrice);
        sb.append(", price=").append(price);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}