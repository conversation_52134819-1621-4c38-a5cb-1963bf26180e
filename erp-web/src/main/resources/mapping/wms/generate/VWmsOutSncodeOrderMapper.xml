<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wms.dao.generate.VWmsOutSncodeOrderMapper">
  <resultMap id="BaseResultMap" type="com.wms.model.ddi.VWmsOutSncodeOrderExtDto">
    <id column="OUT_SNCODE_ORDER_ID" jdbcType="INTEGER" property="outSncodeOrderId" />
    <result column="ORDER_ID" jdbcType="INTEGER" property="orderId" />
    <result column="ORDER_GOODS_ID" jdbcType="INTEGER" property="orderGoodsId" />
    <result column="OPERATE_TYPE" jdbcType="TINYINT" property="operateType" />
    <result column="WMS_ORDER_NO" jdbcType="VARCHAR" property="wmsOrderNo" />
    <result column="SKU" jdbcType="VARCHAR" property="sku" />
    <result column="SKU_ID" jdbcType="INTEGER" property="skuId" />
    <result column="SN_CODE" jdbcType="VARCHAR" property="snCode" />
    <result column="SERIAL_NO" jdbcType="VARCHAR" property="serialNo" />
    <result column="OUT_TIME" jdbcType="TIMESTAMP" property="outTime" />
    <result column="PRODUCT_DATE" jdbcType="TIMESTAMP" property="productDate" />
    <result column="EXPIRATION_DATE" jdbcType="TIMESTAMP" property="expirationDate" />
    <result column="BATCH_NUMBER" jdbcType="VARCHAR" property="batchNumber" />
    <result column="VEDENG_BATCH_NUMER" jdbcType="VARCHAR" property="vedengBatchNumer" />
    <result column="COMMENTS" jdbcType="VARCHAR" property="comments" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MODE_TIME" jdbcType="TIMESTAMP" property="modeTime" />
    <result column="IS_DELETE" jdbcType="BOOLEAN" property="isDelete" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
  </resultMap>
  <sql id="Base_Column_List">
    OUT_SNCODE_ORDER_ID, ORDER_ID, ORDER_GOODS_ID, OPERATE_TYPE, WMS_ORDER_NO, SKU, SKU_ID, 
    SN_CODE, SERIAL_NO, OUT_TIME, PRODUCT_DATE, EXPIRATION_DATE, BATCH_NUMBER, VEDENG_BATCH_NUMER, 
    COMMENTS, ADD_TIME, MODE_TIME, IS_DELETE, CREATOR, UPDATER
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from V_WMS_OUT_SNCODE_ORDER
    where OUT_SNCODE_ORDER_ID = #{outSncodeOrderId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from V_WMS_OUT_SNCODE_ORDER
    where OUT_SNCODE_ORDER_ID = #{outSncodeOrderId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="OUT_SNCODE_ORDER_ID" keyProperty="outSncodeOrderId" parameterType="com.wms.model.ddi.VWmsOutSncodeOrderExtDto" useGeneratedKeys="true">
    insert into V_WMS_OUT_SNCODE_ORDER (ORDER_ID, ORDER_GOODS_ID, OPERATE_TYPE, 
      WMS_ORDER_NO, SKU, SKU_ID, 
      SN_CODE, SERIAL_NO, OUT_TIME, 
      PRODUCT_DATE, EXPIRATION_DATE, BATCH_NUMBER, 
      VEDENG_BATCH_NUMER, COMMENTS, ADD_TIME, 
      MODE_TIME, IS_DELETE, CREATOR, 
      UPDATER)
    values (#{orderId,jdbcType=INTEGER}, #{orderGoodsId,jdbcType=INTEGER}, #{operateType,jdbcType=TINYINT}, 
      #{wmsOrderNo,jdbcType=VARCHAR}, #{sku,jdbcType=VARCHAR}, #{skuId,jdbcType=INTEGER}, 
      #{snCode,jdbcType=VARCHAR}, #{serialNo,jdbcType=VARCHAR}, #{outTime,jdbcType=TIMESTAMP}, 
      #{productDate,jdbcType=TIMESTAMP}, #{expirationDate,jdbcType=TIMESTAMP}, #{batchNumber,jdbcType=VARCHAR}, 
      #{vedengBatchNumer,jdbcType=VARCHAR}, #{comments,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, 
      #{modeTime,jdbcType=TIMESTAMP}, #{isDelete,jdbcType=BOOLEAN}, #{creator,jdbcType=INTEGER}, 
      #{updater,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="OUT_SNCODE_ORDER_ID" keyProperty="outSncodeOrderId" parameterType="com.wms.model.ddi.VWmsOutSncodeOrderExtDto" useGeneratedKeys="true">
    insert into V_WMS_OUT_SNCODE_ORDER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        ORDER_ID,
      </if>
      <if test="orderGoodsId != null">
        ORDER_GOODS_ID,
      </if>
      <if test="operateType != null">
        OPERATE_TYPE,
      </if>
      <if test="wmsOrderNo != null">
        WMS_ORDER_NO,
      </if>
      <if test="sku != null">
        SKU,
      </if>
      <if test="skuId != null">
        SKU_ID,
      </if>
      <if test="snCode != null">
        SN_CODE,
      </if>
      <if test="serialNo != null">
        SERIAL_NO,
      </if>
      <if test="outTime != null">
        OUT_TIME,
      </if>
      <if test="productDate != null">
        PRODUCT_DATE,
      </if>
      <if test="expirationDate != null">
        EXPIRATION_DATE,
      </if>
      <if test="batchNumber != null">
        BATCH_NUMBER,
      </if>
      <if test="vedengBatchNumer != null">
        VEDENG_BATCH_NUMER,
      </if>
      <if test="comments != null">
        COMMENTS,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modeTime != null">
        MODE_TIME,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=INTEGER},
      </if>
      <if test="orderGoodsId != null">
        #{orderGoodsId,jdbcType=INTEGER},
      </if>
      <if test="operateType != null">
        #{operateType,jdbcType=TINYINT},
      </if>
      <if test="wmsOrderNo != null">
        #{wmsOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="skuId != null">
        #{skuId,jdbcType=INTEGER},
      </if>
      <if test="snCode != null">
        #{snCode,jdbcType=VARCHAR},
      </if>
      <if test="serialNo != null">
        #{serialNo,jdbcType=VARCHAR},
      </if>
      <if test="outTime != null">
        #{outTime,jdbcType=TIMESTAMP},
      </if>
      <if test="productDate != null">
        #{productDate,jdbcType=TIMESTAMP},
      </if>
      <if test="expirationDate != null">
        #{expirationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="batchNumber != null">
        #{batchNumber,jdbcType=VARCHAR},
      </if>
      <if test="vedengBatchNumer != null">
        #{vedengBatchNumer,jdbcType=VARCHAR},
      </if>
      <if test="comments != null">
        #{comments,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modeTime != null">
        #{modeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wms.model.ddi.VWmsOutSncodeOrderExtDto">
    update V_WMS_OUT_SNCODE_ORDER
    <set>
      <if test="orderId != null">
        ORDER_ID = #{orderId,jdbcType=INTEGER},
      </if>
      <if test="orderGoodsId != null">
        ORDER_GOODS_ID = #{orderGoodsId,jdbcType=INTEGER},
      </if>
      <if test="operateType != null">
        OPERATE_TYPE = #{operateType,jdbcType=TINYINT},
      </if>
      <if test="wmsOrderNo != null">
        WMS_ORDER_NO = #{wmsOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="sku != null">
        SKU = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="skuId != null">
        SKU_ID = #{skuId,jdbcType=INTEGER},
      </if>
      <if test="snCode != null">
        SN_CODE = #{snCode,jdbcType=VARCHAR},
      </if>
      <if test="serialNo != null">
        SERIAL_NO = #{serialNo,jdbcType=VARCHAR},
      </if>
      <if test="outTime != null">
        OUT_TIME = #{outTime,jdbcType=TIMESTAMP},
      </if>
      <if test="productDate != null">
        PRODUCT_DATE = #{productDate,jdbcType=TIMESTAMP},
      </if>
      <if test="expirationDate != null">
        EXPIRATION_DATE = #{expirationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="batchNumber != null">
        BATCH_NUMBER = #{batchNumber,jdbcType=VARCHAR},
      </if>
      <if test="vedengBatchNumer != null">
        VEDENG_BATCH_NUMER = #{vedengBatchNumer,jdbcType=VARCHAR},
      </if>
      <if test="comments != null">
        COMMENTS = #{comments,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modeTime != null">
        MODE_TIME = #{modeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
    </set>
    where OUT_SNCODE_ORDER_ID = #{outSncodeOrderId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wms.model.ddi.VWmsOutSncodeOrderExtDto">
    update V_WMS_OUT_SNCODE_ORDER
    set ORDER_ID = #{orderId,jdbcType=INTEGER},
      ORDER_GOODS_ID = #{orderGoodsId,jdbcType=INTEGER},
      OPERATE_TYPE = #{operateType,jdbcType=TINYINT},
      WMS_ORDER_NO = #{wmsOrderNo,jdbcType=VARCHAR},
      SKU = #{sku,jdbcType=VARCHAR},
      SKU_ID = #{skuId,jdbcType=INTEGER},
      SN_CODE = #{snCode,jdbcType=VARCHAR},
      SERIAL_NO = #{serialNo,jdbcType=VARCHAR},
      OUT_TIME = #{outTime,jdbcType=TIMESTAMP},
      PRODUCT_DATE = #{productDate,jdbcType=TIMESTAMP},
      EXPIRATION_DATE = #{expirationDate,jdbcType=TIMESTAMP},
      BATCH_NUMBER = #{batchNumber,jdbcType=VARCHAR},
      VEDENG_BATCH_NUMER = #{vedengBatchNumer,jdbcType=VARCHAR},
      COMMENTS = #{comments,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MODE_TIME = #{modeTime,jdbcType=TIMESTAMP},
      IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER}
    where OUT_SNCODE_ORDER_ID = #{outSncodeOrderId,jdbcType=INTEGER}
  </update>
</mapper>