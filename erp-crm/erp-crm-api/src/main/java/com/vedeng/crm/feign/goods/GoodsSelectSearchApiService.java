package com.vedeng.crm.feign.goods;

import com.vedeng.common.core.base.R;
import com.vedeng.common.feign.annotations.FeignApi;
import com.vedeng.crm.feign.goods.dto.GoodsSearchReqDto;
import feign.Headers;
import feign.RequestLine;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/9/25
 */
@FeignApi(serverName = "erpServer")//serverUrl = "http://***********/"
public interface GoodsSelectSearchApiService {

    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /goodsInfo/searchForApi.do")
    R searchForApi(@RequestBody GoodsSearchReqDto goodsSearchReqDto);

}


