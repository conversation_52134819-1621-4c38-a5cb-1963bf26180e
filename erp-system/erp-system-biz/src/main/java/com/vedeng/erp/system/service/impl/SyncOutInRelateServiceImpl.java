package com.vedeng.erp.system.service.impl;

import com.vedeng.erp.system.domain.entity.SyncOutInRelateEntity;
import com.vedeng.erp.system.mapper.SyncOutInRelateMapper;
import com.vedeng.erp.system.service.SyncOutInRelateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SyncOutInRelateServiceImpl implements SyncOutInRelateService {

    @Autowired
    private SyncOutInRelateMapper syncOutInRelateMapper;

    @Override
    public int deleteByPrimaryKey(Integer id) {
        return syncOutInRelateMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(SyncOutInRelateEntity record) {
        return syncOutInRelateMapper.insert(record);
    }

    @Override
    public int insertSelective(SyncOutInRelateEntity record) {
        return syncOutInRelateMapper.insertSelective(record);
    }

    @Override
    public SyncOutInRelateEntity selectByPrimaryKey(Integer id) {
        return syncOutInRelateMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(SyncOutInRelateEntity record) {
        return syncOutInRelateMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(SyncOutInRelateEntity record) {
        return syncOutInRelateMapper.updateByPrimaryKey(record);
    }

    @Override
    public List<SyncOutInRelateEntity> selectByProcessStatus(Integer processStatus) {
        return syncOutInRelateMapper.selectByProcessStatus(processStatus);
    }

    @Override
    public List<SyncOutInRelateEntity> selectByOutBusinessNo(String outBusinessNo) {
        return syncOutInRelateMapper.selectByOutBusinessNo(outBusinessNo);
    }

    @Override
    public List<SyncOutInRelateEntity> selectByInBusinessNo(String inBusinessNo) {
        return syncOutInRelateMapper.selectByInBusinessNo(inBusinessNo);
    }
} 