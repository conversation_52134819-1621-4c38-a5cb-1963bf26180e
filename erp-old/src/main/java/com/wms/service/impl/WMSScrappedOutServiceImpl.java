package com.wms.service.impl;

import com.alibaba.fastjson.JSON;
import com.vedeng.authorization.dao.OrganizationMapper;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.Organization;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.page.Page;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.OrderNoDict;
import com.vedeng.logistics.dao.WarehouseGoodsOperateLogMapper;
import com.vedeng.logistics.model.WarehouseGoodsOperateLog;
import com.vedeng.logistics.service.WarehouseStockService;
import com.vedeng.stock.api.stock.dto.StockInfoDto;
import com.vedeng.stock.api.stock.dto.WarehouseDto;
import com.vedeng.system.service.SysOptionDefinitionService;
import com.wms.constant.*;
import com.wms.dao.WmsLogicalOrdergoodsMapper;
import com.wms.dao.WmsOutputOrderGoodsMapper;
import com.wms.dao.WmsOutputOrderMapper;
import com.wms.dao.WmsScrappedOutMapper;
import com.wms.dto.PutSaleOrderDto;
import com.wms.dto.PutSaleOrderGoodsDto;
import com.wms.dto.StockCalculateDto;
import com.wms.dto.WmsResponse;
import com.wms.model.dto.AddScrappedOutDto;
import com.wms.model.dto.WMSScrappedOutQueryDto;
import com.wms.model.dto.WmsOutputOrderGoodsDto;
import com.wms.model.po.WMSScrappedOutOrder;
import com.wms.model.po.WmsLogicalOrdergoods;
import com.wms.model.po.WmsOutputOrder;
import com.wms.model.po.WmsOutputOrderGoods;
import com.wms.service.WMSInterfaceFactory;
import com.wms.service.WMSScrappedOutService;
import com.wms.service.WmsInterface;
import com.wms.service.stockcalculate.OutputOrderAuditPassCaculateImpl;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class WMSScrappedOutServiceImpl implements WMSScrappedOutService {
    private static final Logger logger = LoggerFactory.getLogger(WMSLendOutServiceImpl.class);

    @Autowired
    private WmsScrappedOutMapper scrappedOutMapper;

    @Autowired
    private WmsOutputOrderMapper outputOrderMapper;

    @Autowired
    private WmsOutputOrderGoodsMapper outputOrderGoodsMapper;

    @Resource
    private UserMapper userMapper;

    @Autowired
    private OrganizationMapper organizationMapper;

    @Autowired
    private WMSInterfaceFactory wmsInterfaceFactory;

    @Autowired
    private WmsLogicalOrdergoodsMapper  wmsLogicalOrdergoodsMapper;

    @Autowired
    private OutputOrderAuditPassCaculateImpl outputOrderAuditPassCaculateImpl;

    @Autowired
    private WarehouseStockService warehouseStockService;

    @Autowired
    private SysOptionDefinitionService sysOptionDefinitionService;

    @Resource
    private WarehouseGoodsOperateLogMapper warehouseGoodsOperateLogMapper;

    @Resource
    private OrderNoDict orderNoDict;
    /**
     * 查询商品报废出库单
     */
    @Override
    public List<WMSScrappedOutOrder> queryScrapedOutlistPage(WMSScrappedOutQueryDto scrappedOutQueryDto, Page page) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("scrappedOutQueryDto", scrappedOutQueryDto);
        map.put("page", page);
        return scrappedOutMapper.queryScrapedOutlistPage(map);
    }


    /**
     * 新增报废出库单
     * @param scrappedOutDto
     */
    @Transactional
    @Override
    public Integer addScrappedOutOrder(AddScrappedOutDto scrappedOutDto, User user) {

        try {

            WmsOutputOrder scrappedOutOrder = convertToScrappedOutBean(scrappedOutDto,user);
            outputOrderMapper.insertSelective(scrappedOutOrder);

            updateOrderNo(scrappedOutOrder);

            List<WmsOutputOrderGoods> scrappedOutOrderGoods = convertToScrappedOutGoodsBean(scrappedOutOrder,scrappedOutDto);
            outputOrderGoodsMapper.batchInsert(scrappedOutOrderGoods);

            return scrappedOutOrder.getId().intValue();

        }catch (Exception e){
            logger.error("WMSScrappedOutServiceImpl->addScrappedOutOrder error :",e);
        }
        return 0;
    }

    private void updateOrderNo(WmsOutputOrder scrappedOutOrder) {
        WmsOutputOrder updateOrder = new WmsOutputOrder();
        updateOrder.setId(scrappedOutOrder.getId());
        updateOrder.setOrderNo(orderNoDict.getOrderNum(scrappedOutOrder.getId().intValue(), OrderNoDict.SCRAP_OUT_TYPE));
        outputOrderMapper.updateByPrimaryKeySelective(updateOrder);
    }

    /**
     * 新增出库单的Bean
     * @param scrappedOutDto
     * @param user
     * @return
     */
    private WmsOutputOrder convertToScrappedOutBean(AddScrappedOutDto scrappedOutDto,User user) throws Exception {

        WmsOutputOrder wmsOutputOrder = new WmsOutputOrder();
        if(scrappedOutDto == null || scrappedOutDto.getScrapDealType() == null){
            throw new Exception("保存报废出库单处理方式为空");
        }
        if(scrappedOutDto.getScrapDealType().equals(SysOptionConstant.SCRAPE_DEAL_DESTORY)){
            wmsOutputOrder.setType(WmsOutputOrderTypeConstant.CRASH);
        }else{
            wmsOutputOrder.setType(WmsOutputOrderTypeConstant.RECEIVING);
        }
        wmsOutputOrder.setVerifyStatus(VerifyStatusEnum.Reviewing.getValue());
        wmsOutputOrder.setReturnStatus(ReturnStatusConstant.UN_RETURN);
        wmsOutputOrder.setOutStatus(OutputStatusConstant.UN_OUTPUT);

        wmsOutputOrder.setScrapDealType(scrappedOutDto.getScrapDealType());
        wmsOutputOrder.setScrapType(scrappedOutDto.getScrapType());
        wmsOutputOrder.setScrapLevel(scrappedOutDto.getScrapLevel());

        User user1 = userMapper.selectByPrimaryKey(scrappedOutDto.getUserId());
        wmsOutputOrder.setApplyer(user1.getUsername());
        wmsOutputOrder.setApplyerId(scrappedOutDto.getUserId());
        Organization organization = organizationMapper.selectByPrimaryKey(scrappedOutDto.getOrgId());
        wmsOutputOrder.setApplyerDepartment(organization.getOrgName());
        wmsOutputOrder.setApplyerDepartmentId(scrappedOutDto.getOrgId());
        wmsOutputOrder.setAppleOutDate(scrappedOutDto.getAppleOutDate());
        wmsOutputOrder.setRemark(scrappedOutDto.getRemark());

        wmsOutputOrder.setBelongDepartment(user.getOrgName());
        wmsOutputOrder.setCreator(user.getUsername());
        wmsOutputOrder.setUpdator(user.getUsername());

        String addTime = DateUtil.convertString(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss");
        wmsOutputOrder.setAddTime(addTime);
        wmsOutputOrder.setUpdateTime(addTime);
        return wmsOutputOrder;
    }

    /**
     * 新增出库单商品的Bean
     * @param scrappedOutOrder
     * @param scrappedOutDto
     * @return
     */
    private List<WmsOutputOrderGoods> convertToScrappedOutGoodsBean(WmsOutputOrder scrappedOutOrder, AddScrappedOutDto scrappedOutDto) {

        if(scrappedOutDto.getSkuNo() == null || scrappedOutDto.getSkuNo().length == 0){
            return null;
        }

        List<WmsOutputOrderGoods> outputOrderGoodsList = new ArrayList<>();

        for(int i = 0;i < scrappedOutDto.getSkuNo().length;i++){
            WmsOutputOrderGoods wmsOutputOrderGoods = new WmsOutputOrderGoods();
            outputOrderGoodsList.add(wmsOutputOrderGoods);

            wmsOutputOrderGoods.setWmsOutputOrderId(scrappedOutOrder.getId());
            wmsOutputOrderGoods.setSkuNo(scrappedOutDto.getSkuNo()[i]);
            wmsOutputOrderGoods.setOutStatus(OutputStatusConstant.UN_OUTPUT);
            wmsOutputOrderGoods.setOutputNum(scrappedOutDto.getOutputNum()[i].intValue());
            wmsOutputOrderGoods.setAddTime(scrappedOutOrder.getAddTime());
            wmsOutputOrderGoods.setUpdateTime(scrappedOutOrder.getAddTime());
        }

        return outputOrderGoodsList;
    }


    /**
     * 查询报废出库订单
     * @param scrappedOutId
     * @return
     */
    @Override
    public WMSScrappedOutOrder findScrappedOutById(Long scrappedOutId) {

        return scrappedOutMapper.selectById(scrappedOutId);
    }

    /**
     * 查询报废出库商品信息
     * @param scrappedOutOrderId
     * @return
     */
    @Override
    public List<WmsOutputOrderGoodsDto> queryOutputGoodsByScrappedOutId(Long scrappedOutOrderId) {

        return scrappedOutMapper.queryOutputGoodsByScrapedOutId(scrappedOutOrderId);
    }

    @Override
    public void addWmsLogicalOrderGodos(User user, Integer scrappedOutOrderId) {
        List<WmsLogicalOrdergoods> wmsLogicalOrdergoodsList = new ArrayList<>();
        WmsOutputOrder scrappedOutOrder = this.findScrappedOutById(Long.valueOf(scrappedOutOrderId));
        List<WmsOutputOrderGoodsDto> wmsOutputOrderGoods = this.queryOutputGoodsByScrappedOutId(Long.valueOf(scrappedOutOrderId));
        for (WmsOutputOrderGoods wmsOutputOrderGood : wmsOutputOrderGoods) {
            WmsLogicalOrdergoods insert = new WmsLogicalOrdergoods();
            insert.setNum(wmsOutputOrderGood.getOutputNum());
            insert.setRelatedId(wmsOutputOrderGood.getId().intValue());
            insert.setOccupyNum(wmsOutputOrderGood.getOutputNum());
            insert.setSku(wmsOutputOrderGood.getSkuNo());
            insert.setGoodsId(Integer.valueOf(wmsOutputOrderGood.getSkuNo().substring(1)));
            insert.setLogicalWarehouseId(LogicalEnum.BHG.getLogicalWarehouseId());
            insert.setOperateType(WmsLogicalOperateTypeEnum.SCRAPPED_OUT.getOperateTypeCode());
            insert.setCreator(user.getUserId());
            insert.setUpdater(user.getUserId());
            wmsLogicalOrdergoodsMapper.insertSelective(insert);
            wmsLogicalOrdergoodsList.add(insert);
        }
        try {
            sysnchStock(scrappedOutOrder,wmsLogicalOrdergoodsList);
        }catch (Exception e){
            logger.error("报废出库占用同步库存服务失败 单号:"+scrappedOutOrder.getOrderNo(),e);
        }
    }


    private void sysnchStock(WmsOutputOrder scrappedOutOrder, List<WmsLogicalOrdergoods> wmsLogicalOrdergoodsList) throws Exception {
        if(CollectionUtils.isEmpty(wmsLogicalOrdergoodsList)){
            return;
        }
        List<StockCalculateDto> stockCalculateList  = new ArrayList<>();
        wmsLogicalOrdergoodsList.stream().forEach( wmsLogicalOrdergoods -> {
            StockCalculateDto stockCalculateDto = new StockCalculateDto();
            if(wmsLogicalOrdergoods.getOccupyNum().equals(0)){
                return;
            }
            stockCalculateDto.setSku(wmsLogicalOrdergoods.getSku());
            stockCalculateDto.setOccupyNum(wmsLogicalOrdergoods.getOccupyNum());
            stockCalculateDto.setStockNum(0);
            stockCalculateDto.setLogicalWarehouseId(wmsLogicalOrdergoods.getLogicalWarehouseId());
            stockCalculateList.add(stockCalculateDto);
        });
        //库存策略计算
        StockInfoDto stockInfoDto  = new StockInfoDto();
        stockInfoDto.setRelatedNo(scrappedOutOrder.getOrderNo());
        List<WarehouseDto> warehouseDtos = outputOrderAuditPassCaculateImpl.calculateStockInfo(stockCalculateList);
        stockInfoDto.setWarehouseStockList(warehouseDtos);

        //更新库存服务
        warehouseStockService.updateStockInfo(stockInfoDto);
    }

    @Override
    public void putScrappedOutOrder(Integer scrappedOutOrderId) throws Exception {
        WMSScrappedOutOrder scrappedOutOrder = this.findScrappedOutById(Long.valueOf(scrappedOutOrderId));

        PutSaleOrderDto putSaleOrderDto = new PutSaleOrderDto();
        putSaleOrderDto.setDocNo(scrappedOutOrder.getOrderNo());
        if(scrappedOutOrder.getType().equals(WmsOutputOrderTypeConstant.CRASH)){
            putSaleOrderDto.setOrderType(WmsInterfaceOrderType.OUT_SCRAPPED);
        }else if(scrappedOutOrder.getType().equals(WmsOutputOrderTypeConstant.RECEIVING)){
            putSaleOrderDto.setOrderType(WmsInterfaceOrderType.OUT_RECEIVER);
        }else{
            throw new Exception("报废出库单业务类型为空 单号:"+scrappedOutOrder.getOrderNo());
        }

        putSaleOrderDto.setOrderTime(scrappedOutOrder.getAddTime());
        putSaleOrderDto.setExpectedShipmentTime1(scrappedOutOrder.getAppleOutDate());
        putSaleOrderDto.setSoReferenceA(scrappedOutOrder.getApplyerDepartment());
        putSaleOrderDto.setSoReferenceB(scrappedOutOrder.getApplyer());
        putSaleOrderDto.setConsigneeId("0");
        putSaleOrderDto.setHedi06(sysOptionDefinitionService.getSysOptionDefinitionById(scrappedOutOrder.getScrapDealType()).getTitle());
        putSaleOrderDto.setHedi07("A");
        putSaleOrderDto.setNotes(scrappedOutOrder.getRemark());

        List<PutSaleOrderGoodsDto> details = new ArrayList<>();
        List<WmsOutputOrderGoodsDto> wmsOutputOrderGoods = this.queryOutputGoodsByScrappedOutId(Long.valueOf(scrappedOutOrderId));
        for (WmsOutputOrderGoods wmsOutputOrderGood : wmsOutputOrderGoods) {
            PutSaleOrderGoodsDto putSaleOrderGoodsDto = new PutSaleOrderGoodsDto();
            putSaleOrderGoodsDto.setSku(wmsOutputOrderGood.getSkuNo());
            putSaleOrderGoodsDto.setQtyOrdered(wmsOutputOrderGood.getOutputNum());
            putSaleOrderGoodsDto.setLotAtt08(LogicalEnum.BHG.getLogicalWarehouseCode());
            putSaleOrderGoodsDto.setDedi07(wmsOutputOrderGood.getId().toString());
            details.add(putSaleOrderGoodsDto);
        }
        putSaleOrderDto.setDetails(details);

        try {
            logger.info("WMS报废出库单下传的 单号:{},请求:{}",scrappedOutOrder.getOrderNo(), JSON.toJSONString(putSaleOrderDto));
            WmsInterface putSaleOrderOutputInterface = wmsInterfaceFactory.getWmsInterface(WMSContant.PUT_ORIGINAL_SALESORDER);

            WmsResponse response = putSaleOrderOutputInterface.request(putSaleOrderDto);

            logger.info("WMS报废出库单下传的 单号:{},响应:{}",scrappedOutOrder.getOrderNo(),JSON.toJSONString(response));
        } catch (Exception e) {
            logger.error("下发报废出库单失败error 单号:"+scrappedOutOrder.getOrderNo(),e);
        }
    }

    @Override
    public WmsOutputOrder getScrappedOrderByNo(String scrappedNo) {

        return scrappedOutMapper.getScrappedOrderByNo(scrappedNo);
    }

    @Override
    public List<WarehouseGoodsOperateLog> getWlogList(Integer wmsOutputOrderId, Integer logOperateType) {
        return warehouseGoodsOperateLogMapper.getWmsOutputOrderLogListByOrderId(wmsOutputOrderId,logOperateType);
    }

}
