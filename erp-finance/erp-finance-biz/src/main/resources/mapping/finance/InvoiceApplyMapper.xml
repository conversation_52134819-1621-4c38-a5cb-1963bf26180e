<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.finance.mapper.InvoiceApplyMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.erp.finance.domain.entity.InvoiceApplyEntity">
        <!--@mbg.generated-->
        <!--@Table T_INVOICE_APPLY-->
        <id column="INVOICE_APPLY_ID" jdbcType="INTEGER" property="invoiceApplyId"/>
        <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId"/>
        <result column="INVOICE_PROPERTY" jdbcType="BOOLEAN" property="invoiceProperty"/>
        <result column="APPLY_METHOD" jdbcType="BOOLEAN" property="applyMethod"/>
        <result column="TYPE" jdbcType="INTEGER" property="type"/>
        <result column="RELATED_ID" jdbcType="INTEGER" property="relatedId"/>
        <result column="IS_AUTO" jdbcType="BOOLEAN" property="isAuto"/>
        <result column="IS_ADVANCE" jdbcType="BOOLEAN" property="isAdvance"/>
        <result column="ADVANCE_VALID_STATUS" jdbcType="BOOLEAN" property="advanceValidStatus"/>
        <result column="ADVANCE_VALID_USERID" jdbcType="INTEGER" property="advanceValidUserid"/>
        <result column="ADVANCE_VALID_TIME" jdbcType="BIGINT" property="advanceValidTime"/>
        <result column="ADVANCE_VALID_COMMENTS" jdbcType="VARCHAR" property="advanceValidComments"/>
        <result column="YY_VALID_STATUS" jdbcType="BOOLEAN" property="yyValidStatus"/>
        <result column="YY_VALID_USERID" jdbcType="INTEGER" property="yyValidUserid"/>
        <result column="YY_VALID_TIME" jdbcType="BIGINT" property="yyValidTime"/>
        <result column="YY_VALID_COMMENTS" jdbcType="VARCHAR" property="yyValidComments"/>
        <result column="VALID_STATUS" jdbcType="BOOLEAN" property="validStatus"/>
        <result column="VALID_USERID" jdbcType="INTEGER" property="validUserid"/>
        <result column="VALID_TIME" jdbcType="BIGINT" property="validTime"/>
        <result column="VALID_COMMENTS" jdbcType="VARCHAR" property="validComments"/>
        <result column="INVOICE_ST_STATUS" jdbcType="BOOLEAN" property="invoiceStStatus"/>
        <result column="INVOICE_PRINT_STATUS" jdbcType="BOOLEAN" property="invoicePrintStatus"/>
        <result column="COMMENTS" jdbcType="VARCHAR" property="comments"/>
        <result column="IS_SIGN" jdbcType="BOOLEAN" property="isSign"/>
        <result column="ADD_TIME" jdbcType="BIGINT" property="addTime"/>
        <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
        <result column="MOD_TIME" jdbcType="BIGINT" property="modTime"/>
        <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
        <result column="EXPRESS_ID" jdbcType="INTEGER" property="expressId"/>
        <result column="IS_OPENING" jdbcType="BOOLEAN" property="isOpening"/>
        <result column="REASONS" jdbcType="VARCHAR" property="reasons"/>
        <result column="WMS_ORDER_NO" jdbcType="VARCHAR" property="wmsOrderNo"/>
        <result column="ERP_ORDER_NO" jdbcType="VARCHAR" property="erpOrderNo"/>
        <result column="IS_SEND_WMS" jdbcType="BOOLEAN" property="isSendWms"/>
        <result column="SIGNER_ID" jdbcType="INTEGER" property="signerId"/>
        <result column="SIGNER_NAME" jdbcType="VARCHAR" property="signerName"/>
        <result column="URAGE" jdbcType="BOOLEAN" property="urage"/>
        <result column="INVOICE_INFO_TYPE" jdbcType="INTEGER" property="invoiceInfoType"/>
        <result column="ADVANCE_VALID_REASON" jdbcType="VARCHAR" property="advanceValidReason"/>
        <result column="INVOICE_MESSAGE" jdbcType="VARCHAR" property="invoiceMessage"/>
        <result column="CREATE_TYPE" jdbcType="VARCHAR" property="createType"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        INVOICE_APPLY_ID,
        COMPANY_ID,
        INVOICE_PROPERTY,
        APPLY_METHOD,
        `TYPE`,
        RELATED_ID,
        IS_AUTO,
        IS_ADVANCE,
        ADVANCE_VALID_STATUS,
        ADVANCE_VALID_USERID,
        ADVANCE_VALID_TIME,
        ADVANCE_VALID_COMMENTS,
        YY_VALID_STATUS,
        YY_VALID_USERID,
        YY_VALID_TIME,
        YY_VALID_COMMENTS,
        VALID_STATUS,
        VALID_USERID,
        VALID_TIME,
        VALID_COMMENTS,
        INVOICE_ST_STATUS,
        INVOICE_PRINT_STATUS,
        COMMENTS,
        IS_SIGN,
        ADD_TIME,
        CREATOR,
        MOD_TIME,
        UPDATER,
        EXPRESS_ID,
        IS_OPENING,
        REASONS,
        WMS_ORDER_NO,
        ERP_ORDER_NO,
        IS_SEND_WMS,
        SIGNER_ID,
        SIGNER_NAME,
        URAGE,
        INVOICE_INFO_TYPE,
        ADVANCE_VALID_REASON,
        INVOICE_MESSAGE,
        CREATE_TYPE
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from T_INVOICE_APPLY
        where INVOICE_APPLY_ID = #{invoiceApplyId,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete
        from T_INVOICE_APPLY
        where INVOICE_APPLY_ID = #{invoiceApplyId,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="INVOICE_APPLY_ID" keyProperty="invoiceApplyId"
            parameterType="com.vedeng.erp.finance.domain.entity.InvoiceApplyEntity" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_INVOICE_APPLY (COMPANY_ID, INVOICE_PROPERTY, APPLY_METHOD,
                                     `TYPE`, RELATED_ID, IS_AUTO,
                                     IS_ADVANCE, ADVANCE_VALID_STATUS, ADVANCE_VALID_USERID,
                                     ADVANCE_VALID_TIME, ADVANCE_VALID_COMMENTS,
                                     YY_VALID_STATUS, YY_VALID_USERID, YY_VALID_TIME,
                                     YY_VALID_COMMENTS, VALID_STATUS, VALID_USERID,
                                     VALID_TIME, VALID_COMMENTS, INVOICE_ST_STATUS,
                                     INVOICE_PRINT_STATUS, COMMENTS, IS_SIGN,
                                     ADD_TIME, CREATOR, MOD_TIME,
                                     UPDATER, EXPRESS_ID, IS_OPENING,
                                     REASONS, WMS_ORDER_NO, ERP_ORDER_NO,
                                     IS_SEND_WMS, SIGNER_ID, SIGNER_NAME,
                                     URAGE)
        values (#{companyId,jdbcType=INTEGER}, #{invoiceProperty,jdbcType=BOOLEAN}, #{applyMethod,jdbcType=BOOLEAN},
                #{type,jdbcType=INTEGER}, #{relatedId,jdbcType=INTEGER}, #{isAuto,jdbcType=BOOLEAN},
                #{isAdvance,jdbcType=BOOLEAN}, #{advanceValidStatus,jdbcType=BOOLEAN},
                #{advanceValidUserid,jdbcType=INTEGER},
                #{advanceValidTime,jdbcType=BIGINT}, #{advanceValidComments,jdbcType=VARCHAR},
                #{yyValidStatus,jdbcType=BOOLEAN}, #{yyValidUserid,jdbcType=INTEGER}, #{yyValidTime,jdbcType=BIGINT},
                #{yyValidComments,jdbcType=VARCHAR}, #{validStatus,jdbcType=BOOLEAN}, #{validUserid,jdbcType=INTEGER},
                #{validTime,jdbcType=BIGINT}, #{validComments,jdbcType=VARCHAR}, #{invoiceStStatus,jdbcType=BOOLEAN},
                #{invoicePrintStatus,jdbcType=BOOLEAN}, #{comments,jdbcType=VARCHAR}, #{isSign,jdbcType=BOOLEAN},
                #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=BIGINT},
                #{updater,jdbcType=INTEGER}, #{expressId,jdbcType=INTEGER}, #{isOpening,jdbcType=BOOLEAN},
                #{reasons,jdbcType=VARCHAR}, #{wmsOrderNo,jdbcType=VARCHAR}, #{erpOrderNo,jdbcType=VARCHAR},
                #{isSendWms,jdbcType=BOOLEAN}, #{signerId,jdbcType=INTEGER}, #{signerName,jdbcType=VARCHAR},
                #{urage,jdbcType=BOOLEAN})
    </insert>
    <insert id="insertSelective" keyColumn="INVOICE_APPLY_ID" keyProperty="invoiceApplyId"
            parameterType="com.vedeng.erp.finance.domain.entity.InvoiceApplyEntity" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_INVOICE_APPLY
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                COMPANY_ID,
            </if>
            <if test="invoiceProperty != null">
                INVOICE_PROPERTY,
            </if>
            <if test="applyMethod != null">
                APPLY_METHOD,
            </if>
            <if test="type != null">
                `TYPE`,
            </if>
            <if test="relatedId != null">
                RELATED_ID,
            </if>
            <if test="isAuto != null">
                IS_AUTO,
            </if>
            <if test="isAdvance != null">
                IS_ADVANCE,
            </if>
            <if test="advanceValidStatus != null">
                ADVANCE_VALID_STATUS,
            </if>
            <if test="advanceValidUserid != null">
                ADVANCE_VALID_USERID,
            </if>
            <if test="advanceValidTime != null">
                ADVANCE_VALID_TIME,
            </if>
            <if test="advanceValidComments != null and advanceValidComments != ''">
                ADVANCE_VALID_COMMENTS,
            </if>
            <if test="yyValidStatus != null">
                YY_VALID_STATUS,
            </if>
            <if test="yyValidUserid != null">
                YY_VALID_USERID,
            </if>
            <if test="yyValidTime != null">
                YY_VALID_TIME,
            </if>
            <if test="yyValidComments != null and yyValidComments != ''">
                YY_VALID_COMMENTS,
            </if>
            <if test="validStatus != null">
                VALID_STATUS,
            </if>
            <if test="validUserid != null">
                VALID_USERID,
            </if>
            <if test="validTime != null">
                VALID_TIME,
            </if>
            <if test="validComments != null and validComments != ''">
                VALID_COMMENTS,
            </if>
            <if test="invoiceStStatus != null">
                INVOICE_ST_STATUS,
            </if>
            <if test="invoicePrintStatus != null">
                INVOICE_PRINT_STATUS,
            </if>
            <if test="comments != null and comments != ''">
                COMMENTS,
            </if>
            <if test="isSign != null">
                IS_SIGN,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
            <if test="expressId != null">
                EXPRESS_ID,
            </if>
            <if test="isOpening != null">
                IS_OPENING,
            </if>
            <if test="reasons != null and reasons != ''">
                REASONS,
            </if>
            <if test="wmsOrderNo != null and wmsOrderNo != ''">
                WMS_ORDER_NO,
            </if>
            <if test="erpOrderNo != null and erpOrderNo != ''">
                ERP_ORDER_NO,
            </if>
            <if test="isSendWms != null">
                IS_SEND_WMS,
            </if>
            <if test="signerId != null">
                SIGNER_ID,
            </if>
            <if test="signerName != null and signerName != ''">
                SIGNER_NAME,
            </if>
            <if test="urage != null">
                URAGE,
            </if>
            <if test="invoiceInfoType != null">
                INVOICE_INFO_TYPE,
            </if>
            <if test="advanceValidReason != null">
                ADVANCE_VALID_REASON,
            </if>
            <if test="invoiceMessage != null">
                INVOICE_MESSAGE,
            </if>
            <if test="createType != null">
                CREATE_TYPE,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                #{companyId,jdbcType=INTEGER},
            </if>
            <if test="invoiceProperty != null">
                #{invoiceProperty,jdbcType=BOOLEAN},
            </if>
            <if test="applyMethod != null">
                #{applyMethod,jdbcType=BOOLEAN},
            </if>
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="relatedId != null">
                #{relatedId,jdbcType=INTEGER},
            </if>
            <if test="isAuto != null">
                #{isAuto,jdbcType=BOOLEAN},
            </if>
            <if test="isAdvance != null">
                #{isAdvance,jdbcType=BOOLEAN},
            </if>
            <if test="advanceValidStatus != null">
                #{advanceValidStatus,jdbcType=BOOLEAN},
            </if>
            <if test="advanceValidUserid != null">
                #{advanceValidUserid,jdbcType=INTEGER},
            </if>
            <if test="advanceValidTime != null">
                #{advanceValidTime,jdbcType=BIGINT},
            </if>
            <if test="advanceValidComments != null and advanceValidComments != ''">
                #{advanceValidComments,jdbcType=VARCHAR},
            </if>
            <if test="yyValidStatus != null">
                #{yyValidStatus,jdbcType=BOOLEAN},
            </if>
            <if test="yyValidUserid != null">
                #{yyValidUserid,jdbcType=INTEGER},
            </if>
            <if test="yyValidTime != null">
                #{yyValidTime,jdbcType=BIGINT},
            </if>
            <if test="yyValidComments != null and yyValidComments != ''">
                #{yyValidComments,jdbcType=VARCHAR},
            </if>
            <if test="validStatus != null">
                #{validStatus,jdbcType=BOOLEAN},
            </if>
            <if test="validUserid != null">
                #{validUserid,jdbcType=INTEGER},
            </if>
            <if test="validTime != null">
                #{validTime,jdbcType=BIGINT},
            </if>
            <if test="validComments != null and validComments != ''">
                #{validComments,jdbcType=VARCHAR},
            </if>
            <if test="invoiceStStatus != null">
                #{invoiceStStatus,jdbcType=BOOLEAN},
            </if>
            <if test="invoicePrintStatus != null">
                #{invoicePrintStatus,jdbcType=BOOLEAN},
            </if>
            <if test="comments != null and comments != ''">
                #{comments,jdbcType=VARCHAR},
            </if>
            <if test="isSign != null">
                #{isSign,jdbcType=BOOLEAN},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
            <if test="expressId != null">
                #{expressId,jdbcType=INTEGER},
            </if>
            <if test="isOpening != null">
                #{isOpening,jdbcType=BOOLEAN},
            </if>
            <if test="reasons != null and reasons != ''">
                #{reasons,jdbcType=VARCHAR},
            </if>
            <if test="wmsOrderNo != null and wmsOrderNo != ''">
                #{wmsOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="erpOrderNo != null and erpOrderNo != ''">
                #{erpOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="isSendWms != null">
                #{isSendWms,jdbcType=BOOLEAN},
            </if>
            <if test="signerId != null">
                #{signerId,jdbcType=INTEGER},
            </if>
            <if test="signerName != null and signerName != ''">
                #{signerName,jdbcType=VARCHAR},
            </if>
            <if test="urage != null">
                #{urage,jdbcType=BOOLEAN},
            </if>
            <if test="invoiceInfoType != null">
                #{invoiceInfoType,jdbcType=INTEGER},
            </if>
            <if test="advanceValidReason != null">
                #{advanceValidReason,jdbcType=VARCHAR},
            </if>
            <if test="invoiceMessage != null">
                #{invoiceMessage,jdbcType=VARCHAR},
            </if>
            <if test="createType != null">
                #{createType,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceApplyEntity">
        <!--@mbg.generated-->
        update T_INVOICE_APPLY
        <set>
            <if test="companyId != null">
                COMPANY_ID = #{companyId,jdbcType=INTEGER},
            </if>
            <if test="invoiceProperty != null">
                INVOICE_PROPERTY = #{invoiceProperty,jdbcType=BOOLEAN},
            </if>
            <if test="applyMethod != null">
                APPLY_METHOD = #{applyMethod,jdbcType=BOOLEAN},
            </if>
            <if test="type != null">
                `TYPE` = #{type,jdbcType=INTEGER},
            </if>
            <if test="relatedId != null">
                RELATED_ID = #{relatedId,jdbcType=INTEGER},
            </if>
            <if test="isAuto != null">
                IS_AUTO = #{isAuto,jdbcType=BOOLEAN},
            </if>
            <if test="isAdvance != null">
                IS_ADVANCE = #{isAdvance,jdbcType=BOOLEAN},
            </if>
            <if test="advanceValidStatus != null">
                ADVANCE_VALID_STATUS = #{advanceValidStatus,jdbcType=BOOLEAN},
            </if>
            <if test="advanceValidUserid != null">
                ADVANCE_VALID_USERID = #{advanceValidUserid,jdbcType=INTEGER},
            </if>
            <if test="advanceValidTime != null">
                ADVANCE_VALID_TIME = #{advanceValidTime,jdbcType=BIGINT},
            </if>
            <if test="advanceValidComments != null and advanceValidComments != ''">
                ADVANCE_VALID_COMMENTS = #{advanceValidComments,jdbcType=VARCHAR},
            </if>
            <if test="yyValidStatus != null">
                YY_VALID_STATUS = #{yyValidStatus,jdbcType=BOOLEAN},
            </if>
            <if test="yyValidUserid != null">
                YY_VALID_USERID = #{yyValidUserid,jdbcType=INTEGER},
            </if>
            <if test="yyValidTime != null">
                YY_VALID_TIME = #{yyValidTime,jdbcType=BIGINT},
            </if>
            <if test="yyValidComments != null and yyValidComments != ''">
                YY_VALID_COMMENTS = #{yyValidComments,jdbcType=VARCHAR},
            </if>
            <if test="validStatus != null">
                VALID_STATUS = #{validStatus,jdbcType=BOOLEAN},
            </if>
            <if test="validUserid != null">
                VALID_USERID = #{validUserid,jdbcType=INTEGER},
            </if>
            <if test="validTime != null">
                VALID_TIME = #{validTime,jdbcType=BIGINT},
            </if>
            <if test="validComments != null and validComments != ''">
                VALID_COMMENTS = #{validComments,jdbcType=VARCHAR},
            </if>
            <if test="invoiceStStatus != null">
                INVOICE_ST_STATUS = #{invoiceStStatus,jdbcType=BOOLEAN},
            </if>
            <if test="invoicePrintStatus != null">
                INVOICE_PRINT_STATUS = #{invoicePrintStatus,jdbcType=BOOLEAN},
            </if>
            <if test="comments != null and comments != ''">
                COMMENTS = #{comments,jdbcType=VARCHAR},
            </if>
            <if test="isSign != null">
                IS_SIGN = #{isSign,jdbcType=BOOLEAN},
            </if>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="modTime != null">
                MOD_TIME = #{modTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
            <if test="expressId != null">
                EXPRESS_ID = #{expressId,jdbcType=INTEGER},
            </if>
            <if test="isOpening != null">
                IS_OPENING = #{isOpening,jdbcType=BOOLEAN},
            </if>
            <if test="reasons != null and reasons != ''">
                REASONS = #{reasons,jdbcType=VARCHAR},
            </if>
            <if test="wmsOrderNo != null and wmsOrderNo != ''">
                WMS_ORDER_NO = #{wmsOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="erpOrderNo != null and erpOrderNo != ''">
                ERP_ORDER_NO = #{erpOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="isSendWms != null">
                IS_SEND_WMS = #{isSendWms,jdbcType=BOOLEAN},
            </if>
            <if test="signerId != null">
                SIGNER_ID = #{signerId,jdbcType=INTEGER},
            </if>
            <if test="signerName != null and signerName != ''">
                SIGNER_NAME = #{signerName,jdbcType=VARCHAR},
            </if>
            <if test="urage != null">
                URAGE = #{urage,jdbcType=BOOLEAN},
            </if>
            <if test="createType != null">
                CREATE_TYPE = #{createType,jdbcType=VARCHAR},
            </if>
        </set>
        where INVOICE_APPLY_ID = #{invoiceApplyId,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceApplyEntity">
        <!--@mbg.generated-->
        update T_INVOICE_APPLY
        set COMPANY_ID             = #{companyId,jdbcType=INTEGER},
            INVOICE_PROPERTY       = #{invoiceProperty,jdbcType=BOOLEAN},
            APPLY_METHOD           = #{applyMethod,jdbcType=BOOLEAN},
            `TYPE`                 = #{type,jdbcType=INTEGER},
            RELATED_ID             = #{relatedId,jdbcType=INTEGER},
            IS_AUTO                = #{isAuto,jdbcType=BOOLEAN},
            IS_ADVANCE             = #{isAdvance,jdbcType=BOOLEAN},
            ADVANCE_VALID_STATUS   = #{advanceValidStatus,jdbcType=BOOLEAN},
            ADVANCE_VALID_USERID   = #{advanceValidUserid,jdbcType=INTEGER},
            ADVANCE_VALID_TIME     = #{advanceValidTime,jdbcType=BIGINT},
            ADVANCE_VALID_COMMENTS = #{advanceValidComments,jdbcType=VARCHAR},
            YY_VALID_STATUS        = #{yyValidStatus,jdbcType=BOOLEAN},
            YY_VALID_USERID        = #{yyValidUserid,jdbcType=INTEGER},
            YY_VALID_TIME          = #{yyValidTime,jdbcType=BIGINT},
            YY_VALID_COMMENTS      = #{yyValidComments,jdbcType=VARCHAR},
            VALID_STATUS           = #{validStatus,jdbcType=BOOLEAN},
            VALID_USERID           = #{validUserid,jdbcType=INTEGER},
            VALID_TIME             = #{validTime,jdbcType=BIGINT},
            VALID_COMMENTS         = #{validComments,jdbcType=VARCHAR},
            INVOICE_ST_STATUS      = #{invoiceStStatus,jdbcType=BOOLEAN},
            INVOICE_PRINT_STATUS   = #{invoicePrintStatus,jdbcType=BOOLEAN},
            COMMENTS               = #{comments,jdbcType=VARCHAR},
            IS_SIGN                = #{isSign,jdbcType=BOOLEAN},
            ADD_TIME               = #{addTime,jdbcType=BIGINT},
            CREATOR                = #{creator,jdbcType=INTEGER},
            MOD_TIME               = #{modTime,jdbcType=BIGINT},
            UPDATER                = #{updater,jdbcType=INTEGER},
            EXPRESS_ID             = #{expressId,jdbcType=INTEGER},
            IS_OPENING             = #{isOpening,jdbcType=BOOLEAN},
            REASONS                = #{reasons,jdbcType=VARCHAR},
            WMS_ORDER_NO           = #{wmsOrderNo,jdbcType=VARCHAR},
            ERP_ORDER_NO           = #{erpOrderNo,jdbcType=VARCHAR},
            IS_SEND_WMS            = #{isSendWms,jdbcType=BOOLEAN},
            SIGNER_ID              = #{signerId,jdbcType=INTEGER},
            SIGNER_NAME            = #{signerName,jdbcType=VARCHAR},
            URAGE                  = #{urage,jdbcType=BOOLEAN},
            CREATE_TYPE = #{createType,jdbcType=VARCHAR}
        where INVOICE_APPLY_ID = #{invoiceApplyId,jdbcType=INTEGER}
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update T_INVOICE_APPLY
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="COMPANY_ID = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.companyId != null">
                        when INVOICE_APPLY_ID = #{item.invoiceApplyId,jdbcType=INTEGER}
                            then #{item.companyId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="INVOICE_PROPERTY = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.invoiceProperty != null">
                        when INVOICE_APPLY_ID = #{item.invoiceApplyId,jdbcType=INTEGER}
                            then #{item.invoiceProperty,jdbcType=BOOLEAN}
                    </if>
                </foreach>
            </trim>
            <trim prefix="APPLY_METHOD = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.applyMethod != null">
                        when INVOICE_APPLY_ID = #{item.invoiceApplyId,jdbcType=INTEGER}
                            then #{item.applyMethod,jdbcType=BOOLEAN}
                    </if>
                </foreach>
            </trim>
            <trim prefix="`TYPE` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.type != null">
                        when INVOICE_APPLY_ID = #{item.invoiceApplyId,jdbcType=INTEGER}
                            then #{item.type,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="RELATED_ID = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.relatedId != null">
                        when INVOICE_APPLY_ID = #{item.invoiceApplyId,jdbcType=INTEGER}
                            then #{item.relatedId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="IS_AUTO = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isAuto != null">
                        when INVOICE_APPLY_ID = #{item.invoiceApplyId,jdbcType=INTEGER}
                            then #{item.isAuto,jdbcType=BOOLEAN}
                    </if>
                </foreach>
            </trim>
            <trim prefix="IS_ADVANCE = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isAdvance != null">
                        when INVOICE_APPLY_ID = #{item.invoiceApplyId,jdbcType=INTEGER}
                            then #{item.isAdvance,jdbcType=BOOLEAN}
                    </if>
                </foreach>
            </trim>
            <trim prefix="ADVANCE_VALID_STATUS = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.advanceValidStatus != null">
                        when INVOICE_APPLY_ID = #{item.invoiceApplyId,jdbcType=INTEGER}
                            then #{item.advanceValidStatus,jdbcType=BOOLEAN}
                    </if>
                </foreach>
            </trim>
            <trim prefix="ADVANCE_VALID_USERID = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.advanceValidUserid != null">
                        when INVOICE_APPLY_ID = #{item.invoiceApplyId,jdbcType=INTEGER}
                            then #{item.advanceValidUserid,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="ADVANCE_VALID_TIME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.advanceValidTime != null">
                        when INVOICE_APPLY_ID = #{item.invoiceApplyId,jdbcType=INTEGER}
                            then #{item.advanceValidTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="ADVANCE_VALID_COMMENTS = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.advanceValidComments != null">
                        when INVOICE_APPLY_ID = #{item.invoiceApplyId,jdbcType=INTEGER}
                            then #{item.advanceValidComments,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="YY_VALID_STATUS = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.yyValidStatus != null">
                        when INVOICE_APPLY_ID = #{item.invoiceApplyId,jdbcType=INTEGER}
                            then #{item.yyValidStatus,jdbcType=BOOLEAN}
                    </if>
                </foreach>
            </trim>
            <trim prefix="YY_VALID_USERID = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.yyValidUserid != null">
                        when INVOICE_APPLY_ID = #{item.invoiceApplyId,jdbcType=INTEGER}
                            then #{item.yyValidUserid,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="YY_VALID_TIME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.yyValidTime != null">
                        when INVOICE_APPLY_ID = #{item.invoiceApplyId,jdbcType=INTEGER}
                            then #{item.yyValidTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="YY_VALID_COMMENTS = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.yyValidComments != null">
                        when INVOICE_APPLY_ID = #{item.invoiceApplyId,jdbcType=INTEGER}
                            then #{item.yyValidComments,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="VALID_STATUS = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.validStatus != null">
                        when INVOICE_APPLY_ID = #{item.invoiceApplyId,jdbcType=INTEGER}
                            then #{item.validStatus,jdbcType=BOOLEAN}
                    </if>
                </foreach>
            </trim>
            <trim prefix="VALID_USERID = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.validUserid != null">
                        when INVOICE_APPLY_ID = #{item.invoiceApplyId,jdbcType=INTEGER}
                            then #{item.validUserid,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="VALID_TIME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.validTime != null">
                        when INVOICE_APPLY_ID = #{item.invoiceApplyId,jdbcType=INTEGER}
                            then #{item.validTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="VALID_COMMENTS = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.validComments != null">
                        when INVOICE_APPLY_ID = #{item.invoiceApplyId,jdbcType=INTEGER}
                            then #{item.validComments,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="INVOICE_ST_STATUS = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.invoiceStStatus != null">
                        when INVOICE_APPLY_ID = #{item.invoiceApplyId,jdbcType=INTEGER}
                            then #{item.invoiceStStatus,jdbcType=BOOLEAN}
                    </if>
                </foreach>
            </trim>
            <trim prefix="INVOICE_PRINT_STATUS = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.invoicePrintStatus != null">
                        when INVOICE_APPLY_ID = #{item.invoiceApplyId,jdbcType=INTEGER}
                            then #{item.invoicePrintStatus,jdbcType=BOOLEAN}
                    </if>
                </foreach>
            </trim>
            <trim prefix="COMMENTS = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.comments != null">
                        when INVOICE_APPLY_ID = #{item.invoiceApplyId,jdbcType=INTEGER}
                            then #{item.comments,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="IS_SIGN = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isSign != null">
                        when INVOICE_APPLY_ID = #{item.invoiceApplyId,jdbcType=INTEGER}
                            then #{item.isSign,jdbcType=BOOLEAN}
                    </if>
                </foreach>
            </trim>
            <trim prefix="ADD_TIME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.addTime != null">
                        when INVOICE_APPLY_ID = #{item.invoiceApplyId,jdbcType=INTEGER}
                            then #{item.addTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="CREATOR = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.creator != null">
                        when INVOICE_APPLY_ID = #{item.invoiceApplyId,jdbcType=INTEGER}
                            then #{item.creator,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="MOD_TIME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.modTime != null">
                        when INVOICE_APPLY_ID = #{item.invoiceApplyId,jdbcType=INTEGER}
                            then #{item.modTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="UPDATER = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updater != null">
                        when INVOICE_APPLY_ID = #{item.invoiceApplyId,jdbcType=INTEGER}
                            then #{item.updater,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="EXPRESS_ID = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.expressId != null">
                        when INVOICE_APPLY_ID = #{item.invoiceApplyId,jdbcType=INTEGER}
                            then #{item.expressId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="IS_OPENING = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isOpening != null">
                        when INVOICE_APPLY_ID = #{item.invoiceApplyId,jdbcType=INTEGER}
                            then #{item.isOpening,jdbcType=BOOLEAN}
                    </if>
                </foreach>
            </trim>
            <trim prefix="REASONS = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.reasons != null">
                        when INVOICE_APPLY_ID = #{item.invoiceApplyId,jdbcType=INTEGER}
                            then #{item.reasons,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="WMS_ORDER_NO = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.wmsOrderNo != null">
                        when INVOICE_APPLY_ID = #{item.invoiceApplyId,jdbcType=INTEGER}
                            then #{item.wmsOrderNo,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="ERP_ORDER_NO = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.erpOrderNo != null">
                        when INVOICE_APPLY_ID = #{item.invoiceApplyId,jdbcType=INTEGER}
                            then #{item.erpOrderNo,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="IS_SEND_WMS = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isSendWms != null">
                        when INVOICE_APPLY_ID = #{item.invoiceApplyId,jdbcType=INTEGER}
                            then #{item.isSendWms,jdbcType=BOOLEAN}
                    </if>
                </foreach>
            </trim>
            <trim prefix="SIGNER_ID = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.signerId != null">
                        when INVOICE_APPLY_ID = #{item.invoiceApplyId,jdbcType=INTEGER}
                            then #{item.signerId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="SIGNER_NAME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.signerName != null">
                        when INVOICE_APPLY_ID = #{item.invoiceApplyId,jdbcType=INTEGER}
                            then #{item.signerName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="URAGE = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.urage != null">
                        when INVOICE_APPLY_ID = #{item.invoiceApplyId,jdbcType=INTEGER}
                            then #{item.urage,jdbcType=BOOLEAN}
                    </if>
                </foreach>
            </trim>
        </trim>
        where INVOICE_APPLY_ID in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.invoiceApplyId,jdbcType=INTEGER}
        </foreach>
    </update>

    <!--auto generated by MybatisCodeHelper on 2023-09-20-->
    <select id="getWaitInvoiceApply" resultType="com.vedeng.erp.finance.dto.InvoiceApplyDto">
        select
            TIA.INVOICE_APPLY_ID,
            TIA.COMPANY_ID,
            TIA.INVOICE_PROPERTY,
            TIA.APPLY_METHOD,
            TIA.`TYPE`,
            TIA.RELATED_ID,
            TIA.IS_AUTO,
            TIA.IS_ADVANCE,
            TIA.ADVANCE_VALID_STATUS,
            TIA.ADVANCE_VALID_USERID,
            TIA.ADVANCE_VALID_TIME,
            TIA.ADVANCE_VALID_COMMENTS,
            TIA.YY_VALID_STATUS,
            TIA.YY_VALID_USERID,
            TIA.YY_VALID_TIME,
            TIA.YY_VALID_COMMENTS,
            TIA.VALID_STATUS,
            TIA.VALID_USERID,
            TIA.VALID_TIME,
            TIA.VALID_COMMENTS,
            TIA.INVOICE_ST_STATUS,
            TIA.INVOICE_PRINT_STATUS,
            TIA.COMMENTS,
            TIA.IS_SIGN,
            TIA.ADD_TIME,
            TIA.CREATOR,
            TIA.MOD_TIME,
            TIA.UPDATER,
            TIA.EXPRESS_ID,
            TIA.IS_OPENING,
            TIA.REASONS,
            TIA.WMS_ORDER_NO,
            TS.SALEORDER_NO ERP_ORDER_NO,
            TS.SALEORDER_NO orderNo,
            TIA.IS_SEND_WMS,
            TS.INVOICE_TYPE invoiceType,
            TS.TRADER_ID traderId,
            TIA.APPLY_METHOD applyMethod,
            TS.TRADER_NAME traderName,
            TIA.INVOICE_INFO_TYPE,
            TIA.INVOICE_MESSAGE
        from T_INVOICE_APPLY TIA
        left join T_SALEORDER TS on TIA.RELATED_ID = TS.SALEORDER_ID
        where TIA.INVOICE_PROPERTY = 3
          AND TIA.TYPE = 505
          AND TIA.VALID_STATUS = 0
          AND ((TIA.IS_ADVANCE = 0) OR (TIA.IS_ADVANCE = 1 AND TIA.ADVANCE_VALID_STATUS = 1))
          AND TIA.COMPANY_ID = 1
          <if test="saleOrderNo != null">
            AND TS.SALEORDER_NO = #{saleOrderNo,jdbcType=INTEGER}
        </if>
    </select>

    <select id="getAtWaitInvoiceApply" resultType="com.vedeng.erp.finance.dto.InvoiceApplyDto">
        select
            apply.INVOICE_APPLY_ID,
            apply.COMPANY_ID,
            apply.INVOICE_PROPERTY,
            apply.APPLY_METHOD,
            apply.`TYPE`,
            apply.RELATED_ID,
            apply.IS_AUTO,
            apply.IS_ADVANCE,
            apply.ADVANCE_VALID_STATUS,
            apply.ADVANCE_VALID_USERID,
            apply.ADVANCE_VALID_TIME,
            apply.ADVANCE_VALID_COMMENTS,
            apply.YY_VALID_STATUS,
            apply.YY_VALID_USERID,
            apply.YY_VALID_TIME,
            apply.YY_VALID_COMMENTS,
            apply.VALID_STATUS,
            apply.VALID_USERID,
            apply.VALID_TIME,
            apply.VALID_COMMENTS,
            apply.INVOICE_ST_STATUS,
            apply.INVOICE_PRINT_STATUS,
            apply.COMMENTS,
            apply.IS_SIGN,
            apply.ADD_TIME,
            apply.CREATOR,
            apply.MOD_TIME,
            apply.UPDATER,
            apply.EXPRESS_ID,
            apply.IS_OPENING,
            apply.REASONS,
            apply.WMS_ORDER_NO,
            apply.INVOICE_INFO_TYPE,
            apply.INVOICE_MESSAGE,
            after.AFTER_SALES_NO ERP_ORDER_NO,
            after.AFTER_SALES_NO orderNo,
            apply.IS_SEND_WMS,
            apply.APPLY_METHOD applyMethod,
            detail.TRADER_ID,
            detail.INVOICE_TYPE invoiceType
        from T_INVOICE_APPLY apply
                 left join T_AFTER_SALES after on apply.RELATED_ID = after.AFTER_SALES_ID
                 left join T_AFTER_SALES_DETAIL detail on after.AFTER_SALES_ID = detail.AFTER_SALES_ID
        where after.TYPE in (584,4090,4091,550,585,539,540) -- 584,销售订单维修4090,销售安调（合同安调）4091,销售安调（附加服务）550,第三方安调585,第三方维修539销售订单退货540销售订单换货
          AND apply.TYPE = 504 -- 售后开票
          AND apply.VALID_STATUS = 0
          AND ((apply.IS_ADVANCE = 0) OR (apply.IS_ADVANCE = 1 AND apply.ADVANCE_VALID_STATUS = 1))
          AND apply.COMPANY_ID = 1
          AND apply.INVOICE_PROPERTY = 3
          <if test="afterSalesNo != null and afterSalesNo != ''">
            AND after.AFTER_SALES_NO = #{afterSalesNo,jdbcType=VARCHAR}
          </if>
          order by apply.INVOICE_APPLY_ID desc
    </select>

    <select id="getInvoiceApply" resultType="com.vedeng.erp.finance.dto.InvoiceApplyDto">
        select
            TIA.INVOICE_APPLY_ID,
            TIA.COMPANY_ID,
            TIA.INVOICE_PROPERTY,
            TIA.APPLY_METHOD,
            TIA.`TYPE`,
            TIA.RELATED_ID,
            TIA.IS_AUTO,
            TIA.IS_ADVANCE,
            TIA.ADVANCE_VALID_STATUS,
            TIA.ADVANCE_VALID_USERID,
            TIA.ADVANCE_VALID_TIME,
            TIA.ADVANCE_VALID_COMMENTS,
            TIA.YY_VALID_STATUS,
            TIA.YY_VALID_USERID,
            TIA.YY_VALID_TIME,
            TIA.YY_VALID_COMMENTS,
            TIA.VALID_STATUS,
            TIA.VALID_USERID,
            TIA.VALID_TIME,
            TIA.VALID_COMMENTS,
            TIA.INVOICE_ST_STATUS,
            TIA.INVOICE_PRINT_STATUS,
            TIA.COMMENTS,
            TIA.IS_SIGN,
            TIA.ADD_TIME,
            TIA.CREATOR,
            TIA.MOD_TIME,
            TIA.UPDATER,
            TIA.EXPRESS_ID,
            TIA.IS_OPENING,
            TIA.REASONS,
            TIA.WMS_ORDER_NO,
            TS.SALEORDER_NO ERP_ORDER_NO,
            TIA.IS_SEND_WMS,
            TS.INVOICE_TYPE invoiceType,
            TS.TRADER_ID traderId,
            TS.TRADER_NAME traderName,
            TIA.INVOICE_INFO_TYPE,
            TIA.INVOICE_MESSAGE
        from T_INVOICE_APPLY TIA
                 left join T_SALEORDER TS on TIA.RELATED_ID = TS.SALEORDER_ID
        where TIA.INVOICE_APPLY_ID = #{invoiceApplyId,jdbcType=INTEGER}
    </select>

    <select id="getAtInvoiceApply" resultType="com.vedeng.erp.finance.dto.InvoiceApplyDto">
        select
        TIA.INVOICE_APPLY_ID,
        TIA.COMPANY_ID,
        TIA.INVOICE_PROPERTY,
        TIA.APPLY_METHOD,
        TIA.`TYPE`,
        TIA.RELATED_ID,
        TIA.IS_AUTO,
        TIA.IS_ADVANCE,
        TIA.ADVANCE_VALID_STATUS,
        TIA.ADVANCE_VALID_USERID,
        TIA.ADVANCE_VALID_TIME,
        TIA.ADVANCE_VALID_COMMENTS,
        TIA.YY_VALID_STATUS,
        TIA.YY_VALID_USERID,
        TIA.YY_VALID_TIME,
        TIA.YY_VALID_COMMENTS,
        TIA.VALID_STATUS,
        TIA.VALID_USERID,
        TIA.VALID_TIME,
        TIA.VALID_COMMENTS,
        TIA.INVOICE_ST_STATUS,
        TIA.INVOICE_PRINT_STATUS,
        TIA.COMMENTS,
        TIA.IS_SIGN,
        TIA.ADD_TIME,
        TIA.CREATOR,
        TIA.MOD_TIME,
        TIA.UPDATER,
        TIA.EXPRESS_ID,
        TIA.IS_OPENING,
        TIA.REASONS,
        TIA.WMS_ORDER_NO,
        TAS.AFTER_SALES_NO  ERP_ORDER_NO,
        TIA.IS_SEND_WMS,
        TS.INVOICE_TYPE invoiceType,
        TS.TRADER_ID traderId,
        T.TRADER_NAME traderName,
        TIA.INVOICE_INFO_TYPE,
        TIA.INVOICE_MESSAGE
        from T_INVOICE_APPLY TIA
        left join T_AFTER_SALES TAS on TIA.RELATED_ID = TAS.AFTER_SALES_ID
        left join T_AFTER_SALES_DETAIL TS on TAS.AFTER_SALES_ID = TS.AFTER_SALES_ID
        left join T_TRADER T on TS.TRADER_ID = T.TRADER_ID
        where TIA.INVOICE_APPLY_ID = #{invoiceApplyId,jdbcType=INTEGER}
    </select>

    <select id="getSaleOrderNoByInvoiceApplyId" resultType="java.lang.String">
        select SALEORDER_NO from T_INVOICE_APPLY a left join T_SALEORDER b on a.RELATED_ID = b.SALEORDER_ID
        where a.INVOICE_APPLY_ID = #{invoiceApplyId,jdbcType=INTEGER} limit 1
    </select>

    <select id="getAfterSalesNoByInvoiceApplyId" resultType="java.lang.String">
        select AFTER_SALES_NO from T_INVOICE_APPLY a left join T_AFTER_SALES b on a.RELATED_ID = b.AFTER_SALES_ID
        where a.INVOICE_APPLY_ID = #{invoiceApplyId,jdbcType=INTEGER} limit 1
    </select>

    <select id="getAftersaleInvoiceApplyByRelatedIdLast" resultType="com.vedeng.erp.finance.dto.InvoiceApplyDto">
        SELECT A.*
        FROM T_INVOICE_APPLY A
        WHERE A.TYPE = 504 AND A.RELATED_ID = #{relatedId,jdbcType=INTEGER}
        ORDER BY A.ADD_TIME DESC LIMIT 1
    </select>

<!--auto generated by MybatisCodeHelper on 2023-11-16-->
    <select id="getByRelatedIdAndType" resultType="com.vedeng.erp.finance.dto.InvoiceApplyDto">
        select
        TIA.*,
        TU.USERNAME creatorName
        from T_INVOICE_APPLY TIA
        left join T_USER TU on TIA.CREATOR = TU.USER_ID
        where TIA.RELATED_ID=#{relatedId,jdbcType=INTEGER} and TIA.`TYPE`=#{type,jdbcType=INTEGER}
    </select>
    <select id="getByAdvanceAndAdvanceValidStatus" resultType="com.vedeng.erp.finance.dto.InvoiceApplyDto">
        select
        TIA.*
        from T_INVOICE_APPLY TIA
        where TIA.IS_ADVANCE=#{advance,jdbcType=INTEGER} and TIA.ADVANCE_VALID_STATUS=#{advanceValidStatus,jdbcType=INTEGER}
    </select>

    <select id="queryAdvanceInvoiceApply" resultType="com.vedeng.erp.finance.dto.InvoiceApplyDto">
        select
            TIA.*
        from T_INVOICE_APPLY TIA
        where  TIA.IS_ADVANCE = 1
          AND TIA.COMPANY_ID = 1
            AND TIA.TYPE in (504,505)
          AND TIA.ADVANCE_VALID_STATUS = 0
    </select>

    <select id="selectInvoiceApplyByEntity" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceApplyEntity" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_INVOICE_APPLY
        where  COMPANY_ID = 1
        <if test="type != null">
          and TYPE = #{type,jdbcType=INTEGER}
        </if>
        <if test="relatedId != null">
          and RELATED_ID = #{relatedId,jdbcType=INTEGER}
        </if>
        <if test="isOpening != null and isOpening == 1">
            and  ((IS_ADVANCE = 1 and ADVANCE_VALID_STATUS in (0,1) and VALID_STATUS = 0) or (IS_ADVANCE = 0 and VALID_STATUS = 0))
        </if>
        limit 100
    </select>

    <select id="getApplyReasonSnapshot" resultType="com.vedeng.erp.finance.dto.InvoiceApplyReasonSnapshotDto">
        select *
        from T_INVOICE_APPLY_REASON_SNAPSHOT
        where INVOICE_APPLY_ID = #{invoiceApplyId,jdbcType=INTEGER}
          and IS_DELETE = 0
    </select>

    <select id="getAppliedNum" resultType="java.math.BigDecimal">
        SELECT
        ROUND( IFNULL( SUM( D.NUM ), 0 ), 2 ) AS appliedNum
        FROM
        T_SALEORDER_GOODS G
        LEFT JOIN T_SALEORDER S ON S.SALEORDER_ID = G.SALEORDER_ID
        LEFT JOIN T_INVOICE_APPLY A ON S.SALEORDER_ID = A.RELATED_ID
        AND ((A.ADVANCE_VALID_STATUS = 0 AND A.VALID_STATUS = 0) OR (A.ADVANCE_VALID_STATUS = 1 AND A.VALID_STATUS = 0)) <!-- 提前开票审核中和开票审核中 -->
        LEFT JOIN T_INVOICE_APPLY_DETAIL D ON G.SALEORDER_GOODS_ID = D.DETAILGOODS_ID
        AND D.INVOICE_APPLY_ID = A.INVOICE_APPLY_ID
        WHERE
        1 = 1
        AND G.SALEORDER_GOODS_ID = #{saleorderGoodsId,jdbcType=INTEGER}
        and A.VALID_STATUS = 1
    </select>

    <select id="getApplyNoPeerBySalesId" resultType="com.vedeng.erp.finance.dto.InvoiceApplyDto">
        select * from T_INVOICE_APPLY where TYPE = 505 and RELATED_ID = #{saleOrderId,jdbcType=INTEGER} and APPLY_METHOD != 2
    </select>

    <select id="getSaleOrderInvoiceApplyAdvanceData" resultType="com.vedeng.erp.finance.dto.InvoiceApplyDto">
        SELECT A.*
        FROM T_INVOICE_APPLY A
        WHERE A.IS_ADVANCE = 1
          AND A.COMPANY_ID = 1
          AND A.TYPE = 505
          AND A.ADVANCE_VALID_STATUS = 0
          AND A.APPLY_METHOD != 2
        GROUP BY A.INVOICE_APPLY_ID
        order by A.INVOICE_APPLY_ID desc;
    </select>

    <select id="getAfterSalesInvoiceApplyAdvanceData" resultType="com.vedeng.erp.finance.dto.InvoiceApplyDto">
        SELECT A.*
        FROM T_INVOICE_APPLY A
        WHERE A.IS_ADVANCE = 1
          AND A.COMPANY_ID = 1
          AND A.TYPE = 504
          AND A.ADVANCE_VALID_STATUS = 0
        GROUP BY A.INVOICE_APPLY_ID
        order by A.INVOICE_APPLY_ID desc;
    </select>

    <select id="getSaleOrderInvoiceApplyData" resultType="com.vedeng.erp.finance.dto.InvoiceApplyDto">
        SELECT A.*
        FROM T_INVOICE_APPLY A
                 INNER JOIN T_SALEORDER B ON A.RELATED_ID = B.SALEORDER_ID
                 LEFT JOIN T_INVOICE TI ON TI.INVOICE_APPLY_ID = A.INVOICE_APPLY_ID
                 LEFT JOIN T_INVOICE_DETAIL TD ON TD.INVOICE_ID = TI.INVOICE_ID
        WHERE B.INVOICE_STATUS != 2
          AND B.COMPANY_ID = 1
          AND A.COMPANY_ID = 1
          AND A.TYPE = 505
          AND A.IS_SIGN = 0
          AND A.YY_VALID_STATUS = 1
          AND A.VALID_STATUS = 0
          AND A.APPLY_METHOD != 2
          AND (
            (A.IS_ADVANCE = 0)
                OR
            (A.IS_ADVANCE = 1 AND A.ADVANCE_VALID_STATUS = 1)
            )
        GROUP BY A.INVOICE_APPLY_ID
        HAVING SUM(IFNULL(TD.TOTAL_AMOUNT, 0)) = 0;
    </select>


    <select id="getSaleOrderInvoiceApplyDataById" resultType="com.vedeng.erp.finance.dto.InvoiceApplyDto">
        SELECT A.*
        FROM T_INVOICE_APPLY A
                 INNER JOIN T_SALEORDER B ON A.RELATED_ID = B.SALEORDER_ID
                 LEFT JOIN T_INVOICE TI ON TI.INVOICE_APPLY_ID = A.INVOICE_APPLY_ID
                 LEFT JOIN T_INVOICE_DETAIL TD ON TD.INVOICE_ID = TI.INVOICE_ID
        WHERE B.INVOICE_STATUS != 2
          AND B.COMPANY_ID = 1
          AND A.COMPANY_ID = 1
          AND A.TYPE = 505
          AND A.IS_SIGN = 0
          AND A.YY_VALID_STATUS = 1
          AND A.VALID_STATUS = 0
          AND A.APPLY_METHOD != 2
          AND (
                (A.IS_ADVANCE = 0)
                OR
                (A.IS_ADVANCE = 1 AND A.ADVANCE_VALID_STATUS = 1)
            )
        AND A.INVOICE_APPLY_ID = #{invoiceApplyId,jdbcType=INTEGER}
        GROUP BY A.INVOICE_APPLY_ID
        HAVING SUM(IFNULL(TD.TOTAL_AMOUNT, 0)) = 0;
    </select>

    <select id="getAfterSalesInvoiceApplyData" resultType="com.vedeng.erp.finance.dto.InvoiceApplyDto">
        SELECT A.*
        FROM T_INVOICE_APPLY A
                 INNER JOIN T_AFTER_SALES TAS ON A.RELATED_ID = TAS.AFTER_SALES_ID
                 LEFT JOIN T_AFTER_SALES_DETAIL TASD on TAS.AFTER_SALES_ID = TASD.AFTER_SALES_ID
                 LEFT JOIN T_INVOICE TI ON TI.INVOICE_APPLY_ID = A.INVOICE_APPLY_ID
                 LEFT JOIN T_INVOICE_DETAIL TD ON TD.INVOICE_ID = TI.INVOICE_ID
        WHERE TASD.INVOICE_STATUS != 2
          AND TAS.COMPANY_ID = 1
          AND A.COMPANY_ID = 1
          AND A.TYPE = 504
          AND A.IS_SIGN = 0
          AND A.YY_VALID_STATUS = 1
          AND A.VALID_STATUS = 0
          AND (
            (A.IS_ADVANCE = 0)
                OR
            (A.IS_ADVANCE = 1 AND A.ADVANCE_VALID_STATUS = 1)
            )
        GROUP BY A.INVOICE_APPLY_ID
        HAVING SUM(IFNULL(TD.TOTAL_AMOUNT, 0)) = 0;
    </select>

    <select id="getInvoicedNum" resultType="java.math.BigDecimal">
        SELECT
        ROUND(SUM(
        CASE WHEN A.VALID_STATUS = 1 THEN (IFNULL( CASE WHEN I.IS_ENABLE = 1 AND I.COLOR_TYPE = 2 THEN D.NUM ELSE - D.NUM END, 0 )) ELSE 0 END
        ),2) AS INVOICE_NUM
        FROM
        T_SALEORDER_GOODS G
        LEFT JOIN T_INVOICE_DETAIL D ON D.DETAILGOODS_ID = G.SALEORDER_GOODS_ID
        LEFT JOIN T_SALEORDER S ON G.SALEORDER_ID = S.SALEORDER_ID
        LEFT JOIN T_INVOICE I ON I.INVOICE_ID = D.INVOICE_ID
        LEFT JOIN T_INVOICE_APPLY A ON I.INVOICE_APPLY_ID = A.INVOICE_APPLY_ID
        WHERE
        1 = 1
        AND G.SALEORDER_GOODS_ID = #{saleorderGoodsId,jdbcType=INTEGER}
        and A.VALID_STATUS = 1
        GROUP BY
        SALEORDER_GOODS_ID
    </select>

    <select id="getInvoicedNumAfter" resultType="java.math.BigDecimal">
        SELECT ROUND(SUM(
                             CASE
                                 WHEN A.VALID_STATUS = 1 THEN (IFNULL(
                                         CASE WHEN I.IS_ENABLE = 1 AND I.COLOR_TYPE = 2 THEN D.NUM ELSE - D.NUM END, 0))
                                 ELSE 0 END
                     ), 2) AS INVOICE_NUM
        FROM T_INVOICE_DETAIL D
                 LEFT JOIN T_INVOICE I ON I.INVOICE_ID = D.INVOICE_ID
                 LEFT JOIN T_INVOICE_APPLY A ON I.INVOICE_APPLY_ID = A.INVOICE_APPLY_ID
        WHERE D.DETAILGOODS_ID = #{detailgoodsId,jdbcType=INTEGER}
          and A.VALID_STATUS = 1
          and I.TYPE = 504
          and A.TYPE = 504
        GROUP BY D.DETAILGOODS_ID;
    </select>
</mapper>