package com.vedeng.esign.service.impl;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.exception.ServiceException;
import com.google.common.collect.Maps;
import com.itextpdf.text.Document;
import com.itextpdf.text.Image;
import com.itextpdf.text.PageSize;
import com.itextpdf.text.pdf.PdfWriter;
import com.vedeng.common.constant.CommonConstants;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.util.StringUtil;
import com.vedeng.common.util.UrlUtils;
import com.vedeng.esign.service.ImgAndPdfService;
import com.vedeng.goods.manufacturer.constants.ManufacturerConstants;
import com.vedeng.goods.manufacturer.dao.ManufacturerMapper;
import com.vedeng.goods.manufacturer.model.Manufacturer;
import com.vedeng.infrastructure.oss.service.OssUtilsService;
import com.vedeng.system.dao.AttachmentMapper;
import com.vedeng.system.model.Attachment;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.ImageType;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/12/18 13:57
 **/
@Service
@Slf4j
public class ImgAndPdfServiceImpl implements ImgAndPdfService {

    /**
     * 最大重试次数
     */
    private static final int MAX_RETRY_COUNT = 3;

    @Resource
    private ManufacturerMapper manufacturerMapper;

    @Resource
    private AttachmentMapper attachmentMapper;

    @Autowired
    private OssUtilsService ossUtilsService;

    @Value("${oss_http}")
    private String ossHttp;


    /**
     * 更新盖章后的信息
     *
     * @param firstEngageId 首营id
     * @param relatedId 附件关联id 兼容工厂和首营
     * @param userId 用户id
     * @param oldB 老数据
     * @param fileUrl 文件类型及地址
     * @param isManufacturer 区分厂商和首营产品
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateBCertificates(Integer firstEngageId, Integer relatedId, Integer userId, List<Attachment> oldB, Map<String, String> fileUrl, boolean isManufacturer) {

        long time = System.currentTimeMillis();
        Set<String> keys = fileUrl.keySet();
        for (String key : keys) {
            String file = fileUrl.get(key);
            AtomicInteger atomicInteger = new AtomicInteger(0);
            List<Attachment> img = pdfPath2ImagePaths(file,atomicInteger);
            if (!CollectionUtils.isEmpty(img)) {
                img.forEach(i -> {
                    i.setAttachmentType(CommonConstants.ATTACHMENT_TYPE_974);
                    i.setAttachmentFunction(Integer.valueOf(key));
                    i.setRelatedId(relatedId);
                    i.setCreator(userId);
                    i.setIsDeleted(ErpConst.DELETE_STATE.IS_NOT_DELETE);
                    i.setAddTime(time);
                    manufacturerMapper.insertAttachement(i);
                });
            }
        }

        if (!org.springframework.util.CollectionUtils.isEmpty(oldB)) {
            //将之前的附件状态更新为 1 新附件 更新 1302 1303 1304 1309 1301的照片 批量软删除
            List<Integer> collect = oldB.stream().map(Attachment::getAttachmentId).collect(Collectors.toList());
            attachmentMapper.batchDeleteByPrimaryKey(collect);
        }

        if (isManufacturer) {
            //更新T_MANUFACTURER
            Manufacturer manufacturer = new Manufacturer();
            manufacturer.setManufacturerId(relatedId);
            manufacturer.setIsUpload(getCurUploadStatusById(relatedId));
            manufacturerMapper.updateByPrimaryKeySelective(manufacturer);
        }
    }

    private int getCurUploadStatusById(Integer manufacturerId) {
        Map<String, Object> tempQueryMap = Maps.newHashMapWithExpectedSize(2);
        tempQueryMap.put("attachmentFunction", ManufacturerConstants.ATTACHMENT_FUN_WITH_B);
        tempQueryMap.put("manufacturerId", manufacturerId);
        tempQueryMap.put("attachmentType", CommonConstants.ATTACHMENT_TYPE_974);
        List<Manufacturer> list = manufacturerMapper.IsUpLoad(tempQueryMap);

        int uploadStatus = ErpConst.STATUS_STATE.NO;
        if (list.size() == ManufacturerConstants.ATTACHMENT_FUN_WITH_B.size()) {
            //根据类型去重查
            uploadStatus = ErpConst.STATUS_STATE.YES;
        } else if (list.size() > 0 && list.size() < ManufacturerConstants.ATTACHMENT_FUN_WITH_B.size()) {
            uploadStatus = ErpConst.STATUS_STATE.SOME;
        }

        return uploadStatus;
    }

    /**
     * 图片转pdf
     * @param sources 附件信息
     * @param target 文件名
     * @return String pdf url
     */
    @Override
    public String convertImg2Pdf(List<Attachment> sources, String target) {

        if (CollectionUtils.isEmpty(sources)) {
            log.info("附件oss信息集合为空");
            return "";
        }
        log.info("oss图片转pdf,图片集合:{},临时文件名:{}", sources, target);
        Document document = new Document();
        // 设置文档页边距
        document.setMargins(0, 0, 0, 0);
        ByteArrayOutputStream os = null;
        try {
            os = new ByteArrayOutputStream();
            PdfWriter.getInstance(document, os);
            // 打开文档
            document.open();
            // 获取图片的宽高
            for (Attachment source : sources) {
                if (StringUtils.isEmpty(source.getUri()) || StringUtils.isEmpty(source.getDomain())) {
                    continue;
                }
                String u = ossHttp + source.getDomain() + source.getUri();
                log.info("oss图片路径：{}", u);
                HttpURLConnection urlConnection = null;
                URL url = new URL(u);
                urlConnection = (HttpURLConnection) url.openConnection();
                // 注意一定要设置超时时间 不然会导致线程阻塞。
                urlConnection.setConnectTimeout(15000);
                urlConnection.setReadTimeout(60000);
                urlConnection.connect();
                InputStream inputStream = urlConnection.getInputStream();
                byte[] bytes = readInputStream(inputStream);
                inputStream.close();
                Image image = Image.getInstance(bytes);
                if (image != null) {
                    float imageHeight = image.getScaledHeight();
                    float imageWidth = image.getScaledWidth();
                    // 横图旋转
                    if (imageHeight < imageWidth) {
                        image.setRotationDegrees(90);
                    }
                    // 定为A4页面
                    document.setPageSize(PageSize.A4);

                    image.scalePercent(BigDecimal.valueOf(this.getPercent(imageHeight, imageWidth)).floatValue());
                    // 图片居中
                    float x = (PageSize.A4.getWidth() - image.getScaledWidth());
                    float y = (PageSize.A4.getHeight() - image.getScaledHeight()) / 2;
                    image.setAbsolutePosition(x, y);
                    // 新建一页添加图片
                    document.newPage();
                    document.add(image);
                }
                urlConnection.disconnect();
            }

        } catch (Exception ioe) {
            log.error("img转pdf异常：",ioe);
            throw new ServiceException("img转pdf异常!");
        } finally {
            // 关闭文档
            try {
                document.close();
            } catch (Exception e) {
                log.error("文件关闭异常！",e);
                throw new ServiceException("文件关闭异常！");
            }
        }
        try (InputStream fileInputStream = new ByteArrayInputStream(os.toByteArray())){
            // pdf上传oss
            log.info("{}生成的pdf临时文件上传oss",target);
            String url = ossUtilsService.uploadFileStream2Oss(fileInputStream, "pdf");
            log.info("{}生成的pdf临时文件上传oss:{}",target,url);
            return url;
        } catch (Exception e) {
            log.error("文件上传异常", e);
            throw new ServiceException("文件上传异常");
        }finally {
            if (null != os) {
                try {
                    os.close();
                } catch (Exception e) {
                    log.error("文件关闭异常！",e);
                    throw new ServiceException("文件关闭异常！");
                }
            }
        }
    }

    /**
     * pdf 转图片
     * @param pdfPath 网络pdf地址
     * @return List<Attachment> 附件信息 img
     */
    public List<Attachment> pdfPath2ImagePaths(String pdfPath,AtomicInteger retryCount) {

        long begin = System.currentTimeMillis();
        log.info("将多页pdf转化为图片，pdf路径为：" + pdfPath);
        PDDocument pdDocument = null;
        List<Attachment> imageList = new ArrayList<>();
        InputStream inputStream=null;
        HttpURLConnection urlConnection = null;
        try {
            URL url = new URL(pdfPath);
            urlConnection = (HttpURLConnection) url.openConnection();
            // 注意一定要设置超时时间 不然会导致线程阻塞。
            urlConnection.setConnectTimeout(15000);
            urlConnection.setReadTimeout(60000);
            urlConnection.connect();
            inputStream = urlConnection.getInputStream();
            pdDocument = PDDocument.load(inputStream);
            int pageCount = pdDocument.getNumberOfPages();
            PDFRenderer pdfRenderer = new PDFRenderer(pdDocument);
            for (int pageIndex = 0; pageIndex < pageCount; pageIndex++) {

                long imgBegin = System.currentTimeMillis();
                BufferedImage image = pdfRenderer.renderImageWithDPI(pageIndex, 105, ImageType.RGB);
                long imgEnd = System.currentTimeMillis();
                ByteArrayOutputStream os = new ByteArrayOutputStream();
                ImageIO.write(image, "png", os);
                log.info("图片耗时:{}", imgEnd-imgBegin);

                try(InputStream fileInputStream =  new ByteArrayInputStream(os.toByteArray())){
                    long imgUpBegin = System.currentTimeMillis();
                    String result = ossUtilsService.uploadFileStream2Oss(fileInputStream, "png");
                    if (!StringUtils.isEmpty(result)) {
                        String[] domainAndUriFromUrl = UrlUtils.getDomainAndUriFromUrl(result);
                        if (domainAndUriFromUrl != null && StringUtil.isNotBlank(domainAndUriFromUrl[0]) && StringUtil.isNotBlank(domainAndUriFromUrl[1])) {
                            Attachment attachment = new Attachment();
                            attachment.setDomain(domainAndUriFromUrl[0]);
                            attachment.setUri(domainAndUriFromUrl[1]);
                            attachment.setSuffix("png");
                            imageList.add(attachment);
                        }
                    }
                    long imgUpEnd= System.currentTimeMillis();
                    log.info("第{}张生成的图片oss路径为：{},img图片上传oss:{}", pageIndex, result,imgUpEnd-imgUpBegin);
                }
                os.close();
            }
        } catch (Exception e) {
            log.warn("pdf转img,访问地址：{}异常：{}",pdfPath,e);
            if (retryCount.get() < MAX_RETRY_COUNT) {
                log.warn("电子签章:厂家三证消费,重试次数{},访问网络文件{}", retryCount.get(), pdfPath);
                retryCount.incrementAndGet();
                pdfPath2ImagePaths(pdfPath, retryCount);
            } else {
                log.error("pdf转img,访问地址：{}重试超过3次异常：{}",pdfPath,e);
                throw new ServiceException("pdf转img异常");
            }

        } finally {
            try {
                if (pdDocument != null) {
                    pdDocument.close();
                }
                if (inputStream != null) {
                    inputStream.close();
                }
                if (urlConnection != null) {
                    urlConnection.disconnect();
                }
            } catch (Exception e) {
                log.error("pdf关闭异常：",e);
            }
        }
        long end = System.currentTimeMillis();
        log.info("pdfPath2ImagePaths总耗时：{}",end-begin);
        log.info("处理pdf:{}转图片结果集{}",pdfPath,JSON.toJSONString(imageList));
        return imageList;
    }

    /**
     * 在不改变图片形状的同时，判断，如果h>w，则按h压缩，否则在w>h或w=h的情况下，按宽度压缩
     *
     * @param h 图片高
     * @param w 图片宽
     * @return double 缩放比例
     */
    public double getPercent(float h, float w) {
        log.info("图片高：{}图片宽：{}",h,w);
        double p;
        float p2;
        float p3;
        if (h < w) {
            p2 = PageSize.A4.getHeight() / w * 100;
            p3 = PageSize.A4.getWidth() / h * 100;
        } else {
            p2 = PageSize.A4.getHeight() / h * 100;
            p3 = PageSize.A4.getWidth() / w * 100;
        }
        p = Math.floor(BigDecimal.valueOf(Math.min(p2, p3)).doubleValue());
        log.info("缩放比例:"+p);
        return p;
    }

    public static byte[] readInputStream(InputStream inStream) throws Exception {
        ByteArrayOutputStream outStream = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024*4];
        int len = 0;
        while ((len = inStream.read(buffer)) != -1) {
            outStream.write(buffer, 0, len);
        }
        inStream.close();
        return outStream.toByteArray();
    }
}
