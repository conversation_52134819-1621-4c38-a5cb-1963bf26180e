package com.vedeng.goods.mapper;

import com.vedeng.erp.saleorder.dto.SaleOrderGoodsImageDto;
import com.vedeng.goods.domain.dto.GoodsDto;
import com.vedeng.goods.domain.dto.SkuImageDto;
import com.vedeng.goods.domain.entity.CoreSku;
import com.vedeng.goods.domain.entity.GoodsAttachmentEntity;
import com.vedeng.goods.dto.CoreSkuDto;
import com.vedeng.goods.dto.KingDeeSkuInfoDto;
import com.vedeng.goods.dto.ProductManageAndAsistDto;
import com.vedeng.goods.query.RelatedSkuQuery;
import com.vedeng.goods.query.SearchGoodsQuery;
import com.vedeng.goods.vo.CoreSkuVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Entity com.vedeng.erp.goods.model.CoreSku
 */
@Repository("newCoreSkuMapper")
public interface CoreSkuMapper  {

    /**
     * 根据搜索条件查询商品基本信息
     * @param serachGoodsQuery
     * @return
     */
    List<GoodsDto> listGoodsInfoForSerachGoodsQuery(SearchGoodsQuery serachGoodsQuery);

    /**
     * 根据商品ID获取商品基本信息
     * @param skuId
     * @return
     */
    CoreSkuDto getCoreSkuInfoBySkuId(Integer skuId);

    CoreSkuDto getInfoBySkuNo(String skuNo);

    List<CoreSkuDto> getInfoBySkuNos(@Param(value = "skuNos") List<String> skuNos);

    GoodsAttachmentEntity getGoodsAttachment(Integer skuId);

    /**
     * 根据商品ID获取商品基本信息
     * @param skuNo
     * @return
     */
    CoreSkuDto getCoreSkuInfoBySkuNo(String skuNo);

    /**
     * 根据skuId查询
     * @param skuId 主键
     * @return CoreSku
     */
    CoreSku findBySkuId(@Param("skuId")Integer skuId);


    /**
     * 根据条件批量更新机构等级(V_CORE_SKU)
     * @param list
     * @return
     */
    int updateBatchSelectiveInstitutionLevel(List<CoreSku> list);

    /**
     * 根据条件批量更新机构等级(V_CORE_SKU_SEARCH)
     * @param list
     * @return
     */
    int updateBatchSelectiveInstitutionLevelSkuSearch(List<CoreSku> list);

    /**
     * 根据sku关键字查询所有sku
     * @param relatedSkuQuery 查询参数
     * @return  List<CoreSkuVo>
     */
    List<CoreSkuVo> selectAllSkuByKeywords(RelatedSkuQuery relatedSkuQuery);

    /**
     * 根据skuNo 查询是否有独家产品
     * @param skuNos skuNo集合
     * @return Integer
     */
    Integer selectSkusHaveExclusive(@Param("skuNos") List<String> skuNos);

    /**
     * 根据条件批量更新货期、报备信息、商品等级、审核状态(PURCHASE_TIME、IS_NEED_REPORT、GOODS_LEVEL_NO、CHECK_STATUS)
     * @Param list
     * @Return {@link int}
     */
    int updateBatchSelectiveInitSkuInfo2Front(List<CoreSku> list);

    /**
     * 根据订货号集合批量查询sku
     * @Param skuNoList 订货号集合
     * @Return {@link List<CoreSku>}
     */
    List<CoreSku> listBySkuNoList(List<String> list);

    /**
     * 根据skuId查询信息
     * @param skuId skuId
     * @return CoreSkuVo
     */
    CoreSkuVo getGoodsInfoBySkuId(@Param("skuId") Integer skuId);

    /**
     *
     * @param skuNos
     * @return List<CoreSkuVo>
     */
    List<CoreSkuVo> getGoodsInfoBySkuNos(@Param("skuNos") List<String> skuNos);

    /**
     * 查询所有虚拟商品信息
     * @param status 是否包含禁用商品，1：不包含 0：包含
     * @return 虚拟商品信息集合
     */
    List<CoreSkuVo> getAllVirtualGoodsInfo(@Param("status") Integer status);

    /**
     * 查询所有产品助理 产品经理信息
     * @param skus 商品
     * @return List<ProductManageAndAsistDto>
     */
    List<ProductManageAndAsistDto> batchQueryProductManageAndAsist(@Param("list") List<String> skus);

    /**
     * 查询金蝶所需要的 sku相关信息
     *
     * @param skuId skuId
     * @param limit limit
     * @return KingDeeSkuInfoDto
     */
    List<KingDeeSkuInfoDto> getKingDeeSkuInfoBySkuId(@Param("skuId") Integer skuId, @Param("limit") Integer limit);

    /**
     * 获取审核通过的sku总数
     *
     * @return sku总数
     */
    Integer getCheckPassSkuNum();

    /**
     * 查询所有的skuId
     *
     * @return 所有skuId集合
     */
    List<Integer> getAllSkuId();


    List<CoreSkuVo> findBySkuIdIn(@Param("skuIdCollection")Collection<Integer> skuIdCollection);

    /**
     * 查询所有特殊商品（不可见+固定）
     * @return
     */
    List<Integer> findAllSpecialSkus();


    String getTaxCategoryNo(Integer goodsId);

    List<SkuImageDto> getSkuImage(@Param("skuNoList") List<String> skuNoList);

    List<CoreSkuDto> findBySkuNoIn(@Param("skuNoCollection")Collection<String> skuNoCollection);

    Map<String, Object> skuTipMap(@Param("skuNo") String skuNo);



}




