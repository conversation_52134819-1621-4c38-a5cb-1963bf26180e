package com.vedeng.erp.aftersale.service;

import com.vedeng.erp.aftersale.dto.AfterSaleSkuInfoDto;
import com.vedeng.erp.aftersale.dto.AfterSalesDetailApiDto;
import com.vedeng.erp.aftersale.dto.AfterSalesDto;
import com.vedeng.erp.aftersale.dto.AfterSalesWithDetailDto;
import com.vedeng.erp.finance.dto.InvoiceApplyDto;
import com.vedeng.erp.saleorder.dto.AfterSalesGoodsDto;
import com.vedeng.erp.saleorder.dto.OrderFinanceInfoDto;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 销售售后api
 *
 * <AUTHOR>
 */
public interface AfterSalesApiService {
    /**
     * 根据售后id查询售后信息
     *
     * @param afterSalesNo
     * @return
     * <AUTHOR>
     */
    AfterSalesDto queryInfoByNo(String afterSalesNo);

    /**
     * @param afterSalesId
     * @param invoiceId
     * <AUTHOR>
     * @desc 更新采购售后退票状态
     */
    void updateAfterSalesInvoiceStatus(Integer afterSalesId, Integer invoiceId);

    /**
     * 根据售后单id查询售后信息
     *
     * @param afterSalesId
     * @return
     * <AUTHOR>
     */
    AfterSalesDto queryInfoById(Integer afterSalesId);

    /**
     * 根据售后id更新售后单状态
     *
     * @param afterSalesId
     * @param afterStatus
     * @return
     * <AUTHOR>
     */
    int updateAfterStatusById(Integer afterSalesId, Integer afterStatus);

    /**
     * 查询沟通记录关联的售后单信息（id 单号）
     *
     * @param afterSaleIdList 售后单id集合
     * @return id 单号
     */
    List<Map<String, Object>> getCommunicateAfterSaleInfo(List<Integer> afterSaleIdList);


    /**
     * 获取销售售后单
     *
     * @param saleOrderId 销售id
     * @return List<AfterSalesDto>
     */
    List<AfterSalesDto> findByOrderIdGetSaleOrderAfterSale(Integer saleOrderId);


    /**
     * 根据售后单id查询售后商品信息
     *
     * @param afterSalesId 售后单id
     * @return List<AfterSalesGoodsDto>
     */
    List<AfterSalesGoodsDto> getAfterSalesGoodsByAfterSalesId(Integer afterSalesId);

    /**
     * 根据关联id集合查询售后单信息
     *
     * @param relatedIdList
     * @return
     */
    List<AfterSalesDto> findUnderwayThOrTpByOrderIds(List<Integer> relatedIdList);

    /**
     * 根据售后单id查询信息
     *
     * @param afterSalesId afterSalesId
     * @return AfterSalesDto
     */
    AfterSalesDto getAfterSalesById(Integer afterSalesId);
    AfterSalesDto getAfterSalesByNo(String afterSalesNo);


    List<String> getCannotReturnSkuByAfterSalesId(Integer afterSalesId);

    Integer getAfterSalesNumBySaleorderGoodsId(Integer saleorderGoodsId);

    /**
     * 根据售后单id查询售后详情
     *
     * @param afterSalesId
     * @return
     */
    AfterSalesWithDetailDto findAfterSalesDetailByAfterSalesId(Integer afterSalesId);

    /**
     * 更新发票状态
     *
     * @param invoiceApplyDto
     */
    void tryUpdateInvoiceStatus(InvoiceApplyDto invoiceApplyDto);

    /**
     * 根据售后单id查询售后单详情
     * @param afterSalesId 售后单id
     * @return AfterSalesDetailDto
     */
    AfterSalesDetailApiDto getAfterSalesDetailByAfterSalesId(Integer afterSalesId);

    /**
     * 根据售后单id和商品类型查询售后商品信息
     *
     * @param afterSalesId 售后单id
     * @param goodsType    商品类型
     * @return List<AfterSalesGoodsDto>
     */
    List<AfterSalesGoodsDto> findGoodsByAfterSalesIdAndGoodsType(Integer afterSalesId, Integer goodsType);

    /**
     * 获取销售第三方售后订单财务信息
     * @param afterSalesId
     * @return
     */
    OrderFinanceInfoDto getAftersaleFinanceInfo(Integer afterSalesId);

    AfterSaleSkuInfoDto getGoodsNameModelAndSpecByAfterSalesGoodsId(Integer detailGoodsId);
    /**
     * 保存开票状态
     * @param afterSalesId
     * @param status
     */
    void saveMakeOutInvoiceStatus(Integer afterSalesId, Integer status);

    /**
     * 查询销售单关联的进行中 退货，换货，退款，退票 的售后单
     * @param saleOrderId
     * @return
     */
    List<AfterSalesDto> getOngoingAfterSalesByOrderId(Integer saleOrderId);

    /**
     * 查询销售单关联的已完成的退票售后单
     * @param saleOrderId
     * @return
     */
    List<AfterSalesDto> getCompletedAfterSalesByOrderId(Integer saleOrderId);

    /**
     * 根据销售单查询商品明细售后数量
     * @param saleOrderId
     * @return
     */
    List<AfterSalesGoodsDto> getAfterSalesNum(Integer saleOrderId);

    /**
     * 退票,退货售后单
     * @param saleorderId
     * @return
     */
    List<AfterSalesDto> getAfterSaleListBySaleId(Integer saleorderId);

    /**
     * 进行中售后数量
     * @param saleOrderId
     * @return Integer
     */
    Integer queryAfterSaleInProgress(Integer saleOrderId);

    /**
     * 获取单个商品的退货数量
     * @param saleorderGoodsId
     * @return
     */
    int getPurchaseAfterSalesGoodsNum(Integer saleorderGoodsId);

    /**
     * 获取单个商品的退货金额
     * @param saleorderGoodsId
     * @return
     */
    BigDecimal getPurchaseAfterSalesGoodsAmount(Integer saleorderGoodsId);

    /**
     * 推送售后单到前台商城
     * @param afterSalesId
     */
    void pushAfterSalesToFrontMall(Integer afterSalesId);

    /**
     * ERP客户档案时间轴 售后完结、关闭
     * @param afterSalesId
     */
    void trackCompleteClose(Integer afterSalesId);

    /**
     * 处理客户账户信息
     * @param payApplyId
     * @param afterSalesId
     */
    void handleCustomerBankAccount(Integer payApplyId, Integer afterSalesId);
}
