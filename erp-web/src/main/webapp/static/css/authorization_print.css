html,
body {
    padding: 0;
    margin: 0;
    background: #fff;
    font-family: '微软雅黑', sans-serif;
    font-size: 14px;
    line-height: 1.5;
    color: #333;
}
.wrapper {
    padding: 20px;
    width: 800px;
    margin: 0 auto;
    margin-bottom: 30px;
}
.wrapper img {
    max-width: 100%;
}
.content {
    padding: 0 5px;
    margin-bottom: 100px;
    font-size: 14px;
}
.content p {
    margin: 0;
    line-height: 50px;
    letter-spacing: 2px;
    text-indent: 2em;
}
.content p.right {
    text-align: right;
}
.content p.top {
    margin-top: 80px;
}
.strong {
    color: #027DB4;
    font-size: 16px;
}
.header {
    display: flex;
    align-items: center;
    padding-bottom: 10px;
    border-bottom: 5px solid #0271c1;
}
.header .logo {
    width: 240px;
}
.header .logo img {
    width: 240px;
}
.header .header-txt {
    flex: 1;
    color: #80b8e0;
    font-weight: bold;
    text-align: right;
    font-size: 12px;
}
.title {
    font-size: 24px;
    font-weight: bold;
    text-align: center;
    margin-bottom: 20px;
    margin-top: 10px;
}
.footer {
    border-top: 5px solid #0271c1;
    display: flex;
    color: #80b8e0;
    font-size: 12px;
    font-weight: bold;
    padding-top: 10px;
}
.footer .footer-l {
    width: 50%;
}
.footer .footer-r {
    width: 50%;
    text-align: right;
}
.wrapper .input-print {
    border: 0;
    border-bottom: 1px solid #ccc;
    text-align: center;
    line-height: 21px;
}
.wrapper .input-print.left {
    text-align: left;
}
.wrapper .input-print:focus {
    outline: 0;
}
.wrapper .input-print.s {
    width: 100px;
}
.wrapper .input-print.l {
    width: 200px;
}
.wrapper .input-print.xl {
    width: 300px;
}
.wrapper .input-print.x2 {
    width: 60px;
}

.wrapper .input-print.input-date {
    cursor: pointer;
}
.wrapper .select-print {
    border: 1px solid #ccc;
    height: 24px;
}
.wrapper .form-item {
    margin-top: 20px;
    padding: 7px 0;
    display: flex;
}
.wrapper .form-item .item-label {
    width: 80px;
    text-align: right;
    margin-right: 10px;
}
.wrapper .form-item .item-fields {
    flex: 1;
}
.wrapper .form-item .tip {
    margin-top: 10px;
}
.wrapper .form-item .tip .tip-b {
    color: #f60;
}
.wrapper .form-item .txt-blue {
    color: #027DB4;
}
.wrapper .form-item .input-textarea {
    width: 100%;
    resize: none;
}
.wrapper .form-item .txt-lg {
    font-size: 16px;
}


.print-tab {
    max-width: 1200px;
    margin: 0 auto 20px auto;
    display: flex;
}

.tab-item {
    padding: 10px 15px;
    background: #eee;
    text-align: center;
    cursor: pointer;
    color: #333;
}

.tab-item.active {
    background: #d9eaf7;
}

.item-status.status-red {
    color: #e64545;
}

.calendar  {
    box-sizing: content-box;
}

.calendar .title {
    font-size: 12px;
}

.bt-bg-style.ml10, .bt-bg-style.ml4{
    margin-left: 0 !important;
}