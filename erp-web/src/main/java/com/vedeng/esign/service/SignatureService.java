package com.vedeng.esign.service;

import com.vedeng.authorization.model.User;
import com.vedeng.firstengage.model.FirstEngage;
import com.vedeng.goods.manufacturer.model.Manufacturer;

/**
 * <AUTHOR>
 * @date 2021/12/10 13:57
 **/
public interface SignatureService {
    /**
     * 厂商的电子验签请求
     * @param manufacturer 工厂信息
     * @param sessUser 用户信息
     */
    void checkManufacture(Manufacturer manufacturer, User sessUser);

    /**
     * 医疗器械电子签章
     * @param firstEngage 首营信息
     * @param sessUser 用户信息
     */
    void checkFirstengage(FirstEngage firstEngage, User sessUser);

}
