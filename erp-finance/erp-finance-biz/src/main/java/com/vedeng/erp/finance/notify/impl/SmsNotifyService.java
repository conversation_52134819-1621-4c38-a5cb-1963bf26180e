package com.vedeng.erp.finance.notify.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.finance.domain.dto.SaleInvoiceQrRequestDto;
import com.vedeng.erp.finance.domain.dto.SaleInvoiceQrResponseDto;
import com.vedeng.erp.finance.dto.InvoiceDto;
import com.vedeng.erp.finance.enums.TaxesInterfaceCodeEnum;
import com.vedeng.erp.finance.notify.NotifyService;
import com.vedeng.erp.finance.service.TaxesOpenApiService;
import com.vedeng.infrastructure.shorturl.dto.ShortUrlResponseDto;
import com.vedeng.infrastructure.shorturl.service.ShortUrlApiService;
import com.vedeng.infrastructure.sms.constants.SmsTmpConstant;
import com.vedeng.infrastructure.sms.service.SmsService;
import com.vedeng.infrastructure.taxes.config.TaxesConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description
 * @date 2024/1/22 15:43
 **/
@Service
@Slf4j
public class SmsNotifyService implements NotifyService {

    @Autowired
    private TaxesConfig taxesConfig;

    @Autowired
    private ShortUrlApiService shortUrlApiService;

    @Autowired
    private TaxesOpenApiService taxesOpenApiService;

    @Autowired
    private SmsService smsService;

    @Override
    /**
     *  1。调用税金“获取二维码”链接接口
     * 2。调用前台长链接转短链接接口
     * 3。短信交付，调用短信服务，发送短信
     */
    public void notifyMessage(InvoiceDto invoiceDto) {

        log.info("短信交付：数电发票开蓝票,发票:{}", JSON.toJSONString(invoiceDto));
        SaleInvoiceQrRequestDto saleInvoiceQrRequestDto = new SaleInvoiceQrRequestDto();
        saleInvoiceQrRequestDto.setKprq(DateUtil.formatDateTime(invoiceDto.getOpenInvoiceTime()));
        saleInvoiceQrRequestDto.setFphm(invoiceDto.getInvoiceNo());
        saleInvoiceQrRequestDto.setNsrsbh(taxesConfig.getTaxNo());
        SaleInvoiceQrResponseDto saleInvoiceQrResponseDto = (SaleInvoiceQrResponseDto) taxesOpenApiService.openapi(saleInvoiceQrRequestDto,
                TaxesInterfaceCodeEnum.QR_CODE);
        if (saleInvoiceQrResponseDto.getIsSuccess()) {
            ShortUrlResponseDto shortUrlResponseDto = shortUrlApiService.getShortUrl(saleInvoiceQrResponseDto.getData());
            if (shortUrlResponseDto == null) {
                log.error("短链接生成失败,saleInvoiceQrResponseDto：{},longLinkUrl:{}", JSON.toJSONString(saleInvoiceQrResponseDto), saleInvoiceQrResponseDto.getData());
                return;
            }
            if (StrUtil.isNotEmpty(invoiceDto.getInvoiceTraderContactMobile())) {
                smsService.sendTplSms(invoiceDto.getInvoiceTraderContactMobile(), SmsTmpConstant.OPEN_INVOICE,
                        StrUtil.format("@1@={}", shortUrlResponseDto.getShortCode()));
            }
        }
    }
}
