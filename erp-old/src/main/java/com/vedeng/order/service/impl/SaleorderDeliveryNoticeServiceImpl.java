package com.vedeng.order.service.impl;

import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.order.dao.SaleorderDeliveryNoticeGoodsMapper;
import com.vedeng.order.dao.SaleorderDeliveryNoticeMapper;
import com.vedeng.order.model.*;
import com.vedeng.order.service.SaleorderDeliveryNoticeService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName SaleorderDeliveryNoticeServiceImpl
 * @Description 发货通知Service实现类
 * @Date 2020/7/21 11:46
 */
@Service
public class SaleorderDeliveryNoticeServiceImpl implements SaleorderDeliveryNoticeService {


    private static final Logger LOGGER=LoggerFactory.getLogger(SaleorderDeliveryNoticeServiceImpl.class);
    @Autowired
    private SaleorderDeliveryNoticeMapper saleorderDeliveryNoticeMapper;

    @Autowired
    private SaleorderDeliveryNoticeGoodsMapper saleorderDeliveryNoticeGoodsMapper;

    @Override
    @Transactional
    public int saveOrUpdateDeliveryNotice(String[] thisTimeDeliveryNums, Saleorder saleorder, SaleorderDeliveryNotice deliveryNotice, User user) {
        int result=0;
        if (deliveryNotice.getDeliveryNoticeId()!=null&&deliveryNotice.getDeliveryNoticeId()>0){
           //编辑发货通知
            SaleorderDeliveryNotice notice = saleorderDeliveryNoticeMapper.selectByPrimaryKey(deliveryNotice.getDeliveryNoticeId());
            BeanUtils.copyProperties(deliveryNotice,notice);
            notice.setUpdater(user.getUserId());
            notice.setModTime(System.currentTimeMillis());
            result = saleorderDeliveryNoticeMapper.updateByPrimaryKeySelective(notice);

            //删除原发货通知单中的产品信息
            SaleorderDeliveryNoticeGoodsExample example=new SaleorderDeliveryNoticeGoodsExample();
            example.createCriteria().andDeliveryNoticeIdEqualTo(deliveryNotice.getDeliveryNoticeId());
            SaleorderDeliveryNoticeGoods updateGoods=new SaleorderDeliveryNoticeGoods();
            updateGoods.setDeleteState(ErpConst.DELETE_STATE.IS_DELETE);
            int i = saleorderDeliveryNoticeGoodsMapper.updateByExampleSelective(updateGoods, example);
            LOGGER.info("删除 ：{} 条产品信息",i);
        }else{
            //新增发货通知
            deliveryNotice.setCreator(user.getUserId());
            deliveryNotice.setAddTime(System.currentTimeMillis());
            deliveryNotice.setUpdater(user.getUserId());
            deliveryNotice.setModTime(System.currentTimeMillis());
            deliveryNotice.setOrderId(saleorder.getSaleorderId());
            deliveryNotice.setOrderNo(saleorder.getSaleorderNo());
            //生成发货通知单的末三位序号
            String nextDeliveryNoticeNoStr="";
            try {
                 nextDeliveryNoticeNoStr=createDeliveryNoticeNo(saleorder.getSaleorderId());
            }catch (Exception e){
                LOGGER.error("生成发货通知单的末三位序号出现错误",e);
                throw new RuntimeException();
            }
            deliveryNotice.setDeliveryNoticeNo(nextDeliveryNoticeNoStr);
            //保存发货通知单
             result=saleorderDeliveryNoticeMapper.insertSelective(deliveryNotice);
        }
        if (result>0){
            String string = Arrays.toString(thisTimeDeliveryNums);
            String[] split = string.split(",");

            if (split.length==3){
                Integer  saleorderGoodsId =Integer.parseInt(thisTimeDeliveryNums[0]);
                Integer  num =Integer.parseInt(thisTimeDeliveryNums[1]);
                Integer goodsId=Integer.parseInt(thisTimeDeliveryNums[2]);
                String thisTimeDeliveryNum=saleorderGoodsId+","+num+","+goodsId;
                thisTimeDeliveryNums= new String[]{thisTimeDeliveryNum};
            }

            for (String thisTimeDeliveryNum:thisTimeDeliveryNums){
                String[] saleorderGoodsIdAndNums =thisTimeDeliveryNum.split(",");
                Integer  saleorderGoodsId =Integer.parseInt(saleorderGoodsIdAndNums[0]);
                Integer  num =Integer.parseInt(saleorderGoodsIdAndNums[1]);
                Integer goodsId=Integer.parseInt(saleorderGoodsIdAndNums[2]);
                SaleorderDeliveryNoticeGoods saleorderDeliveryNoticeGoods=new SaleorderDeliveryNoticeGoods();
                saleorderDeliveryNoticeGoods.setDeliveryNoticeId(deliveryNotice.getDeliveryNoticeId());
                saleorderDeliveryNoticeGoods.setSaleorderGoodsId(saleorderGoodsId);
                saleorderDeliveryNoticeGoods.setNum(num);
                saleorderDeliveryNoticeGoods.setGoodsId(goodsId);
                saleorderDeliveryNoticeGoods.setOrderId(saleorder.getSaleorderId());
                saleorderDeliveryNoticeGoods.setDeleteState(ErpConst.DELETE_STATE.IS_NOT_DELETE);
                //保存发货通知单的产品信息
                saleorderDeliveryNoticeGoodsMapper.insert(saleorderDeliveryNoticeGoods);
            }


        }
        return result;
    }

    /**
     * 生成发货通知单 单号
     * @Param: [saleorderId]
     * @Return: java.lang.String
     * @Author: Rivan
     * @Date: 2020/7/21 16:42
     */
    private String createDeliveryNoticeNo(Integer saleorderId){
        String nextDeliveryNoticeNoStr=ErpConst.DELIVERY_NOTICE_NO.START;
        String maxDeliveryNoticeNo=saleorderDeliveryNoticeMapper.getMaxDeliveryNoticeNoBySaleorderId(saleorderId);
        if (StringUtils.isBlank(maxDeliveryNoticeNo)){
            return nextDeliveryNoticeNoStr;
        }
        if(ErpConst.DELIVERY_NOTICE_NO.END.equals(maxDeliveryNoticeNo)){
            throw new RuntimeException("单号已达最大999，无法继续新增发货通知");
        }
        //+1生成新的发货通知单No
        int nextDeliveryNoticeNo = Integer.parseInt(maxDeliveryNoticeNo) + 1;
        nextDeliveryNoticeNoStr=String.format("%03d",Integer.valueOf(nextDeliveryNoticeNo));
        return nextDeliveryNoticeNoStr;
    }

    @Override
    public SaleorderDeliveryNotice getSaleorderDeliveryNoticeById(SaleorderDeliveryNotice saleorderDeliveryNotice) {
        return saleorderDeliveryNoticeMapper.selectByPrimaryKey(saleorderDeliveryNotice.getDeliveryNoticeId());
    }

    @Override
    public List<SaleorderDeliveryNoticeGoods> getSaleorderDeliveryNoticeGoodsByDeliveryNoticeId(SaleorderDeliveryNotice saleorderDeliveryNotice) {
        SaleorderDeliveryNoticeGoodsExample example=new SaleorderDeliveryNoticeGoodsExample();
        example.createCriteria().andDeliveryNoticeIdEqualTo(saleorderDeliveryNotice.getDeliveryNoticeId())
                .andDeleteStateEqualTo(ErpConst.DELETE_STATE.IS_NOT_DELETE);
        return saleorderDeliveryNoticeGoodsMapper.selectByExample(example);
    }

    @Override
    public List<SaleorderDeliveryNotice> getSaleorderDeliveryNoticeByOrderId(Saleorder saleorder) {
        SaleorderDeliveryNoticeExample example=new SaleorderDeliveryNoticeExample();
        example.createCriteria().andOrderIdEqualTo(saleorder.getSaleorderId());
        return saleorderDeliveryNoticeMapper.selectByExample(example);
    }

    @Override
    public int updateSaleorderDeliveryNoticeStatus(Integer deliveryNoticeId,Integer status) {
        SaleorderDeliveryNotice notice=new SaleorderDeliveryNotice();
        notice.setDeliveryNoticeId(deliveryNoticeId);
        notice.setStatus(status);
        int result = saleorderDeliveryNoticeMapper.updateByPrimaryKeySelective(notice);
        return result;
    }

    @Override
    public int updateSaleorderDeliveryNoticeAuditStatus(Integer deliveryNoticeId,Integer auditStatus) {
        SaleorderDeliveryNotice notice=new SaleorderDeliveryNotice();
        notice.setDeliveryNoticeId(deliveryNoticeId);
        notice.setAuditStatus(auditStatus);
        int result = saleorderDeliveryNoticeMapper.updateByPrimaryKeySelective(notice);
        return result;
    }

    @Override
    public int getDeliveryNoticeGoodNum(Integer saleorderId, Integer saleorderGoodsId,Integer deliveryNoticeId) {
        return saleorderDeliveryNoticeMapper.getDeliveryNoticeGoodNum(saleorderId,saleorderGoodsId,deliveryNoticeId);
    }

    @Override
    public int updateDeliveryNoticeByPrimaryKeySelective(Integer deliveryNoticeId) {
        SaleorderDeliveryNotice notice=new SaleorderDeliveryNotice();
        notice.setDeliveryNoticeId(deliveryNoticeId);
        notice.setValidTime(System.currentTimeMillis());
        int result = saleorderDeliveryNoticeMapper.updateByPrimaryKeySelective(notice);
        return result;
    }
}
