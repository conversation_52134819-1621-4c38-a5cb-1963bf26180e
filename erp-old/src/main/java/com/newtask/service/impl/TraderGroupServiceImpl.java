package com.newtask.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.newtask.service.TraderGroupService;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.http.HttpRestClientUtil;
import com.vedeng.common.http.NewHttpClientUtils;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.util.BitsetUtils;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.common.util.StringUtil;
import com.vedeng.crm.api.dto.customergroup.TraderGroupEXTDto;
import com.vedeng.crm.api.dto.label.SimpleLabelQueryReqDTO;
import com.vedeng.trader.dao.RTraderGroupJTraderMapper;
import com.vedeng.trader.dao.RTraderLabelJTraderMapper;
import com.vedeng.trader.dao.TraderMapper;
import com.vedeng.trader.group.model.TraderGroup;
import com.vedeng.trader.model.RTraderGroupJTrader;
import com.vedeng.trader.model.Trader;
import com.vedeng.trader.model.vo.TraderCustomerVo;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

@Service
public class TraderGroupServiceImpl extends TraderGroupOrLabelService implements TraderGroupService {

    private Logger logger = LoggerFactory.getLogger(TraderGroupServiceImpl.class);

    @Autowired
    private RTraderGroupJTraderMapper rTraderGroupJTraderMapper;

    @Autowired
    private RTraderLabelJTraderMapper rTraderLabelJTraderMapper;

    @Autowired
    private TraderMapper traderMapper;

    public static ThreadPoolExecutor getExecutor() {
        return executor;
    }

    /**
     * <b>Description:</b>处理分群集合<br>
     *
     * @param
     * @return
     * @Note <b>Author:calvin</b>
     * <br><b>Date:</b> 2020/5/27
     */
    @Override
    public ResultInfo handleTraderGroup(List<TraderGroupEXTDto> traderGroupList, List<Integer> organizations) {
        for (TraderGroupEXTDto g : traderGroupList) {
            if (g == null || g.getTraderGroupId() == null || g.getRuleCondition() == null) {
                continue;
            }
            ResultInfo result = handleOneGroup(g, organizations);
            if (result.getStatus().equals(0)) {
                saveResultToCrm(g.getTraderGroupId(), 1, "group");
            } else {
                saveResultToCrm(g.getTraderGroupId(), 0, "group");
            }
        }
        return new ResultInfo(0, "操作成功");
    }

    /**
     * <b>Description:</b>处理单个分群<br>
     *
     * @param
     * @return
     * @Note <b>Author:calvin</b>
     * <br><b>Date:</b> 2020/5/27
     */
    @Override
    public ResultInfo handleOneGroup(TraderGroupEXTDto group, List<Integer> organizations) {
        if (group == null || group.getTraderGroupId() == null) {
            return new ResultInfo(-1, "客户群信息为空");
        }

        if (group.getRuleCondition() == null) {
            return new ResultInfo(-1, "操作失败，生效规则不得为空");
        }
        try {
            BitSet result = getBaseTraderIds(group, organizations);
            logger.info("基本信息结果集",result);
            solveCustomerCondition(result, group);
            if (result.length() == 0) {
                return new ResultInfo(0, "操作成功");
            }
            if (CollectionUtils.isEmpty(group.getBehaviorList())) {
                updateTraderGroupRelation(result, group.getTraderGroupId());
                return new ResultInfo(0, "操作成功");
            }
            ResultInfo behaviorRes=solveBehaviors(result,group.getBehaviorList(),group);
            if(behaviorRes.getCode()==-1){
                return behaviorRes;
            }
            updateTraderGroupRelation(result, group.getTraderGroupId());
        } catch (Exception ex) {
            logger.error("处理单个分群报错", ex);
            return new ResultInfo(-1, "处理单个分群出错");
        }
        return new ResultInfo(0, "操作成功");
    }


    private BitSet solveCustomerCondition(BitSet res, TraderGroupEXTDto group) {
        solveTraderIds(res, group);
        solveTags(res, group);
        return res;
    }



    /**
     * <b>Description:</b>处理指定标签<br>
     *
     * @param
     * @return
     * @Note <b>Author:calvin</b>
     * <br><b>Date:</b> 2020/5/27
     */
    private void solveTags(BitSet res, TraderGroupEXTDto group) {
        try {
            if (StringUtil.isNotBlank(group.getTraderTagIds()) && group.getTraderTagIds().length() > 2) {
                BitSet tagSet = new BitSet();
                TypeReference<List<Integer>> typeReference = new TypeReference<List<Integer>>() {
                };
                List<Integer> tagIds = JsonUtils.readValueByType(group.getTraderTagIds(), typeReference);
                if (CollectionUtils.isEmpty(tagIds)) {
                    return;
                }
                tagIds=getLabelIdsNotDelete(tagIds);
                if(CollectionUtils.isEmpty(tagIds)){
                    return;
                }
                List<Integer> traderIds = rTraderLabelJTraderMapper.getTraderIdsByLabelIds(tagIds);
                for (Integer id : traderIds) {
                    if (id == null) {
                        continue;
                    }
                    tagSet.set(id);
                }
                if (group.getRuleCondition() == 0) {
                    res.or(tagSet);
                } else if (group.getRuleCondition() == 1) {
                    res.and(tagSet);
                }
            }
        } catch (Exception ex) {
            logger.error("处理标签出错", ex);
        }
    }






    /**
     * <b>Description:</b>更新客户分群信息<br>
     *
     * @param
     * @return
     * @Note <b>Author:calvin</b>
     * <br><b>Date:</b> 2020/5/27
     */
    private void updateTraderGroupRelation(BitSet result, Integer groupId) {
        Page page = new Page(1, 1000);
        int currentPage = 1;
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("groupId", groupId);
        queryMap.put("page", page);
        BitSet old = new BitSet();
        do {
            List<Integer> oldTraderIds = rTraderGroupJTraderMapper.getTraderIdsListPage(queryMap);
            if (CollectionUtils.isNotEmpty(oldTraderIds)) {
                for (Integer id : oldTraderIds) {
                    if (id == null) {
                        continue;
                    }
                    old.set(id);
                }
            }
            currentPage++;
            page.setPageNo(currentPage);
        } while (currentPage <= page.getTotalPage());
        List<Integer> insertIds = null;
        if (old.length() == 0) {
            insertIds = BitsetUtils.bitSet2List(result);
        } else {
            BitSet oldOne = (BitSet) old.clone();
            old.andNot(result);
            List<Integer> deleteTraderIds = BitsetUtils.bitSet2List(old);
            if(CollectionUtils.isNotEmpty(deleteTraderIds)) {
                rTraderGroupJTraderMapper.deleteByGroupIdAndTraderIds(groupId, deleteTraderIds);
            }
            result.andNot(oldOne);
            insertIds = BitsetUtils.bitSet2List(result);
        }
        if (CollectionUtils.isNotEmpty(insertIds)) {
            for (Integer id : insertIds) {
                if (id == null) {
                    continue;
                }
                RTraderGroupJTrader rTraderGroupJTrader = new RTraderGroupJTrader();
                rTraderGroupJTrader.setTraderGroupId(groupId);
                rTraderGroupJTrader.setTraderId(id);
                rTraderGroupJTraderMapper.insert(rTraderGroupJTrader);
            }
        }
    }
    @Override
    public List<TraderGroup> getTraderGroupInfo(List<Integer> traderGroupIdList) {
        try {
            String json = JsonUtils.translateToJson(traderGroupIdList);
            String url = crmUrl+"api/customergroup/queryGroupInfo";
            logger.info("getTraderGroupInfo 查询客户分群信息:"+traderGroupIdList.toString());
            JSONObject resultInfo = NewHttpClientUtils.httpPost(url, json);
            if(resultInfo != null){
                logger.info("getTraderGroupInfo 结果:{}",resultInfo.toString());
                List<TraderGroup> result =  ((List<Map<String, Object>>) resultInfo.get("data")).stream().map(
                        map1 ->{
                            TraderGroup traderGroup = new TraderGroup();
                            traderGroup.setTraderGroupName(map1.get("traderGroupName").toString());
                            traderGroup.setTraderGroupId(Integer.valueOf(map1.get("traderGroupId").toString()));
                            return traderGroup;
                        }
                ).collect(Collectors.toList());
                return result;
            }
        }catch (Exception e){
            logger.error("getTraderGroupInfo error",e);
        }
        return new ArrayList<>();
    }

    @Override
    public Map<Integer, TraderGroup> getTraderGroupMap(List<TraderGroup> traderGroupList) {
        Map<Integer, TraderGroup> result = new HashMap<>(16);
        try {
            if(CollectionUtils.isNotEmpty(traderGroupList)){
                result = traderGroupList.stream().collect(Collectors.toMap(TraderGroup::getTraderGroupId,item -> item));
            }
        }catch (Exception e){
            logger.error("getTraderGroupMap error",e);
        }
        return result;
    }

    @Override
    public List<TraderCustomerVo> setTraderGroupname(List<TraderCustomerVo> list, List<TraderGroup> traderGroupList) {
        try {
            if(!org.springframework.util.CollectionUtils.isEmpty(list) && !org.springframework.util.CollectionUtils.isEmpty(traderGroupList)){
                Map<Integer,TraderGroup>  map = this.getTraderGroupMap(traderGroupList);
                for (TraderCustomerVo traderCustomerVo : list) {
                    List<Integer> traderGroupIdList = traderCustomerVo.getTraderGroupIdList();
                    if(!org.springframework.util.CollectionUtils.isEmpty(traderGroupIdList)) {
                        StringBuffer sb = new StringBuffer();
                        for (Integer traderGroupId : traderGroupIdList) {
                            TraderGroup traderGroup = map.get(traderGroupId);
                            if(traderGroup == null){
                                continue;
                            }
                            sb = sb.append(traderGroup.getTraderGroupName()).append("/");
                        }
                        if(sb.length()>0){
                            traderCustomerVo.setTraderGroupStr(sb.deleteCharAt(sb.length() - 1).toString());
                        }
                    }
                }
            }
        }catch (Exception e){
            logger.error("setTraderGroupname error",e);
        }
        return list;
    }

    @Override
    public List<Trader> queryNoPublicGroupList() {
        return traderMapper.queryNoPublicGroupList();
    }


    private List<Integer> getLabelIdsNotDelete(List<Integer> labelIds){
        List<Integer> ids=null;
        SimpleLabelQueryReqDTO queryReqDTO=new SimpleLabelQueryReqDTO();
        queryReqDTO.setLabelIds(labelIds);
        com.alibaba.fastjson.TypeReference<RestfulResult<List<Integer>>> typeReference=new com.alibaba.fastjson.TypeReference<RestfulResult<List<Integer>>>(){};
        String url=crmUrl+"api/customer/label/ids/not/delete";
        RestfulResult<List<Integer>> result = HttpRestClientUtil.restPost(url, typeReference, null, queryReqDTO);
        if(result!=null&&result.isSuccess()&&CollectionUtils.isNotEmpty(result.getData())) {
            ids = result.getData();
        }
        return ids;
    }
}
